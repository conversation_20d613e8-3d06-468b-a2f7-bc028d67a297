<% include header.html %>
  <el-container id="Main">
    <% include eheader.html %>
      <el-container>
        <% include eside.html %>
          <el-main>
            <el-row>
              <el-col :span="24">
                <el-breadcrumb separator-class="el-icon-arrow-right">
                  <el-breadcrumb-item>
                    <a href='/'>首页</a>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    <% if(bigType=='sku' ){ %><a href='/sku/service'>SKU配置</a>
                      <% }else{ %><a href='/sku/solution'>SKU配置</a>
                        <% } %>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    <% if(bigType=='sku' ){ %>添加SKU<% }else{ %>添加解决方案<% } %>
                  </el-breadcrumb-item>
                </el-breadcrumb>
              </el-col>
            </el-row>
            <el-row>
              <h3 class="stit">基本属性</h3>
              <el-form :model="form" label-width="120px" :rules="rules" ref="form">
                <el-form-item label="名称" prop="name">
                  <el-input v-model:trim="form.name"></el-input>
                </el-form-item>
                <el-form-item label="别名" prop="alias">
                  <el-input v-model:trim="form.alias"></el-input>
                </el-form-item>
                <el-form-item label="所属行业">
                  <el-checkbox v-for="(item, index) in tradeList" :key='index' v-model="item.checked"
                    style="width:200px;margin:0 30px 5px 0 " border>{{item.name}}</el-checkbox>
                </el-form-item>
                <el-form-item label="所属服务">
                  <el-checkbox v-for="(item, index) in serviceList" :key='index' v-model="item.checked"
                    style="width:200px;margin:0 30px 5px 0 " border>{{item.name}}</el-checkbox>
                </el-form-item>
                <el-form-item label="BU">
                  <el-select placeholder="请选择" v-model="form.bu_id" style="width:100%" clearable>
                    <el-option v-for="item of buList" :key="item.buId" :label="item.buName" :value="item.buId">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="目的国">
                  <el-select multiple placeholder="请选择" v-model="form.region" style="width:100%">
                    <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-form>
              <hr>
              <h3 class="stit">描述</h3>
              <el-form :model="form" label-width="120px">
                <el-form-item label="素材图">
                  <el-upload class="cover-uploader box1"
                    :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"' :data='uploadData'
                    :show-file-list="false"
                    :on-success="(response, file, fileList) => { return handleAvatarSuccess(response, file, fileList, 'all')}"
                    :before-upload="beforeAvatarUpload" name="file" accept="image/jpeg,image/png,image/gif">
                    <img v-if="sourceImage" :src="sourceImage" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <span>图片格式JPG,PNG,GIF,建议尺寸858px * 360px</span>
                </el-form-item>
                <el-form-item label="ALT属性">
                  <el-input placeholder="请输入ALT属性..." v-model="form.img_alt" maxlength='50'></el-input>
                </el-form-item>
                <el-form-item label="PC端封面图">
                  <div>
                    <el-switch v-model="form.banner_type" :active-value=0 :inactive-value=1 active-text="图片"
                      inactive-text="自定义">
                    </el-switch>
                  </div>
                  <div v-show='!form.banner_type'>
                    <el-upload class="cover-uploader box2"
                      :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                      :data='uploadData' :show-file-list="false"
                      :on-success="(response, file, fileList) => { return handleAvatarSuccess(response, file, fileList, 'cover_img')}"
                      :before-upload="beforeAvatarUpload" name="file" accept="image/jpeg,image/png,image/gif">
                      <img v-if="form.cover_img" :src="form.cover_img" class="avatar">
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <span>图片格式JPG,PNG,GIF,建议尺寸858px * 290px</span>
                  </div>
                  <div v-show='form.banner_type'>
                    <div>
                      <textarea cols="60" rows="5" class="el-input__inner foolist" id='banner_content'
                        v-model="form.banner_content" style="height:100px" placeholder="内容"></textarea>
                    </div>
                  </div>
                </el-form-item>

                <el-form-item label="PC缩略图">
                  <el-upload class="cover-uploader box3"
                    :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"' :data='uploadData'
                    :show-file-list="false"
                    :on-success="(response, file, fileList) => { return handleAvatarSuccess(response, file, fileList, 'thumb_img')}"
                    :before-upload="beforeAvatarUpload" name="file" accept="image/jpeg,image/png,image/gif">
                    <img v-if="form.thumb_img" :src="form.thumb_img" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <span>图片格式JPG,PNG,GIF,建议尺寸284px * 142px</span>
                </el-form-item>

                <el-form-item label="移动端封面图">
                  <div>
                    <el-switch v-model="form.m_banner_type" :active-value=0 :inactive-value=1 active-text="图片"
                      inactive-text="自定义">
                    </el-switch>
                  </div>
                  <div v-show='!form.m_banner_type'>
                    <el-upload class="cover-uploader box4"
                      :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                      :data='uploadData' :show-file-list="false"
                      :on-success="(response, file, fileList) => { return handleAvatarSuccess(response, file, fileList, 'm_cover_img')}"
                      :before-upload="beforeAvatarUpload" name="file" accept="image/jpeg,image/png,image/gif">
                      <img v-if="form.m_cover_img" :src="form.m_cover_img" class="avatar">
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <span>图片格式JPG,PNG,GIF,建议尺寸750px * 360px</span>
                  </div>
                  <div v-show='form.m_banner_type'>
                    <div>
                      <textarea cols="60" rows="5" class="el-input__inner foolist" id='m_banner_content'
                        v-model="form.m_banner_content" style="height:100px" placeholder="内容"></textarea>
                    </div>
                  </div>
                </el-form-item>

                <el-form-item label="移动端缩略图">
                  <el-upload class="cover-uploader box5"
                    :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"' :data='uploadData'
                    :show-file-list="false"
                    :on-success="(response, file, fileList) => { return handleAvatarSuccess(response, file, fileList, 'm_thumb_img')}"
                    :before-upload="beforeAvatarUpload" name="file" accept="image/jpeg,image/png,image/gif">
                    <img v-if="form.m_thumb_img" :src="form.m_thumb_img" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <span>图片格式JPG,PNG,GIF,建议尺寸214px * 110px</span>
                </el-form-item>

                <el-form-item label="副标题">
                  <el-input v-model="form.sub_title"></el-input>
                </el-form-item>
                <el-form-item label="描述">
                  <textarea v-model="form.description" cols="60" rows="5" class="el-input__inner"
                    style="height:100px"></textarea>
                </el-form-item>
                <el-form-item label="是否留言表单">
                  <el-switch v-model="form.is_message" active-value=1 inactive-value=0 active-color="#13ce66"></el-switch>
                </el-form-item>
                <el-form-item label="是否可订购">
                  <el-switch v-model="form.is_buy" active-color="#13ce66"></el-switch>
                </el-form-item>
                <el-form-item label="订购链接" v-if="form.is_buy">
                  <el-input v-model:trim="form.buy_url" placeholder="https://"></el-input>
                </el-form-item>
                <el-form-item label="是否开启课程">
                  <el-switch v-model="form.is_el" active-color="#13ce66"></el-switch>
                </el-form-item>
                <el-form-item label="课程链接" v-if="form.is_el">
                  <el-input v-model="form.el_url" placeholder="https://"></el-input>
                </el-form-item>
                <el-form-item label="服务推荐">
                  <div>
                    <el-button icon='el-icon-plus' @click='handlerAddRecommend'>添加服务推荐</el-button>
                  </div>
                  <draggable class='card_box' v-model="recommends" group="people" @start="drag=true" @end="drag=false">
                    <el-card :body-style="{ padding: '0px' }" v-for="(item, index) in recommends" :key="item.sku_id">
                      <template v-if='item.sku_id'>
                        <img :src="item.sku_image" class="image">
                        <span>{{ item.sku_name }}</span>
                        <div class="bottom clearfix">
                          <el-button type="primary" @click='handlerRecimmendEdit(index)' icon='el-icon-edit' circle>
                          </el-button>
                          <el-button type="danger" @click='handlerRemoveSku(index)' icon='el-icon-delete' circle>
                          </el-button>
                        </div>
                      </template>
                      <template v-else>
                        <el-button icon='el-icon-plus' style='width: 300px; height: 300px;'
                          @click='handlerRecimmendEdit(index)'></el-button>
                      </template>
                    </el-card>
                  </draggable>
                </el-form-item>
                <el-form-item label="标签">
                  <el-tag :key="tag" v-for="tag in form.tag" closable :disable-transitions="false"
                    @close="handleClose(tag)" style="margin-right:10px"> {{tag}} </el-tag>
                  <el-input class="input-new-tag" v-if="inputVisible" v-model="inputValue" ref="saveTagInput"
                    size="small" @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm"
                    style="width:120px"></el-input>
                  <el-button v-else class="button-new-tag" size="small" @click="showInput">+ New Tag</el-button>
                </el-form-item>
              </el-form>
              <hr style="margin-top:30px">
              <div style="text-align: center;margin-top:-30px;">
                <el-radio-group v-model="form.type" style="margin-bottom: 30px;">
                  <el-radio-button label="3">普通类</el-radio-button>
                  <el-radio-button label="4">培训类</el-radio-button>
                </el-radio-group>
              </div>
              <template v-if="form.type == 4">
                <el-form :model="form" label-width="120px">
                  <el-form-item label="开课时间">
                    <el-select multiple placeholder="请选择" v-model="form.course.time" style="width:100%">
                      <el-option key="一月" label="一月" value="一月"></el-option>
                      <el-option key="二月" label="二月" value="二月"></el-option>
                      <el-option key="三月" label="三月" value="三月"></el-option>
                      <el-option key="四月" label="四月" value="四月"></el-option>
                      <el-option key="五月" label="五月" value="五月"></el-option>
                      <el-option key="六月" label="六月" value="六月"></el-option>
                      <el-option key="七月" label="七月" value="七月"></el-option>
                      <el-option key="八月" label="八月" value="八月"></el-option>
                      <el-option key="九月" label="九月" value="九月"></el-option>
                      <el-option key="十月" label="十月" value="十月"></el-option>
                      <el-option key="十一月" label="十一月" value="十一月"></el-option>
                      <el-option key="十二月" label="十二月" value="十二月"></el-option>
                      <el-option key="常年开课" label="常年开课" value="常年开课"></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="开课地点">
                    <el-select multiple placeholder="请选择" v-model="form.course.area" style="width:100%">
                      <el-option v-for="city of citys" :key="city.name" :label="city.name" :value="city.name">
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="课程天数">
                    <el-input v-model:number="form.course.days" style="width: 72px;"></el-input> 天
                  </el-form-item>
                </el-form>
              </template>

              <h3 class="stit">楼层信息</h3>
              <el-form :model="form" label-width="120px" v-for="(item, index) in form.sContent" :key='index'>
                <template v-if="item.show">
                  <hr v-if="index != 0" style="border-style: dashed;color:#999">
                  <el-form-item label="楼层模板">
                    <el-col :span="18">
                      <el-button-group>
                        <el-button
                          :class='{"el-button--primary": (!item.template_id && !tempItem.id) || tempItem.id == item.template_id}'
                          @click='getTemplateDtl(tempItem, index, "Editor" + item.id)'
                          v-for='(tempItem, tempIndex) of templates' :key='tempItem.id'>{{ tempItem.name }}
                        </el-button>
                      </el-button-group>
                    </el-col>
                    <el-col :span="6" style="text-align: right;">
                      <el-button v-if='!item.template_id' @click="handleSaveTemp(index)">当前楼层保存为新模板</el-button>
                      <el-button type="danger" icon="el-icon-arrow-up" :disabled="!index" circle
                        @click="_upContent(index)">
                      </el-button>
                      <el-button type="danger" icon="el-icon-arrow-down" :disabled="index === form.sContent.length - 1"
                        circle @click="_downContent(index)"></el-button>
                      <el-button type="danger" icon="el-icon-delete" circle @click="_delContent(index)"></el-button>
                    </el-col>
                  </el-form-item>
                  <el-form-item label="标题">
                    <el-col :span="18">
                      <el-input v-model="item.title" placeholder="请输入标题..." :disabled="item.lock"></el-input>
                    </el-col>
                    <el-col :span="6" style="text-align:right">
                      <span style="float:left;padding-left: 12px;">隐藏标题<el-switch v-model="item.hide_title"></el-switch>
                      </span>
                    </el-col>
                  </el-form-item>
                  <el-form-item label="锚点标题">
                    <el-col :span="18">
                      <el-input v-model="item.anchor_title" placeholder="请输入锚点标题..."
                        @change="((val)=>{handleChangeAnchor(val, 'sContent', index)})"></el-input>
                    </el-col>
                    <el-col :span="6" style="text-align:right">
                      <span style="float:left;padding-left: 12px;">显示锚点
                        <el-switch active-value=1 inactive-value=0 v-model="item.anchor_show"
                          :disabled='!item.anchor_title'></el-switch>
                      </span>
                    </el-col>
                  </el-form-item>
                  <el-form-item label="内容">
                    <textarea :id="'Editor' + item.id" v-model="item.content" cols="60" rows="5"
                      class="el-input__inner foolist foocontent" style="height:100px" placeholder="请输入内容..."></textarea>
                    <div v-if='item.lock' class='content_mark'></div>
                  </el-form-item>
                </template>
              </el-form>
              <el-form :model="form" label-width="120px">
                <el-form-item label="">
                  <el-button type="success" @click='_addContent'>新加楼层</el-button>
                </el-form-item>
              </el-form>
              <hr>

              <div v-for='(floor, floorIndex) of floors'>
                <div class="floor">
                  <div v-if='floor.name === "case"'>
                    <div v-for="(item, index) in form.skuCases" :key='item.id'>
                      <el-row v-if="!index">
                        <el-col :span="20">
                          <h3>资讯楼层标题</h3>
                          <el-input v-model="item.title"
                            style='width: 200px; padding-left: 20px; display: inline-block;'>
                          </el-input>
                        </el-col>
                        <el-col :span="4" style="text-align: right;">
                          <el-button type="danger" icon="el-icon-arrow-up" :disabled="!floorIndex" circle
                            @click="_upFllor(floorIndex)">
                          </el-button>
                          <el-button type="danger" icon="el-icon-arrow-down"
                            :disabled="floorIndex === floors.length - 1" circle @click="_downFloor(floorIndex)">
                          </el-button>
                        </el-col>
                      </el-row>
                      <el-form :model="form" label-width="120px">
                        <el-form :model="form" label-width="120px" v-if="!index">
                          <el-form-item label="锚点标题">
                            <el-col :span="18">
                              <el-input placeholder="请输入锚点标题..." v-model='item.anchor_title'
                                @change="((val)=>{handleChangeAnchor(val, 'skuCases', index)})"></el-input>
                            </el-col>
                            <el-col :span="6" style="text-align:right">
                              <span style="float:left;padding-left: 12px;">显示锚点
                                <el-switch v-model='item.anchor_show' active-value='1' inactive-value='0'
                                  :disabled='!item.anchor_title'></el-switch>
                              </span>
                            </el-col>
                          </el-form-item>
                        </el-form>
                        <el-form-item label="">
                          <el-col :span="20">
                            <el-input placeholder="资讯ID" type='number' v-model="item.case_id" style='width: 600px;'
                              :disabled="!item.edit">
                              <template slot="prepend">
                                <%- view_url %>/case/article/detail-
                              </template>
                              <template slot="append">.html</template>
                            </el-input>
                            <el-tag v-if='item.name'><a target="_blank"
                                :href='"<%- view_url %>/case/article/detail-" + item.case_id + ".html"'>{{ item.name
                                }}</a></el-tag>
                          </el-col>
                          <el-col :span="4" style="text-align: right;">
                            <el-button icon='el-icon-check' circle v-if='item.edit' @click='handleCaseCheck(index)'>
                            </el-button>
                            <el-button icon='el-icon-edit' circle v-if='!item.edit' @click='handleCaseEdit(index)'>
                            </el-button>
                            <el-button icon='el-icon-delete' circle @click='handleCaseDelete(index)'></el-button>
                          </el-col>
                        </el-form-item>
                      </el-form>
                    </div>
                    <el-form :model="form" label-width="120px">
                      <el-form-item label="">
                        <el-button type="success" @click='addSkuCase'>新加资讯</el-button>
                      </el-form-item>
                    </el-form>
                    <hr>
                  </div>

                  <div v-if='floor.name === "resource"'>
                    <div v-for="(item, index) in form.skuResource" :key='item.id'>
                      <el-row v-if="!index">
                        <el-col :span="20">
                          <h3>资料下载楼层标题</h3>
                          <el-input v-model="item.title"
                            style='width: 200px; padding-left: 20px; display: inline-block;'>
                          </el-input>
                        </el-col>
                        <el-col :span="4" style="text-align: right;">
                          <el-button type="danger" icon="el-icon-arrow-up" :disabled="!floorIndex" circle
                            @click="_upFllor(floorIndex)">
                          </el-button>
                          <el-button type="danger" icon="el-icon-arrow-down"
                            :disabled="floorIndex === floors.length - 1" circle @click="_downFloor(floorIndex)">
                          </el-button>
                        </el-col>
                      </el-row>
                      <el-form :model="form" label-width="120px" v-if="!index">
                        <el-form-item label="锚点标题">
                          <el-col :span="18">
                            <el-input placeholder="请输入锚点标题..." v-model='item.anchor_title'
                              @change="((val)=>{handleChangeAnchor(val, 'skuResource', index)})"></el-input>
                          </el-col>
                          <el-col :span="6" style="text-align:right">
                            <span style="float:left;padding-left: 12px;">显示锚点
                              <el-switch v-model='item.anchor_show' active-value='1' inactive-value='0'
                                :disabled='!item.anchor_title'></el-switch>
                            </span>
                          </el-col>
                        </el-form-item>
                      </el-form>
                      <el-form :model="form" label-width="120px">
                        <el-form-item label="">
                          <el-col :span="6">
                            <el-input placeholder="请输入资料名称..." v-model='item.name' :disabled="!item.edit"
                              v-if='item.edit'></el-input>
                            <el-button type="primary" v-else>{{ item.name }}</el-button>
                          </el-col>
                          <el-col :span="10">
                            <el-input placeholder="请输入资料链接..." v-model='item.link' :disabled="!item.edit"></el-input>
                          </el-col>
                          <el-col :span="4">
                            <el-input placeholder="请输入ID..." v-model='item.resource_id' :disabled="!item.edit">
                            </el-input>
                          </el-col>
                          <el-col :span="4" style="text-align: right;">
                            <el-button icon='el-icon-check' circle v-if='item.edit' @click='handleResourceCheck(index)'>
                            </el-button>
                            <el-button icon='el-icon-edit' circle v-if='!item.edit' @click='handleResourceEdit(index)'>
                            </el-button>
                            <el-button icon='el-icon-delete' circle @click='handleResourceDelete(index)'></el-button>
                          </el-col>
                        </el-form-item>
                      </el-form>
                    </div>
                    <el-form :model="form" label-width="120px">
                      <el-form-item label="">
                        <el-button type="success" @click='addResource'>新加资料下载</el-button>
                      </el-form-item>
                    </el-form>
                    <hr>
                  </div>

                  <div v-if='floor.name === "qa"'>
                    <div v-for="(item,index) in form.faq" :key='item.id'>
                      <template v-if="item.show">
                        <el-row v-if="!index">
                          <el-col :span="20">
                            <h3>常见问题楼层标题</h3>
                            <el-input v-model="item.title"
                              style='width: 200px; padding-left: 20px; display: inline-block;'>
                            </el-input>
                          </el-col>
                          <el-col :span="4" style="text-align: right;">
                            <el-button type="danger" icon="el-icon-arrow-up" :disabled="!floorIndex" circle
                              @click="_upFllor(floorIndex)">
                            </el-button>
                            <el-button type="danger" icon="el-icon-arrow-down"
                              :disabled="floorIndex === floors.length - 1" circle @click="_downFloor(floorIndex)">
                            </el-button>
                          </el-col>
                        </el-row>
                        <el-form :model="form" label-width="120px" v-if="!index">
                          <el-form-item label="锚点标题">
                            <el-col :span="18">
                              <el-input placeholder="请输入锚点标题..." v-model='item.anchor_title'
                                @change="((val)=>{handleChangeAnchor(val, 'faq', index)})"></el-input>
                            </el-col>
                            <el-col :span="6" style="text-align:right">
                              <span style="float:left;padding-left: 12px;">显示锚点
                                <el-switch v-model='item.anchor_show' active-value='1' inactive-value='0'
                                  :disabled='!item.anchor_title'></el-switch>
                              </span>
                            </el-col>
                          </el-form-item>
                        </el-form>
                        <el-form :model="form" label-width="120px">
                          <hr v-if="index != 0" style="border-style: dashed;color:#999">
                          <el-form-item label="问题">
                            <el-col :span="18">
                              <el-input v-model:trim="item.question" placeholder="问题"></el-input>
                            </el-col>
                            <el-col :span="6" style="text-align:right">
                              <el-button type="danger" icon="el-icon-delete" circle @click="_delFaq(index)"></el-button>
                            </el-col>

                          </el-form-item>
                          <el-form-item label="内容">
                            <textarea :id="'Faq' + item.id" v-model="item.answer" cols="60" rows="5"
                              class="el-input__inner foolist foofaq" style="height:100px" placeholder="内容"></textarea>
                          </el-form-item>
                        </el-form>
                      </template>
                    </div>
                    <el-form :model="form" label-width="120px">
                      <el-form-item label="">
                        <el-button type="success" @click='_addFaq'>新加常见问题</el-button>
                      </el-form-item>
                    </el-form>
                    <hr>
                  </div>
                </div>
              </div>

              <h3 class="stit">SEO设置-PC端</h3>
              <el-row :gutter="20">
                <el-form label-width="120px">
                  <el-form-item label="Title">
                    <el-input v-model="form.page_title" auto-complete="off" maxlength="50"></el-input>
                  </el-form-item>

                  <el-form-item label="Keywords">
                    <el-input v-model="form.page_keywords" auto-complete="off" maxlength="50"></el-input>
                  </el-form-item>

                  <el-form-item label="Description">
                    <el-input v-model="form.page_description" auto-complete="off" maxlength="200"></el-input>
                  </el-form-item>
                </el-form>
              </el-row>
              <hr>
              <h3 class="stit">SEO设置-移动端<el-button size="small" style="margin-left: 20px;" @click="handleCopyTDK" type="primary">同PC设置</el-button></h3>
              <el-row :gutter="20">
                <el-form label-width="120px">
                  <el-form-item label="mTitle">
                    <el-input v-model="form.mpage_title" auto-complete="off" maxlength="50"></el-input>
                  </el-form-item>

                  <el-form-item label="mKeywords">
                    <el-input v-model="form.mpage_keywords" auto-complete="off" maxlength="50"></el-input>
                  </el-form-item>

                  <el-form-item label="mDescription">
                    <el-input v-model="form.mpage_description" auto-complete="off" maxlength="200"></el-input>
                  </el-form-item>
                </el-form>
              </el-row>
              <hr>
              <el-col :span="24" style="background: #39F;padding:12px 0;color:#FFF">
                <el-col :span="8" style="padding-left:12px;">
                  <el-button @click="_cancel">返回列表</el-button>
                </el-col>
                <el-col :span="8" style="text-align:center;">
                  <el-button type="" @click="preview"> 预 览 </el-button>

                  <el-button type="success" @click='onSubmit(false)'>保存</el-button>
                  <el-button type="success" v-if=" hasPubviewPurview != 1"  @click='_sendcheckDialog'>保存并送审</el-button>
                  <el-button type="success" v-if="hasPubviewPurview == 1" @click='onSubmit(true)'>保存并发布</el-button>

                </el-col>

              </el-col>
            </el-row>

            <el-dialog title="送审提示" :visible.sync="dialogVisible" width="30%">
              <div>您正在送审</div>
              <h3 style="text-indent: 20px;">{{currentNewsTitle}}</h3>
              <div>请选择审批用户分组：</div>
              <el-select v-model="selectGroup" placeholder="请选择">
                <el-option v-for="item in checkGroup" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>

              <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="_sendcheck()">确 定</el-button>
              </span>
            </el-dialog>
            <el-dialog title="添加/编辑SKU" :visible.sync="skuDialog.dialogStatus" width="600px">
              <el-row>
                <el-form :model="form" label-width="50px" :rules="rules">
                  <el-form-item label="URL">
                    <el-col :span='20'>
                      <el-input v-model='skuDialog.sku_url' placeholder="请输入url地址"></el-input>
                    </el-col>
                    <el-col :span='4' style='text-align: right;'>
                      <el-button type="primary" @click='handlerGetSku'>查找</el-button>
                    </el-col>
                  </el-form-item>
                  <template v-if='skuDialog.sku_name'>
                    <el-form-item label="图片">
                      <img :src='skuDialog.sku_image' style="max-width: 510px;" />
                    </el-form-item>
                    <el-form-item label="标题">
                      <el-input v-model='skuDialog.sku_name' :disabled="true"></el-input>
                    </el-form-item>
                  </template>
                </el-form>
              </el-row>
              <span slot="footer" class="dialog-footer" v-if='skuDialog.sku_name'>
                <el-button type="primary" @click='handlerSetSku'>确 定</el-button>
              </span>
            </el-dialog>
            <el-dialog title="新加楼层模板" :visible.sync="templateDialog.status" width="600px">
              <el-form :model="form" label-width="80px" :rules="rules">
                <el-form-item label="模板名称">
                  <el-input v-model='templateDialog.data.name' placeholder="请输入模板名称..."></el-input>
                </el-form-item>
              </el-form>
              <span slot="footer" class="dialog-footer">
                <el-button @click="templateDialog.status = false">取 消</el-button>
                <el-button type="primary" @click='saveTemp'>确 定</el-button>
              </span>
            </el-dialog>
          </el-main>
      </el-container>
  </el-container>
  <style>
    .floor h3 {
      display: inline-block;
    }

    .card_box .el-card {
      float: left;
      width: 300px;
      height: 300px;
      margin: 10px 10px 0 0;
    }

    .card_box .el-card img {
      width: 300px;
      height: 200px;
    }

    .card_box .el-card span,
    .card_box .el-card div {
      padding: 0 15px;
    }

    .card_box .el-card .bottom {
      text-align: right;
    }

    .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .avatar-uploader .el-upload:hover {
      border-color: #409EFF;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      text-align: center;
    }

    .avatar {
      display: block;
    }

    .box1 .avatar-uploader-icon,
    .box1 .avatar {
      width: 858px;
      height: 360px;
      line-height: 360px;
    }

    .box2 .avatar-uploader-icon,
    .box2 .avatar {
      width: 858px;
      height: 290px;
      line-height: 290px;
    }

    .box3 .avatar-uploader-icon,
    .box3 .avatar {
      width: 284px;
      height: 142px;
      line-height: 142px;
    }

    .box4 .avatar-uploader-icon,
    .box4 .avatar {
      width: 750px;
      height: 360px;
      line-height: 360px;
    }

    .box5 .avatar-uploader-icon,
    .box5 .avatar {
      width: 214px;
      height: 110px;
      line-height: 110px;
    }

    .content_mark {
      z-index: 2;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, .3);
    }
  </style>
  <script src="/static/tinymce/tinymce.min.js"></script>
  <script>
    var ai = 0, bi = 0;
    var tinyConfig = {
      height: 600,
      language: 'zh_CN',
      menubar: false,
      plugins: 'advlist autolink link image lists charmap print preview code textcolor table paste media',
      toolbar: 'undo redo | formatselect fontsizeselect bold italic underline forecolor backcolor  | alignleft aligncenter alignright alignjustify superscript subscript | bullist numlist outdent indent | removeformat | image media link table | code preview',
      fontsize_formats: '12px 14px 16px 18px 20px 24px 36px',
      file_browser_callback_types: 'image',
      // images_upload_url: '<%- ticMallHost %>ticCenter/business/api.v0.platform/fileUpload/uploadOss',
      images_reuse_filename: true,
      // images_upload_base_path: '/',
      relative_urls: false,
      branding: false,
      width: '100%',
      images_upload_handler: function (blobInfo, success, failure) {
        let that = this
        let fd = new FormData()
        let file = blobInfo.blob();
        fd.append('file', file, file.name)
        fd.append('systemID', 5);
        let config = {
          headers: { 'Content-Type': 'multipart/form-data' }
        }
        axios.post('<%- ticMallHost %>ticCenter/business/api.v0.platform/fileUpload/uploadOss', fd, config)
          .then(function (result) {
            if (result.data.resultCode === '0') {
              success(result.data.data.fileName)
            } else {
              that.$message.success(result.data.res);
              failure()
            }
          });
      }
    };
    var main = new Vue({
      el: '#Main',
      data: {
        ticMallHost: '<%- ticMallHost %>',
        uploadData: {
          systemID: 5
        },
        floors: [],
        skuDialog: {
          dialogStatus: false,
          sku_url: '',
          sku_image: '',
          sku_name: '',
          sku_id: ''
        },
        templateDialog: {
          status: false,
          data: {
            name: '',
            title: '',
            content: ''
          }
        },
        recommends: [],
        recommendIndex: -1,
        sourceImage: '',
        form: {
          id: 0,
          name: '',
          alias: 'product',
          bu_id: '',
          region: '',
          tradeList: '',
          serviceList: '',
          cover_img: '',
          thumb_img: '',
          banner_content: '',
          banner_type: '',
          m_cover_img: '',
          m_thumb_img: '',
          m_banner_content: '',
          m_banner_type: '',
          sub_title: '',
          description: '',
          seo_text: '',
          tag: [],
          is_buy: false,
          buy_url: 'https://',
          is_use: false,
          sContent: [],
          faq: [],
          course: {
            time: [],
            area: [],
            days: '',
          },
          type: 3,
          page_title: '',
          page_keywords: '',
          page_description: '',
          mpage_title: '',
          mpage_keywords: '',
          mpage_description: '',
          is_el: false,
          el_url: 'https://',
          catalogTrade: [],
          catalogService: [],
          recommends: [],
          skuCases: [],
          skuResource: [],
          skuFloor: ['case', 'resource', 'qa'],
          is_message: '1',
          img_alt: ''
        },
        buList: [], // bu列表数据
        options: <%- region -%>,
        citys: <%- citys %>,
        tradeList: <%- tradeList %>,
        serviceList: <%- serviceList %>,
        loading: false,
        tradeDialog: false,
        serviceDialog: false,
        treeset: {
          label: 'name',
          children: 'child'
        },
        inputVisible: false,
        inputValue: '',
        rules: {},
        hasPubviewPurview:<%- hasPubviewPurview %>,
        hasCheckedPurview:<%- hasCheckedPurview %>,
        checkGroup: [],
        selectGroup: null,
        dialogVisible: false,
        currentNewsTitle: '',
        templates: [{
          name: '不使用模板',
          id: 0,
        }],
        lockIndex: -1
      },
      methods: {
      handleCopyTDK(){
                this.form.mpage_title = this.form.page_title;
                this.form.mpage_keywords = this.form.page_keywords;
                this.form.mpage_description = this.form.page_description;
            },
        handlerRecimmendEdit(index) {
          this.recommendIndex = index;
          // 有数据的编辑
          if (this.recommends[index].sku_id) {
            this.skuDialog.sku_id = this.recommends[index].sku_id
            this.skuDialog.sku_image = this.recommends[index].sku_image
            this.skuDialog.sku_name = this.recommends[index].sku_name
            this.skuDialog.sku_url = this.recommends[index].sku_url
          } else {
            // 无数据的编辑
            this.skuDialog.sku_id = ''
            this.skuDialog.sku_image = ''
            this.skuDialog.sku_name = ''
            this.skuDialog.sku_url = ''
          }
          this.skuDialog.dialogStatus = true
        },
        // 删除单个推荐
        handlerRemoveSku(index) {
          this.$confirm('是否删除?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.recommends.splice(index, 1);
          }).catch(e => {
            return e;
          });
        },
        // 添加一个空的占位推荐
        handlerAddRecommend() {
          const flag = this.recommends.some(item => {
            return !item.sku_id;
          })
          if (!flag) this.recommends.push({});
        },
        // 获取到的数据添加到渲染源数组内
        handlerSetSku() {
          const item = {
            sku_image: this.skuDialog.sku_image,
            sku_name: this.skuDialog.sku_name,
            sku_id: this.skuDialog.sku_id,
            sku_url: this.skuDialog.sku_url
          }
          this.recommends[this.recommendIndex] = item;
          this.skuDialog.dialogStatus = false;
        },
        // 根据url获取数据
        handlerGetSku() {
          if (this.skuDialog.sku_url) {
            var that = this;
            axios.post('/homepage/getSku', { skuUrl: that.skuDialog.sku_url, _csrf: '<%- csrf %>' })
              .then(function (result) {
                if (result.data.success) {
                  that.skuDialog.sku_image = result.data.data.thumb_img;
                  that.skuDialog.sku_name = result.data.data.name;
                  that.skuDialog.sku_id = result.data.data.id;
                } else {
                  that.$message.error(result.data.msg);
                }
              }).catch(e => {
                that.$message.error(e);
              });
          } else {
            that.$message.error('请输入url地址。');
          }
        },
        _sendcheck() {
          var that = this;
          if (!this.selectGroup) {
            that.loading = false;
            this.$message.error('请选择审批用户分组');
            return;
          }

          this.$refs['form'].validate((valid) => {
            if (valid) {
              var loading = this.$loading({
                lock: true,
                text: 'Loading',
                spinner: 'el-icon-loading',
                background: 'rgba(255, 255, 255, 0.7)'
              });

              that.form.sContent.forEach(function (item, i) {
                var thisid = 'Editor' + item.id;
                item.content = tinymce.get(thisid).getContent();
              });

              that.form.faq.forEach(function (item, i) {
                var thisid = 'Faq' + item.id;
                item.answer = tinymce.get(thisid).getContent();
              });

              that.form.tradeList = []
              that.tradeList.forEach(item => {
                if (item.checked) {
                  that.form.catalogTrade.push(item.alias)
                  that.form.tradeList.push({ id: item.id, name: item.name });
                }
              });
              that.form.serviceList = []
              that.serviceList.forEach(item => {
                if (item.checked) {
                  that.form.catalogService.push(item.alias)
                  that.form.serviceList.push({ id: item.id, name: item.name });
                }
              });
              var msg = [];
              if (this.form.name == '') {
                msg.push('名称未设置');
              }

              if (this.form.tradeList.length == 0 && this.form.serviceList.length == 0) {
                msg.push('必须选择一个分类');
              }
              if (this.form.bu_id == '') {
                msg.push('请选择BU');
              }
              if (msg.length > 0) {
                this.form.is_use = false;
                this.$alert('<p>请更改以下错误后，再保存</p><p style="color:#900">' + msg.join('<br>') + '<p>', '保存错误', {
                  confirmButtonText: '确定',
                  dangerouslyUseHTMLString: true
                }).catch(function (e) {
                  return e;
                });
                loading.close();
                return;
              }
              var data_type = '';
              var params = that.form;
              // 获取banner编辑的富文本内容
              params.banner_content = tinymce.get('banner_content').getContent()
              params.m_banner_content = tinymce.get('m_banner_content').getContent()

              // 楼层排序
              this.form.skuFloor = []
              this.floors.forEach(item => {
                this.form.skuFloor.push(item.name)
              })
              params.is_use = false;
              params._csrf = '<%- csrf %>';
              var url = '/sku/solution/new';

              axios.post(url, params)
                .then(function (result) {
                  loading.close();

                  if (result.data.success) {
                    //window.location.href = '/sku/service'
                    var msg = '保存成功。';
                    that.$message({
                      message: msg,
                      type: 'success'
                    });
									
									<% if (bigType == 'sku') { %>
                      url = '/sku/service/' + result.data.data.id;
                      data_type = 'sku_service';
									<% } else { %>
                      url = '/sku/solution/' + result.data.data.id;
                      data_type = 'sku_solution';
									<% } %>
									
									var objData = { data_type: data_type, data_id: result.data.data.id, data_name: that.form.name, receive_groupid: that.selectGroup };
                    axios.post('/checkflow/apply', objData)
                      .then(function (result) {
                        if (result.data.success) {
                          that.loading = false;
                          that.dialogVisible = false;
                          if (result.data.success) {
                            that.$message.success('送审成功');
                            window.location.href = url;
                          }
                          else {
                            if (result.data.needReLogin == 1) {
                              that.$alert(result.data.msg, '提示', {
                                confirmButtonText: '确定', callback: action => {
                                  window.location.href = result.data.loginUrl;
                                }
                              });
                            }
                            else {
                              that.$alert(result.data.msg, '提示', {
                                confirmButtonText: '确定', callback: action => {
                                  window.location.reload();
                                }
                              });
                            }
                          }
                        }
                      });

                  } else {
                    if (result.data.needReLogin == 1) {
                      that.$alert(result.data.msg, '提示', {
                        confirmButtonText: '确定', callback: action => {
                          window.location.href = result.data.loginUrl;
                        }
                      });
                    }
                    else {
                      that.$alert(result.data.msg, '提示', {
                        confirmButtonText: '确定', callback: action => {
                          window.location.reload();
                        }
                      });
                    }
                  }
                });
            } else {

              return false;
            }
          });

        },
        _sendcheckDialog() {
          var that = this;
          that.loading = true;
          that.currentId = 0;
          that.selectGroup = null;

          var postdata = { type: 'sku_solution', byIdSelect: '1', trade: that.form.tradeList, service: that.form.serviceList };

          axios.post('/role/getRoleList', postdata)
            .then(function (result) {
              if (result.data.success) {
                that.loading = false;
                that.dialogVisible = true;
                that.currentId = that.form.id;
                that.currentNewsTitle = that.form.title;
                that.checkGroup = result.data.data;
              }
              else {
                if (result.data.needReLogin == 1) {
                  that.$alert(result.data.msg, '提示', {
                    confirmButtonText: '确定', callback: action => {
                      window.location.href = result.data.loginUrl;
                    }
                  });
                }
                else {
                  that.$alert(result.data.msg, '提示', {
                    confirmButtonText: '确定', callback: action => {
                      window.location.reload();
                    }
                  });
                }
              }
            });
        },
        handleAvatarSuccess(res, file, fileList, flag) {
          if (res.resultCode === '0') {
            if (flag === 'all' && res.data) {
              this.sourceImage = `${res.data.fileName}?azure_blob_process=image/resize,m_fill,w_858,h_360`
              this.form.cover_img = `${res.data.fileName}?azure_blob_process=image/resize,m_fill,w_858,h_290`
              this.form.thumb_img = `${res.data.fileName}?azure_blob_process=image/resize,m_fill,w_284,h_142`
              this.form.m_cover_img = `${res.data.fileName}?azure_blob_process=image/resize,m_fill,w_760,h_310`
              this.form.m_thumb_img = `${res.data.fileName}?azure_blob_process=image/resize,m_fill,w_214,h_110`
            } else {
              this.form[flag] = res.data.fileName;
            }
          } else {
            this.$message.error(res.resultMsg);
          }
        },
        beforeAvatarUpload(file) {
          const isJPG = file.type === 'image/jpeg';
          const isPNG = file.type === 'image/png';
          const isGIF = file.type === 'image/gif';
          const isLt2M = file.size / 1024 / 1024 < 10;

          if (!isJPG && !isPNG && !isGIF) {
            this.$message.error('图片格式错误!');
            return false;
          }
          if (!isLt2M) {
            this.$message.error('上传图片大小不能超过 10MB!');
            return false;
          }
          //return isJPG && isLt2M;
        },
        showInput() {
          this.inputVisible = true;
          this.$nextTick(_ => {
            this.$refs.saveTagInput.$refs.input.focus();
          });
        },
        handleInputConfirm() {
          let inputValue = this.inputValue;
          if (inputValue) {
            this.form.tag.push(inputValue);
          }
          this.inputVisible = false;
          this.inputValue = '';
        },
        handleClose(tag) {
          this.form.tag.splice(this.form.tag.indexOf(tag), 1);
        },
        onSubmitDraft() {

          var that = this;

          this.$refs['form'].validate((valid) => {
            if (valid) {
              var loading = this.$loading({
                lock: true,
                text: 'Loading',
                spinner: 'el-icon-loading',
                background: 'rgba(255, 255, 255, 0.7)'
              });

              that.form.sContent.forEach(function (item, i) {
                var thisid = 'Editor' + item.id;
                item.content = tinymce.get(thisid).getContent();
              });

              that.form.faq.forEach(function (item, i) {
                var thisid = 'Faq' + item.id;
                item.answer = tinymce.get(thisid).getContent();
              });

              var msg = [];
              if (this.form.name == '') {
                msg.push('名称未设置');
              }

              if (this.form.tradeList.length == 0 && this.form.serviceList.length == 0) {
                msg.push('必须选择一个分类');
              }
              if (this.form.bu_id == '') {
                msg.push('请选择BU');
              }
              if (msg.length > 0) {
                this.form.is_use = false;
                this.$alert('<p>请更改以下错误后，再保存</p><p style="color:#900">' + msg.join('<br>') + '<p>', '保存错误', {
                  confirmButtonText: '确定',
                  dangerouslyUseHTMLString: true
                }).catch(function (e) {
                  return e;
                });
                loading.close();
                return;
              }
              var params = that.form;
              // 获取banner编辑的富文本内容
              params.banner_content = tinymce.get('banner_content').getContent()
              params.m_banner_content = tinymce.get('m_banner_content').getContent()
              // 楼层排序
              this.form.skuFloor = []
              this.floors.forEach(item => {
                this.form.skuFloor.push(item.name)
              })

              params.is_use = false;
              params._csrf = '<%- csrf %>';

              var url = '/sku/solution/new';
              axios.post(url, params)
                .then(function (result) {
                  loading.close();

                  if (result.data.success) {
                    //window.location.href = '/sku/service'
                    var msg = '保存成功。';
                    that.$message({
                      message: msg,
                      type: 'success'
                    });

                    setTimeout(function () {
			                <% if (bigType == 'sku') { %>
                        url = '/sku/service/' + result.data.data.id;
			                <% } else { %>
                        url = '/sku/solution/' + result.data.data.id;
			                <% } %>
                      window.location.href = url;
                    }, 200);
                  } else {
                    if (result.data.needReLogin == 1) {
                      that.$alert(result.data.msg, '提示', {
                        confirmButtonText: '确定', callback: action => {
                          window.location.href = result.data.loginUrl;
                        }
                      });
                    }
                    else {
                      that.$alert(result.data.msg, '提示', {
                        confirmButtonText: '确定', callback: action => {
                          window.location.reload();
                        }
                      });
                    }
                  }
                });
            } else {

              return false;
            }
          });


        },
        onSubmit(is_use) {
          this.form.recommends = this.recommends
          var that = this;

          this.$refs['form'].validate((valid) => {
            if (valid) {
              var loading = this.$loading({
                lock: true,
                text: 'Loading',
                spinner: 'el-icon-loading',
                background: 'rgba(255, 255, 255, 0.7)'
              });

              that.form.sContent.forEach(function (item, i) {
                var thisid = 'Editor' + item.id;
                item.content = tinymce.get(thisid).getContent();
              });

              that.form.faq.forEach(function (item, i) {
                var thisid = 'Faq' + item.id;
                item.answer = tinymce.get(thisid).getContent();
              });

              that.form.serviceList = [];
              that.form.tradeList = [];
              that.tradeList.forEach(item => {
                if (item.checked) {
                  that.form.catalogTrade.push(item.alias)
                  that.form.tradeList.push({ id: item.id, name: item.name });
                }
              });
              that.serviceList.forEach(item => {
                if (item.checked) {
                  that.form.catalogService.push(item.alias)
                  that.form.serviceList.push({ id: item.id, name: item.name });
                }
              });

              var msg = [];
              if (this.form.name == '') {
                msg.push('名称未设置');
              }

              if (this.form.tradeList.length == 0 && this.form.serviceList.length == 0) {
                msg.push('必须选择一个分类');
              }
              if (this.form.bu_id == '') {
                msg.push('请选择BU');
              }
              if (that.form.sContent.length && is_use) {
                that.form.sContent.forEach(function (item, i) {
                  if ((!item.title || !item.content) && item.show) {
                    msg.push('楼层标题和内容不能为空。');
                  }
                });
              }

              if (that.form.skuCases.length && is_use) {
                that.form.skuCases.forEach(function (item, i) {
                  if (!item.case_id) {
                    msg.push('资讯ID不能为空。');
                  }
                });
              }

              if (that.form.skuResource.length && is_use) {
                that.form.skuResource.forEach(function (item, i) {
                  if (!item.name || !item.link) {
                    msg.push('资料名称和资料链接不能为空。');
                  }
                });
              }

              if (that.form.faq.length && is_use) {
                that.form.faq.forEach(function (item, i) {
                  if (!item.question || !item.answer) {
                    msg.push('问题和内容不能为空。');
                  }
                });
              }

              if (msg.length > 0) {
                this.$alert('<p>请更改以下错误后，再保存</p><p style="color:#900">' + msg.join('<br>') + '<p>', '保存错误', {
                  confirmButtonText: '确定',
                  dangerouslyUseHTMLString: true
                }).catch(function (e) {
                  return e;
                });
                loading.close();
                return;
              }
              var params = that.form;
              // 获取banner编辑的富文本内容
              params.banner_content = tinymce.get('banner_content').getContent()
              params.m_banner_content = tinymce.get('m_banner_content').getContent()

              // 楼层排序
              this.form.skuFloor = []
              this.floors.forEach(item => {
                this.form.skuFloor.push(item.name)
              })

              params.is_use = is_use;
              params._csrf = '<%- csrf %>';
              var url = '/sku/solution/new';
              axios.post(url, params)
                .then(function (result) {
                  loading.close();

                  if (result.data.success) {
                    //window.location.href = '/sku/service'
                    var msg = '保存成功。';
                    that.$message({
                      message: msg,
                      type: 'success'
                    });

                    setTimeout(function () {
                      <% if (bigType == 'sku') { %>
                        url = '/sku/service/' + result.data.data.id;
                      <% } else { %>
                        url = '/sku/solution/' + result.data.data.id;
                      <% } %>
                      window.location.href = url;
                    }, 200);
                  } else {
                    if (result.data.needReLogin == 1) {
                      that.$alert(result.data.msg, '提示', {
                        confirmButtonText: '确定', callback: action => {
                          window.location.href = result.data.loginUrl;
                        }
                      });
                    }
                    else {
                      that.$alert(result.data.msg, '提示', {
                        confirmButtonText: '确定', callback: action => {
                          window.location.reload();
                        }
                      });
                    }
                  }
                });
            } else {

              return false;
            }
          });


        },
        _addContent() {
          ai++;
          this.form.sContent.push({
            id: 'emp' + ai,
            title: '',
            content: '',
            hide_title: false,
            show: true,
            lock: false
          });

          this.$nextTick(_ => {
            tinyConfig.selector = '#Editoremp' + ai;
            tinymce.init(tinyConfig);
          });
        },
        _addFaq() {
          bi++;
          this.form.faq.push({
            id: 'emp' + bi,
            title: '',
            content: '',
            show: true,
          });

          this.$nextTick(_ => {
            tinyConfig.selector = '#Faqemp' + bi;
            tinymce.init(tinyConfig);
          });
        },
        _delContent(i) {
          this.$confirm('是否删除此楼层?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.form.sContent.splice(i, 1);
            this.form.sContent[i].show = false;
          }).catch(e => {
            return e;
          });
        },
        _delFaq(i) {
          this.$confirm('是否删除此常见问题?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.form.faq.splice(i, 1);
            // this.form.faq[i].show = false;
          }).catch(e => {
            return e;
          });
        },
        _default() {
          this.form.id = 0;
          this.form.name = '';
          this.form.alias = '';
        },
        _cancel() {
          window.location = '/sku/solution/';
        },
        _useCheck() {
          var msg = [], form = this.form;
          this.form.sContent.forEach(function (item, i) {
            var thisid = 'Editor' + item.id;
            item.content = tinymce.get(thisid).getContent();
          });

          this.form.faq.forEach(function (item, i) {
            var thisid = 'Faq' + item.id;
            item.answer = tinymce.get(thisid).getContent();
          });
          if (form.name == '') {
            msg.push('名称未设置');
          }

          if (form.tradeList.length == 0 && form.serviceList.length == 0) {
            msg.push('必须选择一个分类');
          }
          if (form.bu_id == '') {
            msg.push('请选择BU');
          }

          if (form.sub_title == '') {
            msg.push('副标题未设置');
          }

          if (form.description == '') {
            msg.push('描述未设置');
          }

          if (form.is_buy && form.buy_url == '') {
            msg.push('订购链接未设置');
          }

          if (form.cover_img == '') {
            msg.push('未上传封面图');
          }

          if (form.thumb_img == '') {
            msg.push('未上传缩略图');
          }

          if (form.m_cover_img == '') {
            msg.push('未上传移动端封面图');
          }

          if (form.m_thumb_img == '') {
            msg.push('未上传移动端缩略图');
          }

          if (form.sContent[0].content == '') {
            msg.push('请填写楼层内容');
          }

          if (msg.length > 0) {
            form.is_use = false;
            this.$alert('<p>请更改以下错误后，再上架</p><p style="color:#900">' + msg.join('<br>') + '<p>', '上架错误', {
              confirmButtonText: '确定',
              dangerouslyUseHTMLString: true
            }).catch(function (e) {
              return e;
            });
          } else {
            var loading = this.$loading({
              lock: true,
              text: 'Loading',
              spinner: 'el-icon-loading',
              background: 'rgba(255, 255, 255, 0.7)'
            });

            var params = that.form;
            // 获取banner编辑的富文本内容
            params.banner_content = tinymce.get('banner_content').getContent()
            params.m_banner_content = tinymce.get('m_banner_content').getContent()
            // 楼层排序
            this.form.skuFloor = []
            this.floors.forEach(item => {
              this.form.skuFloor.push(item.name)
            })
            params._csrf = '<%- csrf %>';
            var url = '/sku/solution/new';

            axios.post(url, params)
              .then(function (result) {
                loading.close();

                if (result.data.success) {
                  //window.location.href = '/sku/service'
                  var msg = '保存成功。';
                  that.$message({
                    message: msg,
                    type: 'success'
                  });

                  setTimeout(function () {
                    window.location.href = '/sku/solution/' + result.data.data.id;
                  }, 200);
                } else {
                  if (result.data.needReLogin == 1) {
                    that.$alert(result.data.msg, '提示', {
                      confirmButtonText: '确定', callback: action => {
                        window.location.href = result.data.loginUrl;
                      }
                    });
                  }
                  else {
                    that.$alert(result.data.msg, '提示', {
                      confirmButtonText: '确定', callback: action => {
                        window.location.reload();
                      }
                    });
                  }
                }
              });
          }
        },
        preview() {
          var that = this;
          that.form.sContent.forEach(function (item, i) {
            var thisid = 'Editor' + item.id;
            item.content = tinymce.get(thisid).getContent();
          });

          that.form.faq.forEach(function (item, i) {
            var thisid = 'Faq' + item.id;
            item.answer = tinymce.get(thisid).getContent();
          });
          var params = that.form;
          // 获取banner编辑的富文本内容
          params.banner_content = tinymce.get('banner_content').getContent()
          params.m_banner_content = tinymce.get('m_banner_content').getContent()
          // 楼层排序
          this.form.skuFloor = []
          this.floors.forEach(item => {
            this.form.skuFloor.push(item.name)
          })
          params._csrf = '<%- csrf %>';
          axios.post('/preview/presku', params).
            then(result => {
              if (result.data.success) {
                setTimeout(function () {
                  window.open('/preview/presku/' + result.data.data);
                }, 500);
              }
            });
        },
        // 保存模板
        handleSaveTemp(index) {
          const { title } = this.form.sContent[index];
          const content = tinymce.get('Editor' + this.form.sContent[index].id).getContent();
          if (title && content) {
            this.templateDialog.status = true;
            this.templateDialog.data.title = title;
            this.templateDialog.data.content = content;
            this.templateDialog.data.name = ''
          } else {
            this.$message.error('标题和内容不能为空');
          }
        },
        saveTemp() {
          const that = this
          if (this.templateDialog.data.name) {
            axios.post('/template/sku/add', this.templateDialog.data)
              .then(function (result) {
                if (result.data.success) {
                  that.templateDialog.status = false
                  that.getTemplates()
                } else {
                  that.$message.error(result.data.msg || '添加错误，请稍后重试');
                }
              });
          } else {
            that.$message.error('模板名称不能为空');
          }
        },
        // 获取模板列表
        getTemplates() {
          var that = this;
          const params = {
            page: 1,
            limit: 999
          }
          axios.post('/template/sku/qry', params)
            .then(function (result) {
              if (result.data.success) {
                that.templates = [{
                  name: '不使用模板',
                  id: 0,
                }].concat(result.data.list)
              } else {
                that.$message.error('获取列表出错');
              }
            });
        },
        // 获取模板详情
        getTemplateDtl(tempItem, index, focusId) {
          var that = this;
          const { id } = tempItem;
          if (id) {
            axios.post('/template/sku/dtl', { id })
              .then(function (result) {
                if (result.data.success) {
                  that.form.sContent[index].title = result.data.detail.title
                  // 设置富文本获取焦点
                  tinymce.EditorManager.get(focusId).focus();
                  // 设置富文本内容
                  tinymce.activeEditor.setContent(result.data.detail.content)
                  that.lockInput(index);
                  that.form.sContent[index].template_id = id;
                } else {
                  that.$message.error('获取详情出错');
                }
              });
          } else {
            this.unlockInput(index);
            that.form.sContent[index].template_id = 0;
          }
        },
        // 锁定楼层信息输入
        lockInput(index) {
          this.form.sContent[index].lock = true
        },
        // 解锁楼层信息输入
        unlockInput(index) {
          this.form.sContent[index].lock = false
        },
        _upContent(index) {
          if (index === 0) {
            return;
          } else {
            this.form.sContent[index] = this.form.sContent.splice(index - 1, 1, this.form.sContent[index])[0];
            this.moveFloorEdit(index)
          }
        },
        _downContent(index) {
          if (index === this.form.sContent.length - 1) {
            return;
          } else {
            this.form.sContent[index] = this.form.sContent.splice(index + 1, 1, this.form.sContent[index])[0];
            this.moveFloorEdit(index)
          }
        },
        // 楼层位子更换后渲染富文本
        moveFloorEdit(index) {
          var that = this
          setTimeout(() => {
            that.form.sContent.forEach(function (item, i) {
              var thisid = 'Editor' + item.id;
              item.content = tinymce.get(thisid).getContent();
              const tinymce1 = tinymce.get(thisid)
              if (tinymce1) {
                tinymce1.destroy()
              }
              that.$nextTick(() => {
                tinyConfig.selector = "#" + thisid;
                tinymce.init(tinyConfig);
                tinymce.get(thisid).setContent(item.content)
              })
            });
          }, 100);
        },
        _delContent(i) {
          this.$confirm('是否删除此楼层?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.form.sContent[i].show = false;
          }).catch(e => {
            return e;
          });
        },
        // 添加案例
        addSkuCase() {
          this.form.skuCases.push({
            case_id: '',
            title: '',
            name: '',
            anchor_show: '0',
            anchor_title: '',
            edit: true
          })
        },
        // 获取案例详情
        handleCaseCheck(index) {
          var that = this;
          const { case_id: id } = this.form.skuCases[index];
          if (id) {
            axios.post('/case/dtl', { id })
              .then(function (result) {
                if (result.data.success) {
                  that.form.skuCases[index].name = result.data.detail.title;
                  that.form.skuCases[index].edit = false
                } else {
                  that.$message.error('找不到该ID所对应的资讯');
                }
              });
          } else {
            this.$message.error('请输入资讯ID');
          }
        },
        // 编辑案例信息
        handleCaseEdit(index) {
          this.form.skuCases[index].edit = true
        },
        // 删除案例
        handleCaseDelete(index) {
          this.$confirm('是否删除此资讯?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.form.skuCases.splice(index, 1);
          }).catch(e => {
            return e;
          });
        },
        // 添加资料
        addResource() {
          this.form.skuResource.push({
            title: '',
            link: '',
            name: '',
            resource_id: 0,
            anchor_show: '0',
            anchor_title: '',
            edit: true
          })
        },
        // 保存资料
        handleResourceCheck(index) {
          const { name, link } = this.form.skuResource[index]
          if (!name || !link) {
            this.$message.error('请输入资料名称和链接。');
          } else {
            this.form.skuResource[index].edit = false
          }
        },
        // 编辑资料
        handleResourceEdit(index) {
          this.form.skuResource[index].edit = true
        },
        // 删除资料
        handleResourceDelete(index) {
          this.$confirm('是否删除此资料?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.form.skuResource.splice(index, 1);
          }).catch(e => {
            return e;
          });
        },
        // 楼层位子更换
        _downFloor(index) {
          if (index === this.floors.length - 1) {
            return;
          } else {
            this.floors[index] = this.floors.splice(index + 1, 1, this.floors[index])[0];
            this.resetRender();
          }
        },
        _upFllor(index) {
          if (index === 0) {
            return;
          } else {
            this.floors[index] = this.floors.splice(index - 1, 1, this.floors[index])[0];
            this.resetRender();
          }
        },
        resetRender() {
          var that = this;
          setTimeout(() => {
            that.form.faq.forEach(function (item, i) {
              var thisid = 'Faq' + item.id;
              item.answer = tinymce.get(thisid).getContent()
              console.log(item.answer)
              const tinymce1 = tinymce.get(thisid)
              if (tinymce1) {
                tinymce1.destroy()
              }
              that.$nextTick(() => {
                tinyConfig.selector = "#" + thisid;
                tinymce.init(tinyConfig);
                tinymce.get(thisid).setContent(item.answer)
              })
            });
          }, 100);
        },
        // 锚点标题输入
        handleChangeAnchor(val, type, index) {
          if (!val) {
            this.form[type][index].anchor_show = '0'
          }
        },
        // 获取bu列表
        qryBuList() {
          var that = this;
          axios.post('/setting/buManage/qry', {}).
            then(function (result) {
              if (result.data.resultCode === '0') {
                that.buList = result.data.data.items
              } else {
                that.$message.error(result.data.resultMsg);
              }
            });
        }
      },
      mounted() {
        this.$nextTick(function () {
          tinyConfig.selector = '.foolist';
          tinymce.init(tinyConfig);
          document.getElementById('preLoading').style.display = 'none';
          this.getTemplates();

          this.form.skuFloor.forEach((item, index) => {
            const obj = {}
            if (item === 'case') {
              obj.name = 'case';
              obj.datas = this.form.skuCases;
            } else if (item === 'resource') {
              obj.name = 'resource';
              obj.datas = this.form.skuResource;
            } else {
              obj.name = 'qa';
              obj.datas = this.form.faq;
            }
            this.floors.push(obj)
          })
          this.qryBuList()
        });
      }
    });
  </script>
  <% include footer.html %>