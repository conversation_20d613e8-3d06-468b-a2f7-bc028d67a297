'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const { env } = require('../../config/info').siteInfo;
class ResourceController extends Controller {
  async typeList() {
    const {
      ctx
    } = this;

    const typeList = await ctx.service.resource.typeList();
    const logs = await ctx.service.logs.listAll({
      model: '资料下载-资源类别',
      page: 1,
      limit: 10
    });

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;
    let hasEditPurview = 1;

    if (this.ctx.session.userInfo.type != 1) {
      let objService = await ctx.service.admin.getDataPurviewByService(this.ctx.session.userInfo.name);
      let objTrade = await ctx.service.admin.getDataPurviewByTrade(this.ctx.session.userInfo.name);

      if (objService.checked != 1 && objTrade.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (objService.publish != 1 && objTrade.publish != 1) {
        hasPubviewPurview = 0;
      }
      if (objService.edit != 1 && objTrade.edit != 1) {
        hasEditPurview = 0;
      }
    }

    await ctx.render('resource_type', {
      site_title: _info.site_title,
      page_title: '资源类别',
      active: '6-2',
      list: JSON.stringify(typeList.list),
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
      hasEditPurview: hasEditPurview
    });
  }

  async addType() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;

    const result = await ctx.service.resource.addType(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async getType() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const info = await ctx.service.resource.typeGet(params);

    ctx.body = {
      success: true,
      data: info.info
    };
  }

  async typeDelete() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const result = await ctx.service.resource.typeDelete(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async typeEdit() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const result = await ctx.service.resource.typeEdit(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async resourceList() {
    const {
      ctx
    } = this;

    const params = {
      type: 0,
      page: 1,
      is_publish: 1
    }

    const typeList = await ctx.service.resource.typeList();


    let serviceList;
    let tradeList;
    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      params.allowServiceList = paramsData.ids;
      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      params.allowTradeList = paramsData.ids;
      tradeList = await ctx.service.catalog.getTradeList(paramsData);
    } else {
      serviceList = await ctx.service.catalog.getSercataList();
      tradeList = await ctx.service.catalog.getTradeList();
    }

    const result = await ctx.service.resource.getList(params);
    result.list.some(item => {
      item.is_publish == 1 ? item.is_publish = true : item.is_publish = false;
    });

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;
    let hasEditPurview = 1;
    if (this.ctx.session.userInfo.type != 1) {
      let objService = await ctx.service.admin.getDataPurviewByService(this.ctx.session.userInfo.name);
      let objTrade = await ctx.service.admin.getDataPurviewByTrade(this.ctx.session.userInfo.name);

      if (objService.checked != 1 && objTrade.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (objService.publish != 1 && objTrade.publish != 1) {
        hasPubviewPurview = 0;
      }
      if (objService.edit != 1 && objTrade.edit != 1) {
        hasEditPurview = 0;
      }
    }
    await ctx.render('resource_list', {
      site_title: _info.site_title,
      page_title: '资料列表',
      active: '6-1',
      list: JSON.stringify(result.list),
      resourceType: JSON.stringify(typeList.list),
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      total: result.total,
      csrf: ctx.csrf,
      type: 'list',
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
      hasEditPurview: hasEditPurview,
      buIds: this.ctx.session.buIds ? JSON.stringify(this.ctx.session.buIds) : JSON.stringify([]),
      serviceId: this.ctx.query.serviceId
    });
  }

  async resourceDraftList() {
    const {
      ctx
    } = this;

    const params = {
      type: 0,
      page: 1,
      is_publish: 0
    }

    const typeList = await ctx.service.resource.typeList();

    let serviceList;
    let tradeList;
    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      params.allowServiceList = paramsData.ids;
      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      params.allowTradeList = paramsData.ids;
      tradeList = await ctx.service.catalog.getTradeList(paramsData);
    } else {
      serviceList = await ctx.service.catalog.getSercataList();
      tradeList = await ctx.service.catalog.getTradeList();
    }

    const result = await ctx.service.resource.getList(params);
    result.list.some(item => {
      item.is_publish == 1 ? item.is_publish = true : item.is_publish = false;
    });

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;
    let hasEditPurview = 1;
    if (this.ctx.session.userInfo.type != 1) {
      let objService = await ctx.service.admin.getDataPurviewByService(this.ctx.session.userInfo.name);
      let objTrade = await ctx.service.admin.getDataPurviewByTrade(this.ctx.session.userInfo.name);

      if (objService.checked != 1 && objTrade.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (objService.publish != 1 && objTrade.publish != 1) {
        hasPubviewPurview = 0;
      }
      if (objService.edit != 1 && objTrade.edit != 1) {
        hasEditPurview = 0;
      }
    }

    await ctx.render('resource_list', {
      site_title: _info.site_title,
      page_title: '资料列表',
      active: '6-3',
      list: JSON.stringify(result.list),
      resourceType: JSON.stringify(typeList.list),
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      total: result.total,
      csrf: ctx.csrf,
      type: 'draft',
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
      hasEditPurview: hasEditPurview,
      buIds: this.ctx.session.buIds ? JSON.stringify(this.ctx.session.buIds) : JSON.stringify([])
    });
  }

  async getList() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;

    // if (this.ctx.session.userInfo.type != 1) {
    //   let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
    //   if (dataids == null) {
    //     dataids = {};
    //     dataids.ids = [0];
    //   }
    //   let paramsData = [];
    //   paramsData.ids = dataids.ids;
    //   params.allowServiceList = paramsData.ids;


    //   dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
    //   if (dataids == null) {
    //     dataids = {};
    //     dataids.ids = [0];
    //   }
    //   paramsData = [];
    //   paramsData.ids = dataids.ids;
    //   params.allowTradeList = paramsData.ids;
    // }

    // const result = await ctx.service.resource.getList(params);
    // result.list.some(item => {
    //   item.is_publish == 1 ? item.is_publish = true : item.is_publish = false;
    // });

    const result = await ctx.service.external.qryResourceList(params)

    ctx.body = {
      success: true,
      data: result
    };
  }

  async resourceAdd() {
    const {
      ctx
    } = this;

    let serviceList = null;
    let tradeList = null;

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      tradeList = await ctx.service.catalog.getTradeList(paramsData);
    } else {
      serviceList = await ctx.service.catalog.getSercataList();
      tradeList = await ctx.service.catalog.getTradeList();
    }

    const typeList = await ctx.service.resource.typeList();

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }

      if (dataids.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (dataids.publish != 1) {
        hasPubviewPurview = 0;
      }
    }

    await ctx.render('resource_add', {
      site_title: _info.site_title,
      ticMallHost: env[this.app.config.env].ticMallHost,
      page_title: '添加资源',
      active: '6-1',
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      typeList: JSON.stringify(typeList.list),
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview
    });
  }

  async resourceEdit() {
    const {
      ctx
    } = this;

    const id = ctx.params.id;

    var checkflag = await ctx.service.admin.checkDataCanOperator('resource', id);

    if (!checkflag) {
      await ctx.render('error', {});
      return;
    }

    let serviceList = null;
    let tradeList = null;

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      tradeList = await ctx.service.catalog.getTradeList(paramsData);
    } else {
      serviceList = await ctx.service.catalog.getSercataList();
      tradeList = await ctx.service.catalog.getTradeList();
    }


    const typeList = await ctx.service.resource.typeList();

    const detail = await ctx.service.resource.getDetail(id);
    const logs = await ctx.service.logs.listAll({
      type: 'resource',
      id: id,
      page: 1,
      limit: 10
    });

    let tradeCataArr = [],
      serviceCataArr = [];
    detail.detail.tradeCata.forEach(item => {
      tradeCataArr.push(item.id);
    });

    detail.detail.serviceCata.forEach(item => {
      serviceCataArr.push(item.id);
    });

    let active = '6-1';
    if (!detail.detail.is_publish) {
      active = '6-3';
    }

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }

      if (dataids.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (dataids.publish != 1) {
        hasPubviewPurview = 0;
      }
    }

    let checkflowResult = await ctx.service.checkflow.getDetail('res', id);
    if (checkflowResult == null) {
      checkflowResult = {};
    }

    await ctx.render('resource_edit', {
      site_title: _info.site_title,
      ticMallHost: env[this.app.config.env].ticMallHost,
      page_title: '编辑资源',
      active: active,
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      typeList: JSON.stringify(typeList.list),
      tradeCata: JSON.stringify(detail.detail.tradeCata),
      serviceCata: JSON.stringify(detail.detail.serviceCata),
      tradeCataArr: JSON.stringify(tradeCataArr),
      serviceCataArr: JSON.stringify(serviceCataArr),
      form: detail.detail,
      id: id,
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
      checkflowResult: JSON.stringify(checkflowResult)
    });
  }

  async resourceSave() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;

    if (params.id && parseInt(params.id) > 0) {
      //检查老数据是否有权限
      var checkflag = await ctx.service.admin.checkDataCanOperator('resource', params.id);
      if (!checkflag) {
        ctx.body = {
          fail: true,
          needReLogin: 1,
          loginUrl: '/login',
          msg: '请重新登录'
        };
        return;
      }
    }

    const result = await ctx.service.resource.resourceAdd(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null,
        id: result.id
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async resourceDelete() {
    const {
      ctx
    } = this;

    const id = ctx.request.body.id;

    var checkflag = await ctx.service.admin.checkDataCanOperator('resource', id);
    if (!checkflag) {
      ctx.body = {
        fail: true,
        data: null
      };
      return;
    }

    const result = await ctx.service.resource.resourceDelete(id);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async resourceUpdatePublish() {
    const {
      ctx
    } = this;
    const id = ctx.request.body.id,
      is_publish = ctx.request.body.is_publish;

    var checkflag = await ctx.service.admin.checkDataCanOperator('resource', id);
    if (!checkflag) {
      ctx.body = {
        fail: true,
        data: null
      };
      return;
    }

    const result = await ctx.service.resource.updatePublish({
      id: id,
      is_publish: is_publish
    });

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async resourceModify() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const result = await ctx.service.resource.resourceModify(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null,
        id: result.id
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  // 批量置顶、热门
  async resourceModiyMultiple() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const result = await ctx.service.resource.resourceModiyMultiple(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null,
        id: result.id
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }
}

module.exports = ResourceController;