'use strict';

const Service = require('egg').Service;
const moment = require('moment');
const request = require('request');
const crypto = require('crypto');
const _siteInfo = require('../../config/info').siteInfo;
const solrUrl = _siteInfo.solrUrl,
  solrPstr = _siteInfo.solrPstr,
  solrPid = _siteInfo.solrPid;
const { env } = require('../../config/info').siteInfo;

class ResourceService extends Service {
  async typeList() {
    const {
      app
    } = this;

    const list = await app.mysql.select('catalog', {
      columns: ['id', 'name', 'alias', 'is_show', 'sort_num'],
      where: {
        is_delete: 0,
        parent_id: 4
      },
      orders: [
        ['sort_num', 'desc']
      ]
    });

    for (let item of list) {
      item.num = await app.mysql.count('resource', {
        catalog_id: item.id,
        is_delete: 0,
        is_publish: 1
      });
      item.is_show = item.is_show ? true : false;
    }

    return {
      list
    };
  }

  async addType(params) {
    const {
      app
    } = this;

    const gmt_create = await moment(new Date()).format('YYYY-MM-DD HH:mm:ss');

    const result = await app.mysql.beginTransactionScope(async conn => {
      await conn.insert('catalog', {
        name: params.name,
        alias: params.alias,
        parent_id: 4,
        sort_num: params.sort_num,
        gmt_create: gmt_create,
      });

      await conn.insert('logs', {
        user_name: this.ctx.session.userInfo.name,
        log: '创建' + params.name,
        model: '资料下载-资源类别',
        name: params.name
      });
      return {
        success: true
      };
    }, this.ctx);

    // const success = result.affectedRows === 1;
    return {
      success: result.success
    };
  }

  async typeGet(params) {
    const {
      app
    } = this;
    const info = await app.mysql.select('catalog', {
      columns: ['id', 'name', 'alias', 'is_show', 'sort_num'],
      where: {
        id: params.id
      },
      limit: 1,
      offset: 0
    });
    info[0].is_show = info[0].is_show ? true : false;

    return {
      info
    };
  }

  async typeEdit(params) {
    const {
      app
    } = this;

    const result = await app.mysql.beginTransactionScope(async conn => {
      await conn.update('catalog', {
        id: Number(params.id),
        name: params.name,
        alias: params.alias,
        is_show: params.is_show,
        sort_num: params.sort_num
      });

      await conn.insert('logs', {
        user_name: this.ctx.session.userInfo.name,
        log: '编辑' + params.name,
        model: '资料下载-资源类别',
        name: params.name
      });
      return {
        success: true
      };
    }, this.ctx);

    // const success = result.affectedRows === 1;
    return {
      success: result.success
    };
  }

  async typeDelete(params) {
    const {
      app
    } = this;

    const result = await app.mysql.beginTransactionScope(async conn => {
      await conn.update('catalog', {
        id: params.id,
        is_delete: 1
      });

      const info = await conn.get('catalog', {
        id: params.id
      });
      await conn.insert('logs', {
        user_name: this.ctx.session.userInfo.name,
        log: '删除' + info.name,
        model: '资料下载-资源类别',
        name: info.name
      });
      return {
        success: true
      };
    }, this.ctx);

    // const success = result.affectedRows === 1;
    return {
      success: result.success
    };
  }

  async getList(params) {
    const {
      app
    } = this;
    let query = ['n.is_delete=0'];
    if (params.type && params.type != 0) {
      query.push('c.id=' + params.type);
    }

    if (params.title) {
      query.push('n.title LIKE "%' + params.title + '%"');
    }

    if (params && typeof params.is_publish == 'number') {
      query.push('n.is_publish=' + params.is_publish);
    }

    if (params && params.service) {
      query.push('cr.catalog_id=' + params.service);
    }

    if (params && params.trade) {
      query.push('cr.catalog_id=' + params.trade);
    }

    let baseCategoryIds_service = [];
    let baseCategoryIds_trade = []

    if (params && params.allowServiceList != null && params.allowServiceList.length > 0) {
      for (let idvalue of params.allowServiceList) {
        if (idvalue != null) {
          baseCategoryIds_service.push(idvalue);
        }
      }
    }

    if (params && params.allowTradeList != null && params.allowTradeList.length > 0) {
      for (let idvalue of params.allowTradeList) {
        if (idvalue != null) {
          baseCategoryIds_trade.push(idvalue);
        }
      }
    }

    if (baseCategoryIds_service.length == 0 && this.ctx.session.userInfo.type == 2) {
      query.push('1=2');
    }

    if (baseCategoryIds_trade.length == 0 && this.ctx.session.userInfo.type == 2) {
      query.push('1=2');
    }

    if (params.create_time) {
      let time = params.create_time;
      query.push(`n.gmt_create>= '${time[0]} 00:00:00'`);
      query.push(`n.gmt_create<= '${time[1]} 23:59:59'`);
    }
    
    if (params.bu_id) {
      query.push('n.bu_id=' + params.bu_id);
    }
    let page = params.page || 1,
      limit = params.limit || 10;
    let start = (page - 1) * limit;

    var countsql = 'SELECT count(distinct(n.id)) as countval FROM resource n LEFT JOIN catalog c ON n.catalog_id=c.id LEFT JOIN catalog_relation cr ON cr.resource_id=n.id';

    var sql = 'SELECT n.id,n.title,n.is_publish,n.is_public,n.gmt_publish_time AS time,n.gmt_create AS create_time,n.path,n.up_type,c.name AS typeName, b.BU_NAME as bu_name FROM resource n LEFT JOIN catalog c ON n.catalog_id=c.id LEFT JOIN catalog_relation cr ON cr.resource_id=n.id  LEFT JOIN BU b ON n.bu_id=b.BU_ID';

    if (baseCategoryIds_trade.length > 0) {
      countsql += " left join (select resource_id from catalog_relation where catalog_type=1 and resource_id is not null and catalog_id not in (" + baseCategoryIds_trade.join(',') + ")) tmptrade on n.id=tmptrade.resource_id";

      sql += " left join (select resource_id from catalog_relation where catalog_type=1 and resource_id is not null and catalog_id not in (" + baseCategoryIds_trade.join(',') + ")) tmptrade on n.id=tmptrade.resource_id";

      query.push('tmptrade.resource_id is null');
    }

    if (baseCategoryIds_service.length > 0) {
      countsql += " left join (select resource_id from catalog_relation where catalog_type=2 and resource_id is not null and catalog_id not in (" + baseCategoryIds_service.join(',') + ")) tmpservice on n.id=tmpservice.resource_id";

      sql += " left join (select resource_id from catalog_relation where catalog_type=2 and resource_id is not null and catalog_id not in (" + baseCategoryIds_service.join(',') + ")) tmpservice on n.id=tmpservice.resource_id";

      query.push('tmpservice.resource_id is null');
    }
    countsql += ' WHERE ' + query.join(' AND ');
    const total = await app.mysql.query(countsql);

    sql += ' WHERE ' + query.join(' AND ') + ' GROUP BY n.id ORDER BY n.gmt_publish_time DESC LIMIT ' + start + ',' + limit;

    const list = await app.mysql.query(sql);
    for (let item of list) {
      item.time = moment(item.time).format('YYYY-MM-DD HH:mm:ss');
      item.create_time = moment(item.create_time).format('YYYY-MM-DD HH:mm:ss');
      let tradeList = [],
        serviceList = [];
      let tradeCata = await this.app.mysql.select('catalog_relation', {
        where: {
          resource_id: item.id,
          catalog_type: 1
        }
      });
      let serviceCate = await this.app.mysql.select('catalog_relation', {
        where: {
          resource_id: item.id,
          catalog_type: 2
        }
      });

      if (tradeCata && tradeCata.length > 0) {
        for (let ti of tradeCata) {
          let tradeInfo = await this.app.mysql.get('catalog', {
            id: ti.catalog_id
          });
          // let tradeParantInfo = await this.app.mysql.get('catalog', {
          //   id: tradeInfo.parent_id
          // });
          //tradeList.push(tradeParantInfo.name + '-' + tradeInfo.name);
          tradeList.push(tradeInfo && tradeInfo.name);
        }
      }

      if (serviceCate && serviceCate.length > 0) {
        for (let si of serviceCate) {
          let serviceInfo = await this.app.mysql.get('catalog', {
            id: si.catalog_id
          });
          let serviceParantInfo = await this.app.mysql.get('catalog', {
            id: serviceInfo.parent_id
          });
          serviceList.push(serviceInfo.name);
        }
      }

      item.tradeList = tradeList;
      item.serviceList = serviceList;

      let checkflowObj = await this.ctx.service.checkflow.getDetail('res', item.id);

      item.tran_result = checkflowObj;
    }

    return {
      list: list,
      total: total[0]["countval"],
      page: page,
      limit: limit
    }
  }

  async getDetail(id) {
    const {
      app
    } = this;

    const detail = await app.mysql.get('resource', {
      id: id
    });

    const tag = await app.mysql.select('tag_relation', {
      columns: ['id', 'sku_id', 'tag_name'],
      where: {
        'sku_id': detail.id
      },
    });

    const tradeCata = await app.mysql.select('catalog_relation', {
      where: {
        resource_id: id,
        catalog_type: 1
      }
    });
    const serviceCata = await app.mysql.select('catalog_relation', {
      where: {
        resource_id: id,
        catalog_type: 2
      }
    });
    const tagList = await app.mysql.select('tag_relation', {
      where: {
        resource_id: id
      }
    });

    if (tradeCata && tradeCata.length > 0) {
      for (let ti of tradeCata) {
        let tradeInfo = await app.mysql.get('catalog', {
          id: ti.catalog_id
        });
        let tradeParantInfo = await app.mysql.get('catalog', {
          id: tradeInfo.parent_id
        });
        ti.parentName = tradeParantInfo.name;
        ti.name = tradeInfo.name;
        ti.id = tradeInfo.id;
      }
    }

    if (serviceCata && serviceCata.length > 0) {
      for (let si of serviceCata) {
        let serviceInfo = await app.mysql.get('catalog', {
          id: si.catalog_id
        });
        let serviceParantInfo = await app.mysql.get('catalog', {
          id: serviceInfo.parent_id
        });
        si.parentName = serviceParantInfo.name;
        si.name = serviceInfo.name;
        si.id = serviceInfo.id;
      }
    }

    if (detail.is_publish == 1) {
      detail.is_publish = true;
    } else {
      detail.is_publish = false;
    }

    detail.tag = [];
    for (let item of tagList) {
      detail.tag.push(item.tag_name);
    }

    detail.tradeCata = tradeCata;
    detail.serviceCata = serviceCata;

    return {
      detail
    };
  }

  async resourceAdd(params) {
    const {
      app
    } = this;

    const gmt_create = await moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
    const row = {
      id: params.id,
      title: params.title,
      bu_id: params.bu_id,
      path: params.path,
      up_type: params.up_type,
      seo_text: params.seo_text,
      gmt_publish_time: params.gmt_publish_time ? moment(new Date(params.gmt_publish_time)).format('YYYY-MM-DD HH:mm:ss') : gmt_create,
      catalog_id: params.type || null,
      is_publish: params.is_publish || 0,
      is_public: params.is_public,
      size: params.size
    }

    const tradeList = params.tradeList,
      serviceList = params.serviceList,
      tagList = params.tag;

    let result = '',
      sid = 0;
    let orgResource, orgCatat = [],
      orgCatat2 = [],
      orgCatas = [],
      orgCatas2 = [],
      tagCata = [],
      newCatat = [],
      newCatas = [],
      newTag = [];
    let changeValue = [];
    if (params.id && params.id != 0) {
      sid = params.id;

      orgCatat = await this.app.mysql.select('catalog_relation', {
        where: {
          resource_id: sid,
          catalog_type: 1
        }
      });
      orgCatas = await this.app.mysql.select('catalog_relation', {
        where: {
          resource_id: sid,
          catalog_type: 2
        }
      });
      tagCata = await this.app.mysql.select('tag_relation', {
        where: {
          resource_id: sid
        }
      });
      for (let item of orgCatat) {
        orgCatat2.push(item.catalog_id);
      }

      for (let item of orgCatas) {
        orgCatas2.push(item.catalog_id);
      }

      for (let item of tradeList) {
        newCatat.push(item)
      }

      for (let item of serviceList) {
        newCatas.push(item)
      }

      if (newCatat.sort().toString() != orgCatat2.sort().toString()) {
        changeValue.push('修改基本属性-所属行业');
      }

      if (newCatas.sort().toString() != orgCatas2.sort().toString()) {
        changeValue.push('修改基本属性-所属服务');
      }

      let tagCata2 = [];
      for (let item of tagCata) {
        tagCata2.push(item.tag_name);
      }

      if (tagCata2.sort().toString() != tagList.sort().toString()) {
        changeValue.push('修改资源内容-标签');
      }

      orgResource = await this.app.mysql.get('resource', {
        id: sid
      });

      if (params.title != orgResource.title) {
        changeValue.push('修改资源内容-名称');
      }

      if (params.bu_id != orgResource.bu_id) {
        changeValue.push('修改基本属性-BU');
      }

      if (params.type != orgResource.catalog_id) {
        changeValue.push('修改基本属性-类型');
      }

      if (params.up_type != orgResource.up_type) {
        changeValue.push('修改资源内容-上传类型');
      }

      if (params.path != orgResource.path) {
        changeValue.push('修改资源内容-文件地址');
      }

      if (params.seo_text != orgResource.seo_text) {
        changeValue.push('修改资源内容-关键字');
      }

      if (params.gmt_publish_time != orgResource.gmt_publish_time) {
        changeValue.push('修改资源内容-发布时间');
      }

      if (params.is_publish != orgResource.is_publish) {
        changeValue.push('修改上下架');
      }

      if (params.is_public != orgResource.is_public) {
        changeValue.push('修会员权限');
      }

      result = await app.mysql.beginTransactionScope(async conn => {
        row.gmt_modify = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        await conn.update('resource', row);
        await conn.delete('catalog_relation', {
          resource_id: sid
        });
        await conn.delete('tag_relation', {
          resource_id: sid
        });

        let checkflowResult = await this.ctx.service.checkflow.getDetail('res', params.id);

        if (row.is_publish == 1 && checkflowResult != null) {
          await conn.update('checkflow', {
            id: checkflowResult.id,
            tran_note: '',
            tran_status: 10,
            tran_admin_time: gmt_create,
            tran_admin_id: this.ctx.session.userInfo.id
          });
        } else if (checkflowResult != null) {
          await conn.update('checkflow', {
            id: checkflowResult.id,
            is_overdue: 1
          });
        }
        if (changeValue.length > 0) {
          await conn.insert('logs', {
            user_name: this.ctx.session.userInfo.name,
            resource_id: sid,
            log: changeValue.join(','),
            model: '资料下载',
            name: params.title
          });
        }
        return {
          success: true
        };
      }, this.ctx);

    } else {
      row.gmt_create = gmt_create;

      result = await app.mysql.beginTransactionScope(async conn => {
        const insertResult = await conn.insert('resource', row);

        sid = insertResult.insertId;
        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          resource_id: sid,
          log: '创建' + row.title,
          model: '资料下载',
          name: params.title
        });

        return {
          success: true
        };

      }, this.ctx);
    }

    let tradeNames = [],
      serviceNames = [];
    for (let item of tradeList) {
      await this.app.mysql.insert('catalog_relation', {
        resource_id: sid,
        catalog_id: item,
        catalog_type: 1,
        gmt_create: gmt_create
      });
      let cataInfo = await app.mysql.get('catalog', {
        id: item
      });
      if (cataInfo) {
        tradeNames.push(cataInfo.name);
      }
    }

    for (let item of serviceList) {
      await this.app.mysql.insert('catalog_relation', {
        resource_id: sid,
        catalog_id: item,
        catalog_type: 2,
        gmt_create: gmt_create
      });
      let cataInfo = await app.mysql.get('catalog', {
        id: item
      });
      if (cataInfo) {
        serviceNames.push(cataInfo.name);
      }
    }

    for (let item of tagList) {
      if (item) {
        await this.app.mysql.insert('tag_relation', {
          tag_name: item,
          resource_id: sid
        });
      }
    }

    let summary = params.path.split('/');
    summary = summary[summary.length - 1];
    if (params.is_publish) {
      let options = {
        url: env[this.app.config.env].solrUrl + '/add',
        method: 'POST',
        headers: {
          pid: solrPid,
          timestamp: new Date().getTime(),
        },
        json: true,
        body: {
          id: 'R_' + sid,
          title: params.title,
          alias: row.alias,
          sub_title: '',
          content: '',
          // summary: summary,
          summary: '',
          url: params.path,
          type: '4',
          buy: 0,
          industry: tradeNames.join(','),
          service: serviceNames.join(','),
        }
      }

      let parr = JSON.stringify(options.body);
      parr += solrPstr;
      parr = crypto.createHash('md5').update(parr).digest("hex");
      options.headers.sign = parr;

      request(options, function (error, response, body) {
        if (error || response.statusCode > 400) {
          return;
        }
      });
    } else {
      let options = {
        url: env[this.app.config.env].solrUrl + '/del',
        method: 'POST',
        headers: {
          pid: solrPid,
          timestamp: new Date().getTime(),
        },
        json: true,
        body: {
          id: 'R_' + sid,
        }
      }

      let parr = JSON.stringify(options.body);
      parr += solrPstr;
      parr = crypto.createHash('md5').update(parr).digest("hex");
      options.headers.sign = parr;

      request(options, function (error, response, body) {
        if (error || response.statusCode > 400) {
          return;
        }
      });
    }

    // const success = result.affectedRows === 1;
    return {
      success: result.success,
      id: sid
    };
  }

  async resourceDelete(id) {
    const {
      app
    } = this;

    const result = await app.mysql.beginTransactionScope(async conn => {
      await conn.update('resource', {
        id: id,
        is_delete: 1,
        gmt_modify :moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      });

      if (typeof id == 'object') {
        for (let item of id) {
          const info = await this.app.mysql.get('resource', {
            id: item
          });
          await conn.insert('logs', {
            user_name: this.ctx.session.userInfo.name,
            resource_id: item,
            log: '删除' + info.title,
            model: '资料下载',
            name: info.title
          });
        }
      } else {
        const info = await this.app.mysql.get('resource', {
          id: id
        });
        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          resource_id: id,
          log: '删除' + info.title,
          model: '资料下载',
          name: info.title
        });

      }

      return {
        success: true
      };

    }, this.ctx);

    let options = {
      url: env[this.app.config.env].solrUrl + '/del',
      method: 'POST',
      headers: {
        pid: solrPid,
        timestamp: new Date().getTime(),
      },
      json: true,
      body: {
        id: 'R_' + id,
      }
    }

    let parr = JSON.stringify(options.body);
    parr += solrPstr;
    parr = crypto.createHash('md5').update(parr).digest("hex");
    options.headers.sign = parr;

    request(options, function (error, response, body) {
      if (error || response.statusCode > 400) {
        return;
      }
    });

    return {
      success: result.success
    };
  }

  async updatePublish(params) {
    const {
      app
    } = this;

    if (!params.id) {
      return 'fail';
    }

    const row = {
      id: params.id,
      is_publish: params.is_publish
    }

    const result = await app.mysql.beginTransactionScope(async conn => {
      row.gmt_modify = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      await conn.update('resource', row);

      const info = await conn.get('resource', {
        id: params.id
      });
      await conn.insert('logs', {
        user_name: this.ctx.session.userInfo.name,
        resource_id: params.id,
        log: '修改上下架',
        model: '资料下载',
        name: info.title
      });

      return {
        success: true
      };

    }, this.ctx);

    if (params.is_publish == 1) {

      const info = await app.mysql.get('resource', {
        id: params.id
      });
      if (!info) {
        return;
      }

      let tradeNames = [],
        serviceNames = [];
      const tradeList = await app.mysql.select('catalog_relation', {
        resource_id: params.id,
        catalog_type: 1
      });
      if (tradeList && tradeList.length > 0) {
        for (let item of tradeList) {
          let cataInfo = await app.mysql.get('catalog', {
            id: item.catalog_id
          });
          if (cataInfo) {
            tradeNames.push(cataInfo.name);
          }
        }
      }

      const serviceList = await app.mysql.select('catalog_relation', {
        resource_id: params.id,
        catalog_type: 2
      });
      if (serviceList && serviceList.length > 0) {
        for (let item of serviceList) {
          let cataInfo = await app.mysql.get('catalog', {
            id: item.catalog_id
          });
          if (cataInfo) {
            serviceNames.push(cataInfo.name);
          }
        }
      }

      let summary = info.path.split('/');
      summary = summary[summary.length - 1];
      let options = {
        url: env[this.app.config.env].solrUrl + '/add',
        method: 'POST',
        headers: {
          pid: solrPid,
          timestamp: new Date().getTime(),
        },
        json: true,
        body: {
          id: 'R_' + info.id,
          title: info.title,
          alias: row.alias,
          sub_title: '',
          // summary: summary,
          summary: '',
          content: '',
          url: info.path,
          type: '4',
          buy: 0,
          industry: tradeNames.join(','),
          service: serviceNames.join(','),
        }
      }

      let parr = JSON.stringify(options.body);
      parr += solrPstr;
      parr = crypto.createHash('md5').update(parr).digest("hex");
      options.headers.sign = parr;

      request(options, function (error, response, body) {
        if (error || response.statusCode > 400) {
          return;
        }
      });
    } else {
      let options = {
        url: env[this.app.config.env].solrUrl + '/del',
        method: 'POST',
        headers: {
          pid: solrPid,
          timestamp: new Date().getTime(),
        },
        json: true,
        body: {
          id: 'R_' + params.id,
        }
      }

      let parr = JSON.stringify(options.body);
      parr += solrPstr;
      parr = crypto.createHash('md5').update(parr).digest("hex");
      options.headers.sign = parr;

      request(options, function (error, response, body) {
        if (error || response.statusCode > 400) {
          return;
        }
      });
    }

    // const success = result.affectedRows === 1;
    return {
      success: result.success
    };
  }

  async resourceModify(params) {
    const {
      app
    } = this;

    const row = {
      id: params.id,
      is_public: params.is_public ? 1 : 0,
      title: params.title,
      gmt_modify: await moment().format('YYYY-MM-DD HH:mm:ss')
    }
    row.gmt_modify = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    const result = await app.mysql.update('resource', row);

    return {
      success: result.affectedRows == 1 ? true : false
    };
  }

  async resourceModiyMultiple(params) {
    const {
      app
    } = this;

    const result = await app.mysql.query(`UPDATE resource SET ${params.column}=0 WHERE id in (${params.id.join(',')})`)

    return {
      success: result.protocol41
    };
  }
}

module.exports = ResourceService;
