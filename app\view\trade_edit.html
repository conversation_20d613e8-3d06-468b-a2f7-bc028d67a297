<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        <a href='/catalog/trade'>行业分类</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        编辑行业
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row>
                            <h3 class="stit">基本属性</h3>
                            <el-form :model="form" label-width="120px" :rules="rules" ref="form">
                                <el-form-item label="中文名称" prop="name">
                                    <el-input v-model.trim="form.name"></el-input>
                                </el-form-item>
                                <el-form-item label="咨询名称" prop="en_name">
                                    <el-input v-model.trim="form.en_name"></el-input>
                                </el-form-item>
                                <el-form-item label="英文名称" prop="alias">
                                    <el-input v-model.trim="form.alias"></el-input>
                                </el-form-item>

                                <el-form-item label="分类短名" prop="short_name">
                                    <el-input v-model.trim="form.short_name"></el-input>
                                </el-form-item>
                                
                                <el-row style="padding-bottom: 0;margin-bottom: 0;">
                                    <el-col :span="12">
                                        <el-form-item label="显示/隐藏">
                                            <el-switch  v-if="onlyEditPurview != 1"  v-model="form.is_show" active-color="#13ce66"></el-switch>
											<el-switch  v-else disabled v-model="form.is_show" active-color="#13ce66"></el-switch>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="12">
                                        <el-form-item label="出现在导航">
                                            <el-switch  v-if="onlyEditPurview != 1" v-model="form.is_navi" active-color="#13ce66"></el-switch>
											<el-switch  v-else disabled v-model="form.is_navi" active-color="#13ce66"></el-switch>
                                        </el-form-item>
                                    </el-col>
                                </el-row>

                                <el-form-item label="行业描述">
                                    <textarea  v-model="form.description" cols="60" rows="5" class="el-input__inner foolist foofaq" style="height:100px" placeholder="行业描述"></textarea>
                                </el-form-item>

                                <el-form-item label="封面图">
                                    <el-upload class="cover-uploader" 
                                        :data='uploadData'
                                        :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                                        :show-file-list="false" 
                                        :on-success="handleAvatarSuccess" 
                                        :before-upload="beforeAvatarUpload" 
                                        name="file" 
                                        accept="image/jpeg,image/png,image/gif" style="width:457px;height:277px;">
                                        <img v-if="form.cover_img" :src="form.cover_img" class="cover" style="position: relative;z-index: 2;width:457px;height:277px;">
                                        <i v-else class="el-icon-plus cover-uploader-icon" style="width:457px;height:277px;line-height: 277px"></i>
                                        <span style="position: absolute;bottom:10px;display:block;width:100%;text-align:center;z-index:1">选择图片(JPG,PNG,GIF, 457px * 277px)</span>
                                    </el-upload>
                                </el-form-item>

                                <el-form-item label="页面链接" v-if="form.id != 0">
                                    <el-input ref="pageLink" readonly :value="'<%- view_url %>/<% if(bigType == 'service'){ %>service<% }else{ %>industry<% } %>/' + form.alias + '/'"></el-input>
                                </el-form-item>
                                <el-form-item label="移动端页面链接" v-if="form.id != 0">
                                    <% if(bigType == 'service'){ %>
                                        <el-input readonly
                                            :value="'<%- mview_url %>/service?cataId=' + form.id ">
                                        </el-input>
                                    <% }else{ %>
                                        <el-input readonly
                                            :value="'<%- mview_url %>/industry/list?alias=' + form.alias + '&name=' + form.name + '&cataId=' + form.id ">
                                        </el-input>
                                    <% } %>
                                    <!-- <el-input readonly 
                                        :value="'<%- mview_url %>/<% if(bigType == 'service'){ %>service<% }else{ %>industry<% } %>/list?alias=' + form.alias + '&name=' + form.name + '&cataId=' + form.id ">
                                    </el-input> -->
                                </el-form-item>
                            </el-form>
                            <hr>
                            <h3 class="stit">Banner设置</h3>
                            <el-form :model="form" label-width="120px">
                                <template v-for="(item, index) in banner" :key='index'>
                                    <el-form-item label="banner图片">
                                        <el-upload 
                                        class="cover-uploader" 
                                        :data='uploadData'
                                        :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                                         :show-file-list="false" 
                                         :on-success="bannerSuccess" 
                                         :before-upload="beforeAvatarUpload" 
                                         name="file" 
                                         accept="image/jpeg,image/png,image/gif" style="width:810px;height:187px;">
                                            <img v-if="item.img_path" :src="item.img_path" @click="_setIndex(index)" class="cover" style="position: relative;z-index: 2;width:810px;height:187px;">
                                            <i v-else class="el-icon-plus cover-uploader-icon" @click="_setIndex(index)" style="width:810px;height:187px;line-height: 187px"></i>
                                            <span style="position: absolute;bottom:10px;display:block;width:100%;text-align:center;z-index:1">选择图片(JPG,PNG,GIF, 1920px * 374px)</span>
                                        </el-upload>
                                    </el-form-item>

                                    <el-form-item label="Banner标题">
                                        <el-col :span="18">
                                            <el-input v-model.trim="item.btn_text"></el-input>
                                        </el-col>
                                        <el-col :span="6" style="text-align:right">
                                            <el-button type="danger" icon="el-icon-delete" circle @click="_delBanner(index)"></el-button>
                                        </el-col>
                                    </el-form-item>
    
                                    <el-form-item label="链接">
                                        <el-input v-model.trim="item.btn_url"></el-input>
                                    </el-form-item>

                                    <el-form-item label="按钮文字">
                                        <el-input v-model.trim="item.btn_value"></el-input>
                                    </el-form-item>

                                    <el-form-item label="Banner文案">
                                        <textarea  v-model="item.btn_description" cols="60" rows="4" class="el-input__inner foolist foofaq" style="height:100px" placeholder="Banner文案"></textarea>
                                    </el-form-item>
                                </template>
                                <el-form-item label="">
                                    <el-button type="success" @click='_addBanner'>新增Banner</el-button>
                                </el-form-item>
                            </el-form>

                            <hr style="margin-top:30px">
                            <div style="text-align: center;margin-top:-30px;">
                                <el-radio-group v-model="form.page_type" style="margin-bottom: 30px;">
                                    <el-radio-button label="1">普通服务</el-radio-button>
                                    <el-radio-button label="2">测试类</el-radio-button>
                                </el-radio-group>
                            </div>
                            <el-row style="margin-bottom:0;">
                                <h3 class="stit" style="float:left;">服务楼层设置</h3>
                                <el-button size="small" type="primary" style="float:right;margin-top:10px" @click="dialogTableVisible = true">编辑TAB</el-button>
                            </el-row>
                            <el-dialog title="服务排序" :visible.sync="dialogTableVisible">
                                <el-table :data="children">
                                    <el-table-column property="name" label="名称" width="240"></el-table-column>
                                    <el-table-column property="name" label="排序">
                                        <template slot-scope="scope">
                                            <el-button type="" size="mini" icon="el-icon-upload2" style="padding:6px" @click="_sortUp(scope.$index)"></el-button>
                                            <el-button type="" size="mini" icon="el-icon-download" style="padding:6px" @click="_sortDown(scope.$index)"></el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <div slot="footer" class="dialog-footer">
                                    <el-button type="primary" @click="_saveSort">确 定</el-button>
                                </div>
                            </el-dialog>
		
                            <el-tabs type="border-card">
                                <el-tab-pane v-for="(item, index) in children" :label="item.name" :name="index" :key='index'>
                                    <el-row>
                                        <el-col v-for="(o, oIndex) in item.skus" :key="oIndex" style="float:left;width:284px;height:234px;margin: 15px;overflow: visible;text-align:center;">
                                            <el-card :body-style="{ padding: '0px' }" style="width:284px;height:234px;">
                                            <img :src="o.thumb_img" class="image" style="width:284px;height:142px;">
                                            <div style="padding: 14px;">
                                                <span>{{o.name}}</span>
                                                <div class="bottom clearfix">
                                                <span style="color:#999">{{ o.sub_title }}</span>
                                                </div>
                                            </div>
                                            </el-card>
                                        </el-col>
                                    </el-row>                                              
                                </el-tab-pane>
                            </el-tabs>
                            
                            <template v-if="form.page_type == 1">
                            <hr>
                            <el-row style="margin-bottom:0;">
                                <h3 class="stit" style="float:left;">解决方案楼层</h3>
                                <!-- <el-button size="small" type="primary" style="float:right;margin-top:10px" @click="dialogSolutionVisible = true">解决方案排序</el-button> -->
                            </el-row>
                            <el-row>
                                <el-col v-for="(o, index) in solution" :key="index" style="float:left;width:593px;height:162px;margin: 15px;overflow: visible;">
                                    <el-card :body-style="{ padding: '0px' }" style="width:593px;height:162px;">
                                    <img :src="o.thumb_img" class="image" style="width:237px;height:162px;float:left;margin-right:28px">
                                    <div style="padding: 14px">
                                        <span>{{o.name}}</span>
                                        <div class="bottom clearfix">
                                        <span style="color:#999">{{ o.sub_title }}</span>
                                        <p style="height:66px;overflow:hidden;font-size:14px;line-height: 22px;color:#999">{{o.description}}</p>
                                        </div>
                                    </div>
                                    </el-card>
                                </el-col>
                            </el-row>
                            <el-dialog title="解决方案排序" :visible.sync="dialogSolutionVisible">
                                <el-table :data="solution">
                                    <el-table-column property="name" label="名称"></el-table-column>
                                    <el-table-column property="name" label="排序" width="120">
                                        <template slot-scope="scope">
                                            <el-button type="" size="mini" icon="el-icon-upload2" style="padding:6px" @click="_sortUp2(scope.$index)"></el-button>
                                            <el-button type="" size="mini" icon="el-icon-download" style="padding:6px" @click="_sortDown2(scope.$index)"></el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <div slot="footer" class="dialog-footer">
                                    <el-button type="primary" @click="_saveSolutionSort">确 定</el-button>
                                </div>
                            </el-dialog>
                            <hr>
                            <h3 class="stit">
                                <el-radio-group v-model="form.case_title">
                                    <el-radio-button label="合作案例"></el-radio-button>
                                    <el-radio-button label="培训动态"></el-radio-button>
                                </el-radio-group>
                            </h3>

                            <el-row>
                                <ul>
                                    <li v-for="(item, index) in cases" :key='index'><a :href="'<%- view_url %>/sku/'+item.id" target="_blank">{{item.title}}</a></li>
                                </ul>
                            </el-row>

                            <hr>
                            <h3 class="stit">相关资源</h3>
                            <el-row>
                                <ul>
                                    <li v-for="(item, index) in resource" :key='index'><a :href="item.path" target="_blank">{{item.title}}</a></li>
                                </ul>
                            </el-row>
                            </template>

                            <hr>
                            <h3 class="stit">SEO设置-PC端</h3>
                            <el-row :gutter="20">
                                <el-form label-width="120px">
                                    <el-form-item label="Title">
                                        <el-input v-model="form.page_title" auto-complete="off" maxlength="50"></el-input>
                                    </el-form-item>

                                    <el-form-item label="Keywords">
                                        <el-input v-model="form.page_keywords" auto-complete="off" maxlength="50"></el-input>
                                    </el-form-item>

                                    <el-form-item label="Description">
                                        <el-input v-model="form.page_description" auto-complete="off" maxlength="200"></el-input>
                                    </el-form-item>
                                </el-form>
                            </el-row>
                            <hr>
                            <h3 class="stit">SEO设置-移动端<el-button size="small" style="margin-left: 20px;" @click="handleCopyTDK" type="primary">同PC设置</el-button></h3>
                            <el-row :gutter="20">
                                <el-form label-width="120px">
                                    <el-form-item label="mTitle">
                                        <el-input v-model="form.mpage_title" auto-complete="off" maxlength="50"></el-input>
                                    </el-form-item>
                            
                                    <el-form-item label="mKeywords">
                                        <el-input v-model="form.mpage_keywords" auto-complete="off" maxlength="50"></el-input>
                                    </el-form-item>
                            
                                    <el-form-item label="mDescription">
                                        <el-input v-model="form.mpage_description" auto-complete="off" maxlength="200"></el-input>
                                    </el-form-item>
                                </el-form>
                            </el-row>
                            <hr>
                            <el-col :span="24" style="background: #39F;padding:12px 0;color:#FFF">
                                <el-col :span="12" style="padding-left:12px;">
                                    <el-button @click="_cancel">返回列表</el-button>
                                </el-col>
                                <el-col :span="12" style="text-align:right;padding-right:12px;">
                                    <el-button type="" @click="preview"> 预 览 </el-button>
                                    <el-button type="success" @click='onSubmit'>保存修改</el-button>
                                </el-col>
                            </el-col>
                        </el-row>
                        <div class="logshow-wrapper" v-if="logs.list.length > 0">
                        <el-row class="logshow" :class="{on: logshow}">
                            <span class="icon"><i class="el-icon-arrow-up" @click="showLog"></i></span>
                            <ul>
                                <li v-for="(item, index) of logs.list" :key='index'>{{item.time}}, {{item.user_name}}, {{item.log}}</li>
                            </ul>
                            <el-pagination
                              @current-change="logPageChange"
                              :current-page.sync="logs.page"
                              :page-size="logs.limit"
                              layout="total, prev, pager, next"
                              :total="logs.total">
                            </el-pagination>
                        </el-row>
                        </div>
                    </el-main>
            </el-container>
    </el-container>
    <script src="/static/tinymce/tinymce.min.js"></script>
    <script>
    var ai = 0, bi = 0;
    var bIndex = 0;
    var main = new Vue({
        el: '#Main',
        data: {
            uploadData: { systemId: 5 },
            ticMallHost: '<%- ticMallHost %>',
            form: {
                id: <%- info.id %>,
                name: '<%- info.name %>',
                en_name: '<%- info.en_name %>',
                alias: '<%- info.alias %>',
                short_name: '<%- info.short_name %>',
                description: '<%- info.description %>',
                is_show: <%- info.is_show %>,
                is_navi: <%- info.is_navi %>,
                cover_img: '<%- info.cover_img %>',
                page_type: <%- info.page_type %>,
                case_title: '<%- info.case_title %>',
                page_title:'<%- info.page_title %>',
                page_keywords:'<%- info.page_keywords %>',
                page_description:'<%- info.page_description %>',
                mpage_title: '<%- info.mpage_title %>',
                mpage_keywords: '<%- info.mpage_keywords %>',
                mpage_description: '<%- info.mpage_description %>',
            },
            banner:<%- JSON.stringify(banner) %>,
            children: <%- JSON.stringify(children) %>,
            solution: <%- JSON.stringify(solution) %>,
            cases: <%- JSON.stringify(cases) %>,
            resource: <%- JSON.stringify(resource) %>,
            rules: {
                name: [
                    { required: true, message: '请输入名称', trigger: 'blur' },
                    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
                    { validator: nameCheck, trigger: 'blur' },
                ],
                en_name: [
                    { required: true, message: '请输入咨询名称', trigger: 'blur' },
                    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
                    { validator: nameCheck, trigger: 'blur' },
                ],
                alias: [
                    { required: true, message: '请输入英文名称', trigger: 'blur' },
                    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
                    { validator: nameCheck, trigger: 'blur' },
                ],
                short_name: [
                    { required: true, message: '请输入分类短名', trigger: 'blur' },
                    { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' },
                    { validator: nameCheck, trigger: 'blur' },
                ]
            },
            dialogTableVisible: false,
            dialogSolutionVisible: false,
            logs: <%- logs %>,
            logshow: false,
			onlyEditPurview :'<%- onlyEditPurview %>'
        },
        methods: {
            handleCopyTDK(){
                this.form.mpage_title = this.form.page_title;
                this.form.mpage_keywords = this.form.page_keywords;
                this.form.mpage_description = this.form.page_description;
            },
            handleAvatarSuccess(res, file) {
                if (res.resultCode === '0') {
                    this.form.cover_img = res.data.fileName;
                } else {
                    this.$message.error(res.resultMsg);
                }
            },
            handleAvatarSuccess2(res, file) {
                if (res.resultCode === '0') {
                    this.form.thumb_img = res.data.fileName;
                } else {
                    this.$message.error(res.resultMsg);
                }
            },
            beforeAvatarUpload(file) {
                const isJPG = file.type === 'image/jpeg';
                const isPNG = file.type === 'image/png';
                const isGIF = file.type === 'image/gif';
                const isLt2M = file.size / 1024 / 1024 < 10;

                if (!isJPG && !isPNG && !isGIF) {
                    this.$message.error('图片格式错误!');
                    return false;
                }
                if (!isLt2M) {
                    this.$message.error('上传图片大小不能超过 10MB!');
                    return false;
                }
                //return isJPG && isLt2M;
            },
            bannerSuccess(res,file) {
                if (res.resultCode === '0') {
                    this.banner[bIndex].img_path = res.data.fileName;
                } else {
                    this.$message.error(res.resultMsg);
                }
            },
            _setIndex(index){
                bIndex = index;
            },
            _addBanner(){
                this.banner.push({img_path:'',btn_text:'',btn_url:'https://',btn_description:''});
            },
            _delBanner(index){
                this.$confirm('是否删除此Banner?', '提示',{
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.banner.splice(index,1);
                }).catch(e => {
                    return e;
                });
            },
            _cancel(){
                var bid = '<%- bigType %>';
                window.location.href = '/catalog/' + bid;
            },
            preview(){
                var that = this;
                var bid = '<%- bigType %>';
                var params = {
                    form: this.form,
                    banner: this.banner,
                    _csrf: '<%- csrf %>',
                    bid: bid
                }
				
                axios.post('/preview/precata',params).
                    then(function(result){

                        if(result.data.success){
                            setTimeout(function(){
                                window.open('/preview/precata/'+result.data.data);
                            },500);
                        }
                    }).catch(e => {
                        return e;
                    });
            },
            onSubmit(){
                var bid = '<%- bigType %>';
                var that = this;
                var loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(255, 255, 255, 0.7)'
                });

                var params = {
                    form: this.form,
                    banner: this.banner,
                    children: this.children,
                    _csrf: '<%- csrf %>',
                    bid: bid,
                    solution: this.solution,
                }

                params.pageLink = this.$refs.pageLink.value
                axios.post('/catalog/edit', params)
                .then(function(result) {
                    loading.close();
                    
                    if (result.data.success) {
                        //window.location.href = '/sku/service'
                        window.location.reload();
                        var msg = '保存成功。';
                        that.$message({
                            message: msg,
                            type: 'success'
                        });
                        
                        /*
                        setTimeout(function(){
                            window.location.href = '/sku/service/'+result.data.data.id;
                        },200);
                        */
                    }else{
                        that.$message.error(result.data.msg);
                    }
                });

            },
            _sortDown(i){
                if(i == this.children.length - 1){
                    return;
                }
                var children = this.children;
                var tempOption = children[i + 1];
                var tempSort = children[i + 1].sort_num;
                children[i + 1].sort_num = children[i].sort_num;
                children[i].sort_num = tempSort;
                Vue.set(children, i + 1, children[i]);
                Vue.set(children, i, tempOption);
            },
            _sortUp(i){
                if(i == 0){
                    return;
                }
                var children = this.children;
                var tempOption = children[i - 1];
                var tempSort = children[i - 1].sort_num;
                children[i - 1].sort_num = children[i].sort_num;
                children[i].sort_num = tempSort;
                Vue.set(children, i - 1, children[i]);
                Vue.set(children, i, tempOption);
            },
            _sortDown2(i){
                if(i == this.children.length - 1){
                    return;
                }
                var children = this.solution;
                var tempOption = children[i + 1];
                var tempSort = children[i + 1].sort_num;
                children[i + 1].sort_num = children[i].sort_num;
                children[i].sort_num = tempSort;
                Vue.set(children, i + 1, children[i]);
                Vue.set(children, i, tempOption);
            },
            _sortUp2(i){
                if(i == 0){
                    return;
                }
                var children = this.solution;
                var tempOption = children[i - 1];
                var tempSort = children[i - 1].sort_num;
                children[i - 1].sort_num = children[i].sort_num;
                children[i].sort_num = tempSort;
                Vue.set(children, i - 1, children[i]);
                Vue.set(children, i, tempOption);
            },
            _saveSort(){
                this.children.forEach(function(item,i){
                    item.sort_num = i;
                });
                this.dialogTableVisible = false;
            },
            _saveSolutionSort(){
                this.solution.forEach(function(item,i){
                    item.sort_num = i;
                });
                this.dialogSolutionVisible = false;
            },
            showLog(){
                if(this.logs.list.length > 1){
                    this.logshow = !this.logshow;
                }
            },
            logPageChange(page){
                axios.post('/logslist',{page:page,type: 'catalog',id: <%- info.id %>,limit:10,_csrf:'<%- csrf %>'}).then(res => {
                    let data = res.data;
                    if(data.success){
                        this.logs = data.data;
                    }
                    
                })
            }
        },
        mounted(){
            this.$nextTick(() => {
                document.getElementById('preLoading').style.display = 'none';
            })
        }
    });
    </script>
    <% include footer.html %>