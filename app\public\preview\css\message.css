
.messageCon{
    background: #fafafa;
    margin-top: 21px;
    margin-bottom: 20px;
}
.messageConT{
    padding: 10px 0;
    font-size: 32px;
    text-align: center;
    font-weight: 400;
}
.messageConS{
    text-align: center;
    font-size: 16px;
    color: #000000;
    padding-bottom: 20px;
}
.tip{
    width: 718px;
    padding-top: 10px;border-top: 1px solid #eeeeee;
    margin: 0 auto 22px;text-align: right;color: gray;
    font-size: 14px;
}
.mustStar{
    color: rgb(254,102,3);
}
.selectBox{
    width: 882px;
    height: 90px;
    margin-bottom: 24px;
}
.select{
    width: 258px;
    /*display: inline-block;*/
    float: left;
    margin-right: 36px;
}
.selectT{
    font-size: 14px;
    margin-bottom: 18px;
}
/*input.selectC:after{*/
    /*content: url("../images/code.jpg");*/
    /*display: block;*/
/*}*/
.selectC{
    width: 258px;height: 40px;
    border-radius: 5px;
}
.selectC1{
    width: 258px;height: 40px;
    border-radius: 5px;
}
input.selectC{
    border: 1px solid #eeeeee;
}
input.selectC1{
    border: 1px solid #eeeeee;
}
.content{
    /*margin-bottom: 16px;*/
    margin-bottom: 54px;
}
#textarea {
    width: 100%;
    height: 160px;
    resize: none;
    outline: none;
    border: none;
    border: 1px solid #eeeeee;
    padding: 15px;
    font-size: 14px;
    line-height: 16px;
    box-sizing: border-box;
}
.yoursInfo{
    height: 410px;
    margin-bottom: 55px;
}
.yoursT{
    height: 44px;
    background: #fafafa;
    text-align: center;
    line-height: 44px;
    margin-bottom: 44px;
    font-size: 16px;
}
.yoursInfoC{
    width: 905px;
}
.yoursInfoCC{
    width: 400px;
    margin-right: 50px;
    display: inline-block;
    margin-bottom: 26px;
    position: relative;
}
.yoursInfoCC .selectC{
    width: 400px;
    padding: 5px 20px;
    box-sizing: border-box;
    outline: none;
    color: #000000;
    font-size: 14px;
}
.yoursInfoCC .selectC1{
    width: 400px;
    padding: 5px 20px;
    box-sizing: border-box;
    outline: none;
    color: #000000;
}
.messFooter{
    width: 216px;
    margin: 0 auto 100px;
}
.submit{
    height: 45px;background: rgb(254,102,3);
    color: white;text-align: center;
    line-height: 45px;
    width: 100%;
    outline: none;
    border: none;
    cursor: pointer;
}
.accept{
    height: 23px;
    position: relative;
}
.acceptIcon{
    display: inline-block;
    width: 15px;height: 15px;background: #eeeeee;
    margin-right: 18px;
    margin-left: 30px;
}
.acceptWord{
    display: inline-block;
    position: absolute;
}
.privacy{
    display: inline-block;position: absolute;
    width: 64px;color: rgb(254,102,3);
}
.select2-selection{
    height: 40px;
}
.form-label{
    height: 50px;
}
.select2-container .select2-selection--single{
    height:40px;
    line-height: 40px;
}
.select2-container--default .select2-selection--single .select2-selection__rendered{
    height:40px;
    line-height: 40px;
}
.select2-container--default .select2-selection--single .select2-selection__arrow{
    top: 9px;
}
.select2-container--default .select2-selection--single{
    border-color: #eeeeee;
}
.select2-container--default .select2-results__option--highlighted[aria-selected]{
    background-color: #f8f8f8;
    color: rgb(254,102,3);
}
.select2-container--default .select2-results__option[aria-selected=true]{
    background-color: #f8f8f8;
    color: rgb(254,102,3);
}
.select2-dropdown{
    border-color: #eeeeee;
}
.acceptIcon.active{
    background: url(../images/accpeted.png);
}
.code{
    position: absolute;
    background: url(../images/code.jpg) no-repeat;
    width: 80px;height: 27px;top: 44px;
    right: 60px;
}
.re{
    width: 22px;height: 22px;
    position: absolute;
    background: url(../images/re.png) no-repeat;
    right: 20px;top: 46px;
    cursor: pointer;
}
#province{
    padding-left: 0;
}
#province .select2-selection.select2-selection--single{
    height: 50px;
}
#province .select2-container--default .select2-selection--single .select2-selection__rendered{
    height: 48px;
    line-height: 48px;
}
#province .select2-container--default .select2-selection--single .select2-selection__arrow{
    top: 13px;
}
.error{
    font-size: 12px;color: #e50000;
    margin: 0;
    margin-top: 5px;
    visibility: hidden;
}
.provice{
    top: 74px;
    z-index: 999;
    background: #ffffff;
    border: 1px solid #eeeeee;
    padding: 10px 0;
    display: none;
}
.citys{
    margin: 0;
    width: 398px;
}
.citys a{
    width: 129px;
    color: #666666;
    text-decoration: none;
    font-size: 14px;
    padding: 5px 0;
    text-align: center;
}
.citys a:hover{
    background: #f8f8f8;
    color: rgb(254,102,3);
}
.SearchBox:after{
    content: '';
    clear: both;
    display: block;
}