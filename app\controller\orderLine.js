'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo; 
const { env } = require('../../config/info').siteInfo;
class LabController extends Controller {
  async orderLine() {
    const {
      ctx
    } = this;

    await ctx.render('order_line', {
      site_title: _info.site_title,
      page_title: '集合页列表',
      active: '18-1',
      csrf: ctx.csrf,
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: 1,
      hasPubviewPurview: 1,
      hasEditPurview: 1
    });
  }

  async recordOrderOnline() {
    const {
      ctx
    } = this;

    await ctx.render('record_orderOnline', {
      site_title: _info.site_title,
      page_title: '集合页查询列表',
      active: '18-2',
      csrf: ctx.csrf,
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: 1,
      hasPubviewPurview: 1,
      hasEditPurview: 1
    });
  }
}
module.exports = LabController;