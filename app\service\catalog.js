'use strict';

const Service = require('egg').Service;
const moment = require('moment');

class CatalogService extends Service {
  async getTradeList(params) {

    var condition = {
      is_delete: 0,
      parent_id: 1
    };
    if (params && params.ids) {
      condition.id = params.ids;
    }

    const list = await this.app.mysql.select('catalog', {
      columns: ['id', 'name', 'alias', 'sort_num', 'is_show', 'is_navi'],
      where: condition,
      orders: [
        ['sort_num', ''],
        //['gmt_modify', 'desc']
      ]
    });

    for (let item of list) {
      item.is_show == 1 ? item.is_show = true : item.is_show = false;
      item.is_navi == 1 ? item.is_navi = true : item.is_navi = false;

      let rid = item.id;
      const child = await this.app.mysql.select('catalog', {
        columns: ['id', 'name', 'alias', 'sort_num', 'is_show', 'is_navi', 'parent_id'],
        where: {
          is_delete: 0,
          parent_id: rid
        },
        orders: [
          ['sort_num', ''],
          //['gmt_modify', 'desc']
        ]
      });

      const skuLen = await this.app.mysql.query('SELECT COUNT(cr.id) as Len FROM catalog_relation cr LEFT JOIN sku s ON cr.sku_id=s.id WHERE cr.catalog_id=' + rid + ' AND s.is_delete=0 AND s.type IN (1,2)');
      item.count = skuLen[0].Len;

      item.child = child;
      for (let citem of item.child) {
        citem.is_show == 1 ? citem.is_show = true : citem.is_show = false;
        citem.is_navi == 1 ? citem.is_navi = true : citem.is_navi = false;
        citem.editModel = false;

        citem.parentName = item.name;

        citem.count = await this.app.mysql.query('SELECT COUNT(cr.id) as Len FROM catalog_relation cr LEFT JOIN sku s ON cr.sku_id=s.id WHERE cr.catalog_id=' + citem.id + ' AND s.is_delete=0');
        citem.count = citem.count[0].Len;
      }

      item.clen = item.child.length - 1;
      item.zindex = 'normal';
      
      /* 获取解决方案数量 */
      const count = await this.app.mysql.query(`SELECT * FROM sku s 
        LEFT JOIN catalog_relation cs ON s.id=cs.sku_id 
        LEFT JOIN catalog c ON c.id=cs.catalog_id 
        WHERE c.id=${rid} AND s.is_delete=0 AND s.type IN (3,4) 
        `);
      item.solutionCount = count.length;
    };

    return {
      list
    };
  }

  async tradeAdd(name, en_name, alias, short, pid) {
    const {
      app
    } = this;
    const gmt_create = moment().format('YYYY-MM-DD HH:mm:ss');
    const len = await this.app.mysql.count('catalog', {
      parent_id: pid
    });

    let repeatCheck;
    en_name
    if (pid == 1 || pid == 2) {
      repeatCheck = await this.app.mysql.query('SELECT COUNT(*) AS len FROM catalog WHERE (name="' + name + '" OR alias="' + alias + '" OR en_name="' + en_name + '") AND parent_id=' + pid + ' AND is_delete=0');
    } else {
      repeatCheck = await this.app.mysql.query('SELECT COUNT(*) AS len FROM catalog WHERE name="' + name + '" AND parent_id=' + pid + ' AND is_delete=0');
    }

    if (repeatCheck[0].len > 0) {
      return 'error';
    } else {
      let num = len + 1;

      const result = await app.mysql.beginTransactionScope(async conn => {
        const insertResult = await conn.insert('catalog', {
          name: name,
          en_name: en_name,
          alias: alias,
          short_name: short,
          is_show: 1,
          is_navi: 1,
          is_delete: 0,
          parent_id: pid,
          type: 1,
          sort_num: num,
          gmt_create: gmt_create
        });
        const id = insertResult.insertId;

        let pinfo;

        if (pid > 2) {
          pinfo = await conn.get('catalog', {
            id: pid
          });
        }
        const info = await conn.get('catalog', {
          id: id
        });

        let pname = '';
        if (info.parent_id == 1) {
          pname = '行业分类';
        } else if (info.parent_id == 2) {
          pname = '服务分类';
        }

        if (pinfo) {
          if (pinfo.parent_id == 1) {
            pname = '行业分类';
          } else if (pinfo.parent_id == 2) {
            pname = '服务分类';
          }
        }


        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          log: '创建' + (pinfo ? pinfo.name + '子类' + name : name),
          model: pname,
          name: name
        });

        return {
          info
        };
      }, this.ctx);

      return {
        info: result.info
      };
    }
  }

  async tradeUpdateSingle(id, value, type) {
    const {
      app
    } = this;
    const row = {
      id: id
    };

    value == true ? value = 1 : value = 0;
    // row[type] = value;
    // 同时改变2个值
    row.is_navi = value
    row.is_show = value

    const result = await app.mysql.beginTransactionScope(async conn => {
      await conn.update('catalog', row);

      let typename = '';
      if (type == 'is_show') {
        typename = '显示隐藏';
      } else if (type == 'is_navi') {
        typename = '出现在导航';
      } else {
        typename = type;
      }
      const info = await conn.get('catalog', {
        id: id
      });
      await conn.insert('logs', {
        user_name: this.ctx.session.userInfo.name,
        log: '修改' + info.name + '的' + typename,
        model: info.parent_id == 1 ? '行业分类' : '服务分类',
        name: info.name
      });

      return {
        success: true
      };
    }, this.ctx);

    // const success = update.affectedRows === 1;

    return {
      success: result.success
    };
  }

  async tradeSort(id, sort, type, pid) {
    const {
      app
    } = this;
    const row = {
      id: id[0],
      sort_num: sort[1],
    };

    const row2 = {
      id: id[1],
      sort_num: sort[0],
    };

    const result = await app.mysql.beginTransactionScope(async conn => {

      await conn.update('catalog', row);
      await conn.update('catalog', row2);

      const info1 = await conn.get('catalog', {
        id: id[0]
      });
      const info2 = await conn.get('catalog', {
        id: id[1]
      });
      await conn.insert('logs', {
        user_name: this.ctx.session.userInfo.name,
        log: '调换位置' + info1.name + ',' + info2.name,
        model: info1.parent_id == 1 ? '行业分类' : '服务分类',
        name: info1.name
      });

      return {
        success: true
      };
    }, this.ctx);

    // const success = update.affectedRows === 1;

    return {
      success: result.success
    };
  }

  async tradeDelete(id, params) {
    const {
      app
    } = this;
    const row = {
      id: id,
      is_delete: 1
    };

    const result = await app.mysql.beginTransactionScope(async conn => {
      await conn.update('catalog', row);

      const info = await conn.get('catalog', {
        id: id
      });
      let pinfo, pid = info.parent_id;
      if (pid > 2) {
        pinfo = await conn.get('catalog', {
          id: pid
        });
        pid = pinfo.parent_id;
      }
      await conn.insert('logs', {
        user_name: this.ctx.session.userInfo.name,
        log: '删除' + (pinfo ? pinfo.name + '子类' + info.name : info.name),
        model: pid == 1 ? '行业分类' : '服务分类',
        name: info.name
      });

      return {
        success: true
      };
    }, this.ctx);

    // 保存成功推送CDN刷新
    // if (result.success) {
    //     const cndParams = {
    //         fileUrls: [params.pageLink]
    //     }
    //     await this.ctx.service.external.CDNRefresh(cndParams);
    // }

    return {
      success: result.success
    };
  }

  //service
  async getSercataList(params, type) {
    // 没有type 原来的逻辑
    let parent_id = 0;
    let queryType = '';
    if (!type) {
      parent_id = 2
      queryType = '1,2'
    } else {
      parent_id = type.includes('1') ? 2 : 1
      queryType = type
    }
    var condition = {
      is_delete: 0,
      parent_id
    };

    if (params && params.ids) {
      condition.id = params.ids;
    }

    const list = await this.app.mysql.select('catalog', {
      columns: ['id', 'name', 'alias', 'sort_num', 'is_show', 'is_navi'],
      where: condition,
      orders: [
        ['sort_num', ''],
      ]
    });

    for (let [index, item] of list.entries()) {

      item.is_show == 1 ? item.is_show = true : item.is_show = false;
      item.is_navi == 1 ? item.is_navi = true : item.is_navi = false;

      let rid = item.id;
      const child = await this.app.mysql.select('catalog', {
        columns: ['id', 'name', 'alias', 'sort_num', 'is_show', 'is_navi'],
        where: {
          is_delete: 0,
          parent_id: rid
        },
        orders: [
          ['sort_num', ''],
          //['gmt_modify', 'desc']
        ]
      });

      const skuLen = await this.app.mysql.query(`SELECT COUNT(cr.id) as Len FROM catalog_relation cr LEFT JOIN sku s ON cr.sku_id=s.id WHERE cr.catalog_id='${rid}' AND s.is_delete=0 AND s.type IN (${queryType})`);
      item.count = skuLen[0].Len;

      item.child = child;
      for (let citem of item.child) {
        citem.is_show == 1 ? citem.is_show = true : citem.is_show = false;
        citem.is_navi == 1 ? citem.is_navi = true : citem.is_navi = false;
        citem.editModel = false;

        citem.parentName = item.name;

        citem.count = await this.app.mysql.query('SELECT COUNT(cr.id) as Len FROM catalog_relation cr LEFT JOIN sku s ON cr.sku_id=s.id WHERE cr.catalog_id=' + citem.id + ' AND s.is_delete=0');
        citem.count = citem.count[0].Len;
      }

      item.clen = item.child.length - 1;


      /* 获取解决方案数量 */
      const count = await this.app.mysql.query(`SELECT * FROM sku s 
        LEFT JOIN catalog_relation cs ON s.id=cs.sku_id 
        LEFT JOIN catalog c ON c.id=cs.catalog_id 
        WHERE c.id=${rid} AND s.is_delete=0 AND s.type IN (3,4) 
        `);
      item.solutionCount = count.length;
    };

    return {
      list
    };
  }

  // async getSolutionCount(params, type) {
  //   const count = await this.app.mysql.query(`SELECT * FROM sku s 
  //     LEFT JOIN catalog_relation cs ON s.id=cs.sku_id 
  //     LEFT JOIN catalog c ON c.id=cs.catalog_id 
  //     WHERE c.id=${params.id} AND s.is_delete=0 AND s.type IN (${type}) 
  //     `);
  //   return count[0].count;
  // }

  async getNaviTrade() {
    const list = await this.app.mysql.select('catalog', {
      columns: ['id', 'name', 'alias'],
      where: {
        is_delete: 0,
        parent_id: 1,
        is_navi: 1,
        is_show: 1
      },
      orders: [
        ['sort_num', ''],
      ]
    });

    return {
      list
    };
  }

  async getNaviService() {
    const list = await this.app.mysql.select('catalog', {
      columns: ['id', 'name', 'alias'],
      where: {
        is_delete: 0,
        parent_id: 2,
        is_show: 1,
        is_navi: 1
      },
      orders: [
        ['sort_num', ''],
      ]
    });

    return {
      list
    };
  }

  async getNewsCata() {
    const list = await this.app.mysql.select('catalog', {
      columns: ['id', 'name'],
      where: {
        is_delete: 0,
        parent_id: 3
      },
      orders: [
        ['id', 'desc']
      ]
    });

    return {
      list
    };
  }

  async getCataName(id) {
    const name = await this.app.mysql.get('catalog', {
      id: id
    }) || '';

    return name.name;
  }

  async getById(id) {
    const name = await this.app.mysql.get('catalog', {
      id: id
    }) || '';

    return name;
  }

  async getCatalogInfo(id) {
    const info = await this.app.mysql.get('catalog', {
      id: id
    });
    const banner = await this.app.mysql.select('banner', {
      columns: ['img_path', 'btn_text', 'btn_url', 'btn_description', 'btn_value'],
      where: {
        catalog_id: id
      },
      orders: [
        ['id', '']
      ]
    });

    const children = await this.app.mysql.select('catalog', {
      columns: ['id', 'name', 'sort_num'],
      where: {
        parent_id: id,
        is_show: 1,
        is_delete: 0
      },
      orders: [
        ['sort_num', '']
      ]
    });

    for (let item of children) {
      item.skus = await this.app.mysql.query('SELECT s.id,s.name,en_name,s.sub_title,s.thumb_img FROM sku s LEFT JOIN catalog_relation cs ON s.id=cs.sku_id LEFT JOIN catalog c ON c.id=cs.catalog_id WHERE c.id=' + item.id + ' AND s.is_delete=0 AND is_use=1 AND s.type IN (1,2) ORDER BY cs.sort_num DESC,s.gmt_modify DESC');
    };

    const solution = await this.app.mysql.query('SELECT s.id,s.name,en_name,s.sub_title,s.thumb_img,s.description,cs.sort_num,cs.id AS csid FROM sku s LEFT JOIN catalog_relation cs ON s.id=cs.sku_id LEFT JOIN catalog c ON c.id=cs.catalog_id WHERE c.id=' + id + ' AND s.is_delete=0 AND is_use=1 AND s.type IN (3,4) ORDER BY cs.sort_num DESC,s.gmt_modify DESC');
    info.is_show === 1 ? info.is_show = true : info.is_show = false;
    info.is_navi === 1 ? info.is_navi = true : info.is_navi = false;

    const cases = await this.app.mysql.query('SELECT s.id,s.title FROM cases s LEFT JOIN catalog_relation cr ON s.id=cr.case_id LEFT JOIN catalog c ON c.id=cr.catalog_id WHERE s.is_delete=0 AND s.is_publish=1 AND c.id=' + id + ' ORDER BY s.is_top,s.gmt_publish_time DESC');

    const resource = await this.app.mysql.query('SELECT s.id,s.title,s.path FROM resource s LEFT JOIN catalog_relation cr ON s.id=cr.resource_id LEFT JOIN catalog c ON c.id=cr.catalog_id WHERE s.is_delete=0 AND s.is_publish=1 AND c.id=' + id + ' ORDER BY s.gmt_publish_time DESC');

    return {
      info,
      banner,
      children,
      solution,
      cases,
      resource
    };
  }

  async editCatalogInfo(params) {
    const {
      app
    } = this;
    const info = params.form,
      banner = params.banner,
      bid = params.bid,
      children = params.children,
      solution = params.solution;

    const gmt_create = moment().format('YYYY-MM-DD HH:mm:ss');

    let pid = 1;
    if (bid == 'service') {
      pid = 2;
    }

    const count = await this.app.mysql.count('catalog', {
      name: info.name,
      parent_id: pid,
      is_delete: 0
    });
    if (count > 1) {
      return 1001; //名称重复
    }

    let changeValue = [];
    let orgInfo = await this.getCatalogInfo(info.id);
    let orgBanner = orgInfo.banner;

    if (orgBanner.length != banner.length) {

      if (orgBanner.length == 0 && banner.length == 1 && !banner[0].img_path && !banner[0].btn_text && (!banner[0].btn_url || banner[0].btn_url == 'https://') && !banner[0].btn_value && !banner[0].btn_description) {

      } else {
        changeValue.push('修改了Banner');
      }

    } else {
      let changeB = false;
      for (let [i, ob] of orgBanner.entries()) {

        if (banner[i]) {
          if (ob.img_path != banner[i].img_path || ob.btn_text != banner[i].btn_text || ob.btn_url != banner[i].btn_url || ob.btn_value != banner[i].btn_value || ob.btn_description != banner[i].btn_description) {
            changeB = true;
          }
        }

      }
      if (changeB) {
        changeValue.push('修改了Banner');
      }
    }

    let orgChildren = orgInfo.children;

    if (JSON.stringify(orgChildren) != JSON.stringify(children)) {
      changeValue.push('修改了服务楼层设置-服务排序');
    }

    if (children) {
      children.forEach(item => {
        let crow = {
          id: item.id,
          sort_num: item.sort_num,
        }
        this.app.mysql.update('catalog', crow);
      })
    }

    let solutionChange = false;
    if (solution) {
      for (let item of solution) {
        const r = await this.app.mysql.get('catalog_relation', {
          id: item.csid
        });
        if (r.sort_num != item.sort_num) {
          solutionChange = true;
        }

        let crow = {
          id: item.csid,
          sort_num: item.sort_num,
        }
        await this.app.mysql.update('catalog_relation', crow);
      }
    }

    if (solutionChange) {
      changeValue.push('修改解决方案楼层-排序');
    }

    if (info) {
      if (orgInfo.info.name != info.name) {
        changeValue.push('修改基本属性-中文名称');
      }

      if (orgInfo.info.en_name != info.en_name) {
        changeValue.push('修改基本属性-咨询名称');
      }

      if (orgInfo.info.alias != info.alias) {
        changeValue.push('修改基本属性-英文名称');
      }

      if ((info.description || orgInfo.info.description) && orgInfo.info.description != info.description) {
        changeValue.push('修改基本属性-行业描述');
      }

      if (orgInfo.info.is_show != info.is_show) {
        changeValue.push('修改基本属性-显示/隐藏');
      }

      if (orgInfo.info.is_navi != info.is_navi) {
        changeValue.push('修改基本属性-出现在导航');
      }

      if ((info.cover_img || orgInfo.info.cover_img) && orgInfo.info.cover_img != info.cover_img) {
        changeValue.push('修改基本属性-封面图');
      }

      if (orgInfo.info.page_type != info.page_type) {
        changeValue.push('修改基本属性-页面类型');
      }

      if (orgInfo.info.case_title != info.case_title) {
        changeValue.push('修改合作案例楼层标题');
      }

      if (orgInfo.info.page_title != info.page_title) {
        changeValue.push('修改SEO设置-Title');
      }

      if (orgInfo.info.page_keywords != info.page_keywords) {
        changeValue.push('修改SEO设置-Keywords');
      }

      if (orgInfo.info.page_description != info.page_description) {
        changeValue.push('修改SEO设置-Description');
      }

      if (orgInfo.info.mpage_title != info.mpage_title) {
        changeValue.push('修改SEO设置-Title');
      }

      if (orgInfo.info.mpage_keywords != info.mpage_keywords) {
        changeValue.push('修改SEO设置-Keywords');
      }

      if (orgInfo.info.mpage_description != info.mpage_description) {
        changeValue.push('修改SEO设置-Description');
      }


      info.is_show ? info.is_show = 1 : info.is_show = 0;
      info.is_navi ? info.is_navi = 1 : info.is_navi = 0;
      // const infoUpdate = await this.app.mysql.update('catalog', info);

      const result = await app.mysql.beginTransactionScope(async conn => {
        if (banner) {
          const bannerDrop = await conn.delete('banner', {
            catalog_id: info.id
          });
          for (let item of banner) {
            item.gmt_create = gmt_create;
            item.catalog_id = info.id;
            await conn.insert('banner', item);
          }
        }
        await conn.update('catalog', info);

        if (changeValue.length > 0) {
          await conn.insert('logs', {
            user_name: this.ctx.session.userInfo.name,
            catalog_id: info.id,
            log: changeValue.join(','),
            model: pid == 1 ? '行业分类首页配置' : '服务分类首页配置',
            name: info.name
          });
        }

        return {
          success: true
        };
      }, this.ctx);

      // const success = infoUpdate.affectedRows === 1;

      return {
        success: result.success
      };
    }

  }

  async getCatalogInfoByAlias(id) {
    const info = await this.app.mysql.get('catalog', {
      alias: id,
      is_delete: 0
    });
    const catid = info.id;
    const banner = await this.app.mysql.select('banner', {
      columns: ['img_path', 'btn_text', 'btn_url', 'btn_description', 'btn_value'],
      where: {
        catalog_id: catid
      },
      orders: [
        ['id', '']
      ]
    });
    const children = await this.app.mysql.select('catalog', {
      columns: ['id', 'name'],
      where: {
        parent_id: catid,
        is_show: 1,
        is_delete: 0
      },
      orders: [
        ['sort_num', '']
      ]
    });

    for (let item of children) {
      item.skus = await this.app.mysql.query('SELECT s.id,s.name,s.sub_title,s.thumb_img FROM sku s LEFT JOIN catalog_relation cs ON s.id=cs.sku_id LEFT JOIN catalog c ON c.id=cs.catalog_id WHERE c.id=' + item.id + ' AND s.is_delete=0 AND is_use=1 AND s.type IN (1,2) ORDER BY cs.sort_num DESC,s.gmt_modify DESC');
    };

    const solution = await this.app.mysql.query('SELECT s.id,s.name,s.sub_title,s.thumb_img,s.description FROM sku s LEFT JOIN catalog_relation cs ON s.id=cs.sku_id LEFT JOIN catalog c ON c.id=cs.catalog_id WHERE c.id=' + catid + ' AND s.is_delete=0 AND is_use=1 AND s.type IN (3,4) ORDER BY cs.sort_num DESC,s.gmt_modify DESC');

    const cases = await this.app.mysql.query('SELECT s.id,s.title FROM cases s LEFT JOIN catalog_relation cr ON s.id=cr.case_id LEFT JOIN catalog c ON c.id=cr.catalog_id WHERE s.is_delete=0 AND s.is_publish=1 AND c.id=' + catid + ' ORDER BY s.is_top,s.gmt_publish_time DESC');

    const resource = await this.app.mysql.query('SELECT s.id,s.title,s.path FROM resource s LEFT JOIN catalog_relation cr ON s.id=cr.resource_id LEFT JOIN catalog c ON c.id=cr.catalog_id WHERE s.is_delete=0 AND s.is_publish=1 AND c.id=' + catid + ' ORDER BY s.gmt_publish_time DESC');

    info.is_show === 1 ? info.is_show = true : info.is_show = false;
    info.is_navi === 1 ? info.is_navi = true : info.is_navi = false;

    return {
      info,
      banner,
      children,
      solution,
      cases,
      resource
    };
  }

  async getCatalogInfoByAliasTrain(id) {
    const info = await this.app.mysql.get('catalog', {
      alias: id,
      is_delete: 0
    });
    const catid = info.id;
    const banner = await this.app.mysql.select('banner', {
      columns: ['img_path', 'btn_text', 'btn_url', 'btn_description', 'btn_value'],
      where: {
        catalog_id: catid
      },
      orders: [
        ['id', '']
      ]
    });
    const children = await this.app.mysql.select('catalog', {
      columns: ['id', 'name'],
      where: {
        parent_id: catid,
        is_show: 1,
        is_delete: 0
      },
      orders: [
        ['sort_num', '']
      ]
    });

    let classTime = [],
      classArea = [],
      classDays = [];
    for (let item of children) {
      item.skus = await this.app.mysql.query('SELECT s.id,s.name,s.sub_title,s.thumb_img,s.class_info FROM sku s LEFT JOIN catalog_relation cs ON s.id=cs.sku_id LEFT JOIN catalog c ON c.id=cs.catalog_id WHERE c.id=' + item.id + ' AND s.is_delete=0 AND is_use=1 AND s.type IN (1,2) ORDER BY cs.sort_num DESC,s.gmt_modify DESC');

      for (let cl of item.skus) {
        let class_info = JSON.parse(cl.class_info);
        if (class_info) {
          class_info.time ? classTime = classTime.concat(class_info.time) : null;
          class_info.area ? classArea = classArea.concat(class_info.area) : null;
          class_info.days ? classDays = classDays.concat(class_info.days) : null;
        }

      }
    };

    var unique = (a) => [...new Set(a)];
    let class_info = {
      classTime: unique(classTime),
      classArea: unique(classArea),
      classDays: unique(classDays),
    }

    const solution = await this.app.mysql.query('SELECT s.id,s.name,s.sub_title,s.thumb_img,s.description FROM sku s LEFT JOIN catalog_relation cs ON s.id=cs.sku_id LEFT JOIN catalog c ON c.id=cs.catalog_id WHERE c.id=' + catid + ' AND s.is_delete=0 AND is_use=1 AND s.type IN (3,4) ORDER BY cs.sort_num DESC,s.gmt_modify DESC');

    const cases = await this.app.mysql.query('SELECT s.id,s.title FROM cases s LEFT JOIN catalog_relation cr ON s.id=cr.case_id LEFT JOIN catalog c ON c.id=cr.catalog_id WHERE s.is_delete=0 AND s.is_publish=1 AND c.id=' + catid + ' ORDER BY s.is_top,s.gmt_publish_time DESC');

    const resource = await this.app.mysql.query('SELECT s.id,s.title,s.path FROM resource s LEFT JOIN catalog_relation cr ON s.id=cr.resource_id LEFT JOIN catalog c ON c.id=cr.catalog_id WHERE s.is_delete=0 AND s.is_publish=1 AND c.id=' + catid + ' ORDER BY s.gmt_publish_time DESC');

    info.is_show === 1 ? info.is_show = true : info.is_show = false;
    info.is_navi === 1 ? info.is_navi = true : info.is_navi = false;

    return {
      info,
      banner,
      children,
      solution,
      cases,
      resource,
      class_info
    };
  }

  async saveRow(data) {
    const {
      app
    } = this;
    const row = {
      id: data.id,
      name: data.name,
    }

    const result = await app.mysql.beginTransactionScope(async conn => {
      await conn.update('catalog', row);

      const info = await conn.get('catalog', {
        id: data.id
      });
      const infop = await conn.get('catalog', {
        id: info.parent_id
      });
      await conn.insert('logs', {
        user_name: this.ctx.session.userInfo.name,
        log: '修改' + infop.name + '子分类' + data.name + '名称',
        model: infop.parent_id == 1 ? '行业分类' : '服务分类',
        name: data.name
      });

      return {
        success: true
      };
    }, this.ctx);

    // const success = update.affectedRows === 1;

    return {
      success: result.success
    };
  }

  async defaultSort(id) {
    const {
      app
    } = this;

    const result = await app.mysql.query('UPDATE catalog_relation SET sort_num=0 WHERE catalog_id=' + id);

    const success = result.affectedRows > 0;

    return {
      success
    };
  }
}

module.exports = CatalogService;