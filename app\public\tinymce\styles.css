.media-uploader {
  cursor: pointer;
  position: relative;
  text-align: center;
  color: #576474;
  background: #e8ebf1;
  padding: 32px;
  font-size: 20px;
  -webkit-font-smoothing: antialiased;
}

.media-uploader--dragover {
  background-color: #eff1f5;
  background-image: linear-gradient(#eff1f5, #f5f6f9);
  cursor: copy;
}

.media-uploader .media-uploader__close {
  color: #c2c6cc;
  position: absolute;
  top: 4px;
  right: 8px;
}
.media-uploader .media-uploader__close:before {
  content: "✕";
  display: inline-block;
  line-height: 1;
  font-size: 1.25em;
  font-style: normal;
  font-weight: bold;
}

.media-uploader .media-uploader__type {
  background: #fff;
  color: #c2c6cc;
  margin: -32px;
  margin-bottom: 0;
  padding: 16px;
}

.media-uploader .media-uploader__urlbox {
  margin-top: 24px;
  margin-bottom: 24px;
}

.media-uploader .media-uploader__url {
  padding: 8px;
  width: 90%;
  border-style: solid;
  border-radius: 3px;
  font-size: 16px;
}
.media-uploader .media-uploader__url.error {
  border-color: #ed3685;
}

.media-uploader .media-uploader__urlbox p.error {
  font-size: 12px;
  color: #ed3685;
}

.media-uploader .media-uploader__prompt {
  position: relative;
}

.media-uploader .media-uploader__prompt label {
  color: #3a9cff;
  cursor: pointer;
}
