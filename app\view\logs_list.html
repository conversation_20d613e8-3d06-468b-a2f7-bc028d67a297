<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        设置
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        操作日志
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="24">
                                <el-form :model="form" :inline="true">
                                    <el-form-item label="模块">
                                        <el-select v-model="form.model" placeholder="请选择">
                                            <el-option value="服务分类"></el-option>
                                            <el-option value="行业分类"></el-option>
                                            <el-option value="行业分类首页配置"></el-option>
                                            <el-option value="服务分类首页配置"></el-option>
                                            <el-option value="SKU配置-服务"></el-option>
                                            <el-option value="SKU配置-解决方案"></el-option>
                                            <el-option value="首页配置"></el-option>
                                            <el-option value="新闻中心"></el-option>
                                            <el-option value="新闻中心-新闻类别"></el-option>
                                            <el-option value="资讯中心"></el-option>
                                            <el-option value="资讯中心-案例类别"></el-option>
                                            <el-option value="资料下载"></el-option>
                                            <el-option value="资料下载-资源类别"></el-option>
                                            <el-option value="运维内容维护"></el-option>
                                        </el-select>
                                    </el-form-item>

                                    <el-form-item label="名称">
                                        <el-input v-model="form.name" placeholder="请输入名称"></el-input>
                                    </el-form-item>

                                    <el-form-item label="时间">
                                        <el-date-picker v-model="form.date" type="datetimerange" align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable>
                                        </el-date-picker>
                                    </el-form-item>

                                    <el-form-item label="用户">
                                        <el-input v-model="form.user_name" placeholder="请输入用户名"></el-input>
                                    </el-form-item>

                                    <el-form-item>
                                        <el-button type="primary" @click="getList">查询</el-button>
                                        <el-button @click=_default>重置</el-button>
                                    </el-form-item>
                                </el-form>
                            </el-col>
                        </el-row>

                        <el-row v-loading="loading">

                            <el-table :data="tableList.list" stripe style="width: 100%">
                                <el-table-column prop="model" label="模块">
                                </el-table-column>
                                <el-table-column prop="name" label="名称">
                                </el-table-column>
                                <el-table-column prop="time" label="时间">
                                </el-table-column>
                                <el-table-column prop="user_name" label="用户">
                                </el-table-column>
                                <el-table-column prop="log" label="操作日志">
                                </el-table-column>
                            </el-table>
                            <el-pagination @current-change="handleCurrentChange" background :current-page.sync="form.page" :page-size="form.limit"
                                layout="total, prev, pager, next" :total="tableList.total" style="padding:10px 0;text-align: right;">
                            </el-pagination>
                        </el-row>
                    </el-main>
            </el-container>
    </el-container>
    <script>
    var main = new Vue({
        el: '#Main',
        data: {
            tableList: {},
            form: {
                model:'',
                name: '',
                date: '',
                user_name: '',
                page:1,
                limit: 30,
            },
            loading: false,
        },
        methods: {
            handleCurrentChange(r) {
                this.getList();
            },
            getList(){
                var that = this;
                
                that.loading = true;
                var params = this.form;
                params._csrf = '<%- csrf %>';
                axios.post('/logslist', params)
                    .then(function(result) {
                        that.loading = false;
                        if (result.data.success) {
                            that.tableList = result.data.data;
                        }else{
                            that.$message.error('获取列表出错');
                        }
                    });
            },
            _default(){
                this.form.model = '';
                this.form.name = '';
                this.form.date = '';
                this.form.user_name = '';
            },
            _export(){

                var params = this.form;
                var that = this;
                that.loading = true;
                params._csrf = '<%- csrf %>';
                axios.post('/analysis/record_export', params)
                    .then(function(result) {
                        that.loading = false;
                        if(result.data.success){
                            window.location = result.data.data;
                        }
                    });
            }
        },
        mounted(){
            this.$nextTick(function(){
                this.getList();
                document.getElementById('preLoading').style.display = 'none';
            });
        }
    });
    </script>
    <% include footer.html %>