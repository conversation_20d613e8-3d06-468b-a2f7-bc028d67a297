<% include header.html %>
<el-container id="Main">
    <% include eheader.html %>
    <el-container>
        <% include eside.html %>
        <el-main>
            <el-row>
                <el-col :span="24">
                    <el-breadcrumb separator-class="el-icon-arrow-right">
                        <el-breadcrumb-item>
                            <a href='/'>首页</a>
                        </el-breadcrumb-item>
                        <el-breadcrumb-item>
                            SKU楼层模板
                        </el-breadcrumb-item>
                    </el-breadcrumb>
                </el-col>
            </el-row>
            <el-row v-loading="loading">
                <el-form :inline="true" :model="form">
                    <el-col :span="18">
                        <el-form-item label="模板名称">
                            <el-input v-model='form.name' placeholder="请输入模板名称" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="模板标题">
                            <el-input v-model='form.title' placeholder="请输入模板标题" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="文章标题">
                            <el-input v-model='form.new_title' placeholder="请输入文章标题" @change="handleChangeNewTitle"
                                clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" style='text-align: right;'>
                        <el-form-item>
                            <el-button type="primary" @click="getList">查询</el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button @click="reset">重置</el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handlerCreate">添加</el-button>
                        </el-form-item>
                    </el-col>
                </el-form>
                <el-col :span="24">
                    <el-table :data="tableList.list" stripe style="width: 100%">
                        <el-table-column prop="id" label="序列号">
                        </el-table-column>
                        <el-table-column prop="name" label="模板名称">
                        </el-table-column>
                        <el-table-column prop="title" label="模板标题">
                        </el-table-column>
                        <el-table-column prop="relevance_case" label="使用的文章">
                            <template slot-scope="scope" v-if='scope.row.relevance_case.length'>
                                <el-tooltip content="Bottom center" placement="bottom" effect="light">
                                    <div slot="content">
                                        <div v-for='(item, index ) of scope.row.relevance_case' :key='index'>
                                            <template v-if='item.type === 1'>
                                                <el-link :href='"/sku/service/" + item.id' target="_blank"
                                                    :underline="false" type="success">{{item.name}}
                                                </el-link>
                                            </template>
                                            <template v-else>
                                                <el-link :href='"/sku/solution/" + item.id' target="_blank"
                                                    :underline="false" type="success">{{item.name}}
                                                </el-link>
                                            </template>
                                        </div>
                                    </div>
                                    <span>{{ scope.row.relevance_case[0].name }}...</span>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column prop="create_time" label="创建时间">
                        </el-table-column>
                        <el-table-column fixed="right" label="操作" width="160">
                            <template slot-scope="scope">
                                <el-button @click="handleEidt(scope.row)" size="mini">编辑</el-button>
                                <el-button type="primary" @click="handleDel(scope.row)" size="mini">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination @current-change="handleCurrentChange" background :current-page.sync="form.page"
                        :page-size="form.limit" layout="total, prev, pager, next" :total="tableList.total"
                        style="padding:10px 0;text-align: right;">
                    </el-pagination>
                </el-col>
            </el-row>
            <el-dialog :title="dialog.title" :visible.sync="dialog.status" width="70%">
                <el-form :model="dialog.data" label-width="90px">
                    <el-form-item label="模板名称">
                        <el-input v-model='dialog.data.name' placeholder="请输入模板名称..." maxlength="200"></el-input>
                    </el-form-item>
                    <el-form-item label="模板标题">
                        <el-input v-model='dialog.data.title' placeholder="请输入模板标题..." maxlength="200"></el-input>
                    </el-form-item>
                    <el-form-item label="模板内容">
                        <textarea id="dialogDataContent" v-model="dialog.data.content" cols="60" rows="5"
                            class="el-input__inner foocontent" style="height:100px" placeholder="请输入模板内容..."></textarea>
                    </el-form-item>
                    <el-form-item label="使用的文章" v-if='dialog.data.relevance_case.length'>
                        <div v-for='(item, index ) of dialog.data.relevance_case' :key='index'>
                            <template v-if='item.type === 1'>
                                <el-link :href='"/sku/service/" + item.id' target="_blank" :underline="false"
                                    type="success">{{item.name}}
                                </el-link>
                            </template>
                            <template v-else>
                                <el-link :href='"/sku/solution/" + item.id' target="_blank" :underline="false"
                                    type="success">{{item.name}}
                                </el-link>
                            </template>
                        </div>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="dialog.status = false">取 消</el-button>
                    <el-button type="primary" @click="handleSubmit">确 定</el-button>
                </span>
            </el-dialog>
        </el-main>
    </el-container>
</el-container>
<script src="/static/tinymce/tinymce.min.js"></script>
<script>
    var tinyConfig = {
        height: 600,
        language: 'zh_CN',
        menubar: false,
        plugins: 'advlist autolink link image lists charmap print preview code textcolor table paste media',
        toolbar: 'undo redo | formatselect fontsizeselect bold italic underline forecolor backcolor  | alignleft aligncenter alignright alignjustify superscript subscript | bullist numlist outdent indent | removeformat | image media link table | code preview',
        fontsize_formats: '12px 14px 16px 18px 20px 24px 36px',
        file_browser_callback_types: 'image',
        // images_upload_url: '<%- ticMallHost %>ticCenter/business/api.v0.platform/fileUpload/uploadOss',
        images_reuse_filename: true,
        // images_upload_base_path: '/',
        relative_urls: false,
        branding: false,
        width: '100%',
        images_upload_handler: function (blobInfo, success, failure) {
            let that = this
            let fd = new FormData()
            let file = blobInfo.blob();
            fd.append('file', file, file.name)
            fd.append('systemID', 5);
            let config = {
                headers: { 'Content-Type': 'multipart/form-data' }
            }
            axios.post('<%- ticMallHost %>ticCenter/business/api.v0.platform/fileUpload/uploadOss', fd, config)
                .then(function (result) {
                    if (result.data.resultCode === '0') {
                        success(result.data.data.fileName)
                    } else {
                        that.$message.success(result.data.res);
                        failure()
                    }
                });
        }
    };
    var main = new Vue({
        el: '#Main',
        data: {
            dialog: {
                status: true,
                title: '添加SKU楼层模板',
                data: {
                    name: '',
                    title: '',
                    content: '',
                    relevance_case: []
                }
            },
            articles: [
                {
                    id: 1,
                    name: '超链接'
                }
            ],
            tableList: {
                list: [],
                total: 0,
            },
            form: {
                page: 1,
                limit: 10,
                name: '',
                title: '',
                new_title: ''
            },
            loading: false,

        },
        methods: {
            handleChangeNewTitle(val) {
                if (val) {
                    this.form.title = ''
                    this.form.name = ''
                }
            },
            handlerCreate() {
                this.dialog.title = '添加SKU楼层模板'
                this.dialog.status = true
                this.dialog.data.name = this.dialog.data.title = this.dialog.data.id = ''
                this.dialog.data.relevance_case = []
                tinymce.activeEditor.setContent('')
            },
            reset() {
                this.form.name = this.form.title = this.form.new_title = ''
                this.getList()
            },
            handleCurrentChange() {
                this.getList();
            },
            handleSubmit() {
                var that = this;
                that.loading = true;
                var params = this.dialog.data;
                params._csrf = '<%- csrf %>';
                params.content = tinymce.get('dialogDataContent').getContent();
                if (params.content && params.name && params.title) {
                    if (params.id) {
                        axios.post('/template/sku/mod', params)
                            .then(function (result) {
                                that.loading = false;
                                console.log(result)
                                if (result.data.success) {
                                    that.dialog.status = false
                                    that.getList()
                                } else {
                                    that.$message.error(result.data.msg || '编辑错误，请稍后重试');
                                }
                            });
                    } else {
                        axios.post('/template/sku/add', params)
                            .then(function (result) {
                                that.loading = false;
                                console.log(result)
                                if (result.data.success) {
                                    that.dialog.status = false
                                    that.getList()
                                } else {
                                    that.$message.error(result.data.msg || '添加错误，请稍后重试');
                                }
                            });
                    }
                } else {
                    that.$message.error('模板名称、标题和内容不能为空');
                }
            },
            handleEidt(data) {
                console.log(data)
                const thatData = { ...data };
                this.dialog.title = '编辑SKU楼层模板'
                this.dialog.status = true;
                this.dialog.data = thatData
                tinymce.activeEditor.setContent(this.dialog.data.content)
            },
            handleDel(row) {
                const { id, relevance_case } = row
                var that = this
                this.$confirm('是否删除？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    console.log(id)
                    if (relevance_case.length) {
                        that.$message.error('模板被使用，暂不能删除。');
                    } else {
                        axios.post('/template/sku/del', { id })
                            .then(function (result) {
                                console.log(result)
                                if (result.data.success) {
                                    that.getList()
                                } else {
                                    that.$message.error('删除记录失败。');
                                }
                            });
                    }
                })
            },
            getList() {
                var that = this;
                that.loading = true;
                var params = this.form;
                params._csrf = '<%- csrf %>';
                axios.post('/template/sku/qry', params)
                    .then(function (result) {
                        that.loading = false;
                        if (result.data.success) {
                            that.tableList.list = result.data.list;
                            that.tableList.total = result.data.total;
                        } else {
                            that.$message.error('获取列表出错');
                        }
                    });
            },
        },
        mounted() {
            this.$nextTick(function () {
                document.getElementById('preLoading').style.display = 'none';
                tinyConfig.selector = '.foocontent';
                tinymce.init(tinyConfig);
                console.log(tinyConfig.selector, tinymce.init(tinyConfig))
                this.dialog.status = false

                this.getList()
            });
        }
    });
</script>
<% include footer.html %>