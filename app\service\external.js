'use strict';

const Service = require('egg').Service;
const axios = require('axios');
const crypto = require('crypto');
const ticSend = {
  pid: 'pid.order',
  pcode: 'kPhG6so9xOHY3QsO'
}
const ticMall = {
  pid: 'pid.mall',
  pcode: 'Z0zCnRE3IaY9Kzem'
}
const {
  userManageHost,
} = require('./../../config/info').siteInfo
const util = require('./../controller/util');
const { env } = require('../../config/info').siteInfo;

function createHeader(type, params, frontUrl) {
  const pmd5 = crypto.createHash('md5').update((type.pid + type.pcode).toUpperCase()).digest('hex')
  const param = JSON.stringify(params) + pmd5.toUpperCase()
  const timestamp = new Date().getTime();
  const sign = crypto.createHash('md5').update(param + timestamp).digest('hex')

  return {
    'Content-Type': 'application/json',
    pid: type.pid,
    pcode: type.pcode,
    timestamp,
    sign,
    frontUrl,
    // version: 'gray'
  }
}

class ExternalService extends Service {
  // 工单列表查询
  async ticketQry(params, headers) {
    const {
      app,
      ctx
    } = this;

    const url = await util.getHost(app.config.env, ctx);
    let result = await axios({
      url: url + '/ticMall/business/api.v1.mall/ticket/qry',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mall/ticket/qry', err);
      return err
    });

    return result;
  }

  // 重发工单
  async ticketResend(params, headers) {
    const {
      app,
      ctx
    } = this;
    const url = await util.getHost(app.config.env, ctx);

    let result = await axios({
      url: url + '/ticMall/business/api.v1.mall/ticket/resend',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      if (err.response.status == '405') {
        return {
          success: 0,
          resultMsg: '您输入的内容存在非法字符，请修改后重试。'
        }
      } else {
        ctx.logger.error('/ticMall/business/api.v1.mall/ticket/resend', err);
        return err
      }
    });

    return {
      success: result && result.resultCode === '0' ? true : false,
      resultMsg: result && result.resultMsg
    };
  }

  async uploadImg(params, headers) {
    const {
      app,
      ctx
    } = this;

    let result = await axios({
      url: `${env[this.app.config.env].userManageHost}FrameWorkApi/file/uploadPublicFile`,
      method: 'post',
      data: params,
      headers: {
        'content-type': 'application/x-www-form-urlencoded'
      },
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${env[this.app.config.env].userManageHost}FrameWorkApi/file/uploadPublicFile`, err);
      return err
    });

    return {
      result
    };
  }

  // 类目列表查询
  async tardeRelevanceQry(params, headers) {
    const {
      app,
      ctx
    } = this;
    const url = await util.getHost(app.config.env, ctx);

    let result = await axios({
      url: url + '/ticMall/business/api.v1.mgmt/product/qry',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mgmt/product/qry', err);
      return err
    });

    return result;
  }

  // 类目新增
  async tardeRelevanceAdd(params, headers) {
    const {
      app,
      ctx
    } = this;
    params.operatorCode = ctx.session.userInfo.user_name || ctx.session.userInfo.name;
    const url = await util.getHost(app.config.env, ctx);

    let result = await axios({
      url: url + '/ticMall/business/api.v1.mgmt/product/add',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mgmt/product/add', err);
      return err
    });

    return result;
  }

  // 类目修改
  async tardeRelevanceMod(params, headers) {
    const {
      app,
      ctx
    } = this;
    // ctx.logger.error('app.config.env', app.config.env);
    params.operatorCode = ctx.session.userInfo.user_name || ctx.session.userInfo.name;
    const url = await util.getHost(app.config.env, ctx);

    let result = await axios({
      url: url + '/ticMall/business/api.v1.mgmt/product/mod',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mgmt/product/mod', err);
      return err
    });

    return result;
  }

  // 类目删除
  async tardeRelevanceDel(params, headers) {
    const {
      app,
      ctx
    } = this;
    params.operatorCode = ctx.session.userInfo.user_name || ctx.session.userInfo.name;
    const url = await util.getHost(app.config.env, ctx);

    let result = await axios({
      url: url + '/ticMall/business/api.v1.mgmt/product/del',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mgmt/product/del', err);
      return err
    });

    return result;
  }

  // 类目排序
  async tardeRelevanceSort(params, headers) {
    const {
      app,
      ctx
    } = this;
    params.operatorCode = ctx.session.userInfo.user_name || ctx.session.userInfo.name;
    const url = await util.getHost(app.config.env, ctx);

    let result = await axios({
      url: url + '/ticMall/business/api.v1.mgmt/product/transSort',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mgmt/product/transSort', err);
      return err
    });

    return result;
  }

  // 查询全部说明
  async tardeRelevanceQryTask(params, headers) {
    const {
      app,
      ctx
    } = this;
    const url = await util.getHost(app.config.env, ctx);

    let result = await axios({
      url: url + '/ticMall/business/api.v1.mall/catalog/qryByAlias',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mall/catalog/qryByAlias', err);
      return err
    });

    return result;
  }

  // 保存说明
  async tardeRelevanceSaveTask(params, headers) {
    const {
      app,
      ctx
    } = this;
    const url = await util.getHost(app.config.env, ctx);

    let result = await axios({
      url: url + '/ticMall/business/api.v1.mgmt/catalog/modExplain',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mgmt/catalog/modExplain', err);
      return err
    });

    return result;
  }

  // 日志查询
  async logQry(params, headers) {
    const {
      app,
      ctx
    } = this;
    const url = await util.getHost(app.config.env, ctx);

    let result = await axios({
      url: url + '/ticMall/business/api.v1.mgmt/logs/qry',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mgmt/logs/qry', err);
      return err
    });

    return result;
  }

  // 查询当前用户的服务或行业
  async getUserCatalogList(params, headers) {
    const {
      app,
      ctx
    } = this;
    const url = await util.getHost(app.config.env, ctx);

    let result = await axios({
      url: url + '/ticMall/business/api.v1.mgmt/catalog/qryByAlias',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data.data.items
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mgmt/catalog/qryByAlias', err);
      return err
    });
    return result;
  }

  // 查询bu列表
  async buManageQry(params, headers) {
    const {
      app,
      ctx
    } = this;
    const url = await util.getHost(app.config.env, ctx);
    let result = await axios({
      url: url + '/ticMall/business/api.v1.mgmt/bu/qry',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mgmt/bu/qry', err);
      return err
    });
    return result;
  }

  // 添加bu
  async buManageAdd(params, headers) {
    const {
      app,
      ctx
    } = this;
    params.operatorCode = ctx.session.userInfo.user_name || ctx.session.userInfo.name;
    const url = await util.getHost(app.config.env, ctx);
    let result = await axios({
      url: url + '/ticMall/business/api.v1.mgmt/bu/add',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mgmt/bu/add', err);
      return err
    });
    return result;
  }

  // 编辑bu
  async buManageModify(params, headers) {
    const {
      app,
      ctx
    } = this;
    params.operatorCode = ctx.session.userInfo.user_name || ctx.session.userInfo.name;
    const url = await util.getHost(app.config.env, ctx);
    let result = await axios({
      url: url + '/ticMall/business/api.v1.mgmt/bu/mod',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mgmt/bu/mod', err);
      return err
    });
    return result;
  }

  // 删除bu
  async buManageDel(params, headers) {
    const {
      app,
      ctx
    } = this;
    params.operatorCode = ctx.session.userInfo.user_name || ctx.session.userInfo.name;
    const url = await util.getHost(app.config.env, ctx);
    let result = await axios({
      url: url + '/ticMall/business/api.v1.mgmt/bu/del',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mgmt/bu/del', err);
      return err
    });
    return result;
  }

  // get IM工具
  async getIMtool(params, headers) {
    const {
      app,
      ctx
    } = this;
    const url = await util.getHost(app.config.env, ctx);
    let result = await axios({
      url: url + '/ticCenter/business/api.v1.center/config/qry',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      ctx.logger.error('/ticCenter/business/api.v1.center/config/qry', app.config.env);
      res.data.data.items.forEach(v => {
        v.groupName = v.paraCode.split("_")[0]
      })
      return res.data && res.data.data.items || []
    }).catch(err => {
      ctx.logger.error('/ticCenter/business/api.v1.center/config/qry', err);
      return err
    });
    return result;
  }

  // modify IM工具
  async IMtoolModify(params, headers) {
    const {
      app,
      ctx
    } = this;
    const url = await util.getHost(app.config.env, ctx);
    let result = await axios({
      url: url + '/ticCenter/business/api.v1.center/config/mod',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticCenter/business/api.v1.center/config/mod', err);
      return err
    });
    return result;
  }

  // 获取导航菜单
  async getNavication(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mgmt/navi/qry',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data && res.data.data.items || []
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/navi/qry`, err);
      return err
    });
  }

  // 保存导航菜单
  async navigationSave(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mgmt/navi/save',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/navi/save`, err);
      return err
    });
  }

  // 删除导航菜单
  async navigationDelete(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mgmt/navi/del',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/navi/del`, err);
      return err
    });
  }

  // 更新导航菜单
  async navigationUpdate(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mgmt/catalog/save',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, {}, {}),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/catalog/save`, err);
      return err
    });
  }

  // 刷新导航菜单
  async navigationRefresh(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mgmt/navi/refresh',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, {}, {}),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/navi/refresh`, err);
      return err
    });
  }

  // 设置类目导航显示(服务端)
  async navigationSetting(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mgmt/catalog/setNavi',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, {}),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/catalog/setNavi`, err);
      return err
    });
  }

  // 获取问卷类目
  async qryCategory(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);

    return await axios({
      url: host + '/ticCenter/business/api.v1.center/catagory/qryUse',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, {}, headers && (headers.fronturl || headers.origin || headers.host)),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticCenter/business/api.v1.center/catagory/qryUse`, err);
      return err
    });
  }

  // 复制案例
  async copyCase(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);
    params.operatorCode = ctx.session.userInfo.user_name || ctx.session.userInfo.name;

    return await axios({
      url: host + '/ticMall/business/api.v1.mgmt/cases/copy',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, {}),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/cases/copy`, err);
      return err
    });
  }

  // 导出excle
  async exportExcle(params, headers) {
    // 返回一个签名题，替客户端签名，客户端返回留信息会导致下载文件是失败的
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);
    return Object.assign(createHeader(ticMall, params, {}), { host })
    // const {
    //   app,
    //   ctx
    // } = this;
    // const host = await util.getHost(app.config.env, ctx);
    // params.operatorCode = ctx.session.userInfo.user_name || ctx.session.userInfo.name;
    // return await axios({
    //   url: host + '/ticMall/business/api.v1.mgmt/res/exp',
    //   method: 'post',
    //   data: JSON.stringify(params),
    //   headers: createHeader(ticMall, params, {}),
    //   responseType: "blob",
    // }).then(res => {
    //   let fileName = ''
    //   var contentDisposition = res.headers["content-disposition"];
    //   if (contentDisposition) {
    //     var patt = new RegExp("filename=([^;]+\\.[^\\.;]+);*");
    //     var result = patt.exec(contentDisposition);
    //     if (result) fileName = decodeURIComponent(result[1]);
    //   }
    //   return {
    //     data: res.data,
    //     fileName
    //   }
    // }).catch(err => {
    //   ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/res/exp`, err);
    //   return err
    // });
  }

  // 合作案例（资讯列表）列表(服务端)
  // http://*************:3000/project/46/interface/api/6625
  async qryCaseList(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);
    params.operatorCode = ctx.session.userInfo.user_name || ctx.session.userInfo.name;

    return await axios({
      url: host + '/ticMall/business/api.v1.mgmt/cases/qryList',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, {}),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/cases/qryList`, err);
      return err
    });
  }

  // 查询关联资讯数量
  async qryRelCaseCount(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);

    return await axios({
      url: host + '/business/api.v1.mgmt/cases/qryRelCaseCount',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, {}),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/cases/qryRelCaseCount`, err);
      return err
    });
  }

  // 解决方案列表(管理端)
  // http://*************:3000/project/46/interface/api/6633
  async qrySkuList(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);
    params.operatorCode = ctx.session.userInfo.user_name || ctx.session.userInfo.name;

    return await axios({
      url: host + '/ticMall/business/api.v1.mgmt/sku/qryList',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, {}),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/sku/qryList`, err);
      return err
    });
  }

  // 资源访问列表
  // http://*************:3000/project/46/interface/api/6609
  async qryResourceList(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);
    params.operatorCode = ctx.session.userInfo.user_name || ctx.session.userInfo.name;

    return await axios({
      url: host + '/business/api.v1.mgmt/res/qryList',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, {}),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/res/qryList`, err);
      return err
    });
  }

  // 查询关联资料数量
  async qryRelResourceCount(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);

    return await axios({
      url: host + '/business/api.v1.mgmt/res/qryRelResourceCount',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, {}),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/res/qryRelResourceCount`, err);
      return err
    });
  }

  // 新闻案例列表(服务端)
  // http://*************:3000/project/46/interface/api/6617
  async qryNewsList(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);
    params.operatorCode = ctx.session.userInfo.user_name || ctx.session.userInfo.name;

    return await axios({
      url: host + '/ticMall/business/api.v1.mgmt/news/qryList',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, {}),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/news/qryList`, err);
      return err
    });
  }

  // CDN刷新
  async CDNRefresh(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.config.env, ctx);
    return await axios({
      url: host + '/ticMall/business/api.v1.mgmt/cdn/refresh',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(ticMall, params, {}),
    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/cdn/refresh`, err);
      return err
    });
  }
  // async CDNRefreshHuawei(params, headers) {
  //   const {
  //     app,
  //     ctx
  //   } = this;
  //   const host = await util.getHost(app.config.env, ctx);
  //   return await axios({
  //     url: host + '/ticMall/business/api.v1.mgmt/cdn/hwRefresh',
  //     method: 'post',
  //     data: JSON.stringify(params),
  //     headers: createHeader(ticMall, params, {}),
  //   }).then(res => {
  //     return res.data
  //   }).catch(err => {
  //     ctx.logger.error(`${host}/ticMall/business/api.v1.mgmt/cdn/hwRefresh`, err);
  //     return err
  //   });
  // }
}

module.exports = ExternalService;
