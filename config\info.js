'use strict';
const crypto = require('crypto');
const pid = 'cmsPid',
  pcode = 'Tic123!';
let upStr = (pid + pcode).toUpperCase();

const pstr = crypto.createHash('md5').update(upStr).digest("hex");
const siteInfo = {
  site_title: 'TIC CMS',
  solrPstr: pstr.toUpperCase(),
  solrPid: pid,
  env: {
    prodGray: {
      portal_url: 'https://portal.sgsonline.com.cn',
      console_url: 'https://console.sgsonline.com.cn',
      host: 'www.sgsonline.com.cn',
      view_url: 'https://www.sgsonline.com.cn',
      mview_url: 'https://m.sgsonline.com.cn',
      solrUrl: 'https://gate.sgsonline.com.cn/solrServer/business/api.v1.solr/content',
      userEmailSendApi: 'https://gate.sgsonline.com.cn/ticSend/openapi/api.v1.send/SysMailMsgSendAction/sendTemplateMail',
      apiSetCacheApi: 'https://gate.sgsonline.com.cn/ticSso/business/api.v1.sso/privilege/refresh',
      apiGetCacheApi: 'https://gate.sgsonline.com.cn/ticSso/business/api.v1.sso/privilege/get',
      userManageHost: 'https://cnapp.sgs.net/', // user manage 域名地址
      ticMallHost: 'https://gate.sgsonline.com.cn/', // ticMall 域名地址
      userMgmLogin: 'https://cnapp.sgs.net/sgsLogin/', //登录入口
      userMgmApiUserInfo: 'https://cnapp.sgs.net/UserManagementApi/resource/querySystemResourceByCode/', //获取用户信息
      userMgmApi: 'https://cnapp.sgs.net/UserManagementApi/login/queryResources', //获取用户权限信息
      userMgmExtLogin: "https://cnapp.sgs.net/UserManagementApi/login/externalLoginIn", // 外网登录
      userInfoApi: 'https://cnapp.sgs.net/UserManagementApi/employee/queryEmployeeInfo/', //获取内网用户emial地址
      userInfoOutApi: 'https://cnapp.sgs.net/UserManagementApi/conpanyEmployee/queryExternalCompanyEmployeeInfo', //获取外网emial地址
      userLogout: 'https://cnapp.sgs.net/UserManagementApi/login/loginOut' //内网用户登出
    },
    prod: {
      portal_url: 'https://portal.sgsonline.com.cn',
      console_url: 'https://console.sgsonline.com.cn',
      host: 'www.sgsonline.com.cn',
      view_url: 'https://www.sgsonline.com.cn',
      mview_url: 'https://m.sgsonline.com.cn',
      solrUrl: 'https://gate.sgsonline.com.cn/solrServer/business/api.v1.solr/content',
      userEmailSendApi: 'https://gate.sgsonline.com.cn/ticSend/openapi/api.v1.send/SysMailMsgSendAction/sendTemplateMail',
      apiSetCacheApi: 'https://gate.sgsonline.com.cn/ticSso/business/api.v1.sso/privilege/refresh',
      apiGetCacheApi: 'https://gate.sgsonline.com.cn/ticSso/business/api.v1.sso/privilege/get',
      userManageHost: 'https://cnapp.sgs.net/', // user manage 域名地址
      ticMallHost: 'https://gate.sgsonline.com.cn/', // ticMall 域名地址
      userMgmLogin: 'https://cnapp.sgs.net/sgsLogin/', //登录入口
      userMgmApiUserInfo: 'https://cnapp.sgs.net/UserManagementApi/resource/querySystemResourceByCode/', //获取用户信息
      userMgmApi: 'https://cnapp.sgs.net/UserManagementApi/login/queryResources', //获取用户权限信息
      userMgmExtLogin: "https://cnapp.sgs.net/UserManagementApi/login/externalLoginIn", // 外网登录
      userInfoApi: 'https://cnapp.sgs.net/UserManagementApi/employee/queryEmployeeInfo/', //获取内网用户emial地址
      userInfoOutApi: 'https://cnapp.sgs.net/UserManagementApi/conpanyEmployee/queryExternalCompanyEmployeeInfo', //获取外网emial地址
      userLogout: 'https://cnapp.sgs.net/UserManagementApi/login/loginOut' //内网用户登出
    },
    uat: {
      portal_url: 'https://portaluat.sgsonline.com.cn',
      console_url: 'https://consoleuat.sgsonline.com.cn',
      host: 'msuat.sgsonline.com.cn',
      view_url: 'http://cmsuat.sgsonline.com.cn',
      mview_url: 'https://muat.sgsonline.com.cn',
      solrUrl: 'https://gateuat.sgsonline.com.cn/solrServer/business/api.v1.solr/content',
      userEmailSendApi: 'https://gateuat.sgsonline.com.cn/ticSend/openapi/api.v1.send/SysMailMsgSendAction/sendTemplateMail',
      apiSetCacheApi: 'https://gateuat.sgsonline.com.cn/ticSso/business/api.v1.sso/privilege/refresh',
      apiGetCacheApi: 'https://gateuat.sgsonline.com.cn/ticSso/business/api.v1.sso/privilege/get',
      userManageHost: 'https://cnapp-uat.sgs.net/', // user manage 域名地址
      ticMallHost: 'https://gateuat.sgsonline.com.cn/', // ticMall 域名地址
      userMgmLogin: 'https://cnapp-uat.sgs.net/sgsLogin/', //登录入口
      userMgmApiUserInfo: 'https://cnapp-uat.sgs.net/UserManagementApi/resource/querySystemResourceByCode/', //获取用户信息
      userMgmApi: 'https://cnapp-uat.sgs.net/UserManagementApi/login/queryResources', //获取用户权限信息
      userMgmExtLogin: "https://cnapp-uat.sgs.net/UserManagementApi/login/externalLoginIn", // 外网登录
      userInfoApi: 'https://cnapp-uat.sgs.net/UserManagementApi/employee/queryEmployeeInfo/', //获取内网用户emial地址
      userInfoOutApi: 'https://cnapp-uat.sgs.net/UserManagementApi/conpanyEmployee/queryExternalCompanyEmployeeInfo', //获取外网emial地址
      userLogout: 'https://cnapp-uat.sgs.net/UserManagementApi/login/loginOut' //内网用户登出
    },
    gray: {
      portal_url: 'https://portaluatgray.sgsonline.com.cn',
      console_url: 'https://consoleuatgray.sgsonline.com.cn',
      host: 'msuatgray.sgsonline.com.cn',
      view_url: 'http://cmsuatgray.sgsonline.com.cn',
      mview_url: 'https://muatgray.sgsonline.com.cn',
      solrUrl: 'https://gateuat.sgsonline.com.cn/solrServer/business/api.v1.solr/content',
      userEmailSendApi: 'https://gateuat.sgsonline.com.cn/ticSend/openapi/api.v1.send/SysMailMsgSendAction/sendTemplateMail',
      apiSetCacheApi: 'https://gateuat.sgsonline.com.cn/ticSso/business/api.v1.sso/privilege/refresh',
      apiGetCacheApi: 'https://gateuat.sgsonline.com.cn/ticSso/business/api.v1.sso/privilege/get',
      userManageHost: 'https://cnapp-uat.sgs.net/', // user manage 域名地址
      ticMallHost: 'https://gateuat.sgsonline.com.cn/', // ticMall 域名地址
      userMgmLogin: 'https://cnapp-uat.sgs.net/sgsLogin/', //登录入口
      userMgmApiUserInfo: 'https://cnapp-uat.sgs.net/UserManagementApi/resource/querySystemResourceByCode/', //获取用户信息
      userMgmApi: 'https://cnapp-uat.sgs.net/UserManagementApi/login/queryResources', //获取用户权限信息
      userMgmExtLogin: "https://cnapp-uat.sgs.net/UserManagementApi/login/externalLoginIn", // 外网登录
      userInfoApi: 'https://cnapp-uat.sgs.net/UserManagementApi/employee/queryEmployeeInfo/', //获取内网用户emial地址
      userInfoOutApi: 'https://cnapp-uat.sgs.net/UserManagementApi/conpanyEmployee/queryExternalCompanyEmployeeInfo', //获取外网emial地址
      userLogout: 'https://cnapp-uat.sgs.net/UserManagementApi/login/loginOut' //内网用户登出
    },
    dev: {
      portal_url: 'https://portaldev.sgsonline.com.cn',
      console_url: 'https://consoledev.sgsonline.com.cn',
      host: 'cmsdev.sgsonline.com.cn',
      view_url: 'https://cmsdev.sgsonline.com.cn',
      mview_url: 'https://mdev.sgsonline.com.cn',
      solrUrl: 'https://gatedev.sgsonline.com.cn/solrServer/business/api.v1.solr/content',
      userEmailSendApi: 'https://gatedev.sgsonline.com.cn/ticSend/openapi/api.v1.send/SysMailMsgSendAction/sendTemplateMail',
      apiSetCacheApi: 'https://gatedev.sgsonline.com.cn/ticSso/business/api.v1.sso/privilege/refresh',
      apiGetCacheApi: 'https://gatedev.sgsonline.com.cn/ticSso/business/api.v1.sso/privilege/get',
      userManageHost: 'https://cnapp-dev.sgs.net/', // user manage 域名地址
      ticMallHost: 'https://gatedev.sgsonline.com.cn/', // ticMall 域名地址
      userMgmLogin: 'https://cnapp-dev.sgs.net/sgsLogin/', //登录入口
      userMgmApiUserInfo: 'https://cnapp-dev.sgs.net/UserManagementApi/resource/querySystemResourceByCode/', //获取用户信息
      userMgmApi: 'https://cnapp-dev.sgs.net/UserManagementApi/login/queryResources', //获取用户权限信息
      userMgmExtLogin: "https://cnapp-dev.sgs.net/UserManagementApi/login/externalLoginIn", // 外网登录
      userInfoApi: 'https://cnapp-dev.sgs.net/UserManagementApi/employee/queryEmployeeInfo/', //获取内网用户emial地址
      userInfoOutApi: 'https://cnapp-dev.sgs.net/UserManagementApi/conpanyEmployee/queryExternalCompanyEmployeeInfo', //获取外网emial地址
      userLogout: 'https://cnapp-dev.sgs.net/UserManagementApi/login/loginOut', //内网用户登出
    },
    test: {
      portal_url: 'https://portaltest.sgsonline.com.cn',
      console_url: 'https://consoletest.sgsonline.com.cn',
      host: 'cmstest.sgsonline.com.cn',
      view_url: 'https://cmstest.sgsonline.com.cn',
      mview_url: 'https://mtest.sgsonline.com.cn',
      solrUrl: 'https://gatetest.sgsonline.com.cn/solrServer/business/api.v1.solr/content',
      userEmailSendApi: 'https://gatetest.sgsonline.com.cn/ticSend/openapi/api.v1.send/SysMailMsgSendAction/sendTemplateMail',
      apiSetCacheApi: 'https://gatetest.sgsonline.com.cn/ticSso/business/api.v1.sso/privilege/refresh',
      apiGetCacheApi: 'https://gatetest.sgsonline.com.cn/ticSso/business/api.v1.sso/privilege/get',
      userManageHost: 'https://cnapp-test.sgs.net/', // user manage 域名地址
      ticMallHost: 'https://gatetest.sgsonline.com.cn/', // ticMall 域名地址
      userMgmLogin: 'https://cnapp-test.sgs/sgsLogin/', //登录入口
      userMgmApiUserInfo: 'https://cnapp-test.sgs/UserManagementApi/resource/querySystemResourceByCode/', //获取用户信息
      userMgmApi: 'https://cnapp-test.sgs/UserManagementApi/login/queryResources', //获取用户权限信息
      userMgmExtLogin: "https://cnapp-test.sgs/UserManagementApi/login/externalLoginIn", // 外网登录
      userInfoApi: 'https://cnapp-test.sgs/UserManagementApi/employee/queryEmployeeInfo/', //获取内网用户emial地址
      userInfoOutApi: 'https://cnapp-test.sgs/UserManagementApi/conpanyEmployee/queryExternalCompanyEmployeeInfo', //获取外网emial地址
      userLogout: 'https://cnapp-test.sgs/UserManagementApi/login/loginOut', //内网用户登出
    },
    local: {
      portal_url: 'https://portaluat.sgsonline.com.cn',
      console_url: 'http://localhost:9015',
      host: 'msuat.sgsonline.com.cn',
      view_url: 'http://cmsuat.sgsonline.com.cn',
      mview_url: 'https://muat.sgsonline.com.cn',
      solrUrl: 'https://gateuat.sgsonline.com.cn/solrServer/business/api.v1.solr/content',
      userEmailSendApi: 'https://gateuat.sgsonline.com.cn/ticSend/openapi/api.v1.send/SysMailMsgSendAction/sendTemplateMail',
      apiSetCacheApi: 'https://gateuat.sgsonline.com.cn/ticSso/business/api.v1.sso/privilege/refresh',
      apiGetCacheApi: 'https://gateuat.sgsonline.com.cn/ticSso/business/api.v1.sso/privilege/get',
      userManageHost: 'https://cnapp-uat.sgs.net/', // user manage 域名地址
      ticMallHost: 'https://gateuat.sgsonline.com.cn/', // ticMall 域名地址
      userMgmLogin: 'https://cnapp-uat.sgs.net/sgsLogin/', //登录入口
      userMgmApiUserInfo: 'https://cnapp-uat.sgs.net/UserManagementApi/resource/querySystemResourceByCode/', //获取用户信息
      userMgmApi: 'https://cnapp-uat.sgs.net/UserManagementApi/login/queryResources', //获取用户权限信息
      userMgmExtLogin: "https://cnapp-uat.sgs.net/UserManagementApi/login/externalLoginIn", // 外网登录
      userInfoApi: 'https://cnapp-uat.sgs.net/UserManagementApi/employee/queryEmployeeInfo/', //获取内网用户emial地址
      userInfoOutApi: 'https://cnapp-uat.sgs.net/UserManagementApi/conpanyEmployee/queryExternalCompanyEmployeeInfo', //获取外网emial地址
      userLogout: 'https://cnapp-uat.sgs.net/UserManagementApi/login/loginOut' //内网用户登出
    },
  }
}

module.exports = {
  siteInfo: siteInfo
}