'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const { env } = require('../../config/info').siteInfo;
const axios = require('axios');
const querystring = require('querystring');
class PassportController extends Controller {
  async login() {
    const {
      ctx,
      app
    } = this;
    const params = ctx.request.body;
    const user_name = params.u,
      password = params.p;

    if (ctx.method == 'GET') {
      await ctx.render('login', {
        site_title: _info.site_title,
        page_title: '登录',
        csrf: ctx.csrf,
        view_url: env[this.app.config.env].view_url,
        mview_url: env[this.app.config.env].mview_url,
        u: '',
        userInfo: {
          www: true
        }
      });
    } else if (ctx.method == 'POST') {
      if (!user_name || !password) {
        ctx.body = {
          code: 0,
          msg: '缺少用户名或密码'
        };
      } else {
        // 用户登录结果
        const loginResult = await ctx.service.admin.login({
          name: user_name,
          pwd: password,
          deleteflag: -1
        });
        // 数据库中无此用户，类admin账户的密码不对
        if (loginResult == null) {
          ctx.body = {
            code: 0,
            msg: '用户名或密码错误'
          };
          // 用户被锁定之后的结果
        } else if (loginResult == -1) {
          ctx.body = {
            code: 0,
            msg: '未授权用户操作'
          };
        } else if (loginResult.type == 1) {
          //本地管理员登录
          ctx.body = {
            code: 1,
            user_name: user_name,
            localUser: "1",
            token: loginResult.pwd
          };
        } else {
          // 输入的账号登录之后再去CS验证密码
          // 输入的账号与AD账号非同一个，需要在CS单独配置申请
          await axios.post(env[this.app.config.env].userMgmExtLogin, querystring.stringify({
            user_name: user_name,
            password: password
          }), {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
            }
          }).then(res => {
            let resText = res.data;
            if (resText && resText.sgsToken) {
              // 密码验证成功之后
              ctx.body = {
                code: 1,
                sgsToken: resText.sgsToken,
                user_name: resText.username
              };
            } else {
              ctx.body = {
                code: 0,
                msg: resText.mes || '用户名或密码错误'
              };
            }
          }).catch(err => {
            ctx.logger.error(new Error('UNCONNECT:\n' + err));
            ctx.body = {
              code: 0,
              msg: err.responseText
            };
          });
        }
      }
    }
  }

  async logout() {
    const {
      ctx,
      app
    } = this;
    // UM退出登录,内网用户需要
    if (ctx.session.userInfo && ctx.session.userInfo.token && ctx.session.userInfo.token.sgsToken) {
      let result = await axios.get(`${env[this.app.config.env].userLogout}/${ctx.session.userInfo.token.sgsToken}`);
    }
    ctx.session.userInfo = null;
    ctx.session = null;
    ctx.redirect('/');
  }

  async check() {
    const {
      ctx
    } = this;
    const userInfo = ctx.session.userInfo || null;
    // 内网登录之后带进去的sgstoken和systemId
    // 外网带进去的sgstoken和userName
    // admin类账号带进去的localUser, token, user_name
    const { sgsToken, systemID, username, localUser, token, user_name } = ctx.request.query;
    let menus = [];
    // 内网登录之后
    if (sgsToken && systemID) {
      // 内网获取登录用户的菜单（获取用户权限信息）
      let menusResult = await axios.post(env[this.app.config.env].userMgmApi, {
        systemName: "TIC CMS",
        sgsToken: Buffer.from(sgsToken, 'base64').toString('ascii')
      });
      for (let item of menusResult.data.menus) {
        if (item.code == 'tic_first_list') {
          for (let code of item.childResources) {
            menus.push(code.code)
          }
          break;
        }
      }
      // 获取用户信息
      // 包含 buttons labs userinfo menus systems等信息
      let result = await axios.get(env[this.app.config.env].userMgmApiUserInfo + '/' + Buffer.from(sgsToken, 'base64').toString('ascii') + '/' + Buffer.from(systemID, 'base64').toString('ascii'));
      debugger
      if (result && result.status === 200) {
        let data = result.data;
        let sysCheck = false;
        data.systems && data.systems.forEach(sys => {
          if (sys.systemName == 'TIC CMS') sysCheck = true;
        });
        if (!sysCheck) {
          ctx.body = {
            code: 0,
            msg: '账号未配置CMS权限。'
          };
          return;
        }
        const loginResult = await ctx.service.admin.getByName(data.userInfo.username);
        // cm配置了菜单，但是在admin没有该用户
        if (!loginResult) {
          ctx.body = {
            code: 0,
            msg: '账号未配置CMS权限。'
          };
          return;
        }
        loginResult.username = data.userInfo.username;
        ctx.session.userInfo = null
        ctx.session.userInfo = loginResult;
        ctx.session.userInfo.systems = data.systems;
        ctx.session.userInfo.token = {
          sgsToken,
          systemID
        }
        debugger
        ctx.redirect('/dashboard');
      }
    } else if (sgsToken && username) {
      // 外网用户登录之后

    } else if (localUser && token && user_name) {
      // 外网的admin类用户登录之后
      const loginResult = await ctx.service.admin.getByName(user_name);
      if (loginResult.pwd == token) {
        ctx.session.userInfo = loginResult;
        ctx.session.userInfo.user_name = user_name;
        ctx.session.userInfo.www = true;
        ctx.redirect('/dashboard');
      }
    }

    // const loginResult = await ctx.service.admin.getByName(username);
    // debugger

    // http://localhost:9001/?sgsToken=MmY4NDY5NTExZDk1NDM4MzllYTNjZTVhNWMzZDlhYTk=&systemID=MGYxZGQ1MjEwOTc0NDY1MDhjYWVhMTY1NjIyZWNmYmE=
    // 内网CS登录
    // if () {}

    // const params = ctx.request.query;
    // let userInfo = ctx.session.userInfo;
    // let oldToken = '';
    // let oldsId = '';

    // userInfo = null;
    // 是否为外网登录
    // const rhost = ctx.request.header['host'];
    // let isExternal = rhost.includes('https://cmsuatmgnt.sgsonline.com.cn') || rhost.includes('https://cmsmgnt.sgsonline.com.cn');
    // let isExternal = false;
    // if (isExternal && !params.sgsToken && !userInfo && !params.localUser) {
    //   ctx.redirect('/login');
    //   return;
    // } else if (isExternal && params.sgsToken) {
    //   ctx.session.userInfo = {
    //     user_name: params.activeUser || '',
    //     www: true
    //   }
    //   // params.sgsToken = Buffer.from(params.sgsToken, 'base64').toString('ascii');
    //   let menusResult = await axios.post(env[this.app.config.env].userMgmApi, {
    //     systemName: "TIC CMS",
    //     sgsToken: params.user_name ? params.sgsToken : Buffer.from(params.sgsToken, 'base64').toString('ascii')
    //   });
    //   let menus = [];
    //   for (let item of menusResult.data.menus) {
    //     if (item.code == 'tic_first_list') {
    //       for (let code of item.childResources) {
    //         menus.push(code.code)
    //       }
    //       break;
    //     }
    //   }

    //   const loginResult = await ctx.service.admin.getByName(params.username);

    //   loginResult.user_name = params.user_name || '';
    //   loginResult.www = true;
    //   ctx.session.userInfo = loginResult;


    //   // var apidata = {};
    //   // apidata.menu = await ctx.service.admin.getMenuPurviewFromDB(params.user_name);

    //   // apidata.userDelete = loginResult.is_delete;

    //   // if (apidata.menu == null) {
    //   // 	apidata.menu = [];
    //   // }

    //   // apidata.serviceData = await ctx.service.admin.getDataPurviewByServiceFromDB(params.user_name);
    //   // apidata.tradeData = await ctx.service.admin.getDataPurviewByTradeFromDB(params.user_name);

    //   // apidata.menu = JSON.stringify(apidata.menu);
    //   // apidata.serviceData = JSON.stringify(apidata.serviceData);
    //   // apidata.tradeData = JSON.stringify(apidata.tradeData);
    //   // apidata.roleid_json = loginResult.roleid_json;
    //   // await ctx.service.admin.setCache(apidata);

    //   // ctx.session.userInfo.menu = menus;
    //   ctx.session.userInfo.user_name = params.user_name;
    //   // ctx.redirect('/dashboard');
    //   const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

    //   async function something(context) {
    //     await delay(2000);
    //     context.redirect('/dashboard');
    //   }

    //   await something(ctx);

    // } else {
    //   if (!userInfo && (!params.sgsToken || !params.systemID) && !params.localUser) {
    //     ctx.redirect(env[this.app.config.env].userMgmLogin);
    //     return;
    //   }
    //   if (userInfo && userInfo.token && userInfo.token.sgsToken) {
    //     oldToken = userInfo.token.sgsToken;
    //   }
    //   if (!userInfo || (userInfo && params.sgsToken && oldToken != params.sgsToken)) {
    //     if (params.localUser == '1') {
    //       const loginResult = await ctx.service.admin.getByName(params.user_name);
    //       if (loginResult.pwd == params.token) {
    //         ctx.session.userInfo = loginResult;
    //         ctx.session.userInfo.user_name = params.user_name;
    //         ctx.session.userInfo.www = true;
    //         // var apidata = {};
    //         // apidata.menu = await ctx.service.admin.getMenuPurviewFromDB(params.user_name);
    //         // if (apidata.menu == null) {
    //         // 	apidata.menu = [];
    //         // }
    //         // apidata.serviceData = await ctx.service.admin.getDataPurviewByServiceFromDB(params.user_name);
    //         // apidata.tradeData = await ctx.service.admin.getDataPurviewByTradeFromDB(params.user_name);
    //         // apidata.userDelete = loginResult.is_delete;
    //         // apidata.roleid_json = loginResult.roleid_json;

    //         // apidata.menu = JSON.stringify(apidata.menu);
    //         // apidata.serviceData = JSON.stringify(apidata.serviceData);
    //         // apidata.tradeData = JSON.stringify(apidata.tradeData);
    //         // await ctx.service.admin.setCache(apidata);
    //         const delay = ms => new Promise(resolve => setTimeout(resolve, ms));
    //         async function something(context) {
    //           await delay(2000);
    //           context.redirect('/dashboard');
    //         }
    //         await something(ctx);
    //       } else {
    //         ctx.body = {
    //           code: 0,
    //           msg: '账号未配置CMS权限。'
    //         };
    //         return;
    //       }
    //     } else {
    //       oldToken = params.sgsToken;
    //       oldsId = params.systemID;
    //       params.sgsToken = Buffer.from(params.sgsToken, 'base64').toString('ascii');
    //       params.systemID = Buffer.from(params.systemID, 'base64').toString('ascii');
    //       //params.systemID=0;
    //       let result = await axios.get(env[this.app.config.env].userMgmApiUserInfo + '/' + params.sgsToken + '/' + params.systemID);
    //       let menusResult = await axios.post(env[this.app.config.env].userMgmApi, {
    //         systemName: "TIC CMS",
    //         sgsToken: params.sgsToken
    //       });
    //       if (result && result.status == '200') {
    //         let data = result.data;
    //         let sysCheck = false;
    //         data.systems && data.systems.forEach(sys => {
    //           if (sys.systemName == 'TIC CMS') {
    //             sysCheck = true;
    //           }
    //         });
    //         if (!sysCheck) {
    //           ctx.body = {
    //             code: 0,
    //             msg: '账号未配置CMS权限。'
    //           };
    //           return;
    //         }
    //         const loginResult = await ctx.service.admin.getByName(result.data.userInfo.username);
    //         // cm配置了菜单，但是在admin没有该用户
    //         if (!loginResult) {
    //           ctx.body = {
    //             code: 0,
    //             msg: '账号未配置CMS权限。'
    //           };
    //           return;
    //         }
    //         loginResult.user_name = params.user_name || '';
    //         ctx.session.userInfo = loginResult;
    //         //ctx.session.userInfo = data.userInfo;
    //         ctx.session.userInfo.systems = data.systems;
    //         for (let item of data.systems) {
    //           item.systemID = Buffer.from(item.systemID).toString('base64');
    //         }
    //         ctx.session.userInfo.token = {
    //           sgsToken: oldToken,
    //           systemID: oldsId
    //         }
    //         let menus = [];
    //         for (let item of menusResult.data.menus) {
    //           if (item.code == 'tic_first_list') {
    //             for (let code of item.childResources) {
    //               menus.push(code.code)
    //             }
    //             break;
    //           }
    //         }

    //         // var apidata = {};
    //         // apidata.menu = await ctx.service.admin.getMenuPurviewFromDB(result.data.userInfo.user_name);
    //         // if (apidata.menu == null) {
    //         // 	apidata.menu = [];
    //         // }


    //         // apidata.serviceData = await ctx.service.admin.getDataPurviewByServiceFromDB(result.data.userInfo.user_name);
    //         // apidata.tradeData = await ctx.service.admin.getDataPurviewByTradeFromDB(result.data.userInfo.user_name);
    //         // apidata.userDelete = loginResult.is_delete;

    //         // apidata.menu = JSON.stringify(apidata.menu);
    //         // apidata.serviceData = JSON.stringify(apidata.serviceData);
    //         // apidata.tradeData = JSON.stringify(apidata.tradeData);
    //         // apidata.roleid_json = loginResult.roleid_json;
    //         // await ctx.service.admin.setCache(apidata);

    //         // ctx.session.userInfo.menu = menus;


    //         const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

    //         async function something(context) {
    //           await delay(2000);
    //           context.redirect('/dashboard');
    //         }

    //         await something(ctx);


    //       } else {
    //         ctx.body = {
    //           code: 0,
    //           msg: '授权错误。'
    //         };
    //       }
    //     }
    //   } else {
    //     ctx.redirect('/dashboard');
    //   }
    // }
  }
}

module.exports = PassportController;