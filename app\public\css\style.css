body,
html {
    height: 100%;
}

body {
    margin: 0;
    padding: 0;
}

a,
.el-table a:hover {
    color: #333;
    text-decoration: none;
}

a:hover,
.el-table a {
    color: #409EFF;
}

.el-header {
    border-bottom: 1px solid #e6e6e6;
    box-shadow: rgba(0, 0, 0, 0.1) 0 3px 8px;
}

.el-container {
    height: 100%;
}

.el-aside {
    height: 100%;
    background-color: #363636;
}

.el-menu {
    height: 100%;
    overflow: auto;
    background-color: #363636;
}

.el-menu-item a {
    display: block;
    color: #949494;
}

.el-submenu__title {
    color: #949494;
}

.el-menu-item.is-active {
    background-color: #515151;
}

.el-menu-item.is-active:before {
    content: '';
    float: left;
    width: 6px;
    height: 100%;
    background-color: #f60;
    margin-left: -40px;
}

.el-menu-item.is-active a {
    color: #fff;
}

.el-row {
    margin-bottom: 20px;
    &:last-child {
        margin-bottom: 0;
    }
}

.el-table th.is-leaf {
    background-color: #f0f2f5;
}

.el-table td.el-table__expanded-cell {
    padding: 0 0 0 50px;
    background-color: #fbfbfb!important;
}

.el-table td.el-table__expanded-cell td,
.el-table td.el-table__expanded-cell .el-table__body-wrapper {
    background-color: #fbfbfb!important;
}

.el-table td.el-table__expanded-cell thead {
    display: none;
}

.el-table td.el-table__expanded-cell td {
    border: 0;
    border-bottom: 1px solid #e6e6e6;
}

.el-table td.el-table__expanded-cell tr>td:first-child>.cell:before {
    content: '└ ';
}


/**/

.cover-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.cover-uploader .el-upload:hover {
    border-color: #409EFF;
}

.cover-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 825px;
    height: 310px;
    line-height: 310px;
    text-align: center;
}

.thumb-uploader .cover-uploader-icon {
    width: 284px;
    height: 142px;
    line-height: 142px;
}

.cover {
    width: 825px;
    height: 310px;
    display: block;
}
.thumb-uploader .cover{
    width: 284px;
    height: 142px;
}

/**/
.spopper{
    width:240px;
}
.contentView *{
    margin: 0;
    padding: 0;
}
hr{
    width: 100%;
    height: 1px;
    border: 0;
    border-top: 1px solid #e6e6e6;
}
textarea.el-input__inner{
    line-height: 24px;
}

.media-uploader {
    cursor: pointer;
    position: relative;
    text-align: center;
    color: #576474;
    background: #e8ebf1;
    padding: 32px;
    font-size: 20px;
    -webkit-font-smoothing: antialiased;
  }
  
  .media-uploader--dragover {
    background-color: #eff1f5;
    background-image: linear-gradient(#eff1f5, #f5f6f9);
    cursor: copy;
  }
  
  .media-uploader .media-uploader__close {
    color: #c2c6cc;
    position: absolute;
    top: 4px;
    right: 8px;
  }
  .media-uploader .media-uploader__close:before {
    content: "✕";
    display: inline-block;
    line-height: 1;
    font-size: 1.25em;
    font-style: normal;
    font-weight: bold;
  }
  
  .media-uploader .media-uploader__type {
    background: #fff;
    color: #c2c6cc;
    margin: -32px;
    margin-bottom: 0;
    padding: 16px;
  }
  
  .media-uploader .media-uploader__urlbox {
    margin-top: 24px;
    margin-bottom: 24px;
  }
  
  .media-uploader .media-uploader__url {
    padding: 8px;
    width: 90%;
    border-style: solid;
    border-radius: 3px;
    font-size: 16px;
  }
  .media-uploader .media-uploader__url.error {
    border-color: #ed3685;
  }
  
  .media-uploader .media-uploader__urlbox p.error {
    font-size: 12px;
    color: #ed3685;
  }
  
  .media-uploader .media-uploader__prompt {
    position: relative;
  }
  
  .media-uploader .media-uploader__prompt label {
    color: #3a9cff;
    cursor: pointer;
  }
  .el-submenu .el-menu-item{
    min-width: 120px;
  }
  
  .logshow-wrapper{
    min-height: 30px;
    position: relative;
    padding-top: 10px;
  }
  .logshow{
    height: 40px;
    overflow: visible;
    /*position: absolute;*/
    width: 100%;
    background-color: #F0F0F0;
    border: 1px dashed #DDD;
    overflow: hidden;
    bottom: 0;
    font-size: 13px;
    min-height: 40px;
    z-index: 99;
    padding: 5px 0;
  }
   .logshow.on{
    height: auto;
   }
  .logshow span.icon{
    float: left;
    width: 20px;
    height: 20px;
  }
  .logshow span.icon i{
    float: left;
    margin-left: 10px;
    margin-top: 5px;
    transition: transform 0.4s;
  }
  .logshow.on span.icon i{
    transform: rotate(180deg);
   }
  .logshow ul{
    padding: 0 10px 0 10px;
    margin: 0;
    height: auto;
    overflow: hidden;
  }
 .logshow ul li{
    list-style: none;
    line-height: 20px;
    padding: 5px 0;
 }
