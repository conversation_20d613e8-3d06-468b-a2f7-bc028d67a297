const Service = require('egg').Service;
const moment = require('moment');

class TemplateService extends Service {
    async tempSkuList(params) {
        const {
            app
        } = this;

        const page = params.page || 1,
            limit = params.limit || 10;
        let qarr = ['is_delete=0', 'type=1']; // 未删除，所属类型为sku
        let qarr1 = ['b.is_delete = 0', 'a.template_id != 0']
        if (params.name) {
            qarr.push('name LIKE "%' + params.name + '%"');
        }
        if (params.title) {
            qarr.push('title LIKE "%' + params.title + '%"');
        }

        let total = '',
            list = [];
        if (!params.new_title) {
            if (qarr.length > 0) {
                qarr = qarr.join(' AND ');
                qarr = ' WHERE ' + qarr;
            } else {
                qarr = '';
            }
            total = await app.mysql.query('SELECT id FROM templates' + qarr);
            list = await app.mysql.query('SELECT * FROM templates' + qarr + ' ORDER BY create_time DESC, id DESC LIMIT ' + (page - 1) * limit + ',' + limit);
        } else {
            if (params.new_title) {
                qarr1.push('b.name LIKE "%' + params.new_title + '%"');
            }
            qarr1 = qarr1.join(' AND ');
            qarr1 = ' WHERE ' + qarr1;
            console.log('SELECT DISTINCT c.id, c.* FROM sku_content a INNER JOIN sku b ON a.sku_id = b.id JOIN templates c ON a.template_id = c.id ' + qarr1 + ' ORDER BY b.gmt_create DESC, id DESC LIMIT ' + (page - 1) * limit + ',' + limit);
            list = await app.mysql.query('SELECT DISTINCT c.id, c.* FROM sku_content a INNER JOIN sku b ON a.sku_id = b.id JOIN templates c ON a.template_id = c.id ' + qarr1 + ' ORDER BY b.gmt_create DESC, id DESC LIMIT ' + (page - 1) * limit + ',' + limit);
        }

        for (let item of list) {
            // 使用forEach格式化之后的时间全部为当前时间 todo
            item.create_time = moment(item.create_time).format('YYYY-MM-DD HH:mm:ss');

            // 模板被使用的文章
            const list = await app.mysql.query(`select DISTINCT b.id,
                a.title as template_title, b.type, b.name
                from sku_content a
                INNER JOIN sku b
                ON a.sku_id = b.id
                WHERE b.is_delete = 0 AND a.template_id = ${item.id}`);
            item.relevance_case = list
        }
        
        return {
            success: true,
            list,
            total: total.length
        };
    }

    // async tempRelevanceQry(params) {
    //     const {
    //         app
    //     } = this;


    // }

    async tempSkuAdd(params) {
        const {
            app
        } = this;

        const row = {
            name: params.name,
            title: params.title,
            content: params.content,
            type: params.type || 1,
            create_time: moment().format('YYYY-MM-DD HH:mm:ss')
        }
        const list = await app.mysql.query(`SELECT id FROM templates WHERE name='${params.name}'`);
        if (list.length && list[0].id == params.id || !list.length) {
            const result = await app.mysql.insert('templates', row);
            return {
                success: result.affectedRows === 1
            };
        } else {
            return {
                success: false,
                msg: '模板名称不能重复'
            };
        }
    }

    async tempSkuDel(params) {
        const {
            app
        } = this;

        const {
            id
        } = params;
        // 物理刪除
        // const result = await app.mysql.delete('templates', {
        //     id
        // });
        // 設置is_delete為1
        const row = {
            id,
            is_delete: 1,
            modify_time: await moment().format('YYYY-MM-DD HH:mm:ss')
        }
        const result = await app.mysql.update('templates', row);
        return {
            success: result.affectedRows === 1
        };
    }

    async tempSkuMod(params) {
        const {
            app
        } = this;

        const row = {
            id: params.id,
            name: params.name,
            title: params.title,
            content: params.content,
            type: params.type || 1,
            modify_time: await moment().format('YYYY-MM-DD HH:mm:ss')
        }
        const list = await app.mysql.query(`SELECT id FROM templates WHERE name='${params.name}'`);
        if (list.length && list[0].id == params.id || !list.length) {
            const result = await app.mysql.update('templates', row);
            return {
                success: result.affectedRows === 1
            };
        } else {
            return {
                success: false,
                msg: '模板名称不能重复'
            };
        }
    }

    async tempSkuDtl(params) {
        const {
            app
        } = this;

        const {
            id
        } = params;
        const detail = await app.mysql.select('templates', {
            columns: ['id', 'name', 'title', 'content'],
            where: {
                id
            },
        });

        return {
            success: true,
            detail: detail[0]
        };
    }
}
module.exports = TemplateService;