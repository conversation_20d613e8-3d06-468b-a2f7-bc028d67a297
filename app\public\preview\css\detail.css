html,body{
    margin: 0;padding: 0;
}

*{
    font-family: Microsoft Yahei;
}
.detailNav{
    border-bottom: 1px solid #eeeeee;
    margin-bottom: 21px;
}
.detailBox{
    width: 1226px;height: auto;
    margin: 28px auto 52px;
    position: relative;
}
.detailbanner{
    width: 825px;
    height: 310px;
    background: #ccc;
    /*margin-left: 88px;*/
    float: left;
    /*background: url(../images/detail1.jpg) no-repeat 100% 100%;*/
    /*background-size: contain;*/
    overflow: hidden;
}
.bannerInfo{
    width: 342px;height: auto;
    float: right;
    /*overflow: hidden;*/
}
.detailbannerBox{
    width: 100%;
    height: 340px;
    margin-bottom: 10px;
}
.word1{
    font-size: 21px;
    color: #000000;
    margin-top: 17px;
}
.word2{
    font-size: 14px;
    color: #999999;
    margin-top: 17px;
}
.word3{
    font-size: 14px;
    color: #666666;
    margin-top: 21px;
    line-height: 1.7;
    margin-bottom: 31px;
    min-height: 89px;
}
.word5-1{
    width: 0;height: 100%;
    position: absolute;
    background: rgb(254,102,3);
    left: 0;top: 0;
}
.word5-2{
     width: 100%;height: 100%;
     position: absolute;
}
.word5:hover .word5-1{
    width: 100%;
    transition: .4s;
}
.word5:hover .word5-2{
    color: white;
}
.word4{
    font-size: 14px;
    color: #666666;
    /*margin-top: 31px;*/
}
.word5{
    width: 222px;
    height: 46px;
    /*background-color: #fe6603;*/
    border: 1px solid #fe6603;
    text-align: center;
    line-height: 48px;
    margin-top: 16px;
    cursor: pointer;
    color: #fe6603;
    position: relative;
}
.serverConBox{
    width: 825px;
    margin-bottom: 57px;
}
.serverTitleBox{
    width: 100%;
    height: 31px;
    border-bottom: 3px solid #f2f2f2;
}
.serverCon{
    width: 74px;
    height: 100%;
    border-bottom: 3px solid #fe6603;
    font-size: 16px;
    font-weight: bold;
    color: #333333;
}
.server-word1{
    font-size: 14px;
    margin-top: 20px;
    line-height: 2;
}
.lei{
    margin-top: 12px;
    margin-bottom: 12px;
}
.leiUl{

}
.leiLi{
    margin-bottom: 8px;
    font-size: 14px;
    list-style: initial;
    margin-left: 16px;
}
.server-word2{
    font-size: 14px;
    color: #000000;
    margin-bottom: 12px;
    line-height: 2;
}
.server-word3{
    font-size: 14px;
    color: #000000;
    margin-bottom: 12px;
    line-height: 2;

}
.questionBox{
    width: 824.5px;
    border-radius: 4px;
    background-color: #f8f8f8;
    margin-top: 25px;
    position: relative;
}
.question-icon{
    width: 26px;height: 26px;
    position: absolute;
    top: 93px;left: 28px;
    background: url(../images/da.png) no-repeat 100% 100%;
    background-size: contain;
}
.question-word1{
    width: 760px;
    margin-left: 31px;
    margin-right: 32px;
    line-height: 67px;
    border-bottom: 1px solid #e0e0e0;
    font-weight: bold;
    font-size: 14px;
}
.question-word2{
    margin-left: 72px;
    margin-right: 32px;
    line-height: 78px;
    color: #333333;
    font-size: 14px;
    padding-top: 10px;
}
.liucheng{
    width: auto;
    margin-top: 24px;
    height: 65px;
}
.liuchengUl{
    width: auto;height: auto;
}
.liuchengLi{
    width: 120px;
    height: 65px;
    border-radius: 4px;
    background-color: #f2f3f5;
    text-align: center;
    line-height: 65px;
    float: left;
    margin-right: 53px;
    color: #333333;
    list-style: none;
    /*cursor: pointer;*/
}
.liuchengLi:last-child{
    margin-right: 0;
}
.recoment{
    width: 824.5px;
    height: 299.5px;
    border-radius: 4px;
    background-color: #f8f8f8;
    /*margin-left: 88px;*/
    position: relative;
}
.square{
    width: 4px;height: 21px;
    position: absolute;
    mix-blend-mode: multiply;
    background-color: #f2f2f2;
    left: 0;top: 18px;
}
.recoment-word1{
    width: auto;
    font-size: 16px;
    color: #333333;
    font-weight: bold;
    position: absolute;
    top: 17px;left: 15px;
    color: #333333;
}
.recoment-word2{
    font-size: 14px;
    color: #333333;
    position: absolute;
    right: 59px;
    top: 22px;
    cursor: pointer;
}
.recomentUl{
    width: auto;
    height: 200px;
    position: absolute;
    top: 63px;left: 20px;
    margin: 0;
    padding: 0;
}
.recomentLi{
    width: 238px;
    height: 199.5px;
    background-color: #ffffff;
    margin-right: 32px;
    float: left;
    list-style: none;
}
.recomentLi:last-child{
    margin-right: 0;
}
.recomentLi-img{
    width: 100%;
    height: 117px;
    margin-bottom: 20px;
    background: #ccc;
    cursor: pointer;
}
.recomentLi-img img{
    width: 100%;
    height: 100%;
}
.recomentLi-word{
    width: 100%;
    text-align: center;
    color: #000000;
    font-size: 16px;
}
.recomentLi:hover{
    box-shadow: 0 15px 30px rgba(0,0,0,0.1);
    -webkit-transform: translate3d(0, -2px, 0);
    transform: translate3d(0, -2px, 0);
    transition: .4s;
}
.recoment-icon{
    width: 16px;height: 16px;
    position: absolute;
    right: 30px;
    top: 24px;
    background: url(../images/huanyipi.jpg) no-repeat 100% 100%;
    background-size: contain;
}
.detailLoca{
    width: 100%;
    margin: 100px auto 0;
    padding: 20px 0 0;
    border-top: 1px solid #eeeeee;
}
.detailLoca ul{
    width: 1226px;
    height: 100%;
    margin: 0 auto;
}
.ngsFix{
    position: fixed;
    top: 100px;
    z-index: 20;
}
.ngsAbsolute{
    position: absolute;
    bottom: 0;
}
.word-day{
    width: 100%;
    height: auto;
    margin-top: 10px;
    overflow: hidden;
}
.word-day-word{
    line-height: 16px;
    float: left;
    color: #999999;
    font-size: 14px;
    width: 100%;
    overflow: hidden;
    padding: 2px 0;
}
.word-day-icon{
    width: 12px;
    height: 15px;
    margin-right: 4px;
    float: left;
    background: url(../images/day.png) no-repeat;
    background-size: contain;
}
.liuchengLilast{
    margin-right: 0;
}
.serverRight0{
    margin-right: 0;
}