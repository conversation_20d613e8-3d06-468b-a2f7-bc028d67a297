'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const nodeExcel = require('excel-export');
const fs = require('fs'),
  path = require('path'),
  moment = require('moment');
const { env } = require('../../config/info').siteInfo;

class RecordController extends Controller {
  async listview() {
    const {
      ctx
    } = this;

    // const list = await ctx.service.record.list();

    await ctx.render('record_list', {
      site_title: _info.site_title,
      page_title: '400电话点击记录',
      active: '10-3',
      csrf: ctx.csrf,
      userInfo: ctx.session.userInfo,
    });
  }

  async list() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const list = await ctx.service.record.list(params);

    ctx.body = {
      success: true,
      data: list
    };
  }

  async export() {
    const {
      ctx,
      app
    } = this;
    const params = ctx.request.body;
    params.getype = 'export';
    const list = await ctx.service.record.list(params);

    const conf = {};
    conf.cols = [{
      caption: 'IP',
      type: 'string',
      width: 100
    },
    {
      caption: '时间',
      type: 'string',
      width: 100

    }, {
      caption: 'URL',
      type: 'string',
      width: 500
    }
    ];

    let temp = [];
    for (let item of list.list) {
      var buffer = [item.ip, moment(item.create_time).format('YYYY/MM/DD HH:mm:ss'), item.url];
      temp.push(buffer);
    }
    conf.rows = temp;

    let result = nodeExcel.execute(conf);
    let uploadDir = path.join(app.baseDir, 'app/public/upload/excel');
    // let dtime = (new Date()).getTime();
    // let filename = '400点击分析';
    let filename = '';
    if (params.date) {
      let fdate = params.date;
      fdate[0] = moment(fdate[0]).format('YYMMDDHHmmss');
      fdate[1] = moment(fdate[1]).format('YYMMDDHHmmss');
      filename = fdate.join('-');
    } else {
      filename = moment(new Date()).format('YYMMDDHHmmss');
    }
    try {
      fs.mkdirSync(uploadDir);
    } catch (e) {
    }
    filename = '400点击分析' + filename;
    let filePath = path.join(uploadDir, filename + ".xlsx"); //文件名
    const exp = fs.writeFileSync(filePath, result, 'binary');

    ctx.body = {
      success: true,
      data: '/static/upload/excel/' + filename + '.xlsx'
    };
  }

  async case() {
    const {
      ctx
    } = this;

    await ctx.render('record_case', {
      site_title: _info.site_title,
      page_title: '资讯中心统计记录',
      active: '10-5',
      csrf: ctx.csrf,
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      userInfo: ctx.session.userInfo,
    });
  }

  async file() {
    const {
      ctx
    } = this;

    await ctx.render('record_file', {
      site_title: _info.site_title,
      page_title: '资源下载统计记录',
      active: '10-6',
      csrf: ctx.csrf,
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      userInfo: ctx.session.userInfo,
    });
  }

  async sku() {
    const {
      ctx
    } = this;

    await ctx.render('record_sku', {
      site_title: _info.site_title,
      page_title: 'SKU统计记录',
      active: '10-7',
      csrf: ctx.csrf,
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      userInfo: ctx.session.userInfo,
    });
  }
  async doccheck() {
    const {
      ctx
    } = this;

    await ctx.render('record_doccheck', {
      site_title: _info.site_title,
      page_title: '报告验证统计',
      active: '10-8',
      csrf: ctx.csrf,
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      userInfo: ctx.session.userInfo,
    });
  }
  async news() {
    const {
      ctx
    } = this;

    await ctx.render('record_news', {
      site_title: _info.site_title,
      page_title: '新闻中心统计',
      active: '10-9',
      csrf: ctx.csrf,
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      userInfo: ctx.session.userInfo,
    });
  }
}

module.exports = RecordController;