'use strict';

const Service = require('egg').Service;
const moment = require('moment');
const request = require('request');
const crypto = require('crypto');
const _siteInfo = require('../../config/info').siteInfo;
const solrUrl = _siteInfo.solrUrl,
	solrPstr = _siteInfo.solrPstr,
	solrPid = _siteInfo.solrPid;

class CheckflowService extends Service {

	async getList(params) {
		if (!params) {
			params = {};
		}

		const {
			app
		} = this;
		let query = ['checkflow.is_overdue=0'];
		let query2 = ['checkflow.is_overdue=0'];
		//状态，送审分组，类型，服务，行业，名称，送审时间，

		if (params && params.tran_status) {
			query.push('checkflow.tran_status=' + params.tran_status);
		}

		if (params && params.fixed_receive_groupid) {
			if (params.fixed_receive_groupid.length > 0) {

				query.push('checkflow.receive_groupid in (' + params.fixed_receive_groupid.join(',') + ')');
				query2.push('checkflow.receive_groupid in (' + params.fixed_receive_groupid.join(',') + ')');
			} else {
				query.push('checkflow.receive_groupid in (0)');
				query2.push('checkflow.receive_groupid in (0)');
			}
		}


		if (params && params.receive_groupid) {
			query.push('checkflow.receive_groupid=' + params.receive_groupid);
			query2.push('checkflow.receive_groupid=' + params.receive_groupid);
		}

		if (params && params.data_type) {
			query.push('checkflow.data_type=\'' + params.data_type + '\'');
			query2.push('checkflow.data_type=\'' + params.data_type + '\'');
		}

		if (params && params.send_admin_id) {
			query.push('checkflow.send_admin_id=' + params.send_admin_id);
			query2.push('checkflow.send_admin_id=' + params.send_admin_id);
		}

		if (params && params.send_admin_groupid) {
			query.push(" replace(replace(admin.roleid_json,'[',','),']',',') like '%," + params.send_admin_groupid + ",%' ");
			query2.push(" replace(replace(admin.roleid_json,'[',','),']',',') like '%," + params.send_admin_groupid + ",%' ");
		}

		if (params.allowTrade == 0 && this.ctx.session.userInfo.type == 2) {
			query.push('1=2');
			query2.push('1=2');
		}

		if (params.allowService == 0 && this.ctx.session.userInfo.type == 2) {
			query.push('1=2');
			query2.push('1=2');
		}


		if (params && params.trade) {
			query.push("replace(replace(checkflow.trade_json,'[',','),']',',') like '%," + params.trade + ",%'");
			query2.push("replace(replace(checkflow.trade_json,'[',','),']',',') like '%," + params.trade + ",%'");
		}

		if (params && params.service) {
			query.push("replace(replace(checkflow.service_json,'[',','),']',',') like '%," + params.service + ",%'");
			query2.push("replace(replace(checkflow.service_json,'[',','),']',',') like '%," + params.service + ",%'");
		}


		if (params && params.data_name) {
			query.push('checkflow.data_name LIKE "%' + params.data_name + '%"');
			query2.push('checkflow.data_name LIKE "%' + params.data_name + '%"');
		}

		if (params && params.send_admin_time) {
			query.push('checkflow.send_admin_time >= \'' + params.send_admin_time[0] + ' 00:00:00' +  '\' and checkflow.send_admin_time <\'' + params.send_admin_time[1] + ' 23:59:59' + '\'');
			query2.push('checkflow.send_admin_time >= \'' + params.send_admin_time[0] + ' 00:00:00' + '\' and checkflow.send_admin_time <\'' + params.send_admin_time[1] + ' 23:59:59' + '\'');
		}

		let page = params.page || 1,
			limit = params.limit || 10;
		let start = (page - 1) * limit;

		//id，名称，类型，链接地址，关联服务，关联行业，状态，送审用户，送审时间，审批时间
		var totalsql = 'SELECT distinct checkflow.id from checkflow checkflow join admin on checkflow.send_admin_id=admin.id ';




		let list = null;

		if (params && params.send_admin_id) //如果是送审的话，需要按照送审失败倒排，然后在按照送审倒排
		{
			var mysql = 'SELECT checkflow.id,checkflow.data_type,checkflow.data_id,checkflow.data_name,checkflow.send_admin_time,checkflow.tran_status,checkflow.tran_admin_time,checkflow.trade_json,checkflow.service_json,admin.name as send_admin_name,admin.roleid_json,checkflow.tran_note,checkadmin.name as tran_admin_name,role.name as receive_groupid_name from checkflow checkflow join admin admin on checkflow.send_admin_id=admin.id join role on checkflow.receive_groupid=role.id left join admin checkadmin on checkflow.tran_admin_id = checkadmin.id';

			if (params && params.allowTrade) {
				mysql += " left join (select checkflow_id as id from checkflow_catalog  where type_id=1 and  catalog_id>0 and catalog_id not in (" + params.allowTrade.join(',') + ")) tmptrade on checkflow.id=tmptrade.id";

				totalsql += " left join (select checkflow_id as id from checkflow_catalog  where type_id=1 and  catalog_id>0 and catalog_id not in (" + params.allowTrade.join(',') + ")) tmptrade on checkflow.id=tmptrade.id";

				query.push('tmptrade.id is null');
				query2.push('tmptrade.id is null');

			}

			if (params && params.allowService) {

				mysql += " left join (select checkflow_id as id from checkflow_catalog  where type_id=2 and  catalog_id>0 and catalog_id not in (" + params.allowService.join(',') + ")) tmpservice on checkflow.id=tmpservice.id";

				totalsql += " left join (select checkflow_id as id from checkflow_catalog  where type_id=2 and  catalog_id>0 and catalog_id not in (" + params.allowService.join(',') + ")) tmpservice on checkflow.id=tmptrade.id";

				query.push('tmpservice.id is null');
				query2.push('tmpservice.id is null');
			}


			list = await app.mysql.query(mysql + ' where ' + query.join(' AND ') + ' order by case when checkflow.tran_status=20 then checkflow.tran_admin_time else null end desc,checkflow.id desc LIMIT ' + start + ',' + limit);


		} else {
			var mysql = 'SELECT checkflow.id,checkflow.data_type,checkflow.data_id,checkflow.data_name,checkflow.send_admin_time,checkflow.tran_status,checkflow.tran_admin_time,checkflow.trade_json,checkflow.service_json,admin.name as send_admin_name,admin.roleid_json,checkflow.tran_note,checkadmin.name as tran_admin_name,role.name as receive_groupid_name from checkflow checkflow join admin admin on checkflow.send_admin_id=admin.id join role on checkflow.receive_groupid=role.id left join admin checkadmin on checkflow.tran_admin_id = checkadmin.id';

			if (params && params.allowTrade) {
				mysql += " left join (select checkflow_id as id from checkflow_catalog  where type_id=1 and  catalog_id>0 and catalog_id not in (" + params.allowTrade.join(',') + ")) tmptrade on checkflow.id=tmptrade.id";

				totalsql += " left join (select checkflow_id as  id from checkflow_catalog  where type_id=1 and  catalog_id>0 and catalog_id not in (" + params.allowTrade.join(',') + ")) tmptrade on checkflow.id=tmptrade.id";

				query.push('tmptrade.id is null');
				query2.push('tmptrade.id is null');

			}

			if (params && params.allowService) {

				mysql += " left join (select checkflow_id as  id from checkflow_catalog  where type_id=2 and  catalog_id>0 and catalog_id not in (" + params.allowService.join(',') + ")) tmpservice on checkflow.id=tmpservice.id";

				totalsql += " left join (select checkflow_id as  id from checkflow_catalog  where type_id=2 and  catalog_id>0 and catalog_id not in (" + params.allowService.join(',') + ")) tmpservice on checkflow.id=tmptrade.id";

				query.push('tmpservice.id is null');
				query2.push('tmpservice.id is null');
			}




			list = await app.mysql.query(mysql + ' where ' + query.join(' AND ') + ' order by checkflow.id asc LIMIT ' + start + ',' + limit);
		}

		for (let item of list) {

			if (item.roleid_json == null) {
				item.roleList = [];
			} else {
				let roleJsonObj = JSON.parse(item.roleid_json);
				if (roleJsonObj.length == 0) {
					item.roleList = [];
					continue;
				}
				let role_ids = [];
				for (var i = 0; i < roleJsonObj.length; i++) {
					role_ids.push(roleJsonObj[i]);
				}

				let roleList = await this.app.mysql.select('role', {
					where: {
						id: role_ids
					}
				});
				item.roleList = [];

				for (var i = 0; i < roleList.length; i++) {

					var roleItem = roleList[i];
					var roleObj = {
						"id": roleItem.id,
						"name": roleItem.name,
						"is_effect": roleItem.is_effect,
						"role_gmt_create": moment(roleItem.gmt_create).format('YYYY-MM-DD HH:mm:ss')
					};

					item.roleList.push(roleObj);
				}

				if (item.data_type == 'res') {
					//getPath
					const resDetail = await app.mysql.get('resource', {
						id: item.data_id
					});
					item.resourceUrl = resDetail.path;
				}
			}

			let trade_json = JSON.parse(item.trade_json);
			let service_json = JSON.parse(item.service_json);

			let trade_arr = [];
			let service_arr = [];

			for (var trande_json_item of trade_json) {
				trade_arr.push(trande_json_item);
			}

			for (var service_json_item of service_json) {
				service_arr.push(service_json_item);
			}

			if (trade_arr.length > 0) {
				let tradeData = await this.app.mysql.select('catalog', {
					where: {
						id: trade_arr
					}
				});
				let tradeList = [];
				for (let tradeListIndex of tradeData) {
					tradeList.push(tradeListIndex.name);
				}
				item.tradeList = tradeList;
			} else {
				item.tradeList = [];
			}
			if (service_arr.length > 0) {
				let serviceData = await this.app.mysql.select('catalog', {
					where: {
						id: service_arr
					}
				});
				let serviceList = [];

				for (let serviceDataIndex of serviceData) {
					serviceList.push(serviceDataIndex.name);
				}

				item.serviceList = serviceList;
			} else {
				item.serviceList = [];
			}

		}
		const total = await app.mysql.query(totalsql + ' WHERE ' + query.join(' AND '));


		var result = {
			list: list,
			total: total.length,
			page: page,
			limit: limit
		};


		if (params.needWaitCheckedNum == 1) {
			var ttsql = 'SELECT distinct checkflow.id from checkflow checkflow join admin on checkflow.send_admin_id=admin.id';
			ttsql += " left join (select checkflow_id as id from checkflow_catalog  where type_id=2 and  catalog_id>0 and catalog_id not in (" + params.allowService.join(',') + ")) tmpservice on checkflow.id=tmpservice.id";

			ttsql += " left join (select checkflow_id as id from checkflow_catalog  where type_id=1 and  catalog_id>0 and catalog_id not in (" + params.allowTrade.join(',') + ")) tmptrade on checkflow.id=tmptrade.id";

			ttsql += " WHERE checkflow.tran_status=0 and " + query2.join(' AND ');


			let waitCheckedNumTotal = await app.mysql.query(ttsql);
			result.waitCheckedNumTotal = waitCheckedNumTotal.length;
		}

		if (params.needFailedNum == 1) {
			var ttsql = 'SELECT distinct checkflow.id from checkflow checkflow join admin on checkflow.send_admin_id=admin.id';

			ttsql += " left join (select checkflow_id as id from checkflow_catalog  where type_id=2 and  catalog_id>0 and catalog_id not in (" + params.allowService.join(',') + ")) tmpservice on checkflow.id=tmpservice.id";

			ttsql += " left join (select checkflow_id as id from checkflow_catalog  where type_id=1 and  catalog_id>0 and catalog_id not in (" + params.allowTrade.join(',') + ")) tmptrade on checkflow.id=tmptrade.id";

			ttsql += " WHERE checkflow.tran_status=20 and " + query2.join(' AND ');

			let failedCheckedNumTotal = await app.mysql.query(ttsql);

			result.failedCheckedNumTotal = failedCheckedNumTotal.length;
		}

		return result;
	}


	async checkflowAdd(params) {
		const {
			app
		} = this;

		const gmt_create = await moment(new Date()).format('YYYY-MM-DD HH:mm:ss');

		var admin_create_id = this.ctx.session.userInfo.id;


		//需要有类型，数据id，消息标题，，接收的组，发送人，发送时间
		//然后根据数据id获取行业jsonid和服务jsonid，用于查询

		//消息标题用于记录日志

		//修改需要把同一条数据之前的状态全部设置

		const row = {
			data_type: params.data_type,
			data_id: params.data_id,
			data_name: params.data_name,
			receive_groupid: params.receive_groupid,
			send_admin_id: admin_create_id,
			send_admin_time: gmt_create,
			tran_status: 0, //0:等待处理
			is_overdue: 0
		}

		let sql = 'select * from catalog_relation ';
		if (params.data_type == 'news') //各种扩展如sku
		{
			sql = sql + ' where news_id=' + params.data_id;
		} else if (params.data_type == 'sku_service') //扩展
		{
			sql = sql + ' where sku_id=' + params.data_id;
		} else if (params.data_type == 'sku_solution') //扩展
		{
			sql = sql + ' where sku_id=' + params.data_id;
		} else if (params.data_type == 'case') {
			sql = sql + ' where case_id=' + params.data_id;
		} else if (params.data_type == 'res') {
			sql = sql + ' where resource_id=' + params.data_id;
		}


		const relationList = await app.mysql.query(sql);

		let trade = [];
		let service = []
		for (let relationIndex in relationList) {
			let relationItem = relationList[relationIndex];

			if (params.data_type == 'sku_service') //sku服务
			{
				const detail = await app.mysql.get('catalog', {
					id: relationItem.catalog_id
				});

				relationItem.catalog_id = detail.parent_id;

				if (relationItem.catalog_type == 1) {
					if (trade.indexOf(relationItem.catalog_id) == -1) {
						trade.push(relationItem.catalog_id);
					}
				} else if (relationItem.catalog_type == 2) {
					if (service.indexOf(relationItem.catalog_id) == -1) {
						service.push(relationItem.catalog_id);
					}
				}
			} else //新闻 sku解决方案 案例 资源
			{
				let relationItem = relationList[relationIndex];
				if (relationItem.catalog_type == 1) {
					trade.push(relationItem.catalog_id);
				} else if (relationItem.catalog_type == 2) {
					service.push(relationItem.catalog_id);
				}
			}
		}


		row.trade_json = JSON.stringify(trade);
		row.service_json = JSON.stringify(service);


		let result = '',
			sid = 0;
		result = await app.mysql.beginTransactionScope(async conn => {

			await app.mysql.query('update checkflow set is_overdue=1 where data_id=' + params.data_id + ' and data_type=\'' + params.data_type + '\'');


			const insertResult = await conn.insert('checkflow', row);
			sid = insertResult.insertId;

			for (var trade_Id in trade) {
				await conn.insert('checkflow_catalog', {
					checkflow_id: sid,
					catalog_id: trade[trade_Id],
					type_id: 1
				});
			}

			for (var service_Id in service) {
				await conn.insert('checkflow_catalog', {
					checkflow_id: sid,
					catalog_id: service[service_Id],
					type_id: 2
				});
			}

			await conn.insert('logs', {
				user_name: this.ctx.session.userInfo.name,
				checkflow_id: sid,
				log: '创建' + row.data_name,
				model: '审核',
				name: row.data_name
			});

			return {
				success: true
			};
		}, this.ctx);

		//send邮件


		const list = await app.mysql.query("SELECT id,name from admin  where is_delete=0 and type=2 and  FIND_IN_SET('" + params.receive_groupid + "',replace(replace(roleid_json,'[',''),']',''))");




		for (let item of list) {
			var email = await this.service.admin.getEmail(item.name);

			if (email) {
				var p = {};
				p.title = params.data_name;
				p.email = email;
				// p.typeName = params.type_name;
				// p.serviceName = params.service_name;
				// p.tradeName = trade_name;
				// p.tranStatus = params.tran_status;
				// p.tranName = params.tran_name;
				// p.groupName = params.group_name;
				// p.sendTime = gmt_create;
				// p.tranAdminTime = params.tran_admin_time;
				// p.refundInfo = params.refund_info
				this.service.admin.sendEmail(p);
			}
		}


		return {
			success: result.success,
			id: sid
		};
	}


	async checkflowOperator(params) {
		const {
			app
		} = this;

		const gmt_create = await moment(new Date()).format('YYYY-MM-DD HH:mm:ss');

		const datarow = await app.mysql.get('checkflow', {
			id: params.id
		});

		if (!datarow || datarow.tran_status != 0) {
			return {
				failed: true,
				msg: '数据已被操作',
				loginUrl: '/login'
			};
		}

		const result = await app.mysql.beginTransactionScope(async conn => {
			await conn.update('checkflow', {
				id: params.id,
				tran_status: params.tran_status,
				tran_admin_id: this.ctx.session.userInfo.id,
				tran_admin_time: gmt_create,
				tran_note: params.tran_note
			});

			if (params.tran_status == 10) //如果是审核操作成功
			{
				if (params.currentType == '新闻') {
					await conn.update('news', {
						id: params.data_id,
						is_publish: 1,
            // gmt_modify :moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
					});
				} else if (params.currentType == 'SKU服务') {
					await conn.update('sku', {
						id: params.data_id,
						is_use: 1,
            // gmt_modify :moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
					});
				} else if (params.currentType == 'SKU解决方案') {
					await conn.update('sku', {
						id: params.data_id,
						is_use: 1,
            // gmt_modify :moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
					});
				} else if (params.currentType == '案例') {
					await conn.update('cases', {
						id: params.data_id,
						is_publish: 1,
            // gmt_modify :moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
					});
				} else if (params.currentType == '资源') {
					await conn.update('resource', {
						id: params.data_id,
						is_publish: 1,
            // gmt_modify :moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
					});
				}
			}

			await conn.insert('logs', {
				user_name: this.ctx.session.userInfo.name,
				checkflow_id: params.id,
				log: '审核' + params.name,
				model: '审核资料',
				name: params.name
			});


			return {
				success: true
			};
		}, this.ctx);

		if (params.tran_status == 20) //失败
		{
			var checkFlowData = await app.mysql.get('checkflow', {
				id: params.id
			});
			var userInfo = await app.mysql.get('admin', {
				id: checkFlowData.send_admin_id
			});
			var email = await this.service.admin.getEmail(userInfo.name);

			if (email) {
				var p = {};
				p.title = params.name;
				p.email = email;
				// p.typeName = params.type_name;
				// p.serviceName = params.service_name;
				// p.tradeName = trade_name;
				// p.tranStatus = params.tran_status_name;
				// p.tranName = params.tran_name;
				// p.groupName = params.group_name;
				// p.sendTime = params.send_admin_time;
				// p.tranAdminTime = gmt_create;
				// p.refundInfo = params.tran_note
				this.service.admin.sendFailedEmail(p);
			}
		}


		return {
			success: result.success
		};
	}

	async getDetail(data_type, data_id) {
		const {
			app
		} = this;

		let sql = 'select checkflow.id,checkflow.data_id,checkflow.tran_status,checkflow.tran_admin_time,admin.`name` from checkflow left join admin on checkflow.tran_admin_id=admin.id where checkflow.is_overdue=0 and checkflow.data_type=\'' + data_type + '\' and checkflow.data_id=' + data_id;
		const result = await app.mysql.query(sql);

		if (result && result.length > 0) {
			let item = result[0];
			if (item.name == null) {
				item.name = '';
			}
			if (item.tran_admin_time == null) {
				item.tran_admin_time = '';
			} else {
				item.tran_admin_time = moment(item.tran_admin_time).format('YYYY-MM-DD HH:mm:ss');
			}

			return result[0];
		}

		return null;
	}

	//检查管理员是否能删除
	async checkAdminCanDelete(admin_ids) {
		const {
			app
		} = this;

		let sql = 'select 1 from checkflow where send_admin_id in (' + admin_ids.join(',') + ') and tran_status in (0) and is_overdue=0';

		const result = await app.mysql.query(sql);

		if (result && result.length > 0) {
			return 0;
		}

		return 1;
	}

	//检查管理员是否能删除
	async checkRoleCanDelete(groupIds) {
		const {
			app
		} = this;

		let sql = 'select 1 from checkflow where receive_groupid in (' + groupIds.join(',') + ') and tran_status in (0) and is_overdue=0';

		const result = await app.mysql.query(sql);

		if (result && result.length > 0) {
			return 0;
		}

		return 1;
	}

}

module.exports = CheckflowService;
