'use strict';

const Service = require('egg').Service;
const moment = require('moment');

class HotWordService extends Service {
  // 列表
  async getList(params) {
    const { app } = this;
    const list = await app.mysql.select('hot_word')
    return { list };
  }

  // 新增
  async insert(params) {
    const { app } = this
    const result = await app.mysql.insert('hot_word', {
      name: params.name,
      is_light: params.is_light,
      create_time: params.create_time
      // id: params.id
    })
    const insertSuccess = result.affectedRows === 1;
    return { insertSuccess };
  }

  // 编辑
  async edit(params) {
    const { app } = this
    const row = {
      id: params.id,
      is_light: params.is_light,
      name: params.name
    }
    const result = await app.mysql.update('hot_word', row) // 更新 posts 表中的记录
    const updateSuccess = result.affectedRows === 1;
    return { updateSuccess };
  }

  async del(params) {
    const { app } = this

    const result = await app.mysql.delete('hot_word', {
      id: params.id
    }) // 更新 posts 表中的记录
    const delSuccess = result.affectedRows === 1;
    return { delSuccess };
  }
}

module.exports = HotWordService;