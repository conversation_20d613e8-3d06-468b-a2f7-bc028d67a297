'use strict';

const Service = require('egg').Service
const moment = require('moment');

class IndexService extends Service {
  async getSku(id, alias) {
    const {
      app
    } = this;

    const result = await app.mysql.query('SELECT * FROM sku WHERE id=' + id + ' AND alias="' + alias + '" AND is_delete=0 AND is_use=1');

    if (!result || result.length == 0) {
      return {
        success: false,
        msg: 'SKU不存在或未上架'
      }
    } else {
      return {
        success: true,
        data: result[0]
      };
    }
  }

  async getJf(id) {
    const {
      app
    } = this;

    const result = await app.mysql.query('SELECT * FROM sku WHERE id=' + id + ' AND is_delete=0 AND is_use=1 AND type IN (3,4)');

    if (!result || result.length == 0) {
      return {
        success: false,
        msg: '解决方案不存在'
      }
    } else {
      return {
        success: true,
        data: result[0]
      };
    }
  }

  async getNews(id) {
    const {
      app
    } = this;

    const result = await app.mysql.query('SELECT * FROM news WHERE id=' + id + ' AND is_delete=0 AND is_publish=1');

    if (!result || result.length == 0) {
      return {
        success: false,
        msg: '新闻不存在'
      }
    } else {
      return {
        success: true,
        data: result[0]
      };
    }
  }

  async getNewsType(catalog_id) {
    const {
      app
    } = this;
    const result = await app.mysql.get('catalog', {
      id: catalog_id
    });
    return {
      result
    };
  }

  async save(params) {
    const {
      app
    } = this;
    const row = {
      id: 1,
      content: params,
    }

    let orgPage = await app.mysql.get('pages', {
      id: 1
    });
    orgPage = orgPage.content;
    try {
      orgPage = JSON.parse(orgPage);
    } catch (e) {
      return e;
    }

    let changeVue = [];
    let nParams = JSON.parse(params);
    if (orgPage.items.length != nParams.items.length) {
      changeVue.push('修改楼层服务数量');
    } else {
      for (let [i, oi] of orgPage.items.entries()) {
        let xi = i + 1;
        if (oi.type != nParams.items[i].type) {
          changeVue.push('修改楼层' + xi + '的显示类型');
        } else {
          if (oi.node.length != nParams.items[i].node.length) {
            changeVue.push('修改楼层' + xi + '的分类信息');
          } else {
            let nn = nParams.items[i].node;
            for (let [x, on] of oi.node.entries()) {
              if (on.title != nn[x].title || on.link != nn[x].link || JSON.stringify(on.skus) != JSON.stringify(nn[x].skus)) {
                changeVue.push('修改楼层' + xi + '的' + nn[x].title + '的信息');
              }
            }
          }
        }
      }
    }

    if (JSON.stringify(orgPage.shops) != JSON.stringify(nParams.shops)) {
      changeVue.push('修改一键下单楼层设置');
    }

    if (JSON.stringify(orgPage.plan) != JSON.stringify(nParams.plan)) {
      changeVue.push('修改定制方案楼层设置');
    }

    if (JSON.stringify(orgPage.customItems) != JSON.stringify(nParams.customItems)) {
      changeVue.push('修改自定义楼层设置');
    }

    if (JSON.stringify(orgPage.banners) != JSON.stringify(nParams.banners)) {
      changeVue.push('修改Banner设置');
    }

    if (JSON.stringify(orgPage.jfItems) != JSON.stringify(nParams.jfItems)) {
      changeVue.push('修改解决方案楼层信息');
    }

    if (JSON.stringify(orgPage.newsItems) != JSON.stringify(nParams.newsItems)) {
      changeVue.push('修改新闻楼层信息');
    }

    if (orgPage.page_title != nParams.page_title) {
      changeVue.push('修改SEO设置-Title');
    }

    if (orgPage.page_keywords != nParams.page_keywords) {
      changeVue.push('修改SEO设置-Keywords');
    }

    if (orgPage.page_description != nParams.page_description) {
      changeVue.push('修改SEO设置-Description');
    }

    if (orgPage.mpage_title != nParams.mpage_title) {
      changeValue.push('修改移动端SEO设置-Title');
    }

    if (orgPage.mpage_keywords != nParams.mpage_keywords) {
      changeValue.push('修改移动端SEO设置-Keywords');
    }

    if (orgPage.mpage_description != nParams.mpage_description) {
      changeValue.push('修改移动端SEO设置-Description');
    }


    const result = await app.mysql.beginTransactionScope(async conn => {
      await conn.update('pages', row);

      if (changeVue.length > 0) {
        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          log: changeVue.join(','),
          model: 'pc端首页配置',
          name: 'pc端首页配置'
        })
      };
      return {
        success: true
      };
    }, this.ctx);

    // const success = result.affectedRows === 1;
    return {
      success: result.success
    };
  }

  async saveMobile(params) {
    const {
      app
    } = this;
    const row = {
      id: 2,
      content: params,
    }

    let orgPage = await app.mysql.get('pages', {
      id: 2
    });
    orgPage = orgPage.content;
    try {
      orgPage = JSON.parse(orgPage);
    } catch (e) {
      return e;
    }

    let changeVue = [];
    let nParams = JSON.parse(params);
    if (orgPage.items1.length != nParams.items1.length) {
      changeVue.push('修改楼层服务数量');
    } else {
      orgPage.items1.forEach((item, index) => {
        if (item.list.tit !== nParams.items1[index].list.tit) {
          changeVue.push(`修改楼层${item.tit}标题信息`);
        }
        if (item.list.length !== nParams.items1[index].list.length) {
          changeVue.push(`修改楼层${item.tit}下相关信息`);
        }
      })
      // for(let [i,oi] of orgPage.items1.entries()){
      //     let xi = i + 1;
      //     if(oi.type != nParams.items1[i].type){
      //         changeVue.push('修改楼层' + xi + '的显示类型');
      //     }else{
      //         if(oi.node.length != nParams.items1[i].node.length){
      //             changeVue.push('修改楼层' + xi + '的分类信息');
      //         }else{
      //             let nn = nParams.items1[i].node;
      //             for(let [x,on] of oi.node.entries()){
      //                 if(on.title != nn[x].title || on.link != nn[x].link || JSON.stringify(on.skus) != JSON.stringify(nn[x].skus)){
      //                     changeVue.push('修改楼层' + xi + '的'+ nn[x].title+'的信息');
      //                 }
      //             }
      //         }
      //     }
      // }
    }

    if (JSON.stringify(orgPage.banners) != JSON.stringify(nParams.banners)) {
      changeVue.push('修改Banner设置');
    }

    if (JSON.stringify(orgPage.jfItems) != JSON.stringify(nParams.jfItems)) {
      changeVue.push('修改解决方案楼层信息');
    }

    if (JSON.stringify(orgPage.newsItems) != JSON.stringify(nParams.newsItems)) {
      changeVue.push('修改新闻楼层信息');
    }

    if (orgPage.page_title != nParams.page_title) {
      changeVue.push('修改SEO设置-Title');
    }

    if (orgPage.page_keywords != nParams.page_keywords) {
      changeVue.push('修改SEO设置-Keywords');
    }

    if (orgPage.page_description != nParams.page_description) {
      changeVue.push('修改SEO设置-Description');
    }

    if (orgPage.mpage_title != nParams.mpage_title) {
      changeValue.push('修改移动端SEO设置-Title');
    }

    if (orgPage.mpage_keywords != nParams.mpage_keywords) {
      changeValue.push('修改移动端SEO设置-Keywords');
    }

    if (orgPage.mpage_description != nParams.mpage_description) {
      changeValue.push('修改移动端SEO设置-Description');
    }


    const result = await app.mysql.beginTransactionScope(async conn => {
      await conn.update('pages', row);

      if (changeVue.length > 0) {
        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          log: changeVue.join(','),
          model: '移动端首页配置',
          name: '移动端首页配置'
        })
      };
      return {
        success: true
      };
    }, this.ctx);

    // const success = result.affectedRows === 1;
    return {
      success: result.success
    };
  }

  async getDetail() {
    const {
      app
    } = this;
    const detail = await app.mysql.get('pages', {
      id: 1
    });

    return {
      detail
    };
  }

  async getDetailMobile() {
    const {
      app
    } = this;
    const detail = await app.mysql.get('pages', {
      id: 2
    });
    // 查询一级服务
    const serviceList = await app.mysql.select('catalog', {
      where: {
        parent_id: 2
      }
    })
    detail.serviceList = serviceList
    return {
      detail
      // serviceList
    };
  }

  // 查询核心供应商详情
  async getCoreSupplier() {
    const {
      app
    } = this;
    const detail = await app.mysql.get('pages', {
      id: 3
    });
    return {
      detail
    };
  }

  async coreSupplierSave(params) {
    const {
      app
    } = this;
    let orgPage = await app.mysql.get('pages', {
      id: 3
    });
    params　=　JSON.parse(params)
    orgPage = JSON.parse(orgPage.content);
    let changeVue = []
    if (orgPage.banner_img != params.banner_img) {
      changeVue.push('修改Banner');
    }
    if (orgPage.content != params.content) {
      changeVue.push('修改内容信息');
    }
    if (JSON.stringify(orgPage.item_list) != JSON.stringify(params.item_list)) {
      changeVue.push('修改核心供应商');
    }
    if (orgPage.page_title != params.page_title) {
      changeVue.push('修改SEO设置-Title');
    }
    if (orgPage.page_keywords != params.page_keywords) {
      changeVue.push('修改SEO设置-Keywords');
    }
    if (orgPage.page_description != params.page_description) {
      changeVue.push('修改SEO设置-Description');
    }
    const result = await app.mysql.beginTransactionScope(async conn => {
      // params.item_list = JSON.stringify(params.item_list)
      // params.content = JSON.stringify(params.content)
      // console.log('params', params)
      await conn.update('pages', {
        id: 3,
        content: JSON.stringify(params),
      });

      if (changeVue.length > 0) {
        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          log: changeVue.join(','),
          model: '核心供应商',
          name: '核心供应商'
        })
      };
      return {
        success: true
      };
    }, this.ctx);
    return {
      success: result.success
    };
  }

}

module.exports = IndexService;