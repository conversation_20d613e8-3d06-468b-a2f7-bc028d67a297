'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const { env } = require('../../config/info').siteInfo;
const moment = require('moment');
class RoleController extends Controller {

  //用户列表
  async roleList() {
    const {
      ctx
    } = this;

    const params = {
      page: 1,
      is_delete: 0
    };
    const result = await ctx.service.role.getList(params);

    result.list.some(item => {
      item.is_effect == 1 ? item.is_effect = true : item.is_effect = false;
      item.gmt_create = moment(item.gmt_create).format('YYYY-MM-DD HH:mm:ss');
    });

    await ctx.render('role_list', {
      site_title: _info.site_title,
      page_title: '分组列表',
      active: '14-2',
      list: JSON.stringify(result.list),
      total: result.total,
      csrf: ctx.csrf,
      type: 'list',
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
    });
  }

  async getList() {

    const {
      ctx
    } = this;

    const params = ctx.request.body;
    params.is_delete = 0;

    const result = await ctx.service.role.getList(params);

    result.list.some(item => {
      item.is_effect == 1 ? item.is_effect = true : item.is_effect = false;
      item.gmt_create = moment(item.gmt_create).format('YYYY-MM-DD HH:mm:ss');
    });

    ctx.body = {
      success: true,
      data: result
    };
  }

  async roleAdd() {
    const {
      ctx
    } = this;

    await ctx.render('role_add', {
      site_title: _info.site_title,
      page_title: '添加分组',
      active: '14-2',
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
    });
  }

  async roleEdit() {
    const {
      ctx
    } = this;

    const id = ctx.params.id;

    const detail = await ctx.service.role.getDetail(id);
    detail.is_effect == 1 ? detail.is_effect = true : detail.is_effect = false;

    const logs = await ctx.service.logs.listAll({
      type: 'role',
      id: id,
      page: 1,
      limit: 10
    });

    await ctx.render('role_edit', {
      site_title: _info.site_title,
      page_title: '编辑分组',
      active: '14-2',
      form: detail,
      id: id,
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs)
    });
  }

  async roleSave() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;

    const flag = await ctx.service.role.checkNameIsExists(params.name, params.id);
    if (flag) {
      ctx.body = {
        fail: true,
        data: null,
        msg: "分组名称重复"
      };
      return;
    }

    params.is_effect = params.is_effect == true ? params.is_effect = 1 : params.is_effect = 0;

    const result = await ctx.service.role.roleAdd(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null,
        id: result.id
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async roleEffect() {
    const {
      ctx
    } = this;

    const id = [];
    id.push(ctx.request.body.id);

    if (ctx.request.body.is_effect == 0) //无效的时候判断
    {
      let deleteFlag = await ctx.service.checkflow.checkRoleCanDelete(id);

      if (deleteFlag == 0) {
        ctx.body = {
          fail: true,
          data: null,
          msg: '有未处理的数据,无法删除'
        };
        return;
      }
    }

    const params = ctx.request.body;
    const result = await ctx.service.role.updateEffect(params);


    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null,
        msg: '系统错误'
      };
    }
  }

  async roleDelete() {
    const {
      ctx
    } = this;

    const id = ctx.request.body.id;

    if (typeof id == 'object') {
      for (let item of id) {


        const adminCount = await ctx.service.role.getAdminCount(item);

        if (adminCount > 0) {
          ctx.body = {
            fail: true,
            data: null,
            msg: "分组包含用户，无法删除"
          };
          return;
        }
      }
    } else {
      const adminCount = await ctx.service.role.getAdminCount(id);
      if (adminCount > 0) {
        ctx.body = {
          fail: true,
          data: null,
          msg: "分组包含用户，无法删除"
        };
        return;
      }
    }

    let deleteFlag = await ctx.service.checkflow.checkRoleCanDelete(id);

    if (deleteFlag == 0) {
      ctx.body = {
        fail: true,
        data: null,
        msg: '有未处理的数据,无法删除'
      };
      return;
    }

    const result = await ctx.service.role.roleDelete(id);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async rolePurview() {
    const {
      ctx
    } = this;

    const id = ctx.params.id;

    const detail = await ctx.service.role.getDetail(id);
    detail.is_effect == 1 ? detail.effectText = '开启' : detail.effectText = '冻结';

    const userList = await ctx.service.admin.getList({
      is_delete: "0",
      type: '2',
      is_effect: 1,
      limit: "10000"
    });

    const userListFormat = [];

    for (var i = 0; i < userList.list.length; i++) {
      userListFormat.push({
        key: userList.list[i].id,
        label: userList.list[i].name,
        disabled: false
      });
    }

    const groupUserList = await ctx.service.admin.getList({
      role_id: id,
      is_effect: 1,
      type: '2',
      is_delete: "0",
      limit: "10000"
    });
    const groupUserListFormat = [];
    for (var i = 0; i < groupUserList.list.length; i++) {
      groupUserListFormat.push(groupUserList.list[i].id);
    }

    detail.create_time = moment(detail.gmt_create).format('YYYY-MM-DD HH:mm:ss');

    //服务
    const serviceList = await ctx.service.catalog.getSercataList(); //服务分类
    const serviceListFormat = [];

    for (var i = 0; i < serviceList.list.length; i++) {
      serviceListFormat.push({
        key: serviceList.list[i].id,
        label: serviceList.list[i].name,
        disabled: false
      });
    }

    //行业
    const tradeList = await ctx.service.catalog.getTradeList(); //行业分类
    const tradeListFormat = [];

    for (var i = 0; i < tradeList.list.length; i++) {
      tradeListFormat.push({
        key: tradeList.list[i].id,
        label: tradeList.list[i].name,
        disabled: false
      });
    }

    const menuList = await ctx.service.menu.getList({
      hidePurviewModule: 1
    });


    //菜单权限
    const rolePurviewList = await ctx.service.role.getPurview(id);
    for (var i = 0; i < menuList.list.length; i++) {
      if (menuList.list[i].url != null) {
        //表示有权限
        menuList.list[i].role_purview_json = []; //role权限
        for (let function_item of menuList.list[i].function_json) {
          //遍历每个权限 ，然后查询该角色是否有该权限
          rolePurviewList.list.some(item => {
            item.is_effect == 1 ? item.is_effect = true : item.is_effect = false;
            if (item.menu_id == menuList.list[i].id && item.function_value.indexOf(function_item) != -1) {
              menuList.list[i].role_purview_json && menuList.list[i].role_purview_json.push(function_item);
            }
          });
        }
      }
    }

    //内容权限

    let traceDataPurview = await ctx.service.role.getDataPurview(id, '行业分类权限');
    if (traceDataPurview == null) {
      traceDataPurview = {};
      traceDataPurview.ids = [];
    } else {
      traceDataPurview.ids = JSON.parse(traceDataPurview.ids);
    }
    let serviceDataPurview = await ctx.service.role.getDataPurview(id, '服务分类权限');
    if (serviceDataPurview == null) {
      serviceDataPurview = {};
      serviceDataPurview.ids = [];
    } else {
      serviceDataPurview.ids = JSON.parse(serviceDataPurview.ids);
    }

    const bu_ids = detail.bu_ids ? detail.bu_ids.split(',') : []

    await ctx.render('role_purview', {
      site_title: _info.site_title,
      page_title: '编辑分组权限',
      active: '14-2',
      form: detail,
      userList: JSON.stringify(userListFormat),
      groupUserList: JSON.stringify(groupUserListFormat),
      serviceList: JSON.stringify(serviceListFormat),
      tradeList: JSON.stringify(tradeListFormat),
      menuList: JSON.stringify(menuList.list),
      serviceDataPurview: JSON.stringify(serviceDataPurview),
      traceDataPurview: JSON.stringify(traceDataPurview),
      id: id,
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      buList: JSON.stringify(detail.buList),
      bu_ids: JSON.stringify(bu_ids),
    });
  }

  async rolePurviewSave() {

    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const result = await ctx.service.role.savePurview(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null,
        msg: result.msg
      };
    }
  }

  async roleGetCheckedRoleList() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;

    let result;
    if (params.byIdSelect == '1') {
      result = await ctx.service.role.getCheckedRoleListBySelectIDS(params);
    } else {

      result = await ctx.service.role.getCheckedRoleList(params);
    }

    ctx.body = {
      success: true,
      data: result
    };
  }
}

module.exports = RoleController;