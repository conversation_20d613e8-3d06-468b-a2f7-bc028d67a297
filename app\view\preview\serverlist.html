<% include header.html %>

<div class="banner">
    <div class="swiper-container">
        <div class="swiper-wrapper">
            
            <% for(item of banner){ %>
            <div class="swiper-slide bannerLi1" style="background-image:url(<%- item.img_path %>)">
                <% if(item.btn_url && !item.btn_value){ %><a href="<%- item.btn_url %>" target="blank" style="display:block;width:100%;height:100%;"><% } %>
                <div class="baa">
                    <div class="bannerTitle"><%- item.btn_text %></div>
                    <div class="bannerDetail"><%- item.btn_description %></div>
                    <a class="bannerMore" href="<%- item.btn_url %>" target="_blank">
                        <%- item.btn_value %>
                        <div class="bannermoreBox">
            
                        </div>
                        <div class="bannermoreword"><%- item.btn_value %></div>
                    </a>
            
                </div>
                <% if(item.btn_url && !item.btn_value){ %></a><% } %>
            </div>
            <% } %>
        </div>
        <!-- Add Arrows -->
        <div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>
    </div>
</div>
<div class="location">
    <ul>
        <li class="locationLi">
            <a href="">首页</a></li>
        <li class="locationLi icon"></li>
        <li class="locationLi"><a href="javascript:;"><% if(detail.parantId == 1){ %>您的行业<% }else{ %>我们的服务<% } %></a></li>
        <li class="locationLi icon"></li>
        <li class="locationLi"><a href="./"><%- detail.name %></a></li>
        <li class="locationLi icon"></li>
        <li class="locationLi"><a href="javascript:;">全部服务</a></li>
    </ul>
</div>
<div class="listBigBox1">
    <div class="categorybox">
        <div class="categorybox-wrap">
        <ul class="categoryUl" id="CataUl">
            <li class="categoryLi">服务类别：</li>
            <li class="categoryLi categoryLi1" data-role="<%- detail.id %>" onclick="_getCata(<%- detail.id %>,this)">全部服务</li>
            <% for(var item of children){ %>
            <li class="categoryLi categoryLi1" data-role="<%- item.id %>" onclick="_getCata(<%- item.id %>,this)"><%- item.name %></li>
            <% } %>
        </ul>

        <div class="keySearch">
            <div class="keyWord"></div>
            <input type="text" class="keyInput" placeholder="在结果中搜索" id="sKeyword">
            <div class="navSearchIcon listSearch" onclick="_search(this)">
                <div class="navSearchIconIcon navSearchIconIcon1">

                </div>
            </div>
        </div>
        </div>
        

    </div>

    <div class="listBigBox">

        <div class="listBox">
            <ul class="listUl" id="List2">
            </ul>
            <ul class="serverpages" id="Pages">
            </ul>
        </div>
    </div>
</div>

<script src="/static/preview/js/swiper.min.js"></script>
<script>
    var swiper = new Swiper('.swiper-container', {
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        loop: true
    });

    var pdata = {
        page: <%- page || 1 %>,
        limit: 5,
        id: <%- id %>,
        name:' <%- name %>',
        _csrf: '<%- csrf %>',
    }

    $(function(){
        _getData();
    })

    function _getData(){
        $.get('/preview/getCataSkus',pdata,function(r){
            if(r.success){
                var data = r.data;
                var $_ul = '';
                for(var i=0;i<data.length;i++){
                    var item = data[i];
                    var li = '<li class="listLi"><div class="list-img"><img src="'+item.thumb_img+'" alt="" style="width:284px;height:142px;"></div>';
                    li += '<div class="listwordbox"> <div class="listwordTS"><a class="listwordT" href="/preview/sku/'+item.id+'">'+item.name+'</a><div class="listwordS">'+item.sub_title+'</div></div>';
                    li += '<div class="listwordI">'+item.description+'</div>';
                    //li += '<div class="listwordC"><span class="listwordC1"></span>';
                    //for(var n=0;n<item.tags.length;n++){
                    //    li += '<span class="listwordC2">'+item.tags[n].tag_name+'</span>';
                    //}
                    li += '</div></div></li>';

                    $_ul += li;
                }

                $('#List2').html($_ul);

                var $_pages = '';
                for(var i = 1; i<=Math.ceil(r.total/5);i++){
                    var pclass = 'serverpage';
                    if(i == r.page){
                        pclass += ' pageActive';
                    }
                    $_pages += '<li class="'+pclass+'" onclick="setPage(this)">'+i+'</li>';
                }
                $('#Pages').html($_pages);

                $('#CataUl>li').each(function(i,item){
                    var zid = $(item).data('role');
                    if(zid == pdata.id){
                        $(item).addClass('clickActive');
                    }
                })
            }
        });
    }

    function _getCata(id,obj){
        if($(obj).hasClass('clickActive')){
            return;
        }

        pdata.page = 1;
        pdata.id = id;
        _getData();
        $(obj).addClass('clickActive').siblings().removeClass('clickActive');
    }

    function _search(obj){
        var keyword = $('#sKeyword').val().trim();

        /*
        if(keyword == ''){
            return;
        }
        */

        pdata.name = keyword;
        _getData();
    }

    function setPage(obj){
        var page = $(obj).text();
        pdata.page = page;
        _getData();
    }
</script>
<% include footer.html %>