function randomWord() {
    var str = "",
        range = 6,
        arr = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

    for (var i = 0; i < range; i++) {
        pos = Math.round(Math.random() * (arr.length - 1));
        str += arr[pos];
    }
    return str;
}

var nameCheck =  function(rule, value, callback){
    var checkStr = /^[^\|"'<>?\*]*$/;
    if(!checkStr.test(value)){
        callback('不能含有以下特殊字符：\|"\'<>?*');
    }else{
        callback();
    }
}

var engCheck =  function(rule, value, callback){
    var checkStr = /^[a-zA-Z0-9]*$/;
    if(!checkStr.test(value)){
        callback('必须为数字英文');
    }else{
        callback();
    }
}