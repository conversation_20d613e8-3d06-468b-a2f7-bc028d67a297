'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo; 
const { env } = require('../../config/info').siteInfo;

class OtherController extends Controller {
  async otherList() {
    const { ctx } = this;
    const result = await ctx.service.other.getList({ page: 1, limit: 10 });
    const logs = await ctx.service.logs.listAll({ model: '其他内容', page: 1, limit: 10 });



    await ctx.render('other_list', {
      site_title: _info.site_title,
      page_title: '其他内容',
      active: '8',
      list: JSON.stringify(result.list) || '[]',
      total: result.total || 0,
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs)
    });
  }

  async otherAdd() {
    const { ctx } = this;

    await ctx.render('other_add', {
      site_title: _info.site_title,
      ticMallHost: env[this.app.config.env].ticMallHost,
      page_title: '添加',
      active: '7',
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
    });
  }

  async otherEditByid() {
    const { ctx } = this;

    const id = ctx.params.id;

    const detail = await ctx.service.other.getDetail(id);
    detail.detail.content = detail.detail.content.replace(/\n/g, '');

    const logs = await ctx.service.logs.listAll({ type: 'other', id: id, page: 1, limit: 10 });
    await ctx.render('other_edit', {
      site_title: _info.site_title,
      ticMallHost: env[this.app.config.env].ticMallHost,
      page_title: '编辑',
      active: '7',
      info: detail.detail,
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs)
    });
  }

  async otherEdit() {
    const { ctx } = this;
    const params = ctx.request.body;

    const result = await ctx.service.other.otherEdit(params);

    if (result.success) {
      ctx.body = { success: true, data: result.success };
    } else {
      ctx.body = { fail: true, msg: result.msg };
    }

  }

  async otherDelete() {
    const { ctx } = this;

    const params = ctx.request.body;
    const result = await ctx.service.other.otherDelete(params.id, params.pageLink);

    ctx.body = { success: true, data: null };
  }

  async getList() {
    const { ctx } = this;
    const params = ctx.request.body;
    const page = params.page || 1,
      limit = params.limit || 10;

    const result = await ctx.service.other.getList({ page: page, limit: limit });

    ctx.body = { success: true, data: { list: result.list, total: result.total } };
  }
}

module.exports = OtherController;
