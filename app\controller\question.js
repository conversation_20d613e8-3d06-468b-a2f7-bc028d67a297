'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo; 
const { env } = require('../../config/info').siteInfo;

class questionController extends Controller {
  async question() {
    const {
      ctx
    } = this;

    await ctx.render('question', {
      site_title: _info.site_title,
      page_title: '常见问题',
      active: '19-1',
      csrf: ctx.csrf,
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: 1,
      hasPubviewPurview: 1,
      hasEditPurview: 1
    });
  }
}
module.exports = questionController;