<% include header.html %>
<div class="bgBanner">
    <img src="/static/preview/images/bgbanner.png" alt="">
    <div class="bgBannerW">
        <div class="bgBannerW1">关于SGS</div>
        <div class="bgBannerW2">全球领先的检验、检测、鉴定和认证机构</div>
    </div>
</div>
<div class="location">
    <ul>
        <li class="locationLi">
            <a href="">首页</a>
        </li>
        <li class="locationLi icon"></li>
        <li class="locationLi">
            <a href="">新闻</a>
        </li>

    </ul>
</div>

<div class="nlBox">
    <div class="nlSearch">
        <div class="nlSearchBox">
            <div class="tSearch">
                <span class="tSearch-word">新闻标题：</span>
                <input type="text" class="tSearch-input" placeholder="请输入关键字">
            </div>
            <div class="dSearch">
                <div class="calendar"></div>
                <span class="dSearch-word">新闻日期：</span>
                <input type="text" class="dSearch-input" placeholder="-">
                <div class="dSearchIb">

                </div>
            </div>
        </div>
    </div>
    <div class="nlLists">
        <ul class="nlListsUl">
            <% for(var item of detail){ %>
            <li class="nlListsLi">
                <span class="nlListsI"></span>
                <a href="/preview/news/detail-<%- item.id %>.html" class="nlListsA"><%- item.title %></a>
                <span class="nlListsT"><%- item.time %></span>
            </li>
            <% } %>
        </ul>

        <ul class="serverpages">
            <% for(var i=1;i<=Math.ceil(total/10);i++){ %>
                <li class="serverpage<% if(i==page){ %> pageActive<% } %>"><a href="?page=<%- i %>"><%- i %></a></li>
            <% } %>
        </ul>
    </div>
</div>

<% include footer.html %>