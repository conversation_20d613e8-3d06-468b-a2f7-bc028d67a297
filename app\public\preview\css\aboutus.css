.bgBanner{
    width: 100%;
    height: 300px;
    /*background: url(../images/bgbanner.png) no-repeat 100% 100%;*/
    margin-top: 97px;
    position: relative;
    margin-bottom: 22px;
}
.bgBanner img{
    width: 100%;height: 100%;
    position: absolute;
    left: 0;top: 0;
}
.bgBannerW1{
    width: auto;
    height: auto;
    font-size: 48px;
    color: white;
    margin-top: 78px;
}
.bgBannerW2{
    width: auto;
    height: auto;
    font-size: 16px;
    color: white;
    margin-top: 10px;
}
.bgBannerW{
    width: 1226px;
    height: 100%;
    margin: 0 auto;
    overflow: hidden;
    position: absolute;
    left: 50%;margin-left: -613px;
}
.aboutBox{
    width: 1226px;height: auto;
    margin: 0 auto;
}
.aboutBox-word1{
    width: 812px;
    margin-bottom: 48px;
    font-size: 18px;
    font-weight: bold;
    color: #000000;
    line-height: 1.6;
}
.aboutBox-word2{
    margin-bottom: 45px;
    font-size: 14px;
    color: #000000;
    line-height: 1.6;
    width: 750px;
}
.aboutBox-word3{
    margin-bottom: 45px;
    font-size: 14px;
    color: #000000;
    line-height: 1.6;
    width: 750px;
}
.location{
    margin-bottom: 45px;
}
.honners{
    width: 812px;height: auto;
}
.honnerT{
    width: 812px;
    margin-bottom: 30px;
    font-size: 18px;
    font-weight: bold;
    color: #000000;
    line-height: 1.6;
}
.honner1{
    margin-bottom: 30px;
    position: relative;
}
.honner1-bg{
    width: 9px;
    height: 3px;
    background: rgb(254,102,3);
    display: inline-block;
    position: absolute;
    top: 9px;
    left: 10px;
}
.honner1-word{
    margin-left: 25px;
    font-size: 14px;
}
.photo{
    width: 345px;height: 345px;
    background: url(../images/guangtou.png);
    float: right;
}