<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        <a href='/superadmin/list'>超级用户配置</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        修改
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row>
                            <h3 class="stit">基本资料</h3>
                            <el-form :model="form" label-width="120px"  ref="form">
							
                                <el-form-item label="原始密码">
                                    <el-input  type="password" v-model:trim="form.pwd"></el-input>
                                </el-form-item>

								<el-form-item label="新密码">
                                    <el-input  type="password" v-model:trim="form.newpassword"></el-input>
                                </el-form-item>
                              
							   <el-form-item label="确认新密码">
							      <el-input  type="password" v-model:trim="form.newpassword2"></el-input>
							    </el-form-item>
                               
                            </el-form>
                           
                            <hr>
                            <el-col :span="24" style="background: #39F;padding:12px 0;color:#FFF">
                                <el-col :span="12" style="padding-left:12px;">
                                    <el-button @click="_cancel">返回列表</el-button>
                                </el-col>
                                <el-col :span="12" style="text-align:right;padding-right:12px;">
                                    <el-button type="success" @click='onSubmit'> 保 存 </el-button>
                                </el-col>
                            </el-col>
                        </el-row>
                    </el-main>
            </el-container>
    </el-container>
    <script>
    var ai = 0, bi = 0;
    var main = new Vue({
        el: '#Main',
        data: {
            form: {
                id: <%- id %>,
				pwd:'',
                newpassword: '',
                newpassword2: ''
            },
            loading: false,
        },
        methods: {
            onSubmit(){
                var that = this;
				
				if(!this.form.pwd){
				    this.$message.error('原始密码不能为空');
				    return;
				}
				
                if(!this.form.newpassword){
                    this.$message.error('新密码不能为空');
                    return;
                }
				
				 if(this.form.newpassword!=this.form.newpassword2){
				    this.$message.error('新密码和确认密码填写不一致');
				    return;
				}

				this.$confirm('是否确认修改数据？', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(()=>{
					this.$refs['form'].validate((valid) => {
						if (valid) {
							var loading = this.$loading({
								lock: true,
								text: 'Loading',
								spinner: 'el-icon-loading',
								background: 'rgba(255, 255, 255, 0.7)'
							});

							var params = that.form;
							params._csrf = '<%- csrf %>';
							axios.post('/superadmin/savepwd', params)
								.then(function(result) {
									loading.close();
									loading.close();
									if (result.data.success) {
										that.$message({
											message: '保存成功。',
											type: 'success'
										});

										window.location = '/superadmin/list';
									}else{
										that.$message.error(result.data.msg);
									}
								});
						} else {
							
							return false;
						}
					});
				});
            },
            
            _cancel(){
                window.location = '/superadmin/list/';
            }
        },
        mounted(){
            this.$nextTick(function(){
              
                document.getElementById('preLoading').style.display = 'none';
            });
        }
    });
    </script>
    <% include footer.html %>