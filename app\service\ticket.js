'use strict';

const Service = require('egg').Service;
const moment = require('moment');

class ticketService extends Service {
    async list(params) {
        const {
            app
        } = this;
        const page = params.page || 1,
            limit = 10;

        const content = params.content,
            customer = params.customer,
            phone = params.phone,
            email = params.email,
            result_code = params.result_code;

        let qarr = [];
        if (content) {
            qarr.push('content LIKE "%' + content + '%"');
        }

        if (customer) {
            qarr.push('customer LIKE "%' + customer + '%"');
        }

        if (phone) {
            qarr.push('phone LIKE "%' + phone + '%"');
        }

        if (email) {
            qarr.push('email LIKE "%' + email + '%"');
        }

        if (result_code) {
            qarr.push('result_code="' + result_code + '"');
        }

        if (qarr.length > 0) {
            qarr = qarr.join(' AND ');
            qarr = ' WHERE ' + qarr;
        } else {
            qarr = '';
        }

        let total = await app.mysql.query('SELECT id FROM ticket' + qarr);
        const list = await app.mysql.query('SELECT * FROM ticket' + qarr + ' ORDER BY gmt_modify DESC,id DESC LIMIT ' + (page - 1) * limit + ',' + limit);

        for (let item of list) {
            let tradeInfo = await app.mysql.get('catalog', {
                id: item.trade_id,
                is_delete: 0
            });
            let serviceInfo = await app.mysql.get('catalog', {
                id: item.service_id,
                is_delete: 0
            });

            if (tradeInfo) {
                item.trade = tradeInfo.name;
            } else {
                item.trade = '其他';
            }

            if (serviceInfo) {
                item.service = serviceInfo.name;
            } else {
                item.service = '其他';
            }
        }

        total = total.length;
        return {
            list,
            total
        };
    }

    async add(params) {
        const {
            app
        } = this;
        const gmt_create = await moment().format('YYYY-MM-DD HH:mm:ss');

        const row = {
            type: params.type,
            trade_Id: params.trade,
            service_Id: params.service,
            content: params.content,
            customer: params.customer,
            phone: params.phone,
            email: params.email,
            provice: params.provice,
            company: params.company || '',
            gmt_create: gmt_create,
        }

        const result = await app.mysql.insert('ticket', row);

        const success = result.affectedRows === 1;

        return {
            success
        };
    }

    async get(id) {
        const {
            app
        } = this;

        const info = await app.mysql.get('ticket', {
            id: id
        });

        return info;
    }

    async update(params) {
        const {
            app
        } = this;

        const row = {
            id: params.id,
            result_code: params.result_code,
            result_text: params.result_text,
            gmt_modify: moment().format('YYYY-MM-DD HH:mm:ss'),
        }

        const result = await app.mysql.update('ticket', row);
    }
}

module.exports = ticketService;