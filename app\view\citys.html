<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        设置
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        城市
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row v-loading="loading">
                            <el-col :span="12">
                                <el-table :data="tableList.list" stripe border style="width: 100%">
                                    <el-table-column prop="id" label="" width="60">
                                        <template slot-scope="scope">
                                            {{scope.$index+1}}
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="name" label="中文名称">
                                    </el-table-column>
                                    <el-table-column fixed="right" label="操作" width="160">
                                        <template slot-scope="scope">
                                            <el-button-group>
                                                <el-button  v-if="hasEditPurview==1"   type="primary" size="mini" @click='_edit(scope.row.id)'>编辑</el-button>
                                                <el-button  v-if="hasEditPurview==1"  type="danger" size="mini" @click='_del(scope.row.id,scope.row.name)'>删除</el-button>
                                            </el-button-group>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <!--<el-pagination @current-change="handleCurrentChange" background :current-page.sync="tableList.curr" :page-size="tableList.limit"
                                    layout="total, prev, pager, next" :total="tableList.total" style="padding:10px 0;text-align: right;">
                                </el-pagination>-->
                                <div class="logshow-wrapper" v-if="logs.list.length > 0">
                                <el-row class="logshow" :class="{on: logshow}">
                                    <span class="icon"><i class="el-icon-arrow-up" @click="showLog"></i></span>
                                    <ul>
                                        <li v-for="(item, index) of logs.list" :key='index'>{{item.time}}, {{item.user_name}}, {{item.log}}</li>
                                    </ul>
                                    <el-pagination
                                      @current-change="logPageChange"
                                      :current-page.sync="logs.page"
                                      :page-size="logs.limit"
                                      layout="total, prev, pager, next"
                                      :total="logs.total">
                                    </el-pagination>
                                </el-row>
                                </div>
                            </el-col>
                            <el-col :span="12"  v-if="hasEditPurview==1" >
                                <el-form ref="form" :model="form" label-width="90px">
                                    <el-form-item label="名称">
                                        <el-input v-model="form.name"></el-input>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="onSubmit" v-if="form.id == 0">新建</el-button>
                                        <el-button type="primary" @click="onModify" v-else>编辑</el-button>
                                        <el-button @click="_default">取消</el-button>
                                    </el-form-item>
                                </el-form>
                            </el-col>
                        </el-row>
                    </el-main>
            </el-container>
    </el-container>
    <script>
    var main = new Vue({
        el: '#Main',
        data: {
            tableList: {
                list: <%- list %>,
                total: 0,
                curr: 0,
                limit: 10
            },
            form: {
                id: 0,
                name: '',
                alias: '',
            },
            pageData: {
                page: 1
            },
            loading: false,
            logs: <%- logs %>,
            logshow: false,
			hasCheckedPurview: <%- hasCheckedPurview %>,
			hasPubviewPurview: <%- hasPubviewPurview %>,
			hasEditPurview: <%- hasEditPurview %>
        },
        methods: {
            handleCurrentChange(r) {
                this.form.page = r;
                this.getData();
            },
            onSubmit() {
                var that = this;
                that.loading = true;
                var params = this.form;
                params._csrf = '<%- csrf %>';
                axios.post('/setting/citys/add', params)
                    .then(function(result) {
                        that.loading = false;
                        if (result.data.success) {
                            window.location.reload();
                        }else{
                            that.$message.error('名称不能为空');
                        }
                    });
            },
            _edit(id) {
                var that = this;
                that.loading = true;
                axios.post('/setting/citys/info', { id: id, _csrf: '<%- csrf %>' })
                    .then(function(result) {
                        if (result.data.success) {
                            that.form = result.data.data[0];
                            that.loading = false;
                        }
                    });
            },
            _del(id,name) {
                var that = this;
                this.$confirm('是否删除'+name+'？', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                    that.loading = true;
                    axios.post('/setting/citys/delete', { id: id, _csrf: '<%- csrf %>' })
                    .then(function(result) {
                        if (result.data.success) {
                            that._default();
                            window.location.reload();
                        }
                    });
                }).catch(e =>{
                    return e;
                });
            },
            getData() {

            },
            onModify() {
                var that = this;
                that.loading = true;
                var params = this.form;
                params._csrf = '<%- csrf %>';
                axios.post('/setting/citys/edit', params)
                    .then(function(result) {
                        if (result.data.success) {
                            window.location.reload();
                        }
                    });
            },
            _default() {
                this.form.id = 0;
                this.form.name = '';
                this.form.alias = '';
            },
            showLog(){
                if(this.logs.list.length > 1){
                    this.logshow = !this.logshow;
                }
            },
            logPageChange(page){
                axios.post('/logslist',{page:page,model:'设置-城市',limit:10,_csrf:'<%- csrf %>'}).then(res => {
                    let data = res.data;
                    if(data.success){
                        this.logs = data.data;
                    }
                    
                })
            }
        },
        mounted(){
            this.$nextTick(() => {
                document.getElementById('preLoading').style.display = 'none';
            })
        }
    });
    </script>
    <% include footer.html %>