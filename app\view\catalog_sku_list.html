<% include header.html %>
  <el-container id="Main">
    <% include eheader.html %>
      <el-container>
        <% include eside.html %>
          <el-main>
            <el-row>
              <el-col :span="24">
                <el-breadcrumb separator-class="el-icon-arrow-right">
                  <el-breadcrumb-item>
                    <a href='/'>首页</a>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    <% if(bigType=='service' ){ %>服务分类<% }else{ %>行业分类<% } %>
                  </el-breadcrumb-item>
                  <% for(var i=0;i<cataList.length;i++){ %>
                    <% if(cataList.length>1 && i == 0){ %>
                      <el-breadcrumb-item>
                        <a href='/catalog/<%- bigType %>/skus/<%- cataList[i].id %>'><%- cataList[i].name %></a>
                      </el-breadcrumb-item>
                      <% }else{ %>
                        <el-breadcrumb-item>
                          <%- cataList[i].name %>
                        </el-breadcrumb-item>
                        <% } %>
                          <% } %>
                            <el-breadcrumb-item>
                              <%- page_title %>
                            </el-breadcrumb-item>
                </el-breadcrumb>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form :model="form" :inline="true">
                  <el-form-item label="订购">
                    <el-select v-model="form.is_buy" placeholder="请选择" clearable style="width:120px">
                      <el-option :key="true" label="可订购" :value="true"></el-option>
                      <el-option :key="false" label="不可订购" :value="false"></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="上下架">
                    <el-select v-model="form.is_use" placeholder="请选择" clearable style="width:120px">
                      <el-option :key="true" label="上架" :value="true"></el-option>
                      <el-option :key="false" label="下架" :value="false"></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="名称">
                    <el-input v-model="form.name" placeholder="服务名称" clearable></el-input>
                  </el-form-item>

                  <el-form-item>
                    <el-button type="primary" @click="_filter">查询</el-button>
                    <el-button @click=_default>重置</el-button>
                  </el-form-item>
                  </el-form-item>
                </el-form>

              </el-col>

              <el-col :span="24" style="text-align:right">
                <el-button size="small" type="primary" @click='_editSort' v-show="!editType">修改序号</el-button>
                <el-button size="small" type="success" @click="_saveSort" v-show="editType">保存</el-button>
                <!--<el-button size="small" @click="_cancelSort" v-show="editType">取消</el-button>-->
                <el-button size="small" @click="_defaultSort">默认排序</el-button>
              </el-col>
            </el-row>
            <el-row v-loading="loading">
              <el-col :span="24">
                <el-table :data="tableList.list" style="width: 100%" @selection-change="change">
                  <el-table-column width="130" style="text-align: center;">
                    <template slot-scope="scope">
                      <span v-show="!editType">{{scope.row.sort_num}}</span>
                      <el-input-number style="padding:0; width: 120px;" size="small" :precision="0" v-show="editType" v-model.trim="scope.row.sort_num"  :min="0" :max="9999"></el-input-number>
                    </template>
                  </el-table-column>
                  <el-table-column width="50">
                    <template slot-scope="scope">
                      <img :src="scope.row.thumb_img" style="width:40px" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="name" label="名称" width="240">
                  </el-table-column>
                  <el-table-column prop="alias" label="链接地址" style="white-space: nowrap;">
                    <template slot-scope="scope" v-if="scope.row.id">
                      <a :href="'<%- view_url %>/sku/'+scope.row.alias+'/'+scope.row.id" target="_blank">链接</a>
                    </template>
                  </el-table-column>
                  <el-table-column label="所属类目" width="120">
                    <template slot-scope="scope">
                      <el-tooltip placement="top" effect="light" popper-class="spopper">
                        <div slot="content">
                          <h3 style="margin:0;padding-bottom:10px">所属服务</h3>
                          <el-col :span="24" v-for="(item, index) in scope.row.serviceList" :key='index'
                            style="padding:5px 0">{{item}}</el-col>
                        </div>
                        <span
                          style="overflow:hidden;white-space: nowrap;text-overflow: ellipsis;">{{scope.row.serviceList[0]}}</span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column label="关联行业" width="120">
                    <template slot-scope="scope">
                      <el-tooltip placement="top" effect="light" popper-class="spopper">
                        <div slot="content">
                          <h3 style="margin:0;padding-bottom:10px">关联行业</h3>
                          <el-col :span="24" v-for="(item, index) in scope.row.tradeList" :key='index'
                            style="padding:5px 0">{{item}}</el-col>
                        </div>
                        <span>{{scope.row.tradeList[0]}}</span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="is_use" label="上下架" width="80">
                    <template slot-scope="scope">
                      <span v-if="scope.row.is_use == 1">上架</span>
                      <span v-else style="color:#900">下架</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="is_buy" label="订购" width="48">
                    <template slot-scope="scope">
                      <span v-if="scope.row.is_buy == 1">是</span>
                      <span v-else style="color:#900">否</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="last_modify" label="最后编辑">
                  </el-table-column>
                  <el-table-column prop="time" label="最后编辑时间">
                  </el-table-column>
                  <el-table-column label="操作" width="160" fixed="right">
                    <template slot-scope="scope">
                      <a class="el-button el-button--primary el-button--mini" style="color:#fff"
                        :href="'/sku/service/' + scope.row.id">编辑</a>
                      <el-button type="danger" size="mini" @click='_del(scope.row.id,scope.row.name)'>解除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination @current-change="handleCurrentChange" background :current-page.sync="form.page"
                  :page-size="form.limit" @size-change="handleSizeChange" layout="total, prev, pager, sizes, next"
                  :total="form.total" :page-sizes="[10, 30, 50, 100]" style="padding:10px 0;text-align: right;">
                </el-pagination>
              </el-col>
            </el-row>
            <div class="logshow-wrapper" v-if="logs.list.length > 0">
              <el-row class="logshow" :class="{on: logshow}">
                <span class="icon"><i class="el-icon-arrow-up" @click="showLog"></i></span>
                <ul>
                  <li v-for="item of logs.list">{{item.time}}, {{item.user_name}}, {{item.log}}</li>
                </ul>
                <el-pagination @current-change="logPageChange" :current-page.sync="logs.page" :page-size="logs.limit"
                  layout="total, prev, pager, next" :total="logs.total">
                </el-pagination>
              </el-row>
            </div>
          </el-main>
      </el-container>
  </el-container>
  <script>
    var main = new Vue({
      el: '#Main',
      data: {
        type: '<%- type %>',
        tableList: {
          list: <%- list %>,
        },
        form: {
          id: <%- id %>,
          is_use: '',
          is_buy: '',
          name: '',
          page: 1,
          limit: 30,
          total: <%- total %>,
        },
        loading: false,
        dialogFormVisible: false,
        check: true,
        checkIds: [],
        editType: false,
        logs: <%- logs %>,
        logshow: false
      },
      methods: {
        handleCurrentChange(val) {
          this.form.page = val;
          this.getData();
        },
        handleSizeChange(val) {
          this.form.limit = val;
          this.getData();
        },
        getData() {
          var that = this;
          that.loading = true;
          var params = that.form;
          params._csrf = '<%- csrf %>';
          url = '/catalog/getCatalogSkus';
          params.type = that.type;  
          axios.post(url, params)
            .then(function (result) {
              if (result.data) {
                that.loading = false;
                that.tableList.list = result.data.list;
                that.form.total = result.data.total;
                /*
                that.tableList.curr = that.form.page;
                */
              }
            });
        },
        _editSort() {
          this.editType = true;
        },
        _saveSort() {
          var postData = [];
          this.tableList.list.forEach(item => {
            postData.push({ catalog_id: <%- id %>, sku_id: item.id, sort_num: item.sort_num || 0, bigType: '<%- bigType %>' });
          });
          this.editType = false;

          url = '/sku/service/saveSort';
          axios.post(url, { data: postData, _csrf: '<%- csrf %>' })
            .then(function (result) {
              if (result.data.success) {
                that._default();
                window.location.reload();
              }
            });
        },
        _cancelSort() {
          this.editType = false;
        },
        _del(id, name) {
          var that = this;
          this.$confirm('是否解除与' + name + '的关联？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            that.loading = true;
            url = '/sku/service/drop';
            axios.post(url, { sku_id: id, id: <%- id %>, _csrf: '<%- csrf %>' })
              .then(function (result) {
                if (result.data.success) {
                  that._default();
                  window.location.reload();
                }
              });
          }).catch(e => {
            return e;
          });
        },
        _delMulti() {
          var that = this;
          this.$confirm('是否解除已选择SKU得关联？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            that.loading = true;
            url = '/sku/service/drop';
            axios.post(url, { sku_id: that.checkIds, id: <%- id %>, _csrf: '<%- csrf %>' })
              .then(function (result) {
                if (result.data.success) {
                  that._default();
                  window.location.reload();
                }
              });
          }).catch(e => {
            return e;
          });
        },
        _filter() {
          var that = this;
          that.form.page = 1;
          this.getData();
        },
        change(val) {
          var that = this;
          that.checkIds = [];
          val.forEach(function (item, i) {
            that.checkIds.push(item.id);
          });

          if (that.checkIds.length == 0) {
            that.check = true;
          } else {
            that.check = false;
          }
        },
        _default() {
          this.form.is_use = '';
          this.form.is_buy = '';
          this.form.name = '';
        },
        _defaultSort() {
        
          var that = this;
          this.$confirm('默认序号为0，是否将所有sku恢复默认排序', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
              var url = '/catalog/defaultSort'
              axios.post(url, { id: <%- id %>, _csrf: '<%- csrf %>' })
              .then(function (result) {
                if (result.data.success) {
                  window.location.reload();
                }
              });
          }).catch(e => {
            return e;
          });

        },
        showLog() {
          if (this.logs.list.length > 1) {
            this.logshow = !this.logshow;
          }
        },
        logPageChange(page) {
          axios.post('/logslist', { page: page, type: 'catalog', id:<%- id %>, limit: 10, _csrf: '<%- csrf %>' }).then(res => {
            let data = res.data;
            if (data.success) {
              this.logs = data.data;
            }

          })
        }
      },
      mounted() {
        this.$nextTick(function () {
          //this.serviceList.unshift({id:'all',name:'所有服务'});
          //this.tradeList.unshift({id:'all',name:'所有行业'});

          document.getElementById('preLoading').style.display = 'none';
        });
      }
    });
  </script>
  <% include footer.html %>