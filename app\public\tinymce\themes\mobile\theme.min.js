!function(){"use strict";var n,e,t,o,r,i,u,a=function(n){return function(){return n}},O={noop:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e]},noarg:function(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t()}},compose:function(t,o){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(o.apply(null,arguments))}},constant:a,identity:function(n){return n},tripleEquals:function(n,e){return n===e},curry:function(i){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];for(var u=new Array(arguments.length-1),t=1;t<arguments.length;t++)u[t-1]=arguments[t];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];for(var t=new Array(arguments.length),o=0;o<t.length;o++)t[o]=arguments[o];var r=u.concat(t);return i.apply(null,r)}},not:function(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return!t.apply(null,arguments)}},die:function(n){return function(){throw new Error(n)}},apply:function(n){return n()},call:function(n){n()},never:a(!1),always:a(!0)},c=function(e){return function(n){return function(n){if(null===n)return"null";var e=typeof n;return"object"===e&&Array.prototype.isPrototypeOf(n)?"array":"object"===e&&String.prototype.isPrototypeOf(n)?"string":e}(n)===e}},E={isString:c("string"),isObject:c("object"),isArray:c("array"),isNull:c("null"),isBoolean:c("boolean"),isUndefined:c("undefined"),isFunction:c("function"),isNumber:c("number")},s=function(u){return function(){for(var n=new Array(arguments.length),e=0;e<n.length;e++)n[e]=arguments[e];if(0===n.length)throw new Error("Can't merge zero objects");for(var t={},o=0;o<n.length;o++){var r=n[o];for(var i in r)r.hasOwnProperty(i)&&(t[i]=u(t[i],r[i]))}return t}},f=s(function(n,e){return E.isObject(n)&&E.isObject(e)?f(n,e):e}),l=s(function(n,e){return e}),D={deepMerge:f,merge:l},d=O.never,m=O.always,g=function(){return p},p=(o={fold:function(n,e){return n()},is:d,isSome:d,isNone:m,getOr:t=function(n){return n},getOrThunk:e=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},or:t,orThunk:e,map:g,ap:g,each:function(){},bind:g,flatten:g,exists:d,forall:m,filter:g,equals:n=function(n){return n.isNone()},equals_:n,toArray:function(){return[]},toString:O.constant("none()")},Object.freeze&&Object.freeze(o),o),v=function(t){var n=function(){return t},e=function(){return r},o=function(n){return n(t)},r={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:m,isNone:d,getOr:n,getOrThunk:n,getOrDie:n,or:e,orThunk:e,map:function(n){return v(n(t))},ap:function(n){return n.fold(g,function(n){return v(n(t))})},each:function(n){n(t)},bind:o,flatten:n,exists:o,forall:o,filter:function(n){return n(t)?r:p},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(d,function(n){return e(t,n)})},toArray:function(){return[t]},toString:function(){return"some("+t+")"}};return r},y={some:v,none:g,from:function(n){return null===n||n===undefined?p:v(n)}},h=(r=Object.keys)===undefined?function(n){var e=[];for(var t in n)n.hasOwnProperty(t)&&e.push(t);return e}:r,b=function(n,e){for(var t=h(n),o=0,r=t.length;o<r;o++){var i=t[o];e(n[i],i,n)}},w=function(o,r){var i={};return b(o,function(n,e){var t=r(n,e,o);i[t.k]=t.v}),i},x=function(n,t){var o=[];return b(n,function(n,e){o.push(t(n,e))}),o},S=function(n){return x(n,function(n){return n})},M={bifilter:function(n,t){var o={},r={};return b(n,function(n,e){(t(n,e)?o:r)[e]=n}),{t:o,f:r}},each:b,map:function(n,o){return w(n,function(n,e,t){return{k:e,v:o(n,e,t)}})},mapToArray:x,tupleMap:w,find:function(n,e){for(var t=h(n),o=0,r=t.length;o<r;o++){var i=t[o],u=n[i];if(e(u,i,n))return y.some(u)}return y.none()},keys:h,values:S,size:function(n){return S(n).length}},T=(O.constant("contextmenu"),O.constant("touchstart")),k=O.constant("touchmove"),C=O.constant("touchend"),A=(O.constant("gesturestart"),O.constant("mousedown")),B=O.constant("mousemove"),R=(O.constant("mouseout"),O.constant("mouseup")),I=O.constant("mouseover"),F=(O.constant("focusin"),O.constant("keydown")),N=O.constant("input"),V=O.constant("change"),H=(O.constant("focus"),O.constant("click")),j=O.constant("transitionend"),z=O.constant("selectstart"),L=function(n){var e,t=!1;return function(){return t||(t=!0,e=n.apply(null,arguments)),e}},P=function(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var o=n[t];if(o.test(e))return o}return undefined}(n,e);if(!t)return{major:0,minor:0};var o=function(n){return Number(e.replace(t,"$"+n))};return U(o(1),o(2))},W=function(){return U(0,0)},U=function(n,e){return{major:n,minor:e}},G={nu:U,detect:function(n,e){var t=String(e).toLowerCase();return 0===n.length?W():P(n,t)},unknown:W},$="Firefox",q=function(n,e){return function(){return e===n}},_=function(n){var e=n.current;return{current:e,version:n.version,isEdge:q("Edge",e),isChrome:q("Chrome",e),isIE:q("IE",e),isOpera:q("Opera",e),isFirefox:q($,e),isSafari:q("Safari",e)}},K={unknown:function(){return _({current:undefined,version:G.unknown()})},nu:_,edge:O.constant("Edge"),chrome:O.constant("Chrome"),ie:O.constant("IE"),opera:O.constant("Opera"),firefox:O.constant($),safari:O.constant("Safari")},X="Windows",Y="Android",J="Solaris",Q="FreeBSD",Z=function(n,e){return function(){return e===n}},nn=function(n){var e=n.current;return{current:e,version:n.version,isWindows:Z(X,e),isiOS:Z("iOS",e),isAndroid:Z(Y,e),isOSX:Z("OSX",e),isLinux:Z("Linux",e),isSolaris:Z(J,e),isFreeBSD:Z(Q,e)}},en={unknown:function(){return nn({current:undefined,version:G.unknown()})},nu:nn,windows:O.constant(X),ios:O.constant("iOS"),android:O.constant(Y),linux:O.constant("Linux"),osx:O.constant("OSX"),solaris:O.constant(J),freebsd:O.constant(Q)},tn=(i=Array.prototype.indexOf)===undefined?function(n,e){return fn(n,e)}:function(n,e){return i.call(n,e)},on=function(n,e){return-1<tn(n,e)},rn=function(n,e){for(var t=n.length,o=new Array(t),r=0;r<t;r++){var i=n[r];o[r]=e(i,r,n)}return o},un=function(n,e){for(var t=0,o=n.length;t<o;t++)e(n[t],t,n)},an=function(n,e){for(var t=n.length-1;0<=t;t--)e(n[t],t,n)},cn=function(n,e){for(var t=[],o=0,r=n.length;o<r;o++){var i=n[o];e(i,o,n)&&t.push(i)}return t},sn=function(n,e){for(var t=0,o=n.length;t<o;t++)if(e(n[t],t,n))return y.some(t);return y.none()},fn=function(n,e){for(var t=0,o=n.length;t<o;++t)if(n[t]===e)return t;return-1},ln=Array.prototype.push,dn=function(n){for(var e=[],t=0,o=n.length;t<o;++t){if(!Array.prototype.isPrototypeOf(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);ln.apply(e,n[t])}return e},mn=function(n,e){for(var t=0,o=n.length;t<o;++t)if(!0!==e(n[t],t,n))return!1;return!0},gn=Array.prototype.slice,pn={map:rn,each:un,eachr:an,partition:function(n,e){for(var t=[],o=[],r=0,i=n.length;r<i;r++){var u=n[r];(e(u,r,n)?t:o).push(u)}return{pass:t,fail:o}},filter:cn,groupBy:function(n,e){if(0===n.length)return[];for(var t=e(n[0]),o=[],r=[],i=0,u=n.length;i<u;i++){var a=n[i],c=e(a);c!==t&&(o.push(r),r=[]),t=c,r.push(a)}return 0!==r.length&&o.push(r),o},indexOf:function(n,e){var t=tn(n,e);return-1===t?y.none():y.some(t)},foldr:function(n,e,t){return an(n,function(n){t=e(t,n)}),t},foldl:function(n,e,t){return un(n,function(n){t=e(t,n)}),t},find:function(n,e){for(var t=0,o=n.length;t<o;t++){var r=n[t];if(e(r,t,n))return y.some(r)}return y.none()},findIndex:sn,flatten:dn,bind:function(n,e){var t=rn(n,e);return dn(t)},forall:mn,exists:function(n,e){return sn(n,e).isSome()},contains:on,equal:function(n,t){return n.length===t.length&&mn(n,function(n,e){return n===t[e]})},reverse:function(n){var e=gn.call(n,0);return e.reverse(),e},chunk:function(n,e){for(var t=[],o=0;o<n.length;o+=e){var r=n.slice(o,o+e);t.push(r)}return t},difference:function(n,e){return cn(n,function(n){return!on(e,n)})},mapToObject:function(n,e){for(var t={},o=0,r=n.length;o<r;o++){var i=n[o];t[String(i)]=e(i,o)}return t},pure:function(n){return[n]},sort:function(n,e){var t=gn.call(n,0);return t.sort(e),t},range:function(n,e){for(var t=[],o=0;o<n;o++)t.push(e(o));return t},head:function(n){return 0===n.length?y.none():y.some(n[0])},last:function(n){return 0===n.length?y.none():y.some(n[n.length-1])}},vn=function(n,e){var t=String(e).toLowerCase();return pn.find(n,function(n){return n.search(t)})},hn=function(n,t){return vn(n,t).map(function(n){var e=G.detect(n.versionRegexes,t);return{current:n.name,version:e}})},bn=function(n,t){return vn(n,t).map(function(n){var e=G.detect(n.versionRegexes,t);return{current:n.name,version:e}})},yn=function(n,r){return n.replace(/\${([^{}]*)}/g,function(n,e){var t,o=r[e];return"string"==(t=typeof o)||"number"===t?o:n})},wn=function(n,e){return-1!==n.indexOf(e)},xn=function(n){return n.replace(/^\s+|\s+$/g,"")},Sn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Tn=function(e){return function(n){return wn(n,e)}},kn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return wn(n,"edge/")&&wn(n,"chrome")&&wn(n,"safari")&&wn(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Sn],search:function(n){return wn(n,"chrome")&&!wn(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return wn(n,"msie")||wn(n,"trident")}},{name:"Opera",versionRegexes:[Sn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Tn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Tn("firefox")},{name:"Safari",versionRegexes:[Sn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(wn(n,"safari")||wn(n,"mobile/"))&&wn(n,"applewebkit")}}],Cn=[{name:"Windows",search:Tn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return wn(n,"iphone")||wn(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Tn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Tn("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Tn("linux"),versionRegexes:[]},{name:"Solaris",search:Tn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Tn("freebsd"),versionRegexes:[]}],On={browsers:O.constant(kn),oses:O.constant(Cn)},En=function(n){var e,t,o,r,i,u,a,c,s,f,l,d=On.browsers(),m=On.oses(),g=hn(d,n).fold(K.unknown,K.nu),p=bn(m,n).fold(en.unknown,en.nu);return{browser:g,os:p,deviceType:(t=g,o=n,r=(e=p).isiOS()&&!0===/ipad/i.test(o),i=e.isiOS()&&!r,u=e.isAndroid()&&3===e.version.major,a=e.isAndroid()&&4===e.version.major,c=r||u||a&&!0===/mobile/i.test(o),s=e.isiOS()||e.isAndroid(),f=s&&!c,l=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),{isiPad:O.constant(r),isiPhone:O.constant(i),isTablet:O.constant(c),isPhone:O.constant(f),isTouch:O.constant(s),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:O.constant(l)})}},Dn={detect:L(function(){var n=navigator.userAgent;return En(n)})},Mn={tap:O.constant("alloy.tap")},An=O.constant("alloy.focus"),Bn=O.constant("alloy.blur.post"),Rn=O.constant("alloy.receive"),In=O.constant("alloy.execute"),Fn=O.constant("alloy.focus.item"),Nn=Mn.tap,Vn=Dn.detect().deviceType.isTouch()?Mn.tap:H,Hn=O.constant("alloy.longpress"),jn=(O.constant("alloy.sandbox.close"),O.constant("alloy.system.init")),zn=O.constant("alloy.system.scroll"),Ln=O.constant("alloy.system.attached"),Pn=O.constant("alloy.system.detached"),Wn=(O.constant("alloy.change.tab"),O.constant("alloy.dismiss.tab"),function(n,e){qn(n,n.element(),e,{})}),Un=function(n,e,t){qn(n,n.element(),e,t)},Gn=function(n){Wn(n,In())},$n=function(n,e,t){qn(n,e,t,{})},qn=function(n,e,t,o){var r=D.deepMerge({target:e},o);n.getSystem().triggerEvent(t,e,M.map(r,O.constant))},_n=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:O.constant(n)}},Kn={fromHtml:function(n,e){var t=(e||document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw console.error("HTML does not have a single root node",n),"HTML must have a single root node";return _n(t.childNodes[0])},fromTag:function(n,e){var t=(e||document).createElement(n);return _n(t)},fromText:function(n,e){var t=(e||document).createTextNode(n);return _n(t)},fromDom:_n,fromPoint:function(n,e,t){return y.from(n.dom().elementFromPoint(e,t)).map(_n)}},Xn=8,Yn=9,Jn=1,Qn=3,Zn=function(n){return n.dom().nodeName.toLowerCase()},ne=function(n){return n.dom().nodeType},ee=function(e){return function(n){return ne(n)===e}},te=ee(Jn),oe=ee(Qn),re=ee(Yn),ie={name:Zn,type:ne,value:function(n){return n.dom().nodeValue},isElement:te,isText:oe,isDocument:re,isComment:function(n){return ne(n)===Xn||"#comment"===Zn(n)}},ue=L(function(){return ae(Kn.fromDom(document))}),ae=function(n){var e=n.dom().body;if(null===e||e===undefined)throw"Body is not available yet";return Kn.fromDom(e)},ce={body:ue,getBody:ae,inBody:function(n){var e=ie.isText(n)?n.dom().parentNode:n.dom();return e!==undefined&&null!==e&&e.ownerDocument.body.contains(e)}},se=function(n){return n.slice(0).sort()},fe={sort:se,reqMessage:function(n,e){throw new Error("All required keys ("+se(n).join(", ")+") were not specified. Specified keys were: "+se(e).join(", ")+".")},unsuppMessage:function(n){throw new Error("Unsupported keys for object: "+se(n).join(", "))},validateStrArr:function(e,n){if(!E.isArray(n))throw new Error("The "+e+" fields must be an array. Was: "+n+".");pn.each(n,function(n){if(!E.isString(n))throw new Error("The value "+n+" in the "+e+" fields was not a string.")})},invalidTypeMessage:function(n,e){throw new Error("All values need to be of type: "+e+". Keys ("+se(n).join(", ")+") were not.")},checkDupes:function(n){var t=se(n);pn.find(t,function(n,e){return e<t.length-1&&n===t[e+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+t.join(", ")+"].")})}},le={immutable:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(e.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+e.length+']", got '+t.length+" arguments");var o={};return pn.each(e,function(n,e){o[n]=O.constant(t[e])}),o}},immutableBag:function(r,i){var u=r.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return fe.validateStrArr("required",r),fe.validateStrArr("optional",i),fe.checkDupes(u),function(e){var t=M.keys(e);pn.forall(r,function(n){return pn.contains(t,n)})||fe.reqMessage(r,t);var n=pn.filter(t,function(n){return!pn.contains(u,n)});0<n.length&&fe.unsuppMessage(n);var o={};return pn.each(r,function(n){o[n]=O.constant(e[n])}),pn.each(i,function(n){o[n]=O.constant(Object.prototype.hasOwnProperty.call(e,n)?y.some(e[n]):y.none())}),o}}},de=function(n,e){for(var t=[],o=function(n){return t.push(n),e(n)},r=e(n);(r=r.bind(o)).isSome(););return t},me="undefined"!=typeof window?window:Function("return this;")(),ge=function(n,e){for(var t=e!==undefined&&null!==e?e:me,o=0;o<n.length&&t!==undefined&&null!==t;++o)t=t[n[o]];return t},pe=function(n,e){var t=n.split(".");return ge(t,e)},ve={getOrDie:function(n,e){var t=pe(n,e);if(t===undefined||null===t)throw n+" not available on this browser";return t}},he=Jn,be=Yn,ye=function(n){return n.nodeType!==he&&n.nodeType!==be||0===n.childElementCount},we={all:function(n,e){var t=e===undefined?document:e.dom();return ye(t)?[]:pn.map(t.querySelectorAll(n),Kn.fromDom)},is:function(n,e){var t=n.dom();if(t.nodeType!==he)return!1;if(t.matches!==undefined)return t.matches(e);if(t.msMatchesSelector!==undefined)return t.msMatchesSelector(e);if(t.webkitMatchesSelector!==undefined)return t.webkitMatchesSelector(e);if(t.mozMatchesSelector!==undefined)return t.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")},one:function(n,e){var t=e===undefined?document:e.dom();return ye(t)?y.none():y.from(t.querySelector(n)).map(Kn.fromDom)}},xe=function(n,e){return n.dom()===e.dom()},Se=(Dn.detect().browser.isIE(),xe),Te=function(n){return Kn.fromDom(n.dom().ownerDocument)},ke=function(n){var e=n.dom();return y.from(e.parentNode).map(Kn.fromDom)},Ce=function(n){var e=n.dom();return y.from(e.previousSibling).map(Kn.fromDom)},Oe=function(n){var e=n.dom();return y.from(e.nextSibling).map(Kn.fromDom)},Ee=function(n){var e=n.dom();return pn.map(e.childNodes,Kn.fromDom)},De=function(n,e){var t=n.dom().childNodes;return y.from(t[e]).map(Kn.fromDom)},Me=le.immutable("element","offset"),Ae={owner:Te,defaultView:function(n){var e=n.dom().ownerDocument.defaultView;return Kn.fromDom(e)},documentElement:function(n){var e=Te(n);return Kn.fromDom(e.dom().documentElement)},parent:ke,findIndex:function(t){return ke(t).bind(function(n){var e=Ee(n);return pn.findIndex(e,function(n){return Se(t,n)})})},parents:function(n,e){for(var t=E.isFunction(e)?e:O.constant(!1),o=n.dom(),r=[];null!==o.parentNode&&o.parentNode!==undefined;){var i=o.parentNode,u=Kn.fromDom(i);if(r.push(u),!0===t(u))break;o=i}return r},siblings:function(e){return ke(e).map(Ee).map(function(n){return pn.filter(n,function(n){return!Se(e,n)})}).getOr([])},prevSibling:Ce,offsetParent:function(n){var e=n.dom();return y.from(e.offsetParent).map(Kn.fromDom)},prevSiblings:function(n){return pn.reverse(de(n,Ce))},nextSibling:Oe,nextSiblings:function(n){return de(n,Oe)},children:Ee,child:De,firstChild:function(n){return De(n,0)},lastChild:function(n){return De(n,n.dom().childNodes.length-1)},childNodesCount:function(n){return n.dom().childNodes.length},hasChildNodes:function(n){return n.dom().hasChildNodes()},leaf:function(n,e){var t=Ee(n);return 0<t.length&&e<t.length?Me(t[e],0):Me(n,e)}},Be=function(e,t){Ae.parent(e).each(function(n){n.dom().insertBefore(t.dom(),e.dom())})},Re=function(n,e){n.dom().appendChild(e.dom())},Ie={before:Be,after:function(n,e){Ae.nextSibling(n).fold(function(){Ae.parent(n).each(function(n){Re(n,e)})},function(n){Be(n,e)})},prepend:function(e,t){Ae.firstChild(e).fold(function(){Re(e,t)},function(n){e.dom().insertBefore(t.dom(),n.dom())})},append:Re,appendAt:function(n,e,t){Ae.child(n,t).fold(function(){Re(n,e)},function(n){Be(n,e)})},wrap:function(n,e){Be(n,e),Re(e,n)}},Fe={before:function(e,n){pn.each(n,function(n){Ie.before(e,n)})},after:function(o,r){pn.each(r,function(n,e){var t=0===e?o:r[e-1];Ie.after(t,n)})},prepend:function(e,n){pn.each(n.slice().reverse(),function(n){Ie.prepend(e,n)})},append:function(e,n){pn.each(n,function(n){Ie.append(e,n)})}},Ne=function(n){var e=n.dom();null!==e.parentNode&&e.parentNode.removeChild(e)},Ve={empty:function(n){n.dom().textContent="",pn.each(Ae.children(n),function(n){Ne(n)})},remove:Ne,unwrap:function(n){var e=Ae.children(n);0<e.length&&Fe.before(n,e),Ne(n)}},He=function(n){Wn(n,Pn());var e=n.components();pn.each(e,He)},je=function(n){var e=n.components();pn.each(e,je),Wn(n,Ln())},ze=function(n,e){Le(n,e,Ie.append)},Le=function(n,e,t){n.getSystem().addToWorld(e),t(n.element(),e.element()),ce.inBody(n.element())&&je(e),n.syncComponents()},Pe=function(n){He(n),Ve.remove(n.element()),n.getSystem().removeFromWorld(n)},We=function(e){var n=Ae.parent(e.element()).bind(function(n){return e.getSystem().getByDom(n).fold(y.none,y.some)});Pe(e),n.each(function(n){n.syncComponents()})},Ue=function(t){return{is:function(n){return t===n},isValue:O.always,isError:O.never,getOr:O.constant(t),getOrThunk:O.constant(t),getOrDie:O.constant(t),or:function(n){return Ue(t)},orThunk:function(n){return Ue(t)},fold:function(n,e){return e(t)},map:function(n){return Ue(n(t))},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOption:function(){return y.some(t)}}},Ge=function(t){return{is:O.never,isValue:O.never,isError:O.always,getOr:O.identity,getOrThunk:function(n){return n()},getOrDie:function(){return O.die(String(t))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return Ge(t)},each:O.noop,bind:function(n){return Ge(t)},exists:O.never,forall:O.always,toOption:y.none}},$e={value:Ue,error:Ge},qe=function(u){if(!E.isArray(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],t={};return pn.each(u,function(n,o){var e=M.keys(n);if(1!==e.length)throw new Error("one and only one name per case");var r=e[0],i=n[r];if(t[r]!==undefined)throw new Error("duplicate key detected:"+r);if("cata"===r)throw new Error("cannot have a case named cata (sorry)");if(!E.isArray(i))throw new Error("case arguments must be an array");a.push(r),t[r]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+r+". Expected "+i.length+" ("+i+"), got "+n);for(var t=new Array(n),e=0;e<t.length;e++)t[e]=arguments[e];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[o].apply(null,t)},match:function(n){var e=M.keys(n);if(a.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+e.join(","));if(!pn.forall(a,function(n){return pn.contains(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+a.join(", "));return n[r].apply(null,t)},log:function(n){console.log(n,{constructors:a,constructor:r,params:t})}}}}),t},_e=qe([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),Ke=function(n){return _e.defaultedThunk(O.constant(n))},Xe=_e.strict,Ye=_e.asOption,Je=_e.defaultedThunk,Qe=(_e.asDefaultedOptionThunk,_e.mergeWithThunk),Ze=(qe([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n){var e=[],t=[];return pn.each(n,function(n){n.fold(function(n){e.push(n)},function(n){t.push(n)})}),{errors:e,values:t}}),nt=function(n){return O.compose($e.error,pn.flatten)(n)},et=function(n,e){var t,o,r=Ze(n);return 0<r.errors.length?nt(r.errors):(t=r.values,o=e,$e.value(D.deepMerge.apply(undefined,[o].concat(t))))},tt=function(n){var e=Ze(n);return 0<e.errors.length?nt(e.errors):$e.value(e.values)},ot=function(e){return function(n){return n.hasOwnProperty(e)?y.from(n[e]):y.none()}},rt=function(n,e){return ot(e)(n)},it=function(n,e){var t={};return t[n]=e,t},ut=function(n,e){return t=n,o=e,r={},pn.each(o,function(n){t[n]!==undefined&&t.hasOwnProperty(n)&&(r[n]=t[n])}),r;var t,o,r},at=function(n,e){return t=n,o=e,r={},M.each(t,function(n,e){pn.contains(o,e)||(r[e]=n)}),r;var t,o,r},ct=function(n){return ot(n)},st=function(n,e){return t=n,o=e,function(n){return ot(t)(n).getOr(o)};var t,o},ft=function(n,e){return rt(n,e)},lt=function(n,e){return it(n,e)},dt=function(n){return e=n,t={},pn.each(e,function(n){t[n.key]=n.value}),t;var e,t},mt=function(n,e){return et(n,e)},gt=function(n,e){return o=e,(t=n).hasOwnProperty(o)&&t[o]!==undefined&&null!==t[o];var t,o},pt=qe([{setOf:["validator","valueType"]},{arrOf:["valueType"]},{objOf:["fields"]},{itemOf:["validator"]},{choiceOf:["key","branches"]},{thunk:["description"]},{func:["args","outputSchema"]}]),vt=qe([{field:["name","presence","type"]},{state:["name"]}]),ht=function(){return ve.getOrDie("JSON")},bt=function(n,e,t){return ht().stringify(n,e,t)},yt=function(n){return E.isObject(n)&&100<M.keys(n).length?" removed due to size":bt(n,null,2)},wt=function(n,e){return $e.error([{path:n,getErrorInfo:e}])},xt=qe([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),St=function(t,o,r){return rt(o,r).fold(function(){return n=r,e=o,wt(t,function(){return'Could not find valid *strict* value for "'+n+'" in '+yt(e)});var n,e},$e.value)},Tt=function(n,e,t){var o=rt(n,e).fold(function(){return t(n)},O.identity);return $e.value(o)},kt=function(r,a,n,c){return n.fold(function(i,e,n,t){var o=function(n){return t.extract(r.concat([i]),c,n).map(function(n){return it(e,c(n))})},u=function(n){return n.fold(function(){var n=it(e,c(y.none()));return $e.value(n)},function(n){return t.extract(r.concat([i]),c,n).map(function(n){return it(e,c(y.some(n)))})})};return n.fold(function(){return St(r,a,i).bind(o)},function(n){return Tt(a,i,n).bind(o)},function(){return(n=a,e=i,$e.value(rt(n,e))).bind(u);var n,e},function(n){return(e=a,t=i,o=n,r=rt(e,t).map(function(n){return!0===n?o(e):n}),$e.value(r)).bind(u);var e,t,o,r},function(n){var e=n(a);return Tt(a,i,O.constant({})).map(function(n){return D.deepMerge(e,n)}).bind(o)})},function(n,e){var t=e(a);return $e.value(it(n,c(t)))})},Ct=function(o){return{extract:function(t,n,e){return o(e,n).fold(function(n){return e=n,wt(t,function(){return e});var e},$e.value)},toString:function(){return"val"},toDsl:function(){return pt.itemOf(o)}}},Ot=function(n){var c=Et(n),s=pn.foldr(n,function(e,n){return n.fold(function(n){return D.deepMerge(e,lt(n,!0))},O.constant(e))},{});return{extract:function(n,e,t){var o,r,i,u=E.isBoolean(t)?[]:(o=t,r=M.keys(o),pn.filter(r,function(n){return gt(o,n)})),a=pn.filter(u,function(n){return!gt(s,n)});return 0===a.length?c.extract(n,e,t):(i=a,wt(n,function(){return"There are unsupported fields: ["+i.join(", ")+"] specified"}))},toString:c.toString,toDsl:c.toDsl}},Et=function(c){return{extract:function(n,e,t){return o=n,r=t,i=c,u=e,a=pn.map(i,function(n){return kt(o,r,n,u)}),et(a,{});var o,r,i,u,a},toString:function(){return"obj{\n"+pn.map(c,function(n){return n.fold(function(n,e,t,o){return n+" -> "+o.toString()},function(n,e){return"state("+n+")"})}).join("\n")+"}"},toDsl:function(){return pt.objOf(pn.map(c,function(n){return n.fold(function(n,e,t,o){return vt.field(n,t,o)},function(n,e){return vt.state(n)})}))}}},Dt=function(r){return{extract:function(t,o,n){var e=pn.map(n,function(n,e){return r.extract(t.concat(["["+e+"]"]),o,n)});return tt(e)},toString:function(){return"array("+r.toString()+")"},toDsl:function(){return pt.arrOf(r)}}},Mt=function(u,a){return{extract:function(t,o,r){var n,e,i=M.keys(r);return(n=t,e=i,Dt(Ct(u)).extract(n,O.identity,e)).bind(function(n){var e=pn.map(n,function(n){return xt.field(n,n,Xe(),a)});return Et(e).extract(t,o,r)})},toString:function(){return"setOf("+a.toString()+")"},toDsl:function(){return pt.setOf(u,a)}}},At=O.constant(Ct($e.value)),Bt=(O.compose(Dt,Et),xt.state),Rt=xt.field,It=function(n){return Rt(n,n,Xe(),At())},Ft=function(n,e){return Rt(n,n,Xe(),e)},Nt=function(n){return Rt(n,n,Xe(),Ct(function(n){return E.isFunction(n)?$e.value(n):$e.error("Not a function")}))},Vt=function(n,e){return Rt(n,n,Xe(),Et(e))},Ht=function(n){return Rt(n,n,Ye(),At())},jt=function(n,e){return Rt(n,n,Ye(),Et(e))},zt=function(n,e){return Rt(n,n,Ye(),Ot(e))},Lt=function(n,e){return Rt(n,n,Ke(e),At())},Pt=function(n,e,t){return Rt(n,n,Ke(e),t)},Wt=function(n,e){return Bt(n,e)},Ut=function(t,e,o,r,i){return ft(r,i).fold(function(){return n=r,e=i,wt(t,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+yt(n)});var n,e},function(n){return Et(n).extract(t.concat(["branch: "+i]),e,o)})},Gt=function(r,i){return{extract:function(e,t,o){return ft(o,r).fold(function(){return n=r,wt(e,function(){return'Choice schema did not contain choice key: "'+n+'"'});var n},function(n){return Ut(e,t,o,i,n)})},toString:function(){return"chooseOn("+r+"). Possible values: "+M.keys(i)},toDsl:function(){return pt.choiceOf(r,i)}}},$t=Ct($e.value),qt=function(n,e,t,o){return e.extract([n],t,o).fold(function(n){return $e.error({input:o,errors:n})},$e.value)},_t=function(n,e,t){return qt(n,e,O.constant,t)},Kt=function(n){return n.fold(function(n){throw new Error(Jt(n))},O.identity)},Xt=function(n,e,t){return Kt((o=t,qt(n,e,O.identity,o)));var o},Yt=function(n,e,t){return Kt(_t(n,e,t))},Jt=function(n){return"Errors: \n"+(e=n.errors,t=10<e.length?e.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):e,pn.map(t,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()}))+"\n\nInput object: "+yt(n.input);var e,t},Qt=function(n,e){return Gt(n,e)},Zt=O.constant($t),no=function(n){if(!gt(n,"can")&&!gt(n,"abort")&&!gt(n,"run"))throw new Error("EventHandler defined by: "+bt(n,null,2)+" does not have can, abort, or run!");return Xt("Extracting event.handler",Ot([Lt("can",O.constant(!0)),Lt("abort",O.constant(!1)),Lt("run",O.noop)]),n)},eo=function(n){var e,o,r,i,t=(e=n,o=function(n){return n.can},function(){var t=Array.prototype.slice.call(arguments,0);return pn.foldl(e,function(n,e){return n&&o(e).apply(undefined,t)},!0)}),u=(r=n,i=function(n){return n.abort},function(){var t=Array.prototype.slice.call(arguments,0);return pn.foldl(r,function(n,e){return n||i(e).apply(undefined,t)},!1)});return no({can:t,abort:u,run:function(){var e=Array.prototype.slice.call(arguments,0);pn.each(n,function(n){n.run.apply(undefined,e)})}})},to=dt,oo=function(n,e){return{key:n,value:no({abort:e})}},ro=function(n,e){return{key:n,value:no({run:e})}},io=function(n,e,t){return{key:n,value:no({run:function(n){e.apply(undefined,[n].concat(t))}})}},uo=function(n){return function(o){return{key:n,value:no({run:function(n,e){var t;t=e,Se(n.element(),t.event().target())&&o(n,e)}})}}},ao=function(n,e,t){var u,o,r=e.partUids()[t];return o=r,ro(u=n,function(n,i){n.getSystem().getByUid(o).each(function(n){var e,t,o,r;t=(e=n).element(),o=u,r=i,e.getSystem().triggerEvent(o,t,r.event())})})},co=function(n){return ro(n,function(n,e){e.cut()})},so=uo(Ln()),fo=uo(Pn()),lo=uo(jn()),mo=(u=In(),function(n){return ro(u,n)}),go=function(n,e){return n},po=le.immutableBag(["tag"],["classes","attributes","styles","value","innerHtml","domChildren","defChildren"]),vo=function(n){return{tag:n.tag(),classes:n.classes().getOr([]),attributes:n.attributes().getOr({}),styles:n.styles().getOr({}),value:n.value().getOr("<none>"),innerHtml:n.innerHtml().getOr("<none>"),defChildren:n.defChildren().getOr("<none>"),domChildren:n.domChildren().fold(function(){return"<none>"},function(n){return 0===n.length?"0 children, but still specified":String(n.length)})}},ho=le.immutableBag([],["classes","attributes","styles","value","innerHtml","defChildren","domChildren"]),bo=function(e,n,t){return n.fold(function(){return t.fold(function(){return{}},function(n){return lt(e,n)})},function(n){return t.fold(function(){return lt(e,n)},function(n){return lt(e,n)})})},yo=function(t,o,r){return lo(function(n,e){r(n,t,o)})},wo=function(n,e,t,o,r,i){var u,a,c=n,s=jt(e,[(u="config",a=n,Rt(u,u,Ye(),a))]);return So(c,s,e,t,o,r,i)},xo=function(n){return{key:n,value:undefined}},So=function(t,n,o,r,e,i,u){var a=function(n){return gt(n,o)?n[o]():y.none()},c=M.map(e,function(n,e){return r=o,i=n,u=e,function(t){var o=arguments;return t.config({name:O.constant(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+u)},function(n){var e=Array.prototype.slice.call(o,1);return i.apply(undefined,[t,n.config,n.state].concat(e))})};var r,i,u}),s=M.map(i,function(n,e){return go(n)}),f=D.deepMerge(s,c,{revoke:O.curry(xo,o),config:function(n){var e=Yt(o+"-config",t,n);return{key:o,value:{config:e,me:f,configAsRaw:L(function(){return Xt(o+"-config",t,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,t){return a(n).bind(function(e){return ft(r,"exhibit").map(function(n){return n(t,e.config,e.state)})}).getOr(ho({}))},name:function(){return o},handlers:function(n){return a(n).bind(function(e){return ft(r,"events").map(function(n){return n(e.config,e.state)})}).getOr({})}});return f},To=function(n,e){return ko(n,e,{validate:E.isFunction,label:"function"})},ko=function(o,r,i){if(0===r.length)throw new Error("You must specify at least one required field.");return fe.validateStrArr("required",r),fe.checkDupes(r),function(e){var t=M.keys(e);pn.forall(r,function(n){return pn.contains(t,n)})||fe.reqMessage(r,t),o(r,t);var n=pn.filter(r,function(n){return!i.validate(e[n],n)});return 0<n.length&&fe.invalidTypeMessage(n,i.label),e}},Co=O.noop,Oo={exactly:O.curry(To,function(e,n){var t=pn.filter(n,function(n){return!pn.contains(e,n)});0<t.length&&fe.unsuppMessage(t)}),ensure:O.curry(To,Co),ensureWith:O.curry(ko,Co)},Eo=Oo.ensure(["readState"]),Do=function(){return Eo({readState:function(){return"No State required"}})},Mo=Object.freeze({init:Do}),Ao=function(n){return dt(n)},Bo=Ot([It("fields"),It("name"),Lt("active",{}),Lt("apis",{}),Lt("extra",{}),Lt("state",Mo)]),Ro=function(n){var e,t,o,r,i,u,a,c,s=Xt("Creating behaviour: "+n.name,Bo,n);return e=s.fields,t=s.name,o=s.active,r=s.apis,i=s.extra,u=s.state,a=Ot(e),c=jt(t,[zt("config",e)]),So(a,c,t,o,r,i,u)},Io=Ot([It("branchKey"),It("branches"),It("name"),Lt("active",{}),Lt("apis",{}),Lt("extra",{}),Lt("state",Mo)]),Fo=(O.constant(undefined),O.constant({}),O.constant({}),O.constant({}),O.constant(Mo));function No(n,e,t){var o=t||!1,r=function(){e(),o=!0},i=function(){n(),o=!1};return{on:r,off:i,toggle:function(){(o?i:r)()},isOn:function(){return o}}}var Vo=function(n,e,t){if(!(E.isString(t)||E.isBoolean(t)||E.isNumber(t)))throw console.error("Invalid call to Attr.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")},Ho=function(n,e,t){Vo(n.dom(),e,t)},jo=function(n,e){var t=n.dom().getAttribute(e);return null===t?undefined:t},zo=function(n,e){var t=n.dom();return!(!t||!t.hasAttribute)&&t.hasAttribute(e)},Lo={clone:function(n){return pn.foldl(n.dom().attributes,function(n,e){return n[e.name]=e.value,n},{})},set:Ho,setAll:function(n,e){var t=n.dom();M.each(e,function(n,e){Vo(t,e,n)})},get:jo,has:zo,remove:function(n,e){n.dom().removeAttribute(e)},hasNone:function(n){var e=n.dom().attributes;return e===undefined||null===e||0===e.length},transfer:function(r,i,n){ie.isElement(r)&&ie.isElement(i)&&pn.each(n,function(n){var e,t,o;t=i,zo(e=r,o=n)&&!zo(t,o)&&Ho(t,o,jo(e,o))})}},Po=function(n,e){var t=Lo.get(n,e);return t===undefined||""===t?[]:t.split(" ")},Wo=Po,Uo=function(n,e,t){var o=Po(n,e).concat([t]);Lo.set(n,e,o.join(" "))},Go=function(n,e,t){var o=pn.filter(Po(n,e),function(n){return n!==t});0<o.length?Lo.set(n,e,o.join(" ")):Lo.remove(n,e)},$o=function(n){return Wo(n,"class")},qo=function(n,e){return Uo(n,"class",e)},_o=function(n,e){return Go(n,"class",e)},Ko=$o,Xo=qo,Yo=_o,Jo=function(n,e){pn.contains($o(n),e)?_o(n,e):qo(n,e)},Qo=function(n){return n.dom().classList!==undefined},Zo=function(n,e){return Qo(n)&&n.dom().classList.contains(e)},nr={add:function(n,e){Qo(n)?n.dom().classList.add(e):Xo(n,e)},remove:function(n,e){var t;Qo(n)?n.dom().classList.remove(e):Yo(n,e),0===(Qo(t=n)?t.dom().classList:Ko(t)).length&&Lo.remove(t,"class")},toggle:function(n,e){return Qo(n)?n.dom().classList.toggle(e):Jo(n,e)},toggler:function(n,e){var t=Qo(n),o=n.dom().classList;return No(function(){t?o.remove(e):Yo(n,e)},function(){t?o.add(e):Xo(n,e)},Zo(n,e))},has:Zo},er=function(n,e,t){nr.remove(n,t),nr.add(n,e)},tr=Object.freeze({toAlpha:function(n,e,t){er(n.element(),e.alpha(),e.omega())},toOmega:function(n,e,t){er(n.element(),e.omega(),e.alpha())},isAlpha:function(n,e,t){return nr.has(n.element(),e.alpha())},isOmega:function(n,e,t){return nr.has(n.element(),e.omega())},clear:function(n,e,t){nr.remove(n.element(),e.alpha()),nr.remove(n.element(),e.omega())}}),or=[It("alpha"),It("omega")],rr=Ro({fields:or,name:"swapping",apis:tr}),ir=function(n){var e=n,t=function(){return e};return{get:t,set:function(n){e=n},clone:function(){return ir(t())}}};function ur(n,e,t,o,r){return n(t,o)?y.some(t):E.isFunction(r)&&r(t)?y.none():e(t,o,r)}var ar=function(n,e,t){for(var o=n.dom(),r=E.isFunction(t)?t:O.constant(!1);o.parentNode;){o=o.parentNode;var i=Kn.fromDom(o);if(e(i))return y.some(i);if(r(i))break}return y.none()},cr=function(n,e){return pn.find(n.dom().childNodes,O.compose(e,Kn.fromDom)).map(Kn.fromDom)},sr=function(n,o){var r=function(n){for(var e=0;e<n.childNodes.length;e++){if(o(Kn.fromDom(n.childNodes[e])))return y.some(Kn.fromDom(n.childNodes[e]));var t=r(n.childNodes[e]);if(t.isSome())return t}return y.none()};return r(n.dom())},fr={first:function(n){return sr(ce.body(),n)},ancestor:ar,closest:function(n,e,t){return ur(function(n){return e(n)},ar,n,e,t)},sibling:function(e,t){var n=e.dom();return n.parentNode?cr(Kn.fromDom(n.parentNode),function(n){return!Se(e,n)&&t(n)}):y.none()},child:cr,descendant:sr},lr=function(n){n.dom().focus()},dr=function(n){var e=n!==undefined?n.dom():document;return y.from(e.activeElement).map(Kn.fromDom)},mr=function(n){var e=Ae.owner(n).dom();return n.dom()===e.activeElement},gr=lr,pr=function(n){n.dom().blur()},vr=dr,hr=function(e){return dr(Ae.owner(e)).filter(function(n){return e.dom().contains(n.dom())})},br=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),yr=tinymce.util.Tools.resolve("tinymce.ThemeManager"),wr=function(n){var e=document.createElement("a");e.target="_blank",e.href=n.href,e.rel="noreferrer noopener";var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),document.body.appendChild(e),e.dispatchEvent(t),document.body.removeChild(e)},xr={formatChanged:O.constant("formatChanged"),orientationChanged:O.constant("orientationChanged"),dropupDismissed:O.constant("dropupDismissed")},Sr=function(n,e){var t=(e||document).createElement("div");return t.innerHTML=n,Ae.children(Kn.fromDom(t))},Tr=function(n){return n.dom().innerHTML},kr=Tr,Cr=function(n,e){var t=Ae.owner(n).dom(),o=Kn.fromDom(t.createDocumentFragment()),r=Sr(e,t);Fe.append(o,r),Ve.empty(n),Ie.append(n,o)},Or=function(n){var e=Kn.fromTag("div"),t=Kn.fromDom(n.dom().cloneNode(!0));return Ie.append(e,t),Tr(e)},Er=function(n,e){return Kn.fromDom(n.dom().cloneNode(e))},Dr=function(n){return Er(n,!1)},Mr=function(n){return e=Dr(n),Or(e);var e},Ar=Object.freeze({events:function(a){return to([ro(Rn(),function(r,i){var n,e,u=a.channels(),t=M.keys(u),o=(n=t,(e=i).universal()?n:pn.filter(n,function(n){return pn.contains(e.channels(),n)}));pn.each(o,function(n){var e=u[n](),t=e.schema(),o=Yt("channel["+n+"] data\nReceiver: "+Mr(r.element()),t,i.data());e.onReceive()(r,o)})})])}}),Br=function(n){for(var e=[],t=function(n){e.push(n)},o=0;o<n.length;o++)n[o].each(t);return e},Rr=function(n,e){for(var t=0;t<n.length;t++){var o=e(n[t],t);if(o.isSome())return o}return y.none()},Ir="unknown",Fr=[],Nr=["alloy/data/Fields","alloy/debugging/Debugging"],Vr={logEventCut:O.noop,logEventStopped:O.noop,logNoParent:O.noop,logEventNoHandlers:O.noop,logEventResponse:O.noop,write:O.noop},Hr=function(n,e,t){var o,r="*"===Fr||pn.contains(Fr,n)?(o=[],{logEventCut:function(n,e,t){o.push({outcome:"cut",target:e,purpose:t})},logEventStopped:function(n,e,t){o.push({outcome:"stopped",target:e,purpose:t})},logNoParent:function(n,e,t){o.push({outcome:"no-parent",target:e,purpose:t})},logEventNoHandlers:function(n,e){o.push({outcome:"no-handlers-left",target:e})},logEventResponse:function(n,e,t){o.push({outcome:"response",purpose:t,target:e})},write:function(){pn.contains(["mousemove","mouseover","mouseout",jn()],n)||console.log(n,{event:n,target:e.dom(),sequence:pn.map(o,function(n){return pn.contains(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+Mr(n.target)+")":n.outcome})})}}):Vr,i=t(r);return r.write(),i},jr=(O.constant(Vr),O.constant(!0),O.constant([It("menu"),It("selectedMenu")])),zr=O.constant([It("item"),It("selectedItem")]),Lr=(O.constant(Ot(zr().concat(jr()))),O.constant(Ot(zr()))),Pr=Vt("initSize",[It("numColumns"),It("numRows")]),Wr=function(n,e,t){var o;return function(){var n=new Error;if(n.stack!==undefined){var e=n.stack.split("\n");pn.find(e,function(e){return 0<e.indexOf("alloy")&&!pn.exists(Nr,function(n){return-1<e.indexOf(n)})}).getOr(Ir)}}(),Rt(e,e,t,(o=function(n){return $e.value(function(){return n.apply(undefined,arguments)})},Ct(function(n){return o(n)})))},Ur=function(n){return Wr(0,n,Ke(O.noop))},Gr=function(n){return Wr(0,n,Ke(y.none))},$r=function(n){return Wr(0,n,Xe())},qr=function(n){return Wr(0,n,Xe())},_r=function(n,e){return Wt(n,O.constant(e))},Kr=function(n){return Wt(n,O.identity)},Xr=O.constant(Pr),Yr=[Ft("channels",Mt($e.value,Ot([$r("onReceive"),Lt("schema",Zt())])))],Jr=Ro({fields:Yr,name:"receiving",active:Ar}),Qr=function(n,e){var t=ti(n,e),o=e.aria();o.update()(n,o,t)},Zr=function(n,e,t){nr.toggle(n.element(),e.toggleClass()),Qr(n,e)},ni=function(n,e,t){nr.add(n.element(),e.toggleClass()),Qr(n,e)},ei=function(n,e,t){nr.remove(n.element(),e.toggleClass()),Qr(n,e)},ti=function(n,e){return nr.has(n.element(),e.toggleClass())},oi=function(n,e,t){(e.selected()?ni:ei)(n,e,t)},ri=Object.freeze({onLoad:oi,toggle:Zr,isOn:ti,on:ni,off:ei}),ii=Object.freeze({exhibit:function(n,e,t){return ho({})},events:function(n,e){var t,o,r,i=(t=n,o=e,r=Zr,mo(function(n){r(n,t,o)})),u=yo(n,e,oi);return to(pn.flatten([n.toggleOnExecute()?[i]:[],[u]]))}}),ui=function(n,e,t){Lo.set(n.element(),"aria-expanded",t)},ai=[Lt("selected",!1),It("toggleClass"),Lt("toggleOnExecute",!0),Pt("aria",{mode:"none"},Qt("mode",{pressed:[Lt("syncWithExpanded",!1),_r("update",function(n,e,t){Lo.set(n.element(),"aria-pressed",t),e.syncWithExpanded()&&ui(n,e,t)})],checked:[_r("update",function(n,e,t){Lo.set(n.element(),"aria-checked",t)})],expanded:[_r("update",ui)],selected:[_r("update",function(n,e,t){Lo.set(n.element(),"aria-selected",t)})],none:[_r("update",O.noop)]}))],ci=Ro({fields:ai,name:"toggling",active:ii,apis:ri}),si=function(t,o){return Jr.config({channels:lt(xr.formatChanged(),{onReceive:function(n,e){e.command===t&&o(n,e.state)}})})},fi=function(n){return Jr.config({channels:lt(xr.orientationChanged(),{onReceive:n})})},li=function(n,e){return{key:n,value:{onReceive:e}}},di="tinymce-mobile",mi={resolve:function(n){return di+"-"+n},prefix:O.constant(di)},gi=function(n,e){e.ignore()||(gr(n.element()),e.onFocus()(n))},pi=Object.freeze({focus:gi,blur:function(n,e){e.ignore()||pr(n.element())},isFocused:function(n){return mr(n.element())}}),vi=Object.freeze({exhibit:function(n,e){return e.ignore()?ho({}):ho({attributes:{tabindex:"-1"}})},events:function(t){return to([ro(An(),function(n,e){gi(n,t),e.stop()})])}}),hi=[Ur("onFocus"),Lt("ignore",!1)],bi=Ro({fields:hi,name:"focusing",active:vi,apis:pi}),yi={isSupported:function(n){return n.style!==undefined}},wi=function(n,e,t){if(!E.isString(t))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);yi.isSupported(n)&&n.style.setProperty(e,t)},xi=function(n,e){yi.isSupported(n)&&n.style.removeProperty(e)},Si=function(n,e,t){var o=n.dom();wi(o,e,t)},Ti=function(n,e){return yi.isSupported(n)?n.style.getPropertyValue(e):""},ki=function(n,e){var t=n.dom(),o=Ti(t,e);return y.from(o).filter(function(n){return 0<n.length})},Ci={copy:function(n,e){var t=n.dom(),o=e.dom();yi.isSupported(t)&&yi.isSupported(o)&&(o.style.cssText=t.style.cssText)},set:Si,preserve:function(n,e){var t=Lo.get(n,"style"),o=e(n);return(t===undefined?Lo.remove:Lo.set)(n,"style",t),o},setAll:function(n,e){var t=n.dom();M.each(e,function(n,e){wi(t,e,n)})},setOptions:function(n,e){var t=n.dom();M.each(e,function(n,e){n.fold(function(){xi(t,e)},function(n){wi(t,e,n)})})},remove:function(n,e){var t=n.dom();xi(t,e),Lo.has(n,"style")&&""===xn(Lo.get(n,"style"))&&Lo.remove(n,"style")},get:function(n,e){var t=n.dom(),o=window.getComputedStyle(t).getPropertyValue(e),r=""!==o||ce.inBody(n)?o:Ti(t,e);return null===r?undefined:r},getRaw:ki,getAllRaw:function(n){var e={},t=n.dom();if(yi.isSupported(t))for(var o=0;o<t.style.length;o++){var r=t.style.item(o);e[r]=t.style[r]}return e},isValidValue:function(n,e,t){var o=Kn.fromTag(n);return Si(o,e,t),ki(o,e).isSome()},reflow:function(n){return n.dom().offsetWidth},transfer:function(o,r,n){ie.isElement(o)&&ie.isElement(r)&&pn.each(n,function(n){var e,t;e=r,ki(o,t=n).each(function(n){ki(e,t).isNone()&&Si(e,t,n)})})}};function Oi(o,r){var n=function(n){var e=r(n);if(e<=0||null===e){var t=Ci.get(n,o);return parseFloat(t)||0}return e},i=function(r,n){return pn.foldl(n,function(n,e){var t=Ci.get(r,e),o=t===undefined?0:parseInt(t,10);return isNaN(o)?n:n+o},0)};return{set:function(n,e){if(!E.isNumber(e)&&!e.match(/^[0-9]+$/))throw o+".set accepts only positive integer values. Value was "+e;var t=n.dom();yi.isSupported(t)&&(t.style[o]=e+"px")},get:n,getOuter:n,aggregate:i,max:function(n,e,t){var o=i(n,t);return o<e?e-o:0}}}var Ei,Di,Mi=Oi("height",function(n){return ce.inBody(n)?n.dom().getBoundingClientRect().height:n.dom().offsetHeight}),Ai=function(n){return Mi.get(n)},Bi=function(n,e,t){return pn.filter(Ae.parents(n,t),e)},Ri=function(n,e){return pn.filter(Ae.siblings(n),e)},Ii=function(n){return we.all(n)},Fi=function(n,e,t){return Bi(n,function(n){return we.is(n,e)},t)},Ni=function(n,e){return Ri(n,function(n){return we.is(n,e)})},Vi=function(n,e){return we.all(e,n)},Hi=function(n,e,t){return fr.ancestor(n,function(n){return we.is(n,e)},t)},ji=function(n){return we.one(n)},zi=Hi,Li=function(n,e){return we.one(e,n)},Pi=function(n,e,t){return ur(we.is,Hi,n,e,t)},Wi={BACKSPACE:O.constant([8]),TAB:O.constant([9]),ENTER:O.constant([13]),SHIFT:O.constant([16]),CTRL:O.constant([17]),ALT:O.constant([18]),CAPSLOCK:O.constant([20]),ESCAPE:O.constant([27]),SPACE:O.constant([32]),PAGEUP:O.constant([33]),PAGEDOWN:O.constant([34]),END:O.constant([35]),HOME:O.constant([36]),LEFT:O.constant([37]),UP:O.constant([38]),RIGHT:O.constant([39]),DOWN:O.constant([40]),INSERT:O.constant([45]),DEL:O.constant([46]),META:O.constant([91,93,224]),F10:O.constant([121])},Ui=function(n,e,t){var o=pn.reverse(n.slice(0,e)),r=pn.reverse(n.slice(e+1));return pn.find(o.concat(r),t)},Gi=function(n,e,t){var o=pn.reverse(n.slice(0,e));return pn.find(o,t)},$i=function(n,e,t){var o=n.slice(0,e),r=n.slice(e+1);return pn.find(r.concat(o),t)},qi=function(n,e,t){var o=n.slice(e+1);return pn.find(o,t)},_i=function(e){return function(n){return pn.contains(e,n.raw().which)}},Ki=function(n){return function(e){return pn.forall(n,function(n){return n(e)})}},Xi=function(n){return!0===n.raw().shiftKey},Yi=function(n){return!0===n.raw().ctrlKey},Ji=(O.not(Yi),O.not(Xi)),Qi=function(n,e){return{matches:n,classification:e}},Zi=function(n,e,t,o){var r=n+e;return o<r?t:r<t?o:r},nu=function(n,e,t){return n<=e?e:t<=n?t:n},eu=function(e,t,n){var o=Vi(e.element(),"."+t.highlightClass());pn.each(o,function(n){nr.remove(n,t.highlightClass()),e.getSystem().getByDom(n).each(function(n){t.onDehighlight()(e,n)})})},tu=function(n,e,t,o){var r=ou(n,e,t,o);eu(n,e),nr.add(o.element(),e.highlightClass()),r||e.onHighlight()(n,o)},ou=function(n,e,t,o){return nr.has(o.element(),e.highlightClass())},ru=function(n,e,t,o){var r=Vi(n.element(),"."+e.itemClass());return y.from(r[o]).fold(function(){return $e.error("No element found with index "+o)},n.getSystem().getByDom)},iu=function(n,e,t){return Li(n.element(),"."+e.itemClass()).bind(n.getSystem().getByDom)},uu=function(n,e,t){var o=Vi(n.element(),"."+e.itemClass());return(0<o.length?y.some(o[o.length-1]):y.none()).bind(n.getSystem().getByDom)},au=function(t,e,n,o){var r=Vi(t.element(),"."+e.itemClass());return pn.findIndex(r,function(n){return nr.has(n,e.highlightClass())}).bind(function(n){var e=Zi(n,o,0,r.length-1);return t.getSystem().getByDom(r[e])})},cu=Object.freeze({dehighlightAll:eu,dehighlight:function(n,e,t,o){var r=ou(n,e,t,o);nr.remove(o.element(),e.highlightClass()),r&&e.onDehighlight()(n,o)},highlight:tu,highlightFirst:function(e,t,o){iu(e,t,o).each(function(n){tu(e,t,o,n)})},highlightLast:function(e,t,o){uu(e,t,o).each(function(n){tu(e,t,o,n)})},highlightAt:function(e,t,o,n){ru(e,t,o,n).fold(function(n){throw new Error(n)},function(n){tu(e,t,o,n)})},highlightBy:function(e,t,o,n){var r=Vi(e.element(),"."+t.itemClass()),i=Br(pn.map(r,function(n){return e.getSystem().getByDom(n).toOption()}));pn.find(i,n).each(function(n){tu(e,t,o,n)})},isHighlighted:ou,getHighlighted:function(n,e,t){return Li(n.element(),"."+e.highlightClass()).bind(n.getSystem().getByDom)},getFirst:iu,getLast:uu,getPrevious:function(n,e,t){return au(n,e,0,-1)},getNext:function(n,e,t){return au(n,e,0,1)}}),su=[It("highlightClass"),It("itemClass"),Ur("onHighlight"),Ur("onDehighlight")],fu=Ro({fields:su,name:"highlighting",apis:cu}),lu=function(){return{get:function(n){return hr(n.element())},set:function(n,e){n.getSystem().triggerFocus(e,n.element())}}},du=function(n,e,a,t,o,i){var u=function(e,t,o,r){var n,i,u=a(e,t,o,r);return(n=u,i=t.event(),pn.find(n,function(n){return n.matches(i)}).map(function(n){return n.classification})).bind(function(n){return n(e,t,o,r)})},r={schema:function(){return n.concat([Lt("focusManager",lu()),_r("handler",r),_r("state",e)])},processKey:u,toEvents:function(o,r){var n=t(o,r),e=to(i.map(function(t){return ro(An(),function(n,e){t(n,o,r,e),e.stop()})}).toArray().concat([ro(F(),function(n,e){u(n,e,o,r).each(function(n){e.stop()})})]));return D.deepMerge(n,e)},toApis:o};return r},mu=function(n){var e=[Ht("onEscape"),Ht("onEnter"),Lt("selector",'[data-alloy-tabstop="true"]'),Lt("firstTabstop",0),Lt("useTabstopAt",O.constant(!0)),Ht("visibilitySelector")].concat([n]),a=function(n,e){var t=n.visibilitySelector().bind(function(n){return Pi(e,n)}).getOr(e);return 0<Ai(t)},c=function(e,n,t,o,r){return r(n,t,function(n){return a(e=o,t=n)&&e.useTabstopAt()(t);var e,t}).fold(function(){return o.cyclic()?y.some(!0):y.none()},function(n){return o.focusManager().set(e,n),y.some(!0)})},i=function(e,n,t,o){var r,i,u=Vi(e.element(),t.selector());return(r=e,i=t,i.focusManager().get(r).bind(function(n){return Pi(n,i.selector())})).bind(function(n){return pn.findIndex(u,O.curry(Se,n)).bind(function(n){return c(e,u,n,t,o)})})},t=O.constant([Qi(Ki([Xi,_i(Wi.TAB())]),function(n,e,t,o){var r=t.cyclic()?Ui:Gi;return i(n,0,t,r)}),Qi(_i(Wi.TAB()),function(n,e,t,o){var r=t.cyclic()?$i:qi;return i(n,0,t,r)}),Qi(_i(Wi.ESCAPE()),function(e,t,n,o){return n.onEscape().bind(function(n){return n(e,t)})}),Qi(Ki([Ji,_i(Wi.ENTER())]),function(e,t,n,o){return n.onEnter().bind(function(n){return n(e,t)})})]),o=O.constant({}),r=O.constant({});return du(e,Do,t,o,r,y.some(function(e,t,n){var o,r,i,u;(o=e,r=t,i=Vi(o.element(),r.selector()),u=pn.filter(i,function(n){return a(r,n)}),y.from(u[r.firstTabstop()])).each(function(n){t.focusManager().set(e,n)})}))},gu=mu(Wt("cyclic",O.constant(!1))),pu=mu(Wt("cyclic",O.constant(!0))),vu=function(n){return"input"===ie.name(n)&&"radio"!==Lo.get(n,"type")||"textarea"===ie.name(n)},hu=function(n,e,t){return vu(t)&&_i(Wi.SPACE())(e.event())?y.none():($n(n,t,In()),y.some(!0))},bu=[Lt("execute",hu),Lt("useSpace",!1),Lt("useEnter",!0),Lt("useControlEnter",!1),Lt("useDown",!1)],yu=function(n,e,t,o){return t.execute()(n,e,n.element())},wu=O.constant({}),xu=O.constant({}),Su=du(bu,Do,function(n,e,t,o){var r=t.useSpace()&&!vu(n.element())?Wi.SPACE():[],i=t.useEnter()?Wi.ENTER():[],u=t.useDown()?Wi.DOWN():[],a=r.concat(i).concat(u);return[Qi(_i(a),yu)].concat(t.useControlEnter()?[Qi(Ki([Yi,_i(Wi.ENTER())]),yu)]:[])},wu,xu,y.none()),Tu=function(n){var t=ir(y.none());return Eo({readState:O.constant({}),setGridSize:function(n,e){t.set(y.some({numRows:O.constant(n),numColumns:O.constant(e)}))},getNumRows:function(){return t.get().map(function(n){return n.numRows()})},getNumColumns:function(){return t.get().map(function(n){return n.numColumns()})}})},ku=Object.freeze({flatgrid:Tu,init:function(n){return n.state()(n)}}),Cu=function(n){return"rtl"===Ci.get(n,"direction")?"rtl":"ltr"},Ou=function(e,t){return function(n){return"rtl"===Cu(n)?t:e}},Eu=function(i){return function(n,e,t,o){var r=i(n.element());return Bu(r,n,e,t,o)}},Du=function(n,e){var t=Ou(n,e);return Eu(t)},Mu=function(n,e){var t=Ou(e,n);return Eu(t)},Au=function(r){return function(n,e,t,o){return Bu(r,n,e,t,o)}},Bu=function(e,t,n,o,r){return o.focusManager().get(t).bind(function(n){return e(t.element(),n,o,r)}).map(function(n){return o.focusManager().set(t,n),!0})},Ru=Au,Iu=Au,Fu=Au,Nu=function(n){var e,t=n.dom();return!((e=t).offsetWidth<=0&&e.offsetHeight<=0)},Vu=le.immutableBag(["index","candidates"],[]),Hu=function(n,e,t){return ju(n,e,t,Nu)},ju=function(n,e,t,o){var r,i,u=O.curry(Se,e),a=Vi(n,t),c=pn.filter(a,Nu);return r=c,i=u,pn.findIndex(r,i).map(function(n){return Vu({index:n,candidates:r})})},zu=function(n,e){return pn.findIndex(n,function(n){return Se(e,n)})},Lu=function(t,n,o,e){return e(Math.floor(n/o),n%o).bind(function(n){var e=n.row()*o+n.column();return 0<=e&&e<t.length?y.some(t[e]):y.none()})},Pu=function(r,n,i,u,a){return Lu(r,n,u,function(n,e){var t=n===i-1?r.length-n*u:u,o=Zi(e,a,0,t-1);return y.some({row:O.constant(n),column:O.constant(o)})})},Wu=function(i,n,u,a,c){return Lu(i,n,a,function(n,e){var t=Zi(n,c,0,u-1),o=t===u-1?i.length-t*a:a,r=nu(e,0,o-1);return y.some({row:O.constant(t),column:O.constant(r)})})},Uu=[It("selector"),Lt("execute",hu),Gr("onEscape"),Lt("captureTab",!1),Xr()],Gu=function(r){return function(n,e,t,o){return Hu(n,e,t.selector()).bind(function(n){return r(n.candidates(),n.index(),o.getNumRows().getOr(t.initSize().numRows()),o.getNumColumns().getOr(t.initSize().numColumns()))})}},$u=function(n,e,t,o){return t.captureTab()?y.some(!0):y.none()},qu=Gu(function(n,e,t,o){return Pu(n,e,t,o,-1)}),_u=Gu(function(n,e,t,o){return Pu(n,e,t,o,1)}),Ku=Gu(function(n,e,t,o){return Wu(n,e,t,o,-1)}),Xu=Gu(function(n,e,t,o){return Wu(n,e,t,o,1)}),Yu=O.constant([Qi(_i(Wi.LEFT()),Du(qu,_u)),Qi(_i(Wi.RIGHT()),Mu(qu,_u)),Qi(_i(Wi.UP()),Ru(Ku)),Qi(_i(Wi.DOWN()),Iu(Xu)),Qi(Ki([Xi,_i(Wi.TAB())]),$u),Qi(Ki([Ji,_i(Wi.TAB())]),$u),Qi(_i(Wi.ESCAPE()),function(n,e,t,o){return t.onEscape()(n,e)}),Qi(_i(Wi.SPACE().concat(Wi.ENTER())),function(e,t,o,n){return(r=e,i=o,i.focusManager().get(r).bind(function(n){return Pi(n,i.selector())})).bind(function(n){return o.execute()(e,t,n)});var r,i})]),Ju=O.constant({}),Qu=du(Uu,Tu,Yu,Ju,{},y.some(function(e,t,n){Li(e.element(),t.selector()).each(function(n){t.focusManager().set(e,n)})})),Zu=function(n,e,t,r){return Hu(n,t,e).bind(function(n){var e=n.index(),t=n.candidates(),o=Zi(e,r,0,t.length-1);return y.from(t[o])})},na=[It("selector"),Lt("getInitial",y.none),Lt("execute",hu),Lt("executeOnMove",!1)],ea=function(e,t,o){return(n=e,r=o,r.focusManager().get(n).bind(function(n){return Pi(n,r.selector())})).bind(function(n){return o.execute()(e,t,n)});var n,r},ta=function(n,e,t){return Zu(n,t.selector(),e,-1)},oa=function(n,e,t){return Zu(n,t.selector(),e,1)},ra=function(o){return function(n,e,t){return o(n,e,t).bind(function(){return t.executeOnMove()?ea(n,e,t):y.some(!0)})}},ia=O.constant({}),ua=O.constant({}),aa=du(na,Do,function(n){return[Qi(_i(Wi.LEFT().concat(Wi.UP())),ra(Du(ta,oa))),Qi(_i(Wi.RIGHT().concat(Wi.DOWN())),ra(Mu(ta,oa))),Qi(_i(Wi.ENTER()),ea),Qi(_i(Wi.SPACE()),ea)]},ia,ua,y.some(function(e,t){t.getInitial()(e).or(Li(e.element(),t.selector())).each(function(n){t.focusManager().set(e,n)})})),ca=le.immutableBag(["rowIndex","columnIndex","cell"],[]),sa=function(n,e,t){return y.from(n[e]).bind(function(n){return y.from(n[t]).map(function(n){return ca({rowIndex:e,columnIndex:t,cell:n})})})},fa=function(n,e,t,o){var r=n[e].length,i=Zi(t,o,0,r-1);return sa(n,e,i)},la=function(n,e,t,o){var r=Zi(t,o,0,n.length-1),i=n[r].length,u=nu(e,0,i-1);return sa(n,r,u)},da=function(n,e,t,o){var r=n[e].length,i=nu(t+o,0,r-1);return sa(n,e,i)},ma=function(n,e,t,o){var r=nu(t+o,0,n.length-1),i=n[r].length,u=nu(e,0,i-1);return sa(n,r,u)},ga=[Vt("selectors",[It("row"),It("cell")]),Lt("cycles",!0),Lt("previousSelector",y.none),Lt("execute",hu)],pa=function(n,e){return function(t,o,u){var a=u.cycles()?n:e;return Pi(o,u.selectors().row()).bind(function(n){var e=Vi(n,u.selectors().cell());return zu(e,o).bind(function(r){var i=Vi(t,u.selectors().row());return zu(i,n).bind(function(n){var e,t,o=(e=i,t=u,pn.map(e,function(n){return Vi(n,t.selectors().cell())}));return a(o,n,r).map(function(n){return n.cell()})})})})}},va=pa(function(n,e,t){return fa(n,e,t,-1)},function(n,e,t){return da(n,e,t,-1)}),ha=pa(function(n,e,t){return fa(n,e,t,1)},function(n,e,t){return da(n,e,t,1)}),ba=pa(function(n,e,t){return la(n,t,e,-1)},function(n,e,t){return ma(n,t,e,-1)}),ya=pa(function(n,e,t){return la(n,t,e,1)},function(n,e,t){return ma(n,t,e,1)}),wa=O.constant([Qi(_i(Wi.LEFT()),Du(va,ha)),Qi(_i(Wi.RIGHT()),Mu(va,ha)),Qi(_i(Wi.UP()),Ru(ba)),Qi(_i(Wi.DOWN()),Iu(ya)),Qi(_i(Wi.SPACE().concat(Wi.ENTER())),function(e,t,o){return hr(e.element()).bind(function(n){return o.execute()(e,t,n)})})]),xa=O.constant({}),Sa=O.constant({}),Ta=du(ga,Do,wa,xa,Sa,y.some(function(e,t){t.previousSelector()(e).orThunk(function(){var n=t.selectors();return Li(e.element(),n.cell())}).each(function(n){t.focusManager().set(e,n)})})),ka=[It("selector"),Lt("execute",hu),Lt("moveOnTab",!1)],Ca=function(e,t,o){return o.focusManager().get(e).bind(function(n){return o.execute()(e,t,n)})},Oa=function(n,e,t){return Zu(n,t.selector(),e,-1)},Ea=function(n,e,t){return Zu(n,t.selector(),e,1)},Da=O.constant([Qi(_i(Wi.UP()),Fu(Oa)),Qi(_i(Wi.DOWN()),Fu(Ea)),Qi(Ki([Xi,_i(Wi.TAB())]),function(n,e,t){return t.moveOnTab()?Fu(Oa)(n,e,t):y.none()}),Qi(Ki([Ji,_i(Wi.TAB())]),function(n,e,t){return t.moveOnTab()?Fu(Ea)(n,e,t):y.none()}),Qi(_i(Wi.ENTER()),Ca),Qi(_i(Wi.SPACE()),Ca)]),Ma=O.constant({}),Aa=O.constant({}),Ba=du(ka,Do,Da,Ma,Aa,y.some(function(e,t,n){Li(e.element(),t.selector()).each(function(n){t.focusManager().set(e,n)})})),Ra=[Gr("onSpace"),Gr("onEnter"),Gr("onShiftEnter"),Gr("onLeft"),Gr("onRight"),Gr("onTab"),Gr("onShiftTab"),Gr("onUp"),Gr("onDown"),Gr("onEscape"),Ht("focusIn")],Ia=O.constant({}),Fa=O.constant({}),Na=du(Ra,Do,function(n,e,t){return[Qi(_i(Wi.SPACE()),t.onSpace()),Qi(Ki([Ji,_i(Wi.ENTER())]),t.onEnter()),Qi(Ki([Xi,_i(Wi.ENTER())]),t.onShiftEnter()),Qi(Ki([Xi,_i(Wi.TAB())]),t.onShiftTab()),Qi(Ki([Ji,_i(Wi.TAB())]),t.onTab()),Qi(_i(Wi.UP()),t.onUp()),Qi(_i(Wi.DOWN()),t.onDown()),Qi(_i(Wi.LEFT()),t.onLeft()),Qi(_i(Wi.RIGHT()),t.onRight()),Qi(_i(Wi.SPACE()),t.onSpace()),Qi(_i(Wi.ESCAPE()),t.onEscape())]},Ia,Fa,y.some(function(e,t){return t.focusIn().bind(function(n){return n(e,t)})})),Va={acyclic:gu.schema(),cyclic:pu.schema(),flow:aa.schema(),flatgrid:Qu.schema(),matrix:Ta.schema(),execution:Su.schema(),menu:Ba.schema(),special:Na.schema()},Ha=(Di=Xt("Creating behaviour: "+(Ei={branchKey:"mode",branches:Va,name:"keying",active:{events:function(n,e){return n.handler().toEvents(n,e)}},apis:{focusIn:function(n){n.getSystem().triggerFocus(n.element(),n.element())},setGridSize:function(n,e,t,o,r){gt(t,"setGridSize")?t.setGridSize(o,r):console.error("Layout does not support setGridSize")}},state:ku}).name,Io,Ei),wo(Qt(Di.branchKey,Di.branches),Di.name,Di.active,Di.apis,Di.extra,Di.state)),ja=function(o,n){return e=o,t={},r=pn.map(n,function(n){return e=n.name(),t="Cannot configure "+n.name()+" for "+o,Rt(e,e,Ye(),Ct(function(n){return $e.error("The field: "+e+" is forbidden. "+t)}));var e,t}).concat([Wt("dump",O.identity)]),Rt(e,e,Ke(t),Et(r));var e,t,r},za=function(n){return n.dump()},La="placeholder",Pa=qe([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Wa=function(n,e,t,o){return t.uiType===La?(i=t,u=o,(r=n).exists(function(n){return n!==i.owner})?Pa.single(!0,O.constant(i)):ft(u,i.name).fold(function(){throw new Error("Unknown placeholder component: "+i.name+"\nKnown: ["+M.keys(u)+"]\nNamespace: "+r.getOr("none")+"\nSpec: "+bt(i,null,2))},function(n){return n.replace()})):Pa.single(!1,O.constant(t));var r,i,u},Ua=function(i,u,a,c){return Wa(i,0,a,c).fold(function(n,e){var t=e(u,a.config,a.validated),o=ft(t,"components").getOr([]),r=pn.bind(o,function(n){return Ua(i,u,n,c)});return[D.deepMerge(t,{components:r})]},function(n,e){return e(u,a.config,a.validated)})},Ga=function(e,t,n,o){var r,i,u,a,c=M.map(o,function(n,e){return t=e,o=n,r=!1,{name:O.constant(t),required:function(){return o.fold(function(n,e){return n},function(n,e){return n})},used:function(){return r},replace:function(){if(!0===r)throw new Error("Trying to use the same placeholder more than once: "+t);return r=!0,o}};var t,o,r}),s=(r=e,i=t,u=n,a=c,pn.bind(u,function(n){return Ua(r,i,n,a)}));return M.each(c,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+bt(t.components(),null,2))}),s},$a=Pa.single,qa=Pa.multiple,_a=O.constant(La),Ka=0,Xa=function(n){var e=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++Ka+String(e)},Ya=qe([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Ja=Lt("factory",{sketch:O.identity}),Qa=Lt("schema",[]),Za=It("name"),nc=Rt("pname","pname",Je(function(n){return"<alloy."+Xa(n.name)+">"}),Zt()),ec=Lt("defaults",O.constant({})),tc=Lt("overrides",O.constant({})),oc=Et([Ja,Qa,Za,nc,ec,tc]),rc=Et([Ja,Qa,Za,ec,tc]),ic=Et([Ja,Qa,Za,nc,ec,tc]),uc=Et([Ja,Qa,Za,It("unit"),nc,ec,tc]),ac=function(n){var e=function(n){return n.name()};return n.fold(e,e,e,e)},cc=function(t,o){return function(n){var e=Yt("Converting part type",o,n);return t(e)}},sc=cc(Ya.required,oc),fc=(cc(Ya.external,rc),cc(Ya.optional,ic)),lc=cc(Ya.group,uc),dc=O.constant("entirety"),mc=function(n,e,t,o){var r=t;return D.deepMerge(e.defaults()(n,t,o),t,{uid:n.partUids()[e.name()]},e.overrides()(n,t,o),{"debug.sketcher":lt("part-"+e.name(),r)})},gc=function(r,n){var i={};return pn.each(n,function(n){var e;(e=n,e.fold(y.some,y.none,y.some,y.some)).each(function(t){var o=pc(r,t.pname());i[t.name()]=function(n){var e=Xt("Part: "+t.name()+" in "+r,Et(t.schema()),n);return D.deepMerge(o,{config:n,validated:e})}})}),i},pc=function(n,e){return{uiType:_a(),owner:n,name:e}},vc=function(n,e,t){return o=e,r=t,i={},u={},pn.each(r,function(n){n.fold(function(o){i[o.pname()]=$a(!0,function(n,e,t){return o.factory().sketch(mc(n,o,e,t))})},function(n){var e=o.parts()[n.name()]();u[n.name()]=O.constant(mc(o,n,e[dc()]()))},function(o){i[o.pname()]=$a(!1,function(n,e,t){return o.factory().sketch(mc(n,o,e,t))})},function(r){i[r.pname()]=qa(!0,function(e,n,t){var o=e[r.name()]();return pn.map(o,function(n){return r.factory().sketch(D.deepMerge(r.defaults()(e,n),n,r.overrides()(e,n)))})})})}),{internals:O.constant(i),externals:O.constant(u)};var o,r,i,u},hc=function(n,e,t){return Ga(y.some(n),e,e.components(),t)},bc=function(n,e,t){var o=e.partUids()[t];return n.getSystem().getByUid(o).toOption()},yc=function(n,e,t){return bc(n,e,t).getOrDie("Could not find part: "+t)},wc=function(e,n){var t,o=(t=n,pn.map(t,ac));return dt(pn.map(o,function(n){return{key:n,value:e+"-"+n}}))},xc=function(e){return Rt("partUids","partUids",Qe(function(n){return wc(n.uid,e)}),Zt())},Sc=Xa("alloy-premade"),Tc=Xa("api"),kc=function(n){return lt(Sc,n)},Cc=function(o){return function(n){var e=Array.prototype.slice.call(arguments,0),t=n.config(Tc);return o.apply(undefined,[t].concat(e))}},Oc=O.constant(Tc),Ec=O.constant("alloy-id-"),Dc=O.constant("data-alloy-id"),Mc=Ec(),Ac=Dc(),Bc=function(n,e){var t=Xa(Mc+n);return Lo.set(e,Ac,t),t},Rc=function(n){var e=ie.isElement(n)?Lo.get(n,Ac):null;return y.from(e)},Ic=function(n){return Xa(n)},Fc=(O.constant(Ac),function(n,e,t,o,r){var i,u,a=(u=r,(0<(i=o).length?[Vt("parts",i)]:[]).concat([It("uid"),Lt("dom",{}),Lt("components",[]),Kr("originalSpec"),Lt("debug.sketcher",{})]).concat(u));return Yt(n+" [SpecSchema]",Ot(a.concat(e)),t)}),Nc=function(n,e,t,o,r){var i,u=Vc(r),a=(i=t,pn.bind(i,function(n){return n.fold(y.none,y.some,y.none,y.none).map(function(n){return Vt(n.name(),n.schema().concat([Kr(dc())]))}).toArray()})),c=xc(t),s=Fc(n,e,u,a,[c]),f=vc(0,s,t),l=hc(n,s,f.internals());return D.deepMerge(o(s,l,u,f.externals()),{"debug.sketcher":lt(n,r)})},Vc=function(n){return D.deepMerge({uid:Ic("uid")},n)},Hc=Ot([It("name"),It("factory"),It("configFields"),Lt("apis",{}),Lt("extraApis",{})]),jc=Ot([It("name"),It("factory"),It("configFields"),It("partFields"),Lt("apis",{}),Lt("extraApis",{})]),zc=function(n){var a=Xt("Sketcher for "+n.name,Hc,n),e=M.map(a.apis,Cc),t=M.map(a.extraApis,function(n,e){return go(n)});return D.deepMerge({name:O.constant(a.name),partFields:O.constant([]),configFields:O.constant(a.configFields),sketch:function(n){return e=a.name,t=a.configFields,o=a.factory,i=Vc(r=n),u=Fc(e,t,i,[],[]),D.deepMerge(o(u,i),{"debug.sketcher":lt(e,r)});var e,t,o,r,i,u}},e,t)},Lc=function(n){var e=Xt("Sketcher for "+n.name,jc,n),t=gc(e.name,e.partFields),o=M.map(e.apis,Cc),r=M.map(e.extraApis,function(n,e){return go(n)});return D.deepMerge({name:O.constant(e.name),partFields:O.constant(e.partFields),configFields:O.constant(e.configFields),sketch:function(n){return Nc(e.name,e.configFields,e.partFields,e.factory,n)},parts:O.constant(t)},o,r)},Pc=zc({name:"Button",factory:function(n,e){var t,o,r,i=(t=n.action(),o=function(n,e){e.stop(),Gn(n)},r=Dn.detect().deviceType.isTouch()?[ro(Nn(),o)]:[ro(H(),o),ro(A(),function(n,e){e.cut()})],to(pn.flatten([t.map(function(t){return ro(In(),function(n,e){t(n),e.stop()})}).toArray(),r]))),u=ft(n.dom(),"attributes").bind(ct("type")),a=ft(n.dom(),"tag");return{uid:n.uid(),dom:n.dom(),components:n.components(),events:i,behaviours:D.deepMerge(Ao([bi.config({}),Ha.config({mode:"execution",useSpace:!0,useEnter:!0})]),za(n.buttonBehaviours())),domModification:{attributes:D.deepMerge(u.fold(function(){return a.is("button")?{type:"button"}:{}},function(n){return{}}),{role:n.role().getOr("button")})},eventOrder:n.eventOrder()}},configFields:[Lt("uid",undefined),It("dom"),Lt("components",[]),ja("buttonBehaviours",[bi,Ha]),Ht("action"),Ht("role"),Lt("eventOrder",{})]}),Wc=Object.freeze({events:function(n){return to([oo(z(),O.constant(!0))])},exhibit:function(n,e){return ho({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}}),Uc=Ro({fields:[],name:"unselecting",active:Wc}),Gc=function(n){var e,t,o,r=Kn.fromHtml(n),i=Ae.children(r),u=(t=(e=r).dom().attributes!==undefined?e.dom().attributes:[],pn.foldl(t,function(n,e){return"class"===e.name?n:D.deepMerge(n,lt(e.name,e.value))},{})),a=(o=r,Array.prototype.slice.call(o.dom().classList,0)),c=0===i.length?{}:{innerHtml:kr(r)};return D.deepMerge({tag:ie.name(r),classes:a,attributes:u},c)},$c=function(n){var e=yn(n,{prefix:mi.prefix()});return Gc(e)},qc=function(n){return{dom:$c(n)}},_c=function(n){return Ao([ci.config({toggleClass:mi.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),si(n,function(n,e){(e?ci.on:ci.off)(n)})])},Kc=function(n,e,t){return Pc.sketch({dom:$c('<span class="${prefix}-toolbar-button ${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:e,buttonBehaviours:D.deepMerge(Ao([Uc.config({})]),t)})},Xc={forToolbar:Kc,forToolbarCommand:function(n,e){return Kc(e,function(){n.execCommand(e)},{})},forToolbarStateAction:function(n,e,t,o){var r=_c(t);return Kc(e,o,r)},forToolbarStateCommand:function(n,e){var t=_c(e);return Kc(e,function(){n.execCommand(e)},t)}},Yc=function(n,e,t){return Math.max(e,Math.min(t,n))},Jc=function(n,e,t,o,r,i,u){var a=t-e;if(o<n.left)return e-1;if(o>n.right)return t+1;var c,s,f,l,d=Math.min(n.right,Math.max(o,n.left))-n.left,m=Yc(d/n.width*a+e,e-1,t+1),g=Math.round(m);return i&&e<=m&&m<=t?(c=m,s=e,f=t,l=r,u.fold(function(){var n=c-s,e=Math.round(n/l)*l;return Yc(s+e,s-1,f+1)},function(n){var e=(c-n)%l,t=Math.round(e/l),o=Math.floor((c-n)/l),r=Math.floor((f-n)/l),i=n+Math.min(r,o+t)*l;return Math.max(n,i)})):g},Qc="slider.change.value",Zc=Dn.detect().deviceType.isTouch(),ns=function(n){var e;return(e=n.event().raw(),Zc&&e.touches!==undefined&&1===e.touches.length?y.some(e.touches[0]):Zc&&e.touches!==undefined?y.none():Zc||e.clientX===undefined?y.none():y.some(e)).map(function(n){return n.clientX})},es=function(n,e){Un(n,Qc,{value:e})},ts=function(i,u,a,n){return ns(n).map(function(n){var e,t,o,r;return e=i,o=n,r=Jc(a,(t=u).min(),t.max(),o,t.stepSize(),t.snapToGrid(),t.snapStart()),es(e,r),n})},os=function(n,e){var t,o,r,i,u=(t=e.value().get(),o=e.min(),r=e.max(),i=e.stepSize(),t<o?t:r<t?r:t===o?o-1:Math.max(o,t-i));es(n,u)},rs=function(n,e){var t,o,r,i,u=(t=e.value().get(),o=e.min(),r=e.max(),i=e.stepSize(),r<t?t:t<o?o:t===r?r+1:Math.min(r,t+i));es(n,u)},is=O.constant(Qc),us=Dn.detect().deviceType.isTouch(),as=function(n,o){return fc({name:n+"-edge",overrides:function(n){var e=to([io(T(),o,[n])]),t=to([io(A(),o,[n]),io(B(),function(n,e){e.mouseIsDown().get()&&o(n,e)},[n])]);return{events:us?e:t}}})},cs=[as("left",function(n,e){es(n,e.min()-1)}),as("right",function(n,e){es(n,e.max()+1)}),sc({name:"thumb",defaults:O.constant({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:to([ao(T(),n,"spectrum"),ao(k(),n,"spectrum"),ao(C(),n,"spectrum")])}}}),sc({schema:[Wt("mouseIsDown",function(){return ir(!1)})],name:"spectrum",overrides:function(o){var t=function(n,e){var t=n.element().dom().getBoundingClientRect();ts(n,o,t,e)},n=to([ro(T(),t),ro(k(),t)]),e=to([ro(A(),t),ro(B(),function(n,e){o.mouseIsDown().get()&&t(n,e)})]);return{behaviours:Ao(us?[]:[Ha.config({mode:"special",onLeft:function(n){return os(n,o),y.some(!0)},onRight:function(n){return rs(n,o),y.some(!0)}}),bi.config({})]),events:us?n:e}}})],ss=function(n,e,t){e.store().manager().onLoad(n,e,t)},fs=function(n,e,t){e.store().manager().onUnload(n,e,t)},ls=Object.freeze({onLoad:ss,onUnload:fs,setValue:function(n,e,t,o){e.store().manager().setValue(n,e,t,o)},getValue:function(n,e,t){return e.store().manager().getValue(n,e,t)}}),ds=Object.freeze({events:function(t,o){var n=t.resetOnDom()?[so(function(n,e){ss(n,t,o)}),fo(function(n,e){fs(n,t,o)})]:[yo(t,o,ss)];return to(n)}}),ms=function(){var n=ir(null);return Eo({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})},gs=function(){var n=ir({});return Eo({readState:function(){return{mode:"dataset",dataset:n.get()}},set:n.set,get:n.get})},ps=Object.freeze({memory:ms,dataset:gs,manual:function(){return Eo({readState:function(){}})},init:function(n){return n.store().manager().state(n)}}),vs=function(n,e,t,o){e.store().getDataKey(),t.set({}),e.store().setData()(n,o),e.onSetValue()(n,o)},hs=[Ht("initialValue"),It("getFallbackEntry"),It("getDataKey"),It("setData"),_r("manager",{setValue:vs,getValue:function(n,e,t){var o=e.store().getDataKey()(n),r=t.get();return ft(r,o).fold(function(){return e.store().getFallbackEntry()(o)},function(n){return n})},onLoad:function(e,t,o){t.store().initialValue().each(function(n){vs(e,t,o,n)})},onUnload:function(n,e,t){t.set({})},state:gs})],bs=[It("getValue"),Lt("setValue",O.noop),Ht("initialValue"),_r("manager",{setValue:function(n,e,t,o){e.store().setValue()(n,o),e.onSetValue()(n,o)},getValue:function(n,e,t){return e.store().getValue()(n)},onLoad:function(e,t,n){t.store().initialValue().each(function(n){t.store().setValue()(e,n)})},onUnload:O.noop,state:Do})],ys=[Ht("initialValue"),_r("manager",{setValue:function(n,e,t,o){t.set(o),e.onSetValue()(n,o)},getValue:function(n,e,t){return t.get()},onLoad:function(n,e,t){e.store().initialValue().each(function(n){t.isNotSet()&&t.set(n)})},onUnload:function(n,e,t){t.clear()},state:ms})],ws=[Pt("store",{mode:"memory"},Qt("mode",{memory:ys,manual:bs,dataset:hs})),Ur("onSetValue"),Lt("resetOnDom",!1)],xs=Ro({fields:ws,name:"representing",active:ds,apis:ls,extra:{setValueFrom:function(n,e){var t=xs.getValue(e);xs.setValue(n,t)}},state:ps}),Ss=Dn.detect().deviceType.isTouch(),Ts=[It("min"),It("max"),Lt("stepSize",1),Lt("onChange",O.noop),Lt("onInit",O.noop),Lt("onDragStart",O.noop),Lt("onDragEnd",O.noop),Lt("snapToGrid",!1),Ht("snapStart"),It("getInitialValue"),ja("sliderBehaviours",[Ha,xs]),Wt("value",function(n){return ir(n.min)})].concat(Ss?[]:[Wt("mouseIsDown",function(){return ir(!1)})]),ks=Oi("width",function(n){return n.dom().offsetWidth}),Cs=function(n,e){ks.set(n,e)},Os=function(n){return ks.get(n)},Es=Dn.detect().deviceType.isTouch(),Ds=Lc({name:"Slider",configFields:Ts,partFields:cs,factory:function(c,n,e,t){var s=c.max()-c.min(),f=function(n){var e=n.element().dom().getBoundingClientRect();return(e.left+e.right)/2},r=function(n){return yc(n,c,"thumb")},i=function(n){var e,t,o,r,i=yc(n,c,"spectrum").element().dom().getBoundingClientRect(),u=n.element().dom().getBoundingClientRect(),a=(e=n,t=i,(r=(o=c).value().get())<o.min()?bc(e,o,"left-edge").fold(function(){return 0},function(n){return f(n)-t.left}):r>o.max()?bc(e,o,"right-edge").fold(function(){return t.width},function(n){return f(n)-t.left}):(o.value().get()-o.min())/s*t.width);return i.left-u.left+a},u=function(n){var e=i(n),t=r(n),o=Os(t.element())/2;Ci.set(t.element(),"left",e-o+"px")},o=function(n,e){var t=c.value().get(),o=r(n);return t!==e||Ci.getRaw(o.element(),"left").isNone()?(c.value().set(e),u(n),c.onChange()(n,o,e),y.some(!0)):y.none()},a=Es?[ro(T(),function(n,e){c.onDragStart()(n,r(n))}),ro(C(),function(n,e){c.onDragEnd()(n,r(n))})]:[ro(A(),function(n,e){e.stop(),c.onDragStart()(n,r(n)),c.mouseIsDown().set(!0)}),ro(R(),function(n,e){c.onDragEnd()(n,r(n)),c.mouseIsDown().set(!1)})];return{uid:c.uid(),dom:c.dom(),components:n,behaviours:D.deepMerge(Ao(pn.flatten([Es?[]:[Ha.config({mode:"special",focusIn:function(n){return bc(n,c,"spectrum").map(Ha.focusIn).map(O.constant(!0))}})],[xs.config({store:{mode:"manual",getValue:function(n){return c.value().get()}}})]])),za(c.sliderBehaviours())),events:to([ro(is(),function(n,e){o(n,e.event().value())}),so(function(n,e){c.value().set(c.getInitialValue()());var t=r(n);u(n),c.onInit()(n,t,c.value().get())})].concat(a)),apis:{resetToMin:function(n){o(n,c.min())},resetToMax:function(n){o(n,c.max())},refresh:u},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,e){n.resetToMin(e)},resetToMax:function(n,e){n.resetToMax(e)},refresh:function(n,e){n.refresh(e)}}}),Ms=function(e,t,o){return Xc.forToolbar(t,function(){var n=o();e.setContextToolbar([{label:t+" group",items:n}])},{})},As=function(n){return[(r=n,i=function(n){return n<0?"black":360<n?"white":"hsl("+n+", 100%, 50%)"},Ds.sketch({dom:$c('<div class="${prefix}-slider ${prefix}-hue-slider-container"></div>'),components:[Ds.parts()["left-edge"](qc('<div class="${prefix}-hue-slider-black"></div>')),Ds.parts().spectrum({dom:$c('<div class="${prefix}-slider-gradient-container"></div>'),components:[qc('<div class="${prefix}-slider-gradient"></div>')],behaviours:Ao([ci.config({toggleClass:mi.resolve("thumb-active")})])}),Ds.parts()["right-edge"](qc('<div class="${prefix}-hue-slider-white"></div>')),Ds.parts().thumb({dom:$c('<div class="${prefix}-slider-thumb"></div>'),behaviours:Ao([ci.config({toggleClass:mi.resolve("thumb-active")})])})],onChange:function(n,e,t){var o=i(t);Ci.set(e.element(),"background-color",o),r.onChange(n,e,o)},onDragStart:function(n,e){ci.on(e)},onDragEnd:function(n,e){ci.off(e)},onInit:function(n,e,t){var o=i(t);Ci.set(e.element(),"background-color",o)},stepSize:10,min:0,max:360,getInitialValue:r.getInitialValue,sliderBehaviours:Ao([fi(Ds.refresh)])}))];var r,i},Bs=function(n,o){var e={onChange:function(n,e,t){o.undoManager.transact(function(){o.formatter.apply("forecolor",{value:t}),o.nodeChanged()})},getInitialValue:function(){return-1}};return Ms(n,"color",function(){return As(e)})},Rs=Ot([It("getInitialValue"),It("onChange"),It("category"),It("sizes")]),Is=function(n){var r=Xt("SizeSlider",Rs,n);return Ds.sketch({dom:{tag:"div",classes:[mi.resolve("slider-"+r.category+"-size-container"),mi.resolve("slider"),mi.resolve("slider-size-container")]},onChange:function(n,e,t){var o;0<=(o=t)&&o<r.sizes.length&&r.onChange(t)},onDragStart:function(n,e){ci.on(e)},onDragEnd:function(n,e){ci.off(e)},min:0,max:r.sizes.length-1,stepSize:1,getInitialValue:r.getInitialValue,snapToGrid:!0,sliderBehaviours:Ao([fi(Ds.refresh)]),components:[Ds.parts().spectrum({dom:$c('<div class="${prefix}-slider-size-container"></div>'),components:[qc('<div class="${prefix}-slider-size-line"></div>')]}),Ds.parts().thumb({dom:$c('<div class="${prefix}-slider-thumb"></div>'),behaviours:Ao([ci.config({toggleClass:mi.resolve("thumb-active")})])})]})},Fs=function(n,e,t){for(var o=n.dom(),r=E.isFunction(t)?t:O.constant(!1);o.parentNode;){o=o.parentNode;var i=Kn.fromDom(o),u=e(i);if(u.isSome())return u;if(r(i))break}return y.none()},Ns=function(n,e,t){return e(n).orThunk(function(){return t(n)?y.none():Fs(n,e,t)})},Vs=["9px","10px","11px","12px","14px","16px","18px","20px","24px","32px","36px"],Hs=function(n){var e,t,o=n.selection.getStart(),r=Kn.fromDom(o),i=Kn.fromDom(n.getBody()),u=(e=function(n){return Se(i,n)},t=r,(ie.isElement(t)?y.some(t):Ae.parent(t)).map(function(n){return Ns(n,function(n){return Ci.getRaw(n,"font-size")},e).getOrThunk(function(){return Ci.get(n,"font-size")})}).getOr(""));return pn.find(Vs,function(n){return u===n}).getOr("medium")},js={candidates:O.constant(Vs),get:function(n){var e,t=Hs(n);return(e=t,pn.findIndex(Vs,function(n){return n===e})).getOr(2)},apply:function(o,n){var e;(e=n,y.from(Vs[e])).each(function(n){var e,t;t=n,Hs(e=o)!==t&&e.execCommand("fontSize",!1,t)})}},zs=js.candidates(),Ls=function(n){return[qc('<span class="${prefix}-toolbar-button ${prefix}-icon-small-font ${prefix}-icon"></span>'),(e=n,Is({onChange:e.onChange,sizes:zs,category:"font",getInitialValue:e.getInitialValue})),qc('<span class="${prefix}-toolbar-button ${prefix}-icon-large-font ${prefix}-icon"></span>')];var e},Ps=function(n){var e=n.uid!==undefined&&gt(n,"uid")?n.uid:Ic("memento");return{get:function(n){return n.getSystem().getByUid(e).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(e).fold(y.none,y.some)},asSpec:function(){return D.deepMerge(n,{uid:e})}}};function Ws(n,e){return Gs(document.createElement("canvas"),n,e)}function Us(n){return n.getContext("2d")}function Gs(n,e,t){return n.width=e,n.height=t,n}var $s={create:Ws,clone:function(n){var e;return Us(e=Ws(n.width,n.height)).drawImage(n,0,0),e},resize:Gs,get2dContext:Us,get3dContext:function(n){var e=null;try{e=n.getContext("webgl")||n.getContext("experimental-webgl")}catch(t){}return e||(e=null),e}},qs={getWidth:function(n){return n.naturalWidth||n.width},getHeight:function(n){return n.naturalHeight||n.height}},_s=window.Promise?window.Promise:function(){var n=function(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],s(n,o(r,this),o(u,this))},e=n.immediateFn||"function"==typeof setImmediate&&setImmediate||function(n){setTimeout(n,1)};function o(n,e){return function(){n.apply(e,arguments)}}var t=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)};function i(o){var r=this;null!==this._state?e(function(){var n=r._state?o.onFulfilled:o.onRejected;if(null!==n){var e;try{e=n(r._value)}catch(t){return void o.reject(t)}o.resolve(e)}else(r._state?o.resolve:o.reject)(r._value)}):this._deferreds.push(o)}function r(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void s(o(e,n),o(r,this),o(u,this))}this._state=!0,this._value=n,a.call(this)}catch(t){u.call(this,t)}}function u(n){this._state=!1,this._value=n,a.call(this)}function a(){for(var n=0,e=this._deferreds.length;n<e;n++)i.call(this,this._deferreds[n]);this._deferreds=null}function c(n,e,t,o){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.resolve=t,this.reject=o}function s(n,e,t){var o=!1;try{n(function(n){o||(o=!0,e(n))},function(n){o||(o=!0,t(n))})}catch(r){if(o)return;o=!0,t(r)}}return n.prototype["catch"]=function(n){return this.then(null,n)},n.prototype.then=function(t,o){var r=this;return new n(function(n,e){i.call(r,new c(t,o,n,e))})},n.all=function(){var c=Array.prototype.slice.call(1===arguments.length&&t(arguments[0])?arguments[0]:arguments);return new n(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){a(e,n)},i)}c[e]=n,0==--u&&r(c)}catch(o){i(o)}}for(var n=0;n<c.length;n++)a(n,c[n])})},n.resolve=function(e){return e&&"object"==typeof e&&e.constructor===n?e:new n(function(n){n(e)})},n.reject=function(t){return new n(function(n,e){e(t)})},n.race=function(r){return new n(function(n,e){for(var t=0,o=r.length;t<o;t++)r[t].then(n,e)})},n}();function Ks(){return new(ve.getOrDie("FileReader"))}var Xs={atob:function(n){return ve.getOrDie("atob")(n)},requestAnimationFrame:function(n){ve.getOrDie("requestAnimationFrame")(n)}};function Ys(a){return new _s(function(n,e){var t=URL.createObjectURL(a),o=new Image,r=function(){o.removeEventListener("load",i),o.removeEventListener("error",u)};function i(){r(),n(o)}function u(){r(),e("Unable to load data of type "+a.type+": "+t)}o.addEventListener("load",i),o.addEventListener("error",u),o.src=t,o.complete&&i()})}function Js(o){return new _s(function(n,t){var e=new XMLHttpRequest;e.open("GET",o,!0),e.responseType="blob",e.onload=function(){200==this.status&&n(this.response)},e.onerror=function(){var n,e=this;t(0===this.status?((n=new Error("No access to download image")).code=18,n.name="SecurityError",n):new Error("Error "+e.status+" downloading image"))},e.send()})}function Qs(n){var e=n.split(","),t=/data:([^;]+)/.exec(e[0]);if(!t)return y.none();for(var o,r,i,u=t[1],a=e[1],c=Xs.atob(a),s=c.length,f=Math.ceil(s/1024),l=new Array(f),d=0;d<f;++d){for(var m=1024*d,g=Math.min(m+1024,s),p=new Array(g-m),v=m,h=0;v<g;++h,++v)p[h]=c[v].charCodeAt(0);l[d]=(o=p,new(ve.getOrDie("Uint8Array"))(o))}return y.some((r=l,i={type:u},new(ve.getOrDie("Blob"))(r,i)))}function Zs(t){return new _s(function(n,e){Qs(t).fold(function(){e("uri is not base64: "+t)},n)})}function nf(t){return new _s(function(n){var e=new Ks;e.onloadend=function(){n(e.result)},e.readAsDataURL(t)})}var ef,tf,of,rf,uf,af,cf,sf,ff={blobToImage:Ys,imageToBlob:function(n){var e=n.src;return 0===e.indexOf("data:")?Zs(e):Js(e)},blobToArrayBuffer:function(t){return new _s(function(n){var e=new Ks;e.onloadend=function(){n(e.result)},e.readAsArrayBuffer(t)})},blobToDataUri:nf,blobToBase64:function(n){return nf(n).then(function(n){return n.split(",")[1]})},dataUriToBlobSync:Qs,canvasToBlob:function(n,t,o){return t=t||"image/png",HTMLCanvasElement.prototype.toBlob?new _s(function(e){n.toBlob(function(n){e(n)},t,o)}):Zs(n.toDataURL(t,o))},canvasToDataURL:function(n,e,t){return e=e||"image/png",n.then(function(n){return n.toDataURL(e,t)})},blobToCanvas:function(n){return Ys(n).then(function(n){var e,t;return e=n,URL.revokeObjectURL(e.src),t=$s.create(qs.getWidth(n),qs.getHeight(n)),$s.get2dContext(t).drawImage(n,0,0),t})},uriToBlob:function(n){return 0===n.indexOf("blob:")?Js(n):0===n.indexOf("data:")?Zs(n):null}},lf=function(n){return ff.blobToBase64(n)},df=function(u){var e=Ps({dom:{tag:"input",attributes:{accept:"image/*",type:"file",title:""},styles:{visibility:"hidden",position:"absolute"}},events:to([co(H()),ro(V(),function(n,e){var t,o,r;(t=e,o=t.event(),r=o.raw().target.files||o.raw().dataTransfer.files,y.from(r[0])).each(function(n){var r,i;r=u,lf(i=n).then(function(o){r.undoManager.transact(function(){var n=r.editorUpload.blobCache,e=n.create(Xa("mceu"),i,o);n.add(e);var t=r.dom.createHTML("img",{src:e.blobUri()});r.insertContent(t)})})})})])});return Pc.sketch({dom:$c('<span class="${prefix}-toolbar-button ${prefix}-icon-image ${prefix}-icon"></span>'),components:[e.asSpec()],action:function(n){e.get(n).element().dom().click()}})},mf=function(n){return n.dom().textContent},gf=function(n,e){n.dom().textContent=e},pf=function(n){return 0<n.length},vf=function(n){return n===undefined||null===n?"":n},hf=function(e,t,n){return n.text.filter(pf).fold(function(){return Lo.get(n=e,"href")===mf(n)?y.some(t):y.none();var n},y.some)},bf=function(n){var e=Kn.fromDom(n.selection.getStart());return Pi(e,"a")},yf={getInfo:function(n){return bf(n).fold(function(){return{url:"",text:n.selection.getContent({format:"text"}),title:"",target:"",link:y.none()}},function(n){return t=mf(e=n),o=Lo.get(e,"href"),r=Lo.get(e,"title"),i=Lo.get(e,"target"),{url:vf(o),text:t!==o?vf(t):"",title:vf(r),target:vf(i),link:y.some(e)};var e,t,o,r,i})},applyInfo:function(r,i){i.url.filter(pf).fold(function(){var e;e=r,i.link.bind(O.identity).each(function(n){e.execCommand("unlink")})},function(t){var n,e,o=(n=i,(e={}).href=t,n.title.filter(pf).each(function(n){e.title=n}),n.target.filter(pf).each(function(n){e.target=n}),e);i.link.bind(O.identity).fold(function(){var n=i.text.filter(pf).getOr(t);r.insertContent(r.dom.createHTML("a",o,r.dom.encode(n)))},function(e){var n=hf(e,t,i);Lo.setAll(e,o),n.each(function(n){gf(e,n)})})})},query:bf},wf=Dn.detect(),xf=function(n,e){var t=e.selection.getRng();n(),e.selection.setRng(t)},Sf=function(n,e){(wf.os.isAndroid()?xf:O.apply)(e,n)},Tf=function(n,e){var t,o;return{key:n,value:{config:{},me:(t=n,o=to(e),Ro({fields:[It("enabled")],name:t,active:{events:O.constant(o)}})),configAsRaw:O.constant({}),initialConfig:{},state:Fo()}}},kf=Object.freeze({getCurrent:function(n,e,t){return e.find()(n)}}),Cf=[It("find")],Of=Ro({fields:Cf,name:"composing",apis:kf}),Ef=zc({name:"Container",factory:function(n,e){return{uid:n.uid(),dom:D.deepMerge({tag:"div",attributes:{role:"presentation"}},n.dom()),components:n.components(),behaviours:za(n.containerBehaviours()),events:n.events(),domModification:n.domModification(),eventOrder:n.eventOrder()}},configFields:[Lt("components",[]),ja("containerBehaviours",[]),Lt("events",{}),Lt("domModification",{}),Lt("eventOrder",{})]}),Df=zc({name:"DataField",factory:function(t,n){return{uid:t.uid(),dom:t.dom(),behaviours:D.deepMerge(Ao([xs.config({store:{mode:"memory",initialValue:t.getInitialValue()()}}),Of.config({find:y.some})]),za(t.dataBehaviours())),events:to([so(function(n,e){xs.setValue(n,t.getInitialValue()())})])}},configFields:[It("uid"),It("dom"),It("getInitialValue"),ja("dataBehaviours",[xs,Of])]}),Mf=function(n,e){if(e===undefined)throw new Error("Value.set was undefined");n.dom().value=e},Af=function(n){return n.dom().value},Bf=O.constant([Ht("data"),Lt("inputAttributes",{}),Lt("inputStyles",{}),Lt("type","input"),Lt("tag","input"),Lt("inputClasses",[]),Ur("onSetValue"),Lt("styles",{}),Ht("placeholder"),Lt("eventOrder",{}),ja("inputBehaviours",[xs,bi]),Lt("selectOnFocus",!0)]),Rf=zc({name:"Input",configFields:Bf(),factory:function(n,e){return{uid:n.uid(),dom:(o=n,{tag:o.tag(),attributes:D.deepMerge(dt([{key:"type",value:o.type()}].concat(o.placeholder().map(function(n){return{key:"placeholder",value:n}}).toArray())),o.inputAttributes()),styles:o.inputStyles(),classes:o.inputClasses()}),components:[],behaviours:(t=n,D.deepMerge(Ao([xs.config({store:{mode:"manual",initialValue:t.data().getOr(undefined),getValue:function(n){return Af(n.element())},setValue:function(n,e){Af(n.element())!==e&&Mf(n.element(),e)}},onSetValue:t.onSetValue()}),bi.config({onFocus:!1===t.selectOnFocus()?O.noop:function(n){var e=n.element(),t=Af(e);e.dom().setSelectionRange(0,t.length)}})]),za(t.inputBehaviours()))),eventOrder:n.eventOrder()};var t,o}}),If=Object.freeze({exhibit:function(n,e){return ho({attributes:dt([{key:e.tabAttr(),value:"true"}])})}}),Ff=[Lt("tabAttr","data-alloy-tabstop")],Nf=Ro({fields:Ff,name:"tabstopping",active:If}),Vf=function(n,e){var t=Ps(Rf.sketch({placeholder:e,onSetValue:function(n,e){Wn(n,N())},inputBehaviours:Ao([Of.config({find:y.some}),Nf.config({}),Ha.config({mode:"execution"})]),selectOnFocus:!1})),o=Ps(Pc.sketch({dom:$c('<button class="${prefix}-input-container-x ${prefix}-icon-cancel-circle ${prefix}-icon"></button>'),action:function(n){var e=t.get(n);xs.setValue(e,"")}}));return{name:n,spec:Ef.sketch({dom:$c('<div class="${prefix}-input-container"></div>'),components:[t.asSpec(),o.asSpec()],containerBehaviours:Ao([ci.config({toggleClass:mi.resolve("input-container-empty")}),Of.config({find:function(n){return y.some(t.get(n))}}),Tf("input-clearing",[ro(N(),function(n){var e=t.get(n);(0<xs.getValue(e).length?ci.off:ci.on)(n)})])])})}},Hf=["input","button","textarea"],jf=function(n,e,t){e.disabled()&&Gf(n,e,t)},zf=function(n){return pn.contains(Hf,ie.name(n.element()))},Lf=function(n){Lo.set(n.element(),"disabled","disabled")},Pf=function(n){Lo.remove(n.element(),"disabled")},Wf=function(n){Lo.set(n.element(),"aria-disabled","true")},Uf=function(n){Lo.set(n.element(),"aria-disabled","false")},Gf=function(e,n,t){n.disableClass().each(function(n){nr.add(e.element(),n)}),(zf(e)?Lf:Wf)(e)},$f=function(n){return zf(n)?Lo.has(n.element(),"disabled"):"true"===Lo.get(n.element(),"aria-disabled")},qf=Object.freeze({enable:function(e,n,t){n.disableClass().each(function(n){nr.remove(e.element(),n)}),(zf(e)?Pf:Uf)(e)},disable:Gf,isDisabled:$f,onLoad:jf}),_f=Object.freeze({exhibit:function(n,e,t){return ho({classes:e.disabled()?e.disableClass().map(pn.pure).getOr([]):[]})},events:function(n,e){return to([oo(In(),function(n,e){return $f(n)}),yo(n,e,jf)])}}),Kf=[Lt("disabled",!1),Ht("disableClass")],Xf=Ro({fields:Kf,name:"disabling",active:_f,apis:qf}),Yf=[ja("formBehaviours",[xs])],Jf=function(n){return"<alloy.field."+n+">"},Qf=function(r,n,e){return D.deepMerge({"debug.sketcher":{Form:e},uid:r.uid(),dom:r.dom(),components:n,behaviours:D.deepMerge(Ao([xs.config({store:{mode:"manual",getValue:function(n){var e,t,o=(e=r,t=n.getSystem(),M.map(e.partUids(),function(n,e){return O.constant(t.getByUid(n))}));return M.map(o,function(n,e){return n().bind(Of.getCurrent).map(xs.getValue)})},setValue:function(t,n){M.each(n,function(e,n){bc(t,r,n).each(function(n){Of.getCurrent(n).each(function(n){xs.setValue(n,e)})})})}}})]),za(r.formBehaviours())),apis:{getField:function(n,e){return bc(n,r,e).bind(Of.getCurrent)}}})},Zf=(Cc(function(n,e,t){return n.getField(e,t)}),function(n){var i,e=(i=[],{field:function(n,e){return i.push(n),t="form",o=Jf(n),r=e,{uiType:_a(),owner:t,name:o,config:r,validated:{}};var t,o,r},record:function(){return i}}),t=n(e),o=e.record(),r=pn.map(o,function(n){return sc({name:n,pname:Jf(n)})});return Nc("form",Yf,r,Qf,t)}),nl=function(n){var e=ir(y.none()),t=function(){e.get().each(n)};return{clear:function(){t(),e.set(y.none())},isSet:function(){return e.get().isSome()},set:function(n){t(),e.set(y.some(n))}}},el={destroyable:function(){return nl(function(n){n.destroy()})},unbindable:function(){return nl(function(n){n.unbind()})},api:function(){var e=ir(y.none()),t=function(){e.get().each(function(n){n.destroy()})};return{clear:function(){t(),e.set(y.none())},isSet:function(){return e.get().isSome()},set:function(n){t(),e.set(y.some(n))},run:function(n){e.get().each(n)}}},value:function(){var e=ir(y.none());return{clear:function(){e.set(y.none())},set:function(n){e.set(y.some(n))},isSet:function(){return e.get().isSome()},on:function(n){e.get().each(n)}}}},tl=function(n){return{xValue:n,points:[]}},ol=function(n,e){if(e===n.xValue)return n;var t=0<e-n.xValue?1:-1,o={direction:t,xValue:e};return{xValue:e,points:(0===n.points.length?[]:n.points[n.points.length-1].direction===t?n.points.slice(0,n.points.length-1):n.points).concat([o])}},rl=function(n){if(0===n.points.length)return 0;var e=n.points[0].direction,t=n.points[n.points.length-1].direction;return-1===e&&-1===t?-1:1===e&&1===t?1:0},il=function(n){var o="navigateEvent",e=Et([It("fields"),Lt("maxFieldIndex",n.fields.length-1),It("onExecute"),It("getInitialValue"),Wt("state",function(){return{dialogSwipeState:el.value(),currentScreen:ir(0)}})]),u=Xt("SerialisedDialog",e,n),r=function(e,n,t){return Pc.sketch({dom:$c('<span class="${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:function(n){Un(n,o,{direction:e})},buttonBehaviours:Ao([Xf.config({disableClass:mi.resolve("toolbar-navigation-disabled"),disabled:!t})])})},i=function(n,r){var i=Vi(n.element(),"."+mi.resolve("serialised-dialog-screen"));Li(n.element(),"."+mi.resolve("serialised-dialog-chain")).each(function(o){0<=u.state.currentScreen.get()+r&&u.state.currentScreen.get()+r<i.length&&(Ci.getRaw(o,"left").each(function(n){var e=parseInt(n,10),t=Os(i[0]);Ci.set(o,"left",e-r*t+"px")}),u.state.currentScreen.set(u.state.currentScreen.get()+r))})},a=function(o){var n=Vi(o.element(),"input");y.from(n[u.state.currentScreen.get()]).each(function(n){o.getSystem().getByDom(n).each(function(n){var e,t;e=o,t=n.element(),e.getSystem().triggerFocus(t,e.element())})});var e=s.get(o);fu.highlightAt(e,u.state.currentScreen.get())},c=Ps(Zf(function(t){return{dom:$c('<div class="${prefix}-serialised-dialog"></div>'),components:[Ef.sketch({dom:$c('<div class="${prefix}-serialised-dialog-chain" style="left: 0px; position: absolute;"></div>'),components:pn.map(u.fields,function(n,e){return e<=u.maxFieldIndex?Ef.sketch({dom:$c('<div class="${prefix}-serialised-dialog-screen"></div>'),components:pn.flatten([[r(-1,"previous",0<e)],[t.field(n.name,n.spec)],[r(1,"next",e<u.maxFieldIndex)]])}):t.field(n.name,n.spec)})})],formBehaviours:Ao([fi(function(n,e){var t;t=e,Li(n.element(),"."+mi.resolve("serialised-dialog-chain")).each(function(n){Ci.set(n,"left",-u.state.currentScreen.get()*t.width+"px")})}),Ha.config({mode:"special",focusIn:function(n){a(n)},onTab:function(n){return i(n,1),y.some(!0)},onShiftTab:function(n){return i(n,-1),y.some(!0)}}),Tf("form-events",[so(function(e,n){u.state.currentScreen.set(0),u.state.dialogSwipeState.clear();var t=s.get(e);fu.highlightFirst(t),u.getInitialValue(e).each(function(n){xs.setValue(e,n)})}),mo(u.onExecute),ro(j(),function(n,e){"left"===e.event().raw().propertyName&&a(n)}),ro(o,function(n,e){var t=e.event().direction();i(n,t)})])])}})),s=Ps({dom:$c('<div class="${prefix}-dot-container"></div>'),behaviours:Ao([fu.config({highlightClass:mi.resolve("dot-active"),itemClass:mi.resolve("dot-item")})]),components:pn.bind(u.fields,function(n,e){return e<=u.maxFieldIndex?[qc('<div class="${prefix}-dot-item ${prefix}-icon-full-dot ${prefix}-icon"></div>')]:[]})});return{dom:$c('<div class="${prefix}-serializer-wrapper"></div>'),components:[c.asSpec(),s.asSpec()],behaviours:Ao([Ha.config({mode:"special",focusIn:function(n){var e=c.get(n);Ha.focusIn(e)}}),Tf("serializer-wrapper-events",[ro(T(),function(n,e){u.state.dialogSwipeState.set(tl(e.event().raw().touches[0].clientX))}),ro(k(),function(n,e){u.state.dialogSwipeState.on(function(n){e.event().prevent(),u.state.dialogSwipeState.set(ol(n,e.event().raw().touches[0].clientX))})}),ro(C(),function(o){u.state.dialogSwipeState.on(function(n){var e=c.get(o),t=-1*rl(n);i(e,t)})})])])}},ul=L(function(t,o){return[{label:"the link group",items:[il({fields:[Vf("url","Type or paste URL"),Vf("text","Link text"),Vf("title","Link title"),Vf("target","Link target"),(n="link",{name:n,spec:Df.sketch({dom:{tag:"span",styles:{display:"none"}},getInitialValue:function(){return y.none()}})})],maxFieldIndex:["url","text","title","target"].length-1,getInitialValue:function(){return y.some(yf.getInfo(o))},onExecute:function(n){var e=xs.getValue(n);yf.applyInfo(o,e),t.restoreToolbar(),o.focus()}})]}];var n}),al=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],cl={events:to([(ef=An(),tf=function(n,e){var t,o,r=e.event().originator(),i=e.event().target();return o=i,!(Se(t=r,n.element())&&!Se(t,o)&&(console.warn(An()+" did not get interpreted by the desired target. \nOriginator: "+Mr(r)+"\nTarget: "+Mr(i)+"\nCheck the "+An()+" event handlers"),1))},{key:ef,value:no({can:tf})})])},sl=O.identity,fl=Oo.exactly(["debugInfo","triggerFocus","triggerEvent","triggerEscape","addToWorld","removeFromWorld","addToGui","removeFromGui","build","getByUid","getByDom","broadcast","broadcastOn"]),ll=function(e){var n=function(n){return function(){throw new Error("The component must be in a context to send: "+n+"\n"+Mr(e().element())+" is not in context.")}};return fl({debugInfo:O.constant("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn")})},dl=function(e,n){var t=pn.map(n,function(n){return Rt(n.name(),n.name(),Ye(),Et([It("config"),Lt("state",Mo)]))}),o=_t("component.behaviours",Et(t),e.behaviours).fold(function(n){throw new Error(Jt(n)+"\nComplete spec:\n"+bt(e,null,2))},O.identity);return{list:n,data:M.map(o,function(n){var e=n();return O.constant(e.map(function(n){return{config:n.config(),state:n.state().init(n.config())}}))})}},ml=function(n){return n.list},gl=function(n){return n.data},pl=function(n,r){var i={};return M.each(n,function(n,o){M.each(n,function(n,e){var t=st(e,[])(i);i[e]=t.concat([r(o,n)])})}),i},vl=function(n,e){return{name:O.constant(n),modification:e}},hl=function(n,e,t){return 1<n.length?$e.error('Multiple behaviours have tried to change DOM "'+e+'". The guilty behaviours are: '+bt(pn.map(n,function(n){return n.name()}))+". At this stage, this is not supported. Future releases might provide strategies for resolving this."):0===n.length?$e.value({}):$e.value(n[0].modification().fold(function(){return{}},function(n){return lt(e,n)}))},bl=function(u,a){return pn.foldl(u,function(n,e){var t=e.modification().getOr({});return n.bind(function(i){var n=M.mapToArray(t,function(n,e){return i[e]!==undefined?(t=a,o=e,r=u,$e.error("Mulitple behaviours have tried to change the _"+o+'_ "'+t+'". The guilty behaviours are: '+bt(pn.bind(r,function(n){return n.modification().getOr({})[o]!==undefined?[n.name()]:[]}),null,2)+". This is not currently supported.")):$e.value(lt(e,n));var t,o,r});return mt(n,i)})},$e.value({})).map(function(n){return lt(a,n)})},yl={classes:function(n,e){var t=pn.bind(n,function(n){return n.modification().getOr([])});return $e.value(lt(e,t))},attributes:bl,styles:bl,domChildren:hl,defChildren:hl,innerHtml:hl,value:hl},wl=function(u,a,n,c){var e=n.slice(0);try{var t=e.sort(function(n,e){var t=n[a](),o=e[a](),r=c.indexOf(t),i=c.indexOf(o);if(-1===r)throw new Error("The ordering for "+u+" does not have an entry for "+t+".\nOrder specified: "+bt(c,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+o+".\nOrder specified: "+bt(c,null,2));return r<i?-1:i<r?1:0});return $e.value(t)}catch(o){return $e.error([o])}},xl=function(n,e){return{handler:O.curry.apply(undefined,[n.handler].concat(e)),purpose:n.purpose}},Sl=function(n){return n.handler},Tl=function(n,e){return{name:O.constant(n),handler:O.constant(e)}},kl=function(n,e,t){var o,r,i,u=D.deepMerge(t,(o=e,r=n,i={},pn.each(o,function(n){i[n.name()]=n.handlers(r)}),i));return pl(u,Tl)},Cl=function(n){var e,o=(e=n,E.isFunction(e)?{can:O.constant(!0),abort:O.constant(!1),run:e}:e);return function(n,e){var t=Array.prototype.slice.call(arguments,0);o.abort.apply(undefined,t)?e.stop():o.can.apply(undefined,t)&&o.run.apply(undefined,t)}},Ol=function(n,e,t){var o,r,i=e[t];return i?wl("Event: "+t,"name",n,i).map(function(n){var e=pn.map(n,function(n){return n.handler()});return eo(e)}):(o=t,r=n,$e.error(["The event ("+o+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+bt(pn.map(r,function(n){return n.name()}),null,2)]))},El=function(n,u){var e=M.mapToArray(n,function(r,i){return(1===r.length?$e.value(r[0].handler()):Ol(r,u,i)).map(function(n){var e,t=Cl(n),o=1<r.length?pn.filter(u,function(e){return pn.contains(r,function(n){return n.name()===e})}).join(" > "):r[0].name();return lt(i,(e=o,{handler:t,purpose:O.constant(e)}))})});return mt(e,{})},Dl=function(n){return _t("custom.definition",Ot([Rt("dom","dom",Xe(),Ot([It("tag"),Lt("styles",{}),Lt("classes",[]),Lt("attributes",{}),Ht("value"),Ht("innerHtml")])),It("components"),It("uid"),Lt("events",{}),Lt("apis",O.constant({})),Rt("eventOrder","eventOrder",(e={"alloy.execute":["disabling","alloy.base.behaviour","toggling"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing"]},_e.mergeWithThunk(O.constant(e))),Zt()),Ht("domModification"),Kr("originalSpec"),Lt("debug.sketcher","unknown")]),n);var e},Ml=function(n){var e,t={tag:n.dom().tag(),classes:n.dom().classes(),attributes:D.deepMerge((e=n,lt(Dc(),e.uid())),n.dom().attributes()),styles:n.dom().styles(),domChildren:pn.map(n.components(),function(n){return n.element()})};return po(D.deepMerge(t,n.dom().innerHtml().map(function(n){return lt("innerHtml",n)}).getOr({}),n.dom().value().map(function(n){return lt("value",n)}).getOr({})))},Al={add:function(e,n){pn.each(n,function(n){nr.add(e,n)})},remove:function(e,n){pn.each(n,function(n){nr.remove(e,n)})},toggle:function(e,n){pn.each(n,function(n){nr.toggle(e,n)})},hasAll:function(e,n){return pn.forall(n,function(n){return nr.has(e,n)})},hasAny:function(e,n){return pn.exists(n,function(n){return nr.has(e,n)})},get:function(n){return Qo(n)?function(n){for(var e=n.dom().classList,t=new Array(e.length),o=0;o<e.length;o++)t[o]=e.item(o);return t}(n):Ko(n)}},Bl=function(e){if(e.domChildren().isSome()&&e.defChildren().isSome())throw new Error("Cannot specify children and child specs! Must be one or the other.\nDef: "+(n=vo(e),bt(n,null,2)));return e.domChildren().fold(function(){var n=e.defChildren().getOr([]);return pn.map(n,Il)},function(n){return n});var n},Rl=function(n){var e=Kn.fromTag(n.tag());Lo.setAll(e,n.attributes().getOr({})),Al.add(e,n.classes().getOr([])),Ci.setAll(e,n.styles().getOr({})),Cr(e,n.innerHtml().getOr(""));var t=Bl(n);return Fe.append(e,t),n.value().each(function(n){Mf(e,n)}),e},Il=function(n){var e=po(n);return Rl(e)},Fl=function(n){var e,t,o,r=(t=ft(e=n,"behaviours").getOr({}),o=pn.filter(M.keys(t),function(n){return t[n]!==undefined}),pn.map(o,function(n){return e.behaviours[n].me}));return dl(n,r)},Nl=Oo.exactly(["getSystem","config","hasConfigured","spec","connect","disconnect","element","syncComponents","readState","components","events"]),Vl=function(t){var n,e,o,r,i,u,a,c,s,f,l=function(){return C},d=ir(ll(l)),m=Kt(Dl(D.deepMerge(t,{behaviours:undefined}))),g=Fl(t),p=ml(g),v=gl(g),h=Ml(m),b={"alloy.base.modification":(n=m,n.domModification().fold(function(){return ho({})},ho))},y=function(e,n,t,o){var r=D.deepMerge({},n);pn.each(t,function(n){r[n.name()]=n.exhibit(e,o)});var i=pl(r,vl),u=M.map(i,function(n,e){return pn.bind(n,function(e){return e.modification().fold(function(){return[]},function(n){return[e]})})}),a=M.mapToArray(u,function(e,t){return ft(yl,t).fold(function(){return $e.error("Unknown field type: "+t)},function(n){return n(e,t)})});return mt(a,{}).map(ho)}(v,b,p,h).getOrDie(),w=(e=h,o=y,r=D.deepMerge({tag:e.tag(),classes:o.classes().getOr([]).concat(e.classes().getOr([])),attributes:D.merge(e.attributes().getOr({}),o.attributes().getOr({})),styles:D.merge(e.styles().getOr({}),o.styles().getOr({}))},o.innerHtml().or(e.innerHtml()).map(function(n){return lt("innerHtml",n)}).getOr({}),bo("domChildren",o.domChildren(),e.domChildren()),bo("defChildren",o.defChildren(),e.defChildren()),o.value().or(e.value()).map(function(n){return lt("value",n)}).getOr({})),po(r)),x=Rl(w),S={"alloy.base.behaviour":(i=m,i.events())},T=(u=v,a=m.eventOrder(),c=p,s=S,f=kl(u,c,s),El(f,a)).getOrDie(),k=ir(m.components()),C=Nl({getSystem:d.get,config:function(n){if(n===Oc())return m.apis();var e=v;return(E.isFunction(e[n.name()])?e[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+bt(t,null,2))})()},hasConfigured:function(n){return E.isFunction(v[n.name()])},spec:O.constant(t),readState:function(n){return v[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},connect:function(n){d.set(n)},disconnect:function(){d.set(ll(l))},element:O.constant(x),syncComponents:function(){var n=Ae.children(x),e=pn.bind(n,function(n){return d.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});k.set(e)},components:k.get,events:O.constant(T)});return C},Hl=function(n){var e,t,o=sl(n),r=(e=o,t=st("components",[])(e),pn.map(t,Ll)),i=D.deepMerge(cl,o,lt("components",r));return $e.value(Vl(i))},jl=function(n){var e=Kn.fromText(n);return zl({element:e})},zl=function(n){var t=Yt("external.component",Ot([It("element"),Ht("uid")]),n),e=ir(ll());t.uid().each(function(n){var e;e=t.element(),Lo.set(e,Ac,n)});var o=Nl({getSystem:e.get,config:y.none,hasConfigured:O.constant(!1),connect:function(n){e.set(n)},disconnect:function(){e.set(ll(function(){return o}))},element:O.constant(t.element()),spec:O.constant(n),readState:O.constant("No state"),syncComponents:O.noop,components:O.constant([]),events:O.constant({})});return kc(o)},Ll=function(e){return(n=e,ft(n,Sc)).fold(function(){var n=D.deepMerge({uid:Ic("")},e);return Hl(n).getOrDie()},function(n){return n});var n},Pl=kc,Wl="alloy.item-hover",Ul="alloy.item-focus",Gl=function(n){(hr(n.element()).isNone()||bi.isFocused(n))&&(bi.isFocused(n)||bi.focus(n),Un(n,Wl,{item:n}))},$l=function(n){Un(n,Ul,{item:n})},ql=O.constant(Wl),_l=O.constant(Ul),Kl=[It("data"),It("components"),It("dom"),Ht("toggling"),Lt("itemBehaviours",{}),Lt("ignoreFocus",!1),Lt("domModification",{}),_r("builder",function(n){return{dom:D.deepMerge(n.dom(),{attributes:{role:n.toggling().isSome()?"menuitemcheckbox":"menuitem"}}),behaviours:D.deepMerge(Ao([n.toggling().fold(ci.revoke,function(n){return ci.config(D.deepMerge({aria:{mode:"checked"}},n))}),bi.config({ignore:n.ignoreFocus(),onFocus:function(n){$l(n)}}),Ha.config({mode:"execution"}),xs.config({store:{mode:"memory",initialValue:n.data()}})]),n.itemBehaviours()),events:to([(e=Vn(),o=Gn,ro(e,function(e,t){e.getSystem().getByDom(t.event().target()).each(function(n){o(e,n,t)})})),co(A()),ro(I(),Gl),ro(Fn(),bi.focus)]),components:n.components(),domModification:n.domModification()};var e,o})],Xl=[It("dom"),It("components"),_r("builder",function(n){return{dom:n.dom(),components:n.components(),events:to([(e=Fn(),ro(e,function(n,e){e.stop()}))])};var e})],Yl=O.constant("item-widget"),Jl=O.constant([sc({name:"widget",overrides:function(e){return{behaviours:Ao([xs.config({store:{mode:"manual",getValue:function(n){return e.data()},setValue:function(){}}})])}}})]),Ql=[It("uid"),It("data"),It("components"),It("dom"),Lt("autofocus",!1),Lt("domModification",{}),xc(Jl()),_r("builder",function(t){var n=vc(Yl(),t,Jl()),e=hc(Yl(),t,n.internals()),o=function(n){return bc(n,t,"widget").map(function(n){return Ha.focusIn(n),n})},r=function(n,e){return vu(e.event().target())||t.autofocus()&&e.setSource(n.element()),y.none()};return D.deepMerge({dom:t.dom(),components:e,domModification:t.domModification(),events:to([mo(function(n,e){o(n).each(function(n){e.stop()})}),ro(I(),Gl),ro(Fn(),function(n,e){t.autofocus()?o(n):bi.focus(n)})]),behaviours:Ao([xs.config({store:{mode:"memory",initialValue:t.data()}}),bi.config({onFocus:function(n){$l(n)}}),Ha.config({mode:"special",onLeft:r,onRight:r,onEscape:function(n,e){return bi.isFocused(n)||t.autofocus()?(t.autofocus()&&e.setSource(n.element()),y.none()):(bi.focus(n),y.some(!0))}})])})})],Zl=Qt("type",{widget:Ql,item:Kl,separator:Xl}),nd=O.constant([lc({factory:{sketch:function(n){var e=Yt("menu.spec item",Zl,n);return e.builder()(e)}},name:"items",unit:"item",defaults:function(n,e){var t=Ic("");return D.deepMerge({uid:t},e)},overrides:function(n,e){return{type:e.type,ignoreFocus:n.fakeFocus(),domModification:{classes:[n.markers().item()]}}}})]),ed=O.constant([It("value"),It("items"),It("dom"),It("components"),Lt("eventOrder",{}),ja("menuBehaviours",[fu,xs,Of,Ha]),Pt("movement",{mode:"menu",moveOnTab:!0},Qt("mode",{grid:[Xr(),_r("config",function(n,e){return{mode:"flatgrid",selector:"."+n.markers().item(),initSize:{numColumns:e.initSize().numColumns(),numRows:e.initSize().numRows()},focusManager:n.focusManager()}})],menu:[Lt("moveOnTab",!0),_r("config",function(n,e){return{mode:"menu",selector:"."+n.markers().item(),moveOnTab:e.moveOnTab(),focusManager:n.focusManager()}})]})),Ft("markers",Lr()),Lt("fakeFocus",!1),Lt("focusManager",lu()),Ur("onHighlight")]),td=(O.constant("menu"),O.constant("alloy.menu-focus")),od=Lc({name:"Menu",configFields:ed(),partFields:nd(),factory:function(n,e,t,o){return D.deepMerge({dom:D.deepMerge(n.dom(),{attributes:{role:"menu"}}),uid:n.uid(),behaviours:D.deepMerge(Ao([fu.config({highlightClass:n.markers().selectedItem(),itemClass:n.markers().item(),onHighlight:n.onHighlight()}),xs.config({store:{mode:"memory",initialValue:n.value()}}),Of.config({find:O.identity}),Ha.config(n.movement().config()(n,n.movement()))]),za(n.menuBehaviours())),events:to([ro(_l(),function(e,t){var n=t.event();e.getSystem().getByDom(n.target()).each(function(n){fu.highlight(e,n),t.stop(),Un(e,td(),{menu:e,item:n})})}),ro(ql(),function(n,e){var t=e.event().item();fu.highlight(n,t)})]),components:e,eventOrder:n.eventOrder()})}}),rd=function(n,e,t,o){var r=n.getSystem().build(o);Le(n,r,t)},id=function(n,e){return n.components()},ud=Object.freeze({append:function(n,e,t,o){rd(n,0,Ie.append,o)},prepend:function(n,e,t,o){rd(n,0,Ie.prepend,o)},remove:function(n,e,t,o){var r=id(n,e);pn.find(r,function(n){return Se(o.element(),n.element())}).each(We)},set:function(e,n,t,o){var r,i,u,a,c,s;i=(r=e).components(),pn.each(i,Pe),Ve.empty(r.element()),r.syncComponents(),u=function(){var n=pn.map(o,e.getSystem().build);pn.each(n,function(n){ze(e,n)})},a=e.element(),c=Ae.owner(a),s=vr(c).bind(function(e){var n=function(n){return Se(e,n)};return n(a)?y.some(a):fr.descendant(a,n)}),u(a),s.each(function(e){vr(c).filter(function(n){return Se(n,e)}).orThunk(function(){gr(e)})})},contents:id}),ad=Ro({fields:[],name:"replacing",apis:ud}),cd=function(t,o,r,n){return ft(r,n).bind(function(n){return ft(t,n).bind(function(n){var e=cd(t,o,r,n);return y.some([n].concat(e))})}).getOr([])},sd=function(n,e){var t={};M.each(n,function(n,e){pn.each(n,function(n){t[n]=e})});var o,r=e,i=(o=e,M.tupleMap(o,function(n,e){return{k:n,v:e}})),u=M.map(i,function(n,e){return[e].concat(cd(t,r,i,e))});return M.map(t,function(n){return ft(u,n).getOr([n])})},fd=O.constant("collapse-item"),ld=zc({name:"TieredMenu",configFields:[qr("onExecute"),qr("onEscape"),$r("onOpenMenu"),$r("onOpenSubmenu"),Ur("onCollapseMenu"),Lt("openImmediately",!0),Vt("data",[It("primary"),It("menus"),It("expansions")]),Lt("fakeFocus",!1),Ur("onHighlight"),Ur("onHover"),Vt("markers",[It("backgroundMenu")].concat(jr()).concat(zr())),It("dom"),Lt("navigateOnHover",!0),Lt("stayInDom",!1),ja("tmenuBehaviours",[Ha,fu,Of,ad]),Lt("eventOrder",{})],apis:{collapseMenu:function(n,e){n.collapseMenu(e)}},factory:function(u,r){var a,c,s,f,l,n,i=function(o,n){return M.map(n,function(n,e){var t=od.sketch(D.deepMerge(n,{value:e,items:n.items,markers:ut(r.markers,["item","selectedItem"]),fakeFocus:u.fakeFocus(),onHighlight:u.onHighlight(),focusManager:u.fakeFocus()?{get:function(n){return fu.getHighlighted(n).map(function(n){return n.element()})},set:function(e,n){e.getSystem().getByDom(n).fold(O.noop,function(n){fu.highlight(e,n)})}}:lu()}));return o.getSystem().build(t)})},d=(a=ir({}),c=ir({}),s=ir({}),f=ir(y.none()),l=ir(O.constant([])),{setContents:function(n,e,t,o){f.set(y.some(n)),a.set(t),c.set(e),l.set(o);var r=o(e),i=sd(r,t);s.set(i)},expand:function(t){return ft(a.get(),t).map(function(n){var e=ft(s.get(),t).getOr([]);return[n].concat(e)})},refresh:function(n){return ft(s.get(),n)},collapse:function(n){return ft(s.get(),n).bind(function(n){return 1<n.length?y.some(n.slice(1)):y.none()})},lookupMenu:n=function(n){return ft(c.get(),n)},otherMenus:function(n){var e=l.get()(c.get());return pn.difference(M.keys(e),n)},getPrimary:function(){return f.get().bind(n)},getMenus:function(){return c.get()},clear:function(){a.set({}),c.set({}),s.set({}),f.set(y.none())},isClear:function(){return f.get().isNone()}}),m=function(n){return xs.getValue(n).value},g=function(n,e){return M.map(u.data().menus(),function(n,e){return pn.bind(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})},p=function(e,n){fu.highlight(e,n),fu.getHighlighted(n).orThunk(function(){return fu.getFirst(n)}).each(function(n){$n(e,n.element(),Fn())})},v=function(n,e){return Br(pn.map(e,n.lookupMenu))},h=function(o,r,i){return y.from(i[0]).bind(r.lookupMenu).map(function(n){var e=v(r,i.slice(1));pn.each(e,function(n){nr.add(n.element(),u.markers().backgroundMenu())}),ce.inBody(n.element())||ad.append(o,Pl(n)),Al.remove(n.element(),[u.markers().backgroundMenu()]),p(o,n);var t=v(r,r.otherMenus(i));return pn.each(t,function(n){Al.remove(n.element(),[u.markers().backgroundMenu()]),u.stayInDom()||ad.remove(o,n)}),n})},b=function(e,t){var n=m(t);return d.expand(n).bind(function(n){return y.from(n[0]).bind(d.lookupMenu).each(function(n){ce.inBody(n.element())||ad.append(e,Pl(n)),u.onOpenSubmenu()(e,t,n),fu.highlightFirst(n)}),h(e,d,n)})},o=function(e,t){var n=m(t);return d.collapse(n).bind(function(n){return h(e,d,n).map(function(n){return u.onCollapseMenu()(e,t,n),n})})},e=function(t){return function(e,n){return Pi(n.getSource(),"."+u.markers().item()).bind(function(n){return e.getSystem().getByDom(n).bind(function(n){return t(e,n)})})}},t=to([ro(td(),function(n,e){var t=e.event().menu();fu.highlight(n,t)}),mo(function(e,n){var t=n.event().target();return e.getSystem().getByDom(t).bind(function(n){return 0===m(n).indexOf("collapse-item")?o(e,n):b(e,n).orThunk(function(){return u.onExecute()(e,n)})})}),so(function(e,n){var t,o;(t=e,o=i(t,u.data().menus()),d.setContents(u.data().primary(),o,u.data().expansions(),function(n){return g(t,n)}),d.getPrimary()).each(function(n){ad.append(e,Pl(n)),u.openImmediately()&&(p(e,n),u.onOpenMenu()(e,n))})})].concat(u.navigateOnHover()?[ro(ql(),function(n,e){var t,o,r=e.event().item();t=n,o=m(r),d.refresh(o).bind(function(n){return h(t,d,n)}),b(n,r),u.onHover()(n,r)})]:[]));return{uid:u.uid(),dom:u.dom(),behaviours:D.deepMerge(Ao([Ha.config({mode:"special",onRight:e(function(n,e){return vu(e.element())?y.none():b(n,e)}),onLeft:e(function(n,e){return vu(e.element())?y.none():o(n,e)}),onEscape:e(function(n,e){return o(n,e).orThunk(function(){return u.onEscape()(n,e)})}),focusIn:function(e,n){d.getPrimary().each(function(n){$n(e,n.element(),Fn())})}}),fu.config({highlightClass:u.markers().selectedMenu(),itemClass:u.markers().menu()}),Of.config({find:function(n){return fu.getHighlighted(n)}}),ad.config({})]),za(u.tmenuBehaviours())),eventOrder:u.eventOrder(),apis:{collapseMenu:function(e){fu.getHighlighted(e).each(function(n){fu.getHighlighted(n).each(function(n){o(e,n)})})}},events:t}},extraApis:{tieredData:function(n,e,t){return{primary:n,menus:e,expansions:t}},singleData:function(n,e){return{primary:n,menus:lt(n,e),expansions:{}}},collapseItem:function(n){return{value:Xa(fd()),text:n}}}}),dd=function(n,e,t,o){return ft(e.routes(),o.start()).map(O.apply).bind(function(n){return ft(n,o.destination()).map(O.apply)})},md=function(n,e,t,o){return dd(0,e,0,o).bind(function(e){return e.transition().map(function(n){return{transition:O.constant(n),route:O.constant(e)}})})},gd=function(t,o,n){var e,r,i;(e=t,r=o,i=n,pd(e,r,i).bind(function(n){return md(e,r,i,n)})).each(function(n){var e=n.transition();nr.remove(t.element(),e.transitionClass()),Lo.remove(t.element(),o.destinationAttr())})},pd=function(n,e,t){var o=n.element();return Lo.has(o,e.destinationAttr())?y.some({start:O.constant(Lo.get(n.element(),e.stateAttr())),destination:O.constant(Lo.get(n.element(),e.destinationAttr()))}):y.none()},vd=function(n,e,t,o){gd(n,e,t),Lo.has(n.element(),e.stateAttr())&&Lo.get(n.element(),e.stateAttr())!==o&&e.onFinish()(n,o),Lo.set(n.element(),e.stateAttr(),o)},hd=Object.freeze({findRoute:dd,disableTransition:gd,getCurrentRoute:pd,jumpTo:vd,progressTo:function(t,o,r,i){var n,e;e=o,Lo.has((n=t).element(),e.destinationAttr())&&(Lo.set(n.element(),e.stateAttr(),Lo.get(n.element(),e.destinationAttr())),Lo.remove(n.element(),e.destinationAttr()));var u,a,c,s=(u=t,a=o,c=i,{start:O.constant(Lo.get(u.element(),a.stateAttr())),destination:O.constant(c)});md(t,o,r,s).fold(function(){vd(t,o,r,i)},function(n){gd(t,o,r);var e=n.transition();nr.add(t.element(),e.transitionClass()),Lo.set(t.element(),o.destinationAttr(),i)})},getState:function(n,e,t){var o=n.element();return Lo.has(o,e.stateAttr())?y.some(Lo.get(o,e.stateAttr())):y.none()}}),bd=Object.freeze({events:function(r,i){return to([ro(j(),function(t,n){var o=n.event().raw();pd(t,r,i).each(function(e){dd(0,r,0,e).each(function(n){n.transition().each(function(n){o.propertyName===n.property()&&(vd(t,r,i,e.destination()),r.onTransition()(t,e))})})})}),so(function(n,e){vd(n,r,i,r.initialState())})])}}),yd=[Lt("destinationAttr","data-transitioning-destination"),Lt("stateAttr","data-transitioning-state"),It("initialState"),Ur("onTransition"),Ur("onFinish"),Ft("routes",Mt($e.value,Mt($e.value,Ot([zt("transition",[It("property"),It("transitionClass")])]))))],wd=Ro({fields:yd,name:"transitioning",active:bd,apis:hd,extra:{createRoutes:function(n){var o={};return M.each(n,function(n,e){var t=e.split("<->");o[t[0]]=lt(t[1],n),o[t[1]]=lt(t[0],n)}),o},createBistate:function(n,e,t){return dt([{key:n,value:lt(e,t)},{key:e,value:lt(n,t)}])},createTristate:function(n,e,t,o){return dt([{key:n,value:dt([{key:e,value:o},{key:t,value:o}])},{key:e,value:dt([{key:n,value:o},{key:t,value:o}])},{key:t,value:dt([{key:n,value:o},{key:e,value:o}])}])}}}),xd=mi.resolve("scrollable"),Sd={register:function(n){nr.add(n,xd)},deregister:function(n){nr.remove(n,xd)},scrollable:O.constant(xd)},Td=function(n){return ft(n,"format").getOr(n.title)},kd=function(n,e,t,o,r){return{data:{value:n,text:e},type:"item",dom:{tag:"div",classes:r?[mi.resolve("styles-item-is-menu")]:[]},toggling:{toggleOnExecute:!1,toggleClass:mi.resolve("format-matches"),selected:t},itemBehaviours:Ao(r?[]:[si(n,function(n,e){(e?ci.on:ci.off)(n)})]),components:[{dom:{tag:"div",attributes:{style:o},innerHtml:e}}]}},Cd=function(n,e,t,o){return{value:n,dom:{tag:"div"},components:[Pc.sketch({dom:{tag:"div",classes:[mi.resolve("styles-collapser")]},components:o?[{dom:{tag:"span",classes:[mi.resolve("styles-collapse-icon")]}},jl(n)]:[jl(n)],action:function(n){if(o){var e=t().get(n);ld.collapseMenu(e)}}}),{dom:{tag:"div",classes:[mi.resolve("styles-menu-items-container")]},components:[od.parts().items({})],behaviours:Ao([Tf("adhoc-scrollable-menu",[so(function(n,e){Ci.set(n.element(),"overflow-y","auto"),Ci.set(n.element(),"-webkit-overflow-scrolling","touch"),Sd.register(n.element())}),fo(function(n){Ci.remove(n.element(),"overflow-y"),Ci.remove(n.element(),"-webkit-overflow-scrolling"),Sd.deregister(n.element())})])])}],items:e,menuBehaviours:Ao([wd.config({initialState:"after",routes:wd.createTristate("before","current","after",{transition:{property:"transform",transitionClass:"transitioning"}})})])}},Od=function(o){var r,i,n,e,t,u=(r=o.formats,i=function(){return a},n=Cd("Styles",[].concat(pn.map(r.items,function(n){return kd(Td(n),n.title,n.isSelected(),n.getPreview(),gt(r.expansions,Td(n)))})),i,!1),e=M.map(r.menus,function(n,e){var t=pn.map(n,function(n){return kd(Td(n),n.title,n.isSelected!==undefined&&n.isSelected(),n.getPreview!==undefined?n.getPreview():"",gt(r.expansions,Td(n)))});return Cd(e,t,i,!0)}),t=D.deepMerge(e,lt("styles",n)),{tmenu:ld.tieredData("styles",t,r.expansions)}),a=Ps(ld.sketch({dom:{tag:"div",classes:[mi.resolve("styles-menu")]},components:[],fakeFocus:!0,stayInDom:!0,onExecute:function(n,e){var t=xs.getValue(e);o.handle(e,t.value)},onEscape:function(){},onOpenMenu:function(n,e){var t=Os(n.element());Cs(e.element(),t),wd.jumpTo(e,"current")},onOpenSubmenu:function(n,e,t){var o=Os(n.element()),r=zi(e.element(),'[role="menu"]').getOrDie("hacky"),i=n.getSystem().getByDom(r).getOrDie();Cs(t.element(),o),wd.progressTo(i,"before"),wd.jumpTo(t,"after"),wd.progressTo(t,"current")},onCollapseMenu:function(n,e,t){var o=zi(e.element(),'[role="menu"]').getOrDie("hacky"),r=n.getSystem().getByDom(o).getOrDie();wd.progressTo(r,"after"),wd.progressTo(t,"current")},navigateOnHover:!1,openImmediately:!0,data:u.tmenu,markers:{backgroundMenu:mi.resolve("styles-background-menu"),menu:mi.resolve("styles-menu"),selectedMenu:mi.resolve("styles-selected-menu"),item:mi.resolve("styles-item"),selectedItem:mi.resolve("styles-selected-item")}}));return a.asSpec()},Ed=function(n){return gt(n,"items")?(e=n,t=D.deepMerge(at(e,["items"]),{menu:!0}),o=Dd(e.items),{item:t,menus:D.deepMerge(o.menus,lt(e.title,o.items)),expansions:D.deepMerge(o.expansions,lt(e.title,e.title))}):{item:n,menus:{},expansions:{}};var e,t,o},Dd=function(n){return pn.foldr(n,function(n,e){var t=Ed(e);return{menus:D.deepMerge(n.menus,t.menus),items:[t.item].concat(n.items),expansions:D.deepMerge(n.expansions,t.expansions)}},{menus:{},expansions:{},items:[]})},Md={expand:Dd},Ad=function(a,n){var c=function(n){return function(){return a.formatter.match(n)}},s=function(n){return function(){return a.formatter.getCssText(n)}},e=ft(n,"style_formats").getOr(al),f=function(n){return pn.map(n,function(n){if(gt(n,"items")){var e=f(n.items);return D.deepMerge((u=n,D.deepMerge(u,{isSelected:O.constant(!1),getPreview:O.constant("")})),{items:e})}return gt(n,"format")?(i=n,D.deepMerge(i,{isSelected:c(i.format),getPreview:s(i.format)})):(o=Xa((t=n).title),r=D.deepMerge(t,{format:o,isSelected:c(o),getPreview:s(o)}),a.formatter.register(o,r),r);var t,o,r,i,u})};return f(e)},Bd=function(t,n,o){var e,r,i,u=(e=t,i=(r=function(n){return pn.bind(n,function(n){return n.items!==undefined?0<r(n.items).length?[n]:[]:!gt(n,"format")||e.formatter.canApply(n.format)?[n]:[]})})(n),Md.expand(i));return Od({formats:u,handle:function(n,e){t.undoManager.transact(function(){ci.isOn(n)?t.formatter.remove(e):t.formatter.apply(e)}),o()}})},Rd=["undo","bold","italic","link","image","bullist","styleselect"],Id=function(n){var e=n.replace(/\|/g," ").trim();return 0<e.length?e.split(/\s+/):[]},Fd=function(n){return pn.bind(n,function(n){return E.isArray(n)?Fd(n):Id(n)})},Nd=function(n){var e=n.toolbar!==undefined?n.toolbar:Rd;return E.isArray(e)?Fd(e):Id(e)},Vd=function(o,r){var n=function(n){return function(){return Xc.forToolbarCommand(r,n)}},e=function(n){return function(){return Xc.forToolbarStateCommand(r,n)}},t=function(n,e,t){return function(){return Xc.forToolbarStateAction(r,n,e,t)}},i=n("undo"),u=n("redo"),a=e("bold"),c=e("italic"),s=e("underline"),f=n("removeformat"),l=t("unlink","link",function(){r.execCommand("unlink",null,!1)}),d=t("unordered-list","ul",function(){r.execCommand("InsertUnorderedList",null,!1)}),m=t("ordered-list","ol",function(){r.execCommand("InsertOrderedList",null,!1)}),g=Ad(r,r.settings),p=function(){return Bd(r,g,function(){r.fire("scrollIntoView")})},v=function(n,e){return{isSupported:function(){return n.forall(function(n){return gt(r.buttons,n)})},sketch:e}};return{undo:v(y.none(),i),redo:v(y.none(),u),bold:v(y.none(),a),italic:v(y.none(),c),underline:v(y.none(),s),removeformat:v(y.none(),f),link:v(y.none(),function(){return e=o,t=r,Xc.forToolbarStateAction(t,"link","link",function(){var n=ul(e,t);e.setContextToolbar(n),Sf(t,function(){e.focusToolbar()}),yf.query(t).each(function(n){t.selection.select(n.dom())})});var e,t}),unlink:v(y.none(),l),image:v(y.none(),function(){return df(r)}),bullist:v(y.some("bullist"),d),numlist:v(y.some("numlist"),m),fontsizeselect:v(y.none(),function(){return e=r,n={onChange:function(n){js.apply(e,n)},getInitialValue:function(){return js.get(e)}},Ms(o,"font-size",function(){return Ls(n)});var e,n}),forecolor:v(y.none(),function(){return Bs(o,r)}),styleselect:v(y.none(),function(){return Xc.forToolbar("style-formats",function(n){r.fire("toReading"),o.dropup().appear(p,ci.on,n)},Ao([ci.config({toggleClass:mi.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),Jr.config({channels:dt([li(xr.orientationChanged(),ci.off),li(xr.dropupDismissed(),ci.off)])})]))})}},Hd=function(n,t){var e=Nd(n),o={};return pn.bind(e,function(n){var e=!gt(o,n)&&gt(t,n)&&t[n].isSupported()?[t[n].sketch()]:[];return o[n]=!0,e})},jd=function(m,g){return function(n){if(m(n)){var e,t,o,r,i,u,a,c=Kn.fromDom(n.target),s=function(){n.stopPropagation()},f=function(){n.preventDefault()},l=O.compose(f,s),d=(e=c,t=n.clientX,o=n.clientY,r=s,i=f,u=l,a=n,{target:O.constant(e),x:O.constant(t),y:O.constant(o),stop:r,prevent:i,kill:u,raw:O.constant(a)});g(d)}}},zd=function(n,e,t,o,r){var i=jd(t,o);return n.dom().addEventListener(e,i,r),{unbind:O.curry(Ld,n,e,i,r)}},Ld=function(n,e,t,o){n.dom().removeEventListener(e,t,o)},Pd=function(n,e,t,o){return zd(n,e,t,o,!1)},Wd=function(n,e,t,o){return zd(n,e,t,o,!0)},Ud=O.constant(!0),Gd={bind:function(n,e,t){return Pd(n,e,Ud,t)},capture:function(n,e,t){return Wd(n,e,Ud,t)}},$d=function(n){var e=n.matchMedia("(orientation: portrait)").matches;return{isPortrait:O.constant(e)}},qd=$d,_d=function(o,e){var n=Kn.fromDom(o),r=null,t=Gd.bind(n,"orientationchange",function(){clearInterval(r);var n=$d(o);e.onChange(n),i(function(){e.onReady(n)})}),i=function(n){clearInterval(r);var e=o.innerHeight,t=0;r=setInterval(function(){e!==o.innerHeight?(clearInterval(r),n(y.some(o.innerHeight))):20<t&&(clearInterval(r),n(y.none())),t++},50)};return{onAdjustment:i,destroy:function(){t.unbind()}}},Kd=function(n){var e=Dn.detect().os.isiOS(),t=$d(n).isPortrait();return e&&!t?n.screen.height:n.screen.width},Xd=function(n){return n.raw().touches===undefined||1!==n.raw().touches.length?y.none():y.some(n.raw().touches[0])},Yd=function(t){var o,r,i,u=ir(y.none()),a=(o=function(n){u.set(y.none()),t.triggerEvent(Hn(),n)},r=400,i=null,{cancel:function(){null!==i&&(clearTimeout(i),i=null)},schedule:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=arguments;i=setTimeout(function(){o.apply(null,t),i=null},r)}}),c=dt([{key:T(),value:function(t){return Xd(t).each(function(n){a.cancel();var e={x:O.constant(n.clientX),y:O.constant(n.clientY),target:t.target};a.schedule(t),u.set(y.some(e))}),y.none()}},{key:k(),value:function(n){return a.cancel(),Xd(n).each(function(i){u.get().each(function(n){var e,t,o,r;e=i,t=n,o=Math.abs(e.clientX-t.x()),r=Math.abs(e.clientY-t.y()),(5<o||5<r)&&u.set(y.none())})}),y.none()}},{key:C(),value:function(e){return a.cancel(),u.get().filter(function(n){return Se(n.target(),e.target())}).map(function(n){return t.triggerEvent(Nn(),e)})}}]);return{fireIfReady:function(e,n){return ft(c,n).bind(function(n){return n(e)})}}},Jd=function(t){var e=Yd({triggerEvent:function(n,e){t.onTapContent(e)}});return{fireTouchstart:function(n){e.fireIfReady(n,"touchstart")},onTouchend:function(){return Gd.bind(t.body(),"touchend",function(n){e.fireIfReady(n,"touchend")})},onTouchmove:function(){return Gd.bind(t.body(),"touchmove",function(n){e.fireIfReady(n,"touchmove")})}}},Qd=6<=Dn.detect().os.version.major,Zd=function(o,e,t){var r=Jd(o),i=Ae.owner(e),u=function(n){return!Se(n.start(),n.finish())||n.soffset()!==n.foffset()},n=function(){var n=o.doc().dom().hasFocus()&&o.getSelection().exists(u);t.getByDom(e).each(!0===(n||vr(i).filter(function(n){return"input"===ie.name(n)}).exists(function(n){return n.dom().selectionStart!==n.dom().selectionEnd}))?ci.on:ci.off)},a=[Gd.bind(o.body(),"touchstart",function(n){o.onTouchContent(),r.fireTouchstart(n)}),r.onTouchmove(),r.onTouchend(),Gd.bind(e,"touchstart",function(n){o.onTouchToolstrip()}),o.onToReading(function(){pr(o.body())}),o.onToEditing(O.noop),o.onScrollToCursor(function(n){n.preventDefault(),o.getCursorBox().each(function(n){var e=o.win(),t=n.top()>e.innerHeight||n.bottom()>e.innerHeight?n.bottom()-e.innerHeight+50:0;0!==t&&e.scrollTo(e.pageXOffset,e.pageYOffset+t)})})].concat(!0===Qd?[]:[Gd.bind(Kn.fromDom(o.win()),"blur",function(){t.getByDom(e).each(ci.off)}),Gd.bind(i,"select",n),Gd.bind(o.doc(),"selectionchange",n)]);return{destroy:function(){pn.each(a,function(n){n.unbind()})}}},nm=function(n,e){var t=parseInt(Lo.get(n,e),10);return isNaN(t)?0:t},em=(of=ie.isText,rf="text",uf=function(n){return of(n)?y.from(n.dom().nodeValue):y.none()},af=Dn.detect().browser,{get:function(n){if(!of(n))throw new Error("Can only get "+rf+" value of a "+rf+" node");return cf(n).getOr("")},getOption:cf=af.isIE()&&10===af.version.major?function(n){try{return uf(n)}catch(e){return y.none()}}:uf,set:function(n,e){if(!of(n))throw new Error("Can only set raw "+rf+" value of a "+rf+" node");n.dom().nodeValue=e}}),tm=function(n){return em.getOption(n)},om=function(n){return"img"===ie.name(n)?1:tm(n).fold(function(){return Ae.children(n).length},function(n){return n.length})},rm=om,im=qe([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),um={before:im.before,on:im.on,after:im.after,cata:function(n,e,t,o){return n.fold(e,t,o)},getStart:function(n){return n.fold(O.identity,O.identity,O.identity)}},am=qe([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),cm=le.immutable("start","soffset","finish","foffset"),sm={domRange:am.domRange,relative:am.relative,exact:am.exact,exactFromRange:function(n){return am.exact(n.start(),n.soffset(),n.finish(),n.foffset())},range:cm,getWin:function(n){var e=n.match({domRange:function(n){return Kn.fromDom(n.startContainer)},relative:function(n,e){return um.getStart(n)},exact:function(n,e,t,o){return n}});return Ae.defaultView(e)}},fm=function(n,e,t,o){var r=Ae.owner(n).dom().createRange();return r.setStart(n.dom(),e),r.setEnd(t.dom(),o),r},lm=function(n,e,t,o){var r=fm(n,e,t,o),i=Se(n,t)&&e===o;return r.collapsed&&!i},dm=function(n,e){n.selectNodeContents(e.dom())},mm=function(n){n.deleteContents()},gm=function(n){return{left:O.constant(n.left),top:O.constant(n.top),right:O.constant(n.right),bottom:O.constant(n.bottom),width:O.constant(n.width),height:O.constant(n.height)}},pm={create:function(n){return n.document.createRange()},replaceWith:function(n,e){mm(n),n.insertNode(e.dom())},selectNodeContents:function(n,e){var t=n.document.createRange();return dm(t,e),t},selectNodeContentsUsing:dm,relativeToNative:function(n,e,t){var o,r,i=n.document.createRange();return o=i,e.fold(function(n){o.setStartBefore(n.dom())},function(n,e){o.setStart(n.dom(),e)},function(n){o.setStartAfter(n.dom())}),r=i,t.fold(function(n){r.setEndBefore(n.dom())},function(n,e){r.setEnd(n.dom(),e)},function(n){r.setEndAfter(n.dom())}),i},exactToNative:function(n,e,t,o,r){var i=n.document.createRange();return i.setStart(e.dom(),t),i.setEnd(o.dom(),r),i},deleteContents:mm,cloneFragment:function(n){var e=n.cloneContents();return Kn.fromDom(e)},getFirstRect:function(n){var e=n.getClientRects(),t=0<e.length?e[0]:n.getBoundingClientRect();return 0<t.width||0<t.height?y.some(t).map(gm):y.none()},getBounds:function(n){var e=n.getBoundingClientRect();return 0<e.width||0<e.height?y.some(e).map(gm):y.none()},isWithin:function(n,e){return e.compareBoundaryPoints(n.END_TO_START,n)<1&&-1<e.compareBoundaryPoints(n.START_TO_END,n)},toString:function(n){return n.toString()}},vm=qe([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),hm=function(n,e,t){return e(Kn.fromDom(t.startContainer),t.startOffset,Kn.fromDom(t.endContainer),t.endOffset)},bm=function(n,e){var r,t,o,i=(r=n,e.match({domRange:function(n){return{ltr:O.constant(n),rtl:y.none}},relative:function(n,e){return{ltr:L(function(){return pm.relativeToNative(r,n,e)}),rtl:L(function(){return y.some(pm.relativeToNative(r,e,n))})}},exact:function(n,e,t,o){return{ltr:L(function(){return pm.exactToNative(r,n,e,t,o)}),rtl:L(function(){return y.some(pm.exactToNative(r,t,o,n,e))})}}}));return(o=(t=i).ltr()).collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return vm.rtl(Kn.fromDom(n.endContainer),n.endOffset,Kn.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return hm(0,vm.ltr,o)}):hm(0,vm.ltr,o)},ym=(vm.ltr,vm.rtl,bm),wm=function(i,n){return bm(i,n).match({ltr:function(n,e,t,o){var r=i.document.createRange();return r.setStart(n.dom(),e),r.setEnd(t.dom(),o),r},rtl:function(n,e,t,o){var r=i.document.createRange();return r.setStart(t.dom(),o),r.setEnd(n.dom(),e),r}})},xm=(document.caretPositionFromPoint||document.caretRangeFromPoint,function(n,e){var t=ie.name(n);return"input"===t?um.after(n):pn.contains(["br","img"],t)?0===e?um.before(n):um.after(n):um.on(n,e)}),Sm=function(n,e,t,o){var r=xm(n,e),i=xm(t,o);return sm.relative(r,i)},Tm=Sm,km=function(n,e){y.from(n.getSelection()).each(function(n){n.removeAllRanges(),n.addRange(e)})},Cm=function(n,e,t,o,r){var i=pm.exactToNative(n,e,t,o,r);km(n,i)},Om=function(i,n){return ym(i,n).match({ltr:function(n,e,t,o){Cm(i,n,e,t,o)},rtl:function(n,e,t,o){var r=i.getSelection();r.setBaseAndExtent?r.setBaseAndExtent(n.dom(),e,t.dom(),o):r.extend?(r.collapse(n.dom(),e),r.extend(t.dom(),o)):Cm(i,t,o,n,e)}})},Em=function(n){var e=Kn.fromDom(n.anchorNode),t=Kn.fromDom(n.focusNode);return lm(e,n.anchorOffset,t,n.focusOffset)?y.some(sm.range(Kn.fromDom(n.anchorNode),n.anchorOffset,Kn.fromDom(n.focusNode),n.focusOffset)):function(n){if(0<n.rangeCount){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return y.some(sm.range(Kn.fromDom(e.startContainer),e.startOffset,Kn.fromDom(t.endContainer),t.endOffset))}return y.none()}(n)},Dm=function(n){var e=n.getSelection();return 0<e.rangeCount?Em(e):y.none()},Mm=function(n,e,t,o,r){var i=Tm(e,t,o,r);Om(n,i)},Am=Dm,Bm=function(n){return Dm(n).map(function(n){return sm.exact(n.start(),n.soffset(),n.finish(),n.foffset())})},Rm=function(n){n.getSelection().removeAllRanges()},Im=function(n,e){var t=wm(n,e);return pm.getFirstRect(t)},Fm=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:O.constant(2),height:n.height}},Nm=function(n){return{left:O.constant(n.left),top:O.constant(n.top),right:O.constant(n.right),bottom:O.constant(n.bottom),width:O.constant(n.width),height:O.constant(n.height)}},Vm={getRectangles:function(n){var e=n.getSelection();return e!==undefined&&0<e.rangeCount?function(t){if(t.collapsed){var o=Kn.fromDom(t.startContainer);return Ae.parent(o).bind(function(n){var e=sm.exact(o,t.startOffset,n,rm(n));return Im(t.startContainer.ownerDocument.defaultView,e).map(Fm).map(pn.pure)}).getOr([])}return pn.map(t.getClientRects(),Nm)}(e.getRangeAt(0)):[]}},Hm=function(n){n.focus();var e=Kn.fromDom(n.document.body);(vr().exists(function(n){return pn.contains(["input","textarea"],ie.name(n))})?function(n){setTimeout(function(){n()},0)}:O.apply)(function(){vr().each(pr),gr(e)})},jm="data-"+mi.resolve("last-outer-height"),zm=function(n,e){Lo.set(n,jm,e)},Lm=function(n){return{top:O.constant(n.top()),bottom:O.constant(n.top()+n.height())}},Pm=function(n,e){var t=nm(e,jm),o=n.innerHeight;return o<t?y.some(t-o):y.none()},Wm=function(n,u){var e=Kn.fromDom(u.document.body),t=Gd.bind(Kn.fromDom(n),"resize",function(){Pm(n,e).each(function(i){var n,e;(n=u,e=Vm.getRectangles(n),0<e.length?y.some(e[0]).map(Lm):y.none()).each(function(n){var e,t,o,r=(e=u,o=i,(t=n).top()>e.innerHeight||t.bottom()>e.innerHeight?Math.min(o,t.bottom()-e.innerHeight+50):0);0!==r&&u.scrollTo(u.pageXOffset,u.pageYOffset+r)})}),zm(e,n.innerHeight)});return zm(e,n.innerHeight),{toEditing:function(){Hm(u)},destroy:function(){t.unbind()}}},Um=function(n){return y.some(Kn.fromDom(n.dom().contentWindow.document.body))},Gm=function(n){return y.some(Kn.fromDom(n.dom().contentWindow.document))},$m=function(n){return y.from(n.dom().contentWindow)},qm=function(n){return $m(n).bind(Am)},_m=function(n){return n.getFrame()},Km=function(n,t){return function(e){return e[n].getOrThunk(function(){var n=_m(e);return function(){return t(n)}})()}},Xm=function(n,e,t,o){return n[t].getOrThunk(function(){return function(n){return Gd.bind(e,o,n)}})},Ym=function(n){return{left:O.constant(n.left),top:O.constant(n.top),right:O.constant(n.right),bottom:O.constant(n.bottom),width:O.constant(n.width),height:O.constant(n.height)}},Jm={getBody:Km("getBody",Um),getDoc:Km("getDoc",Gm),getWin:Km("getWin",$m),getSelection:Km("getSelection",qm),getFrame:_m,getActiveApi:function(a){var c=_m(a);return Um(c).bind(function(u){return Gm(c).bind(function(i){return $m(c).map(function(r){var n=Kn.fromDom(i.dom().documentElement),e=a.getCursorBox.getOrThunk(function(){return function(){return Bm(r).bind(function(n){return Im(r,n).orThunk(function(){return Am(r).filter(function(n){return Se(n.start(),n.finish())&&n.soffset()===n.foffset()}).bind(function(n){var e=n.start().dom().getBoundingClientRect();return 0<e.width||0<e.height?y.some(e).map(Ym):y.none()})})})}}),t=a.setSelection.getOrThunk(function(){return function(n,e,t,o){Mm(r,n,e,t,o)}}),o=a.clearSelection.getOrThunk(function(){return function(){Rm(r)}});return{body:O.constant(u),doc:O.constant(i),win:O.constant(r),html:O.constant(n),getSelection:O.curry(qm,c),setSelection:t,clearSelection:o,frame:O.constant(c),onKeyup:Xm(a,i,"onKeyup","keyup"),onNodeChanged:Xm(a,i,"onNodeChanged","selectionchange"),onDomChanged:a.onDomChanged,onScrollToCursor:a.onScrollToCursor,onScrollToElement:a.onScrollToElement,onToReading:a.onToReading,onToEditing:a.onToEditing,onToolbarScrollStart:a.onToolbarScrollStart,onTouchContent:a.onTouchContent,onTapContent:a.onTapContent,onTouchToolstrip:a.onTouchToolstrip,getCursorBox:e}})})})}},Qm="data-ephox-mobile-fullscreen-style",Zm="position:absolute!important;",ng="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;",eg=Dn.detect().os.isAndroid(),tg=function(n,e){var t,o,r=function(o){return function(n){var e=Lo.get(n,"style"),t=e===undefined?"no-styles":e.trim();t!==o&&(Lo.set(n,Qm,t),Lo.set(n,"style",o))}},i=Fi(n,"*"),u=pn.bind(i,function(n){return Ni(n,"*")}),a=(t=e,(o=Ci.get(t,"background-color"))!==undefined&&""!==o?"background-color:"+o+"!important":"background-color:rgb(255,255,255)!important;");pn.each(u,r("display:none!important;")),pn.each(i,r(Zm+ng+a)),r((!0===eg?"":Zm)+ng+a)(n)},og=function(){var n=Ii("["+Qm+"]");pn.each(n,function(n){var e=Lo.get(n,Qm);"no-styles"!==e?Lo.set(n,"style",e):Lo.remove(n,"style"),Lo.remove(n,Qm)})},rg=function(){var e=ji("head").getOrDie(),n=ji('meta[name="viewport"]').getOrThunk(function(){var n=Kn.fromTag("meta");return Lo.set(n,"name","viewport"),Ie.append(e,n),n}),t=Lo.get(n,"content");return{maximize:function(){Lo.set(n,"content","width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0")},restore:function(){t!==undefined&&null!==t&&0<t.length?Lo.set(n,"content",t):Lo.set(n,"content","user-scalable=yes")}}},ig=function(e,n){var t=rg(),o=el.api(),r=el.api();return{enter:function(){n.hide(),nr.add(e.container,mi.resolve("fullscreen-maximized")),nr.add(e.container,mi.resolve("android-maximized")),t.maximize(),nr.add(e.body,mi.resolve("android-scroll-reload")),o.set(Wm(e.win,Jm.getWin(e.editor).getOrDie("no"))),Jm.getActiveApi(e.editor).each(function(n){tg(e.container,n.body()),r.set(Zd(n,e.toolstrip,e.alloy))})},exit:function(){t.restore(),n.show(),nr.remove(e.container,mi.resolve("fullscreen-maximized")),nr.remove(e.container,mi.resolve("android-maximized")),og(),nr.remove(e.body,mi.resolve("android-scroll-reload")),r.clear(),o.clear()}}},ug=function(e,t){var o=null;return{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){var n=arguments;null===o&&(o=setTimeout(function(){e.apply(null,n),n=o=null},t))}}},ag=function(e,t){var o=null;return{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){var n=arguments;null!==o&&clearTimeout(o),o=setTimeout(function(){e.apply(null,n),n=o=null},t)}}},cg=function(n,e){var t=Ps(Ef.sketch({dom:$c('<div aria-hidden="true" class="${prefix}-mask-tap-icon"></div>'),containerBehaviours:Ao([ci.config({toggleClass:mi.resolve("mask-tap-icon-selected"),toggleOnExecute:!1})])})),o=ug(n,200);return Ef.sketch({dom:$c('<div class="${prefix}-disabled-mask"></div>'),components:[Ef.sketch({dom:$c('<div class="${prefix}-content-container"></div>'),components:[Pc.sketch({dom:$c('<div class="${prefix}-content-tap-section"></div>'),components:[t.asSpec()],action:function(n){o.throttle()},buttonBehaviours:Ao([ci.config({toggleClass:mi.resolve("mask-tap-icon-selected")})])})]})]})},sg=Et([Vt("editor",[It("getFrame"),Ht("getBody"),Ht("getDoc"),Ht("getWin"),Ht("getSelection"),Ht("setSelection"),Ht("clearSelection"),Ht("cursorSaver"),Ht("onKeyup"),Ht("onNodeChanged"),Ht("getCursorBox"),It("onDomChanged"),Lt("onTouchContent",O.noop),Lt("onTapContent",O.noop),Lt("onTouchToolstrip",O.noop),Lt("onScrollToCursor",O.constant({unbind:O.noop})),Lt("onScrollToElement",O.constant({unbind:O.noop})),Lt("onToEditing",O.constant({unbind:O.noop})),Lt("onToReading",O.constant({unbind:O.noop})),Lt("onToolbarScrollStart",O.identity)]),It("socket"),It("toolstrip"),It("dropup"),It("toolbar"),It("container"),It("alloy"),Wt("win",function(n){return Ae.owner(n.socket).dom().defaultView}),Wt("body",function(n){return Kn.fromDom(n.socket.dom().ownerDocument.body)}),Lt("translate",O.identity),Lt("setReadOnly",O.noop),Lt("readOnlyOnInit",O.constant(!0))]),fg={produce:function(n){var e=Xt("Getting AndroidWebapp schema",sg,n);Ci.set(e.toolstrip,"width","100%");var t=Ll(cg(function(){e.setReadOnly(!0),r.enter()},e.translate));e.alloy.add(t);var o={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}};Ie.append(e.container,t.element());var r=ig(e,o);return{setReadOnly:e.setReadOnly,refreshStructure:O.noop,enter:r.enter,exit:r.exit,destroy:O.noop}}},lg=O.constant([Lt("shell",!0),ja("toolbarBehaviours",[ad])]),dg=O.constant([fc({name:"groups",overrides:function(n){return{behaviours:Ao([ad.config({})])}}})]),mg=(O.constant("Toolbar"),Lc({name:"Toolbar",configFields:lg(),partFields:dg(),factory:function(e,n,t,o){var r=function(n){return e.shell()?y.some(n):bc(n,e,"groups")},i=e.shell()?{behaviours:[ad.config({})],components:[]}:{behaviours:[],components:n};return{uid:e.uid(),dom:e.dom(),components:i.components,behaviours:D.deepMerge(Ao(i.behaviours),za(e.toolbarBehaviours())),apis:{setGroups:function(n,e){r(n).fold(function(){throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){ad.set(n,e)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,e,t){n.setGroups(e,t)}}})),gg=O.constant([It("items"),(sf=["itemClass"],Vt("markers",pn.map(sf,It))),ja("tgroupBehaviours",[Ha])]),pg=O.constant([lc({name:"items",unit:"item",overrides:function(n){return{domModification:{classes:[n.markers().itemClass()]}}}})]),vg=(O.constant("ToolbarGroup"),Lc({name:"ToolbarGroup",configFields:gg(),partFields:pg(),factory:function(n,e,t,o){return D.deepMerge({dom:{attributes:{role:"toolbar"}}},{uid:n.uid(),dom:n.dom(),components:e,behaviours:D.deepMerge(Ao([Ha.config({mode:"flow",selector:"."+n.markers().itemClass()})]),za(n.tgroupBehaviours())),"debug.sketcher":t["debug.sketcher"]})}})),hg="data-"+mi.resolve("horizontal-scroll"),bg=function(n){return 0<n.dom().scrollTop||function(n){n.dom().scrollTop=1;var e=0!==n.dom().scrollTop;return n.dom().scrollTop=0,e}(n)},yg=function(n){return 0<n.dom().scrollLeft||function(n){n.dom().scrollLeft=1;var e=0!==n.dom().scrollLeft;return n.dom().scrollLeft=0,e}(n)},wg=function(n){return"true"===Lo.get(n,hg)?yg:bg},xg={exclusive:function(n,e){return Gd.bind(n,"touchmove",function(n){Pi(n.target(),e).filter(wg).fold(function(){n.raw().preventDefault()},O.noop)})},markAsHorizontal:function(n){Lo.set(n,hg,"true")}};function Sg(){var e=function(n){var e=!0===n.scrollable?"${prefix}-toolbar-scrollable-group":"";return{dom:$c('<div aria-label="'+n.label+'" class="${prefix}-toolbar-group '+e+'"></div>'),tgroupBehaviours:Ao([Tf("adhoc-scrollable-toolbar",!0===n.scrollable?[lo(function(n,e){Ci.set(n.element(),"overflow-x","auto"),xg.markAsHorizontal(n.element()),Sd.register(n.element())})]:[])]),components:[Ef.sketch({components:[vg.parts().items({})]})],markers:{itemClass:mi.resolve("toolbar-group-item")},items:n.items}},t=Ll(mg.sketch({dom:$c('<div class="${prefix}-toolbar"></div>'),components:[mg.parts().groups({})],toolbarBehaviours:Ao([ci.config({toggleClass:mi.resolve("context-toolbar"),toggleOnExecute:!1,aria:{mode:"none"}}),Ha.config({mode:"cyclic"})]),shell:!0})),n=Ll(Ef.sketch({dom:{classes:[mi.resolve("toolstrip")]},components:[Pl(t)],containerBehaviours:Ao([ci.config({toggleClass:mi.resolve("android-selection-context-toolbar"),toggleOnExecute:!1})])})),o=function(){mg.setGroups(t,r.get()),ci.off(t)},r=ir([]);return{wrapper:O.constant(n),toolbar:O.constant(t),createGroups:function(n){return pn.map(n,O.compose(vg.sketch,e))},setGroups:function(n){r.set(n),o()},setContextToolbar:function(n){ci.on(t),mg.setGroups(t,n)},restoreToolbar:function(){ci.isOn(t)&&o()},refresh:function(){},focus:function(){Ha.focusIn(t)}}}var Tg=function(n,e){ad.append(n,Pl(e))},kg=function(n,e){ad.remove(n,e)},Cg={makeEditSwitch:function(n){return Ll(Pc.sketch({dom:$c('<div class="${prefix}-mask-edit-icon ${prefix}-icon"></div>'),action:function(){n.run(function(n){n.setReadOnly(!1)})}}))},makeSocket:function(){return Ll(Ef.sketch({dom:$c('<div class="${prefix}-editor-socket"></div>'),components:[],containerBehaviours:Ao([ad.config({})])}))},updateMode:function(n,e,t,o){(!0===t?rr.toAlpha:rr.toOmega)(o),(t?Tg:kg)(n,e)}},Og=function(e,n){return n.getAnimationRoot().fold(function(){return e.element()},function(n){return n(e)})},Eg=function(n){return n.dimension().property()},Dg=function(n,e){return n.dimension().getDimension()(e)},Mg=function(n,e){var t=Og(n,e);Al.remove(t,[e.shrinkingClass(),e.growingClass()])},Ag=function(n,e){nr.remove(n.element(),e.openClass()),nr.add(n.element(),e.closedClass()),Ci.set(n.element(),Eg(e),"0px"),Ci.reflow(n.element())},Bg=function(n,e){nr.remove(n.element(),e.closedClass()),nr.add(n.element(),e.openClass()),Ci.remove(n.element(),Eg(e))},Rg=function(n,e,t){t.setCollapsed(),Ci.set(n.element(),Eg(e),Dg(e,n.element())),Ci.reflow(n.element());var o=Og(n,e);nr.add(o,e.shrinkingClass()),Ag(n,e),e.onStartShrink()(n)},Ig=function(n,e,t){var o=function(n,e){Bg(n,e);var t=Dg(e,n.element());return Ag(n,e),t}(n,e),r=Og(n,e);nr.add(r,e.growingClass()),Bg(n,e),Ci.set(n.element(),Eg(e),o),t.setExpanded(),e.onStartGrow()(n)},Fg=function(n,e,t){var o=Og(n,e);return!0===nr.has(o,e.growingClass())},Ng=function(n,e,t){var o=Og(n,e);return!0===nr.has(o,e.shrinkingClass())},Vg=Object.freeze({grow:function(n,e,t){t.isExpanded()||Ig(n,e,t)},shrink:function(n,e,t){t.isExpanded()&&Rg(n,e,t)},immediateShrink:function(n,e,t){var o,r;t.isExpanded()&&(o=n,r=e,t.setCollapsed(),Ci.set(o.element(),Eg(r),Dg(r,o.element())),Ci.reflow(o.element()),Mg(o,r),Ag(o,r),r.onStartShrink()(o),r.onShrunk()(o))},hasGrown:function(n,e,t){return t.isExpanded()},hasShrunk:function(n,e,t){return t.isCollapsed()},isGrowing:Fg,isShrinking:Ng,isTransitioning:function(n,e,t){return!0===Fg(n,e)||!0===Ng(n,e)},toggleGrow:function(n,e,t){(t.isExpanded()?Rg:Ig)(n,e,t)},disableTransitions:Mg}),Hg=Object.freeze({exhibit:function(n,e){var t=e.expanded();return ho(t?{classes:[e.openClass()],styles:{}}:{classes:[e.closedClass()],styles:lt(e.dimension().property(),"0px")})},events:function(t,o){return to([ro(j(),function(n,e){e.event().raw().propertyName===t.dimension().property()&&(Mg(n,t),o.isExpanded()&&Ci.remove(n.element(),t.dimension().property()),(o.isExpanded()?t.onGrown():t.onShrunk())(n,e))})])}}),jg=[It("closedClass"),It("openClass"),It("shrinkingClass"),It("growingClass"),Ht("getAnimationRoot"),Ur("onShrunk"),Ur("onStartShrink"),Ur("onGrown"),Ur("onStartGrow"),Lt("expanded",!1),Ft("dimension",Qt("property",{width:[_r("property","width"),_r("getDimension",function(n){return Os(n)+"px"})],height:[_r("property","height"),_r("getDimension",function(n){return Ai(n)+"px"})]}))],zg=Object.freeze({init:function(n){var e=ir(n.expanded());return Eo({isExpanded:function(){return!0===e.get()},isCollapsed:function(){return!1===e.get()},setCollapsed:O.curry(e.set,!1),setExpanded:O.curry(e.set,!0),readState:function(){return"expanded: "+e.get()}})}}),Lg=Ro({fields:jg,name:"sliding",active:Hg,apis:Vg,state:zg}),Pg=function(e,t){var o=Ll(Ef.sketch({dom:{tag:"div",classes:mi.resolve("dropup")},components:[],containerBehaviours:Ao([ad.config({}),Lg.config({closedClass:mi.resolve("dropup-closed"),openClass:mi.resolve("dropup-open"),shrinkingClass:mi.resolve("dropup-shrinking"),growingClass:mi.resolve("dropup-growing"),dimension:{property:"height"},onShrunk:function(n){e(),t(),ad.set(n,[])},onGrown:function(n){e(),t()}}),fi(function(n,e){r(O.noop)})])})),r=function(n){window.requestAnimationFrame(function(){n(),Lg.shrink(o)})};return{appear:function(n,e,t){!0===Lg.hasShrunk(o)&&!1===Lg.isTransitioning(o)&&window.requestAnimationFrame(function(){e(t),ad.set(o,[n()]),Lg.grow(o)})},disappear:r,component:O.constant(o),element:o.element}},Wg=Dn.detect().browser.isFirefox(),Ug=Ot([Nt("triggerEvent"),Nt("broadcastEvent"),Lt("stopBackspace",!0)]),Gg=function(e,n){var t,o,r,i,u=Xt("Getting GUI events settings",Ug,n),a=Dn.detect().deviceType.isTouch()?["touchstart","touchmove","touchend","gesturestart"]:["mousedown","mouseup","mouseover","mousemove","mouseout","click"],c=Yd(u),s=pn.map(a.concat(["selectstart","input","contextmenu","change","transitionend","dragstart","dragover","drop"]),function(n){return Gd.bind(e,n,function(e){c.fireIfReady(e,n).each(function(n){n&&e.kill()}),u.triggerEvent(n,e)&&e.kill()})}),f=Gd.bind(e,"keydown",function(n){var e;u.triggerEvent("keydown",n)?n.kill():!0!==u.stopBackspace||(e=n).raw().which!==Wi.BACKSPACE()[0]||pn.contains(["input","textarea"],ie.name(e.target()))||n.prevent()}),l=(t=e,o=function(n){u.triggerEvent("focusin",n)&&n.kill()},Wg?Gd.capture(t,"focus",o):Gd.bind(t,"focusin",o)),d=(r=e,i=function(n){u.triggerEvent("focusout",n)&&n.kill(),setTimeout(function(){u.triggerEvent(Bn(),n)},0)},Wg?Gd.capture(r,"blur",i):Gd.bind(r,"focusout",i)),m=Ae.defaultView(e),g=Gd.bind(m,"scroll",function(n){u.broadcastEvent(zn(),n)&&n.kill()});return{unbind:function(){pn.each(s,function(n){n.unbind()}),f.unbind(),l.unbind(),d.unbind(),g.unbind()}}},$g=function(n,e){var t=ft(n,"target").map(function(n){return n()}).getOr(e);return ir(t)},qg=qe([{stopped:[]},{resume:["element"]},{complete:[]}]),_g=function(n,o,e,t,r,i){var u,a,c,s,f=n(o,t),l=(u=e,a=r,c=ir(!1),s=ir(!1),{stop:function(){c.set(!0)},cut:function(){s.set(!0)},isStopped:c.get,isCut:s.get,event:O.constant(u),setSource:a.set,getSource:a.get});return f.fold(function(){return i.logEventNoHandlers(o,t),qg.complete()},function(e){var t=e.descHandler();return Sl(t)(l),l.isStopped()?(i.logEventStopped(o,e.element(),t.purpose()),qg.stopped()):l.isCut()?(i.logEventCut(o,e.element(),t.purpose()),qg.complete()):Ae.parent(e.element()).fold(function(){return i.logNoParent(o,e.element(),t.purpose()),qg.complete()},function(n){return i.logEventResponse(o,e.element(),t.purpose()),qg.resume(n)})})},Kg=function(e,t,o,n,r,i){return _g(e,t,o,n,r,i).fold(function(){return!0},function(n){return Kg(e,t,o,n,r,i)},function(){return!1})},Xg=function(n,e,t){var o,r,i=(o=e,r=ir(!1),{stop:function(){r.set(!0)},cut:O.noop,isStopped:r.get,isCut:O.constant(!1),event:O.constant(o),setTarget:O.die("Cannot set target of a broadcasted event"),getTarget:O.die("Cannot get target of a broadcasted event")});return pn.each(n,function(n){var e=n.descHandler();Sl(e)(i)}),i.isStopped()},Yg=function(n,e,t,o,r){var i=$g(t,o);return Kg(n,e,t,o,i,r)},Jg=function(n,e,t){return fr.closest(n,function(n){return e(n).isSome()},t).bind(e)},Qg=le.immutable("element","descHandler"),Zg=function(n,e){return{id:O.constant(n),descHandler:O.constant(e)}};function np(){var i={};return{registerId:function(o,r,n){M.each(n,function(n,e){var t=i[e]!==undefined?i[e]:{};t[r]=xl(n,o),i[e]=t})},unregisterId:function(t){M.each(i,function(n,e){n.hasOwnProperty(t)&&delete n[t]})},filterByType:function(n){return ft(i,n).map(function(n){return M.mapToArray(n,function(n,e){return Zg(e,n)})}).getOr([])},find:function(n,e,t){var r=ct(e)(i);return Jg(t,function(n){return t=r,Rc(o=n).fold(function(){return y.none()},function(n){var e=ct(n);return t.bind(e).map(function(n){return Qg(o,n)})});var t,o},n)}}}function ep(){var i=np(),u={},a=function(n){Rc(n.element()).each(function(n){u[n]=undefined,i.unregisterId(n)})};return{find:function(n,e,t){return i.find(n,e,t)},filter:function(n){return i.filterByType(n)},register:function(n){var e,t,o=(t=(e=n).element(),Rc(t).fold(function(){return Bc("uid-",e.element())},function(n){return n}));gt(u,o)&&function(n,e){var t=u[e];if(t!==n)throw new Error('The tagId "'+e+'" is already used by: '+Mr(t.element())+"\nCannot use it for: "+Mr(n.element())+"\nThe conflicting element is"+(ce.inBody(t.element())?" ":" not ")+"already in the DOM");a(n)}(n,o);var r=[n];i.registerId(r,o,n.events()),u[o]=n},unregister:a,getById:function(n){return ct(n)(u)}}}var tp=function(t){var o=function(e){return Ae.parent(t.element()).fold(function(){return!0},function(n){return Se(e,n)})},r=ep(),s=function(n,e){return r.find(o,n,e)},n=Gg(t.element(),{triggerEvent:function(u,a){return Hr(u,a.target(),function(n){return e=s,t=u,r=n,i=(o=a).target(),Yg(e,t,o,i,r);var e,t,o,r,i})},broadcastEvent:function(n,e){var t=r.filter(n);return Xg(t,e)}}),i=fl({debugInfo:O.constant("real"),triggerEvent:function(e,t,o){Hr(e,t,function(n){Yg(s,e,o,t,n)})},triggerFocus:function(a,c){Rc(a).fold(function(){gr(a)},function(n){Hr(An(),a,function(n){var e,t,o,r,i,u;e=s,t=An(),o={originator:O.constant(c),target:O.constant(a)},i=n,u=$g(o,r=a),_g(e,t,o,r,u,i)})})},triggerEscape:function(n,e){i.triggerEvent("keydown",n.element(),e.event())},getByUid:function(n){return m(n)},getByDom:function(n){return g(n)},build:Ll,addToGui:function(n){a(n)},removeFromGui:function(n){c(n)},addToWorld:function(n){e(n)},removeFromWorld:function(n){u(n)},broadcast:function(n){l(n)},broadcastOn:function(n,e){d(n,e)}}),e=function(n){n.connect(i),ie.isText(n.element())||(r.register(n),pn.each(n.components(),e),i.triggerEvent(jn(),n.element(),{target:O.constant(n.element())}))},u=function(n){ie.isText(n.element())||(pn.each(n.components(),u),r.unregister(n)),n.disconnect()},a=function(n){ze(t,n)},c=function(n){We(n)},f=function(t){var n=r.filter(Rn());pn.each(n,function(n){var e=n.descHandler();Sl(e)(t)})},l=function(n){f({universal:O.constant(!0),data:O.constant(n)})},d=function(n,e){f({universal:O.constant(!1),channels:O.constant(n),data:O.constant(e)})},m=function(n){return r.getById(n).fold(function(){return $e.error(new Error('Could not find component with uid: "'+n+'" in system.'))},$e.value)},g=function(n){var e=Rc(n).getOr("not found");return m(e)};return e(t),{root:O.constant(t),element:t.element,destroy:function(){n.unbind(),Ve.remove(t.element())},add:a,remove:c,getByUid:m,getByDom:g,addToWorld:e,removeFromWorld:u,broadcast:l,broadcastOn:d}},op=O.constant(mi.resolve("readonly-mode")),rp=O.constant(mi.resolve("edit-mode"));function ip(n){var e=Ll(Ef.sketch({dom:{classes:[mi.resolve("outer-container")].concat(n.classes)},containerBehaviours:Ao([rr.config({alpha:op(),omega:rp()})])}));return tp(e)}var up=function(n,e){var t=Kn.fromTag("input");Ci.setAll(t,{opacity:"0",position:"absolute",top:"-1000px",left:"-1000px"}),Ie.append(n,t),gr(t),e(t),Ve.remove(t)},ap=function(n){var e=n.getSelection();if(0<e.rangeCount){var t=e.getRangeAt(0),o=n.document.createRange();o.setStart(t.startContainer,t.startOffset),o.setEnd(t.endContainer,t.endOffset),e.removeAllRanges(),e.addRange(o)}},cp={resume:function(n,e){vr().each(function(n){Se(n,e)||pr(n)}),n.focus(),gr(Kn.fromDom(n.document.body)),ap(n)}},sp={stubborn:function(n,e,t,o){var r=function(){cp.resume(e,o)},i=Gd.bind(t,"keydown",function(n){pn.contains(["input","textarea"],ie.name(n.target()))||r()});return{toReading:function(){up(n,pr)},toEditing:r,onToolbarTouch:function(){},destroy:function(){i.unbind()}}},timid:function(n,e,t,o){var r=function(){pr(o)};return{toReading:function(){r()},toEditing:function(){cp.resume(e,o)},onToolbarTouch:function(){r()},destroy:O.noop}}},fp=function(t,o,r,i,n){var u=function(){o.run(function(n){n.refreshSelection()})},e=function(n,e){var t=n-i.dom().scrollTop;o.run(function(n){n.scrollIntoView(t,t+e)})},a=function(){o.run(function(n){n.clearSelection()})},c=function(){t.getCursorBox().each(function(n){e(n.top(),n.height())}),o.run(function(n){n.syncHeight()})},s=Jd(t),f=ag(c,300),l=[t.onKeyup(function(){a(),f.throttle()}),t.onNodeChanged(u),t.onDomChanged(f.throttle),t.onDomChanged(u),t.onScrollToCursor(function(n){n.preventDefault(),f.throttle()}),t.onScrollToElement(function(n){n.element(),e(o,i)}),t.onToEditing(function(){o.run(function(n){n.toEditing()})}),t.onToReading(function(){o.run(function(n){n.toReading()})}),Gd.bind(t.doc(),"touchend",function(n){Se(t.html(),n.target())||Se(t.body(),n.target())}),Gd.bind(r,"transitionend",function(n){var e;"height"===n.raw().propertyName&&(e=Ai(r),o.run(function(n){n.setViewportOffset(e)}),u(),c())}),Gd.capture(r,"touchstart",function(n){var e;o.run(function(n){n.highlightSelection()}),e=n,o.run(function(n){n.onToolbarTouch(e)}),t.onTouchToolstrip()}),Gd.bind(t.body(),"touchstart",function(n){a(),t.onTouchContent(),s.fireTouchstart(n)}),s.onTouchmove(),s.onTouchend(),Gd.bind(t.body(),"click",function(n){n.kill()}),Gd.bind(r,"touchmove",function(){t.onToolbarScrollStart()})];return{destroy:function(){pn.each(l,function(n){n.unbind()})}}},lp=function(n){var t=y.none(),e=[],o=function(n){r()?u(n):e.push(n)},r=function(){return t.isSome()},i=function(n){pn.each(n,u)},u=function(e){t.each(function(n){setTimeout(function(){e(n)},0)})};return n(function(n){t=y.some(n),i(e),e=[]}),{get:o,map:function(t){return lp(function(e){o(function(n){e(t(n))})})},isReady:r}},dp={nu:lp,pure:function(e){return lp(function(n){n(e)})}},mp=function(t){return function(){var n=Array.prototype.slice.call(arguments),e=this;setTimeout(function(){t.apply(e,n)},0)}},gp=function(e){var n=function(n){e(mp(n))};return{map:function(o){return gp(function(t){n(function(n){var e=o(n);t(e)})})},bind:function(t){return gp(function(e){n(function(n){t(n).get(e)})})},anonBind:function(t){return gp(function(e){n(function(n){t.get(e)})})},toLazy:function(){return dp.nu(n)},get:n}},pp={nu:gp,pure:function(e){return gp(function(n){n(e)})}},vp=function(n,e,t){return Math.abs(n-e)<=t?y.none():n<e?y.some(n+t):y.some(n-t)},hp=function(){var s=null;return{animate:function(o,r,n,i,e,t){var u=!1,a=function(n){u=!0,e(n)};clearInterval(s);var c=function(n){clearInterval(s),a(n)};s=setInterval(function(){var t=o();vp(t,r,n).fold(function(){clearInterval(s),a(r)},function(n){if(i(n,c),!u){var e=o();(e!==n||Math.abs(e-r)>Math.abs(t-r))&&(clearInterval(s),a(r))}})},t)}}},bp=function(e,t){return Rr([{width:320,height:480,keyboard:{portrait:300,landscape:240}},{width:320,height:568,keyboard:{portrait:300,landscape:240}},{width:375,height:667,keyboard:{portrait:305,landscape:240}},{width:414,height:736,keyboard:{portrait:320,landscape:240}},{width:768,height:1024,keyboard:{portrait:320,landscape:400}},{width:1024,height:1366,keyboard:{portrait:380,landscape:460}}],function(n){return e<=n.width&&t<=n.height?y.some(n.keyboard):y.none()}).getOr({portrait:t/5,landscape:e/4})},yp=function(n){var e,t=qd(n).isPortrait(),o=bp((e=n).screen.width,e.screen.height),r=t?o.portrait:o.landscape;return(t?n.screen.height:n.screen.width)-n.innerHeight>r?0:r},wp=function(n,e){var t=Ae.owner(n).dom().defaultView;return Ai(n)+Ai(e)-yp(t)},xp=wp,Sp=function(n,e,t){var o=wp(e,t),r=Ai(e)+Ai(t)-o;Ci.set(n,"padding-bottom",r+"px")},Tp=qe([{fixed:["element","property","offsetY"]},{scroller:["element","offsetY"]}]),kp="data-"+mi.resolve("position-y-fixed"),Cp="data-"+mi.resolve("y-property"),Op="data-"+mi.resolve("scrolling"),Ep="data-"+mi.resolve("last-window-height"),Dp=function(n){return nm(n,kp)},Mp=function(n,e){var t=Lo.get(n,Cp);return Tp.fixed(n,t,e)},Ap=function(n,e){return Tp.scroller(n,e)},Bp=function(n){var e=Dp(n);return("true"===Lo.get(n,Op)?Ap:Mp)(n,e)},Rp=function(n,e,t){var o=Ae.owner(n).dom().defaultView.innerHeight;return Lo.set(n,Ep,o+"px"),o-e-t},Ip=function(n){var e=Vi(n,"["+kp+"]");return pn.map(e,Bp)},Fp=function(o,r,i,u){var n,e,t,a,c,s,f,l,d=Ae.owner(o).dom().defaultView,m=(l=Lo.get(f=i,"style"),Ci.setAll(f,{position:"absolute",top:"0px"}),Lo.set(f,kp,"0px"),Lo.set(f,Cp,"top"),{restore:function(){Lo.set(f,"style",l||""),Lo.remove(f,kp),Lo.remove(f,Cp)}}),g=Ai(i),p=Ai(u),v=Rp(o,g,p),h=(t=g,a=v,s=Lo.get(c=o,"style"),Sd.register(c),Ci.setAll(c,{position:"absolute",height:a+"px",width:"100%",top:t+"px"}),Lo.set(c,kp,t+"px"),Lo.set(c,Op,"true"),Lo.set(c,Cp,"top"),{restore:function(){Sd.deregister(c),Lo.set(c,"style",s||""),Lo.remove(c,kp),Lo.remove(c,Op),Lo.remove(c,Cp)}}),b=(e=Lo.get(n=u,"style"),Ci.setAll(n,{position:"absolute",bottom:"0px"}),Lo.set(n,kp,"0px"),Lo.set(n,Cp,"bottom"),{restore:function(){Lo.set(n,"style",e||""),Lo.remove(n,kp),Lo.remove(n,Cp)}}),y=!0,w=function(){var n=d.innerHeight;return nm(o,Ep)<n},x=function(){if(y){var n=Ai(i),e=Ai(u),t=Rp(o,n,e);Lo.set(o,kp,n+"px"),Ci.set(o,"height",t+"px"),Ci.set(u,"bottom",-(n+t+e)+"px"),Sp(r,o,u)}};return Sp(r,o,u),{setViewportOffset:function(n){Lo.set(o,kp,n+"px"),x()},isExpanding:w,isShrinking:O.not(w),refresh:x,restore:function(){y=!1,m.restore(),h.restore(),b.restore()}}},Np=Dp,Vp=hp(),Hp="data-"+mi.resolve("last-scroll-top"),jp=function(n){var e=Ci.getRaw(n,"top").getOr(0);return parseInt(e,10)},zp=function(n){return parseInt(n.dom().scrollTop,10)},Lp=function(n,e){var t=e+Np(n)+"px";Ci.set(n,"top",t)},Pp=function(t,o,r){return pp.nu(function(n){var e=O.curry(zp,t);Vp.animate(e,o,15,function(n){t.dom().scrollTop=n,Ci.set(t,"top",jp(t)+15+"px")},function(){t.dom().scrollTop=o,Ci.set(t,"top",r+"px"),n(o)},10)})},Wp=function(r,i){return pp.nu(function(n){var e=O.curry(zp,r);Lo.set(r,Hp,e());var t=Math.abs(i-e()),o=Math.ceil(t/10);Vp.animate(e,i,o,function(n,e){nm(r,Hp)!==r.dom().scrollTop?e(r.dom().scrollTop):(r.dom().scrollTop=n,Lo.set(r,Hp,n))},function(){r.dom().scrollTop=i,Lo.set(r,Hp,i),n(i)},10)})},Up=function(i,u){return pp.nu(function(n){var e=O.curry(jp,i),t=function(n){Ci.set(i,"top",n+"px")},o=Math.abs(u-e()),r=Math.ceil(o/10);Vp.animate(e,u,r,t,function(){t(u),n(u)},10)})},Gp=function(e,t,o){var r=Ae.owner(e).dom().defaultView;return pp.nu(function(n){Lp(e,o),Lp(t,o),r.scrollTo(0,o),n(o)})},$p=function(n,e,t,o,r){var i=xp(e,t),u=O.curry(ap,n);i<o||i<r?Wp(e,e.dom().scrollTop-i+r).get(u):o<0&&Wp(e,e.dom().scrollTop+o).get(u)},qp=function(u,n){return n(function(o){var r=[],i=0;0===u.length?o([]):pn.each(u,function(n,e){var t;n.get((t=e,function(n){r[t]=n,++i>=u.length&&o(r)}))})})},_p=function(n){return qp(n,pp.nu)},Kp=_p,Xp=function(n,c){return n.fold(function(n,e,t){return o=n,r=e,u=c+(i=t),Ci.set(o,r,u+"px"),pp.pure(i);var o,r,i,u},function(n,e){return t=n,r=c+(o=e),i=Ci.getRaw(t,"top").getOr(o),u=r-parseInt(i,10),a=t.dom().scrollTop+u,Pp(t,a,r);var t,o,r,i,u,a})},Yp=function(n,e){var t=Ip(n),o=pn.map(t,function(n){return Xp(n,e)});return Kp(o)},Jp=function(e,t,n,o,r,i){var u,a,c=(u=function(n){return Gp(e,t,n)},a=ir(dp.pure({})),{start:function(e){var n=dp.nu(function(n){return u(e).get(n)});a.set(n)},idle:function(n){a.get().get(function(){n()})}}),s=ag(function(){c.idle(function(){Yp(n,o.pageYOffset).get(function(){var n;(n=Vm.getRectangles(i),y.from(n[0]).bind(function(n){var e=n.top()-t.dom().scrollTop;return e>o.innerHeight+5||e<-5?y.some({top:O.constant(e),bottom:O.constant(e+n.height())}):y.none()})).each(function(n){t.dom().scrollTop=t.dom().scrollTop+n.top()}),c.start(0),r.refresh()})})},1e3),f=Gd.bind(Kn.fromDom(o),"scroll",function(){o.pageYOffset<0||s.throttle()});return Yp(n,o.pageYOffset).get(O.identity),{unbind:f.unbind}},Qp=function(n){var t=n.cWin(),e=n.ceBody(),o=n.socket(),r=n.toolstrip(),i=n.toolbar(),u=n.contentElement(),a=n.keyboardType(),c=n.outerWindow(),s=n.dropup(),f=Fp(o,e,r,s),l=a(n.outerBody(),t,ce.body(),u,r,i),d=_d(c,{onChange:O.noop,onReady:f.refresh});d.onAdjustment(function(){f.refresh()});var m=Gd.bind(Kn.fromDom(c),"resize",function(){f.isExpanding()&&f.refresh()}),g=Jp(r,o,n.outerBody(),c,f,t),p=function(t,e){var n=t.document,o=Kn.fromTag("div");nr.add(o,mi.resolve("unfocused-selections")),Ie.append(Kn.fromDom(n.documentElement),o);var r=Gd.bind(o,"touchstart",function(n){n.prevent(),cp.resume(t,e),u()}),i=function(n){var e=Kn.fromTag("span");return Al.add(e,[mi.resolve("layer-editor"),mi.resolve("unfocused-selection")]),Ci.setAll(e,{left:n.left()+"px",top:n.top()+"px",width:n.width()+"px",height:n.height()+"px"}),e},u=function(){Ve.empty(o)};return{update:function(){u();var n=Vm.getRectangles(t),e=pn.map(n,i);Fe.append(o,e)},isActive:function(){return 0<Ae.children(o).length},destroy:function(){r.unbind(),Ve.remove(o)},clear:u}}(t,u),v=function(){p.clear()};return{toEditing:function(){l.toEditing(),v()},toReading:function(){l.toReading()},onToolbarTouch:function(n){l.onToolbarTouch(n)},refreshSelection:function(){p.isActive()&&p.update()},clearSelection:v,highlightSelection:function(){p.update()},scrollIntoView:function(n,e){$p(t,o,s,n,e)},updateToolbarPadding:O.noop,setViewportOffset:function(n){f.setViewportOffset(n),Up(o,n).get(O.identity)},syncHeight:function(){Ci.set(u,"height",u.dom().contentWindow.document.body.scrollHeight+"px")},refreshStructure:f.refresh,destroy:function(){f.restore(),d.destroy(),g.unbind(),m.unbind(),l.destroy(),p.destroy(),up(ce.body(),pr)}}},Zp=function(o,n){var r=rg(),i=el.value(),u=el.value(),a=el.api(),c=el.api();return{enter:function(){n.hide();var t=Kn.fromDom(document);Jm.getActiveApi(o.editor).each(function(n){i.set({socketHeight:Ci.getRaw(o.socket,"height"),iframeHeight:Ci.getRaw(n.frame(),"height"),outerScroll:document.body.scrollTop}),u.set({exclusives:xg.exclusive(t,"."+Sd.scrollable())}),nr.add(o.container,mi.resolve("fullscreen-maximized")),tg(o.container,n.body()),r.maximize(),Ci.set(o.socket,"overflow","scroll"),Ci.set(o.socket,"-webkit-overflow-scrolling","touch"),gr(n.body());var e=le.immutableBag(["cWin","ceBody","socket","toolstrip","toolbar","dropup","contentElement","cursor","keyboardType","isScrolling","outerWindow","outerBody"],[]);a.set(Qp(e({cWin:n.win(),ceBody:n.body(),socket:o.socket,toolstrip:o.toolstrip,toolbar:o.toolbar,dropup:o.dropup.element(),contentElement:n.frame(),cursor:O.noop,outerBody:o.body,outerWindow:o.win,keyboardType:sp.stubborn,isScrolling:function(){return u.get().exists(function(n){return n.socket.isScrolling()})}}))),a.run(function(n){n.syncHeight()}),c.set(fp(n,a,o.toolstrip,o.socket,o.dropup))})},refreshStructure:function(){a.run(function(n){n.refreshStructure()})},exit:function(){r.restore(),c.clear(),a.clear(),n.show(),i.on(function(n){n.socketHeight.each(function(n){Ci.set(o.socket,"height",n)}),n.iframeHeight.each(function(n){Ci.set(o.editor.getFrame(),"height",n)}),document.body.scrollTop=n.scrollTop}),i.clear(),u.on(function(n){n.exclusives.unbind()}),u.clear(),nr.remove(o.container,mi.resolve("fullscreen-maximized")),og(),Sd.deregister(o.toolbar),Ci.remove(o.socket,"overflow"),Ci.remove(o.socket,"-webkit-overflow-scrolling"),pr(o.editor.getFrame()),Jm.getActiveApi(o.editor).each(function(n){n.clearSelection()})}}},nv={produce:function(n){var e=Xt("Getting IosWebapp schema",sg,n);Ci.set(e.toolstrip,"width","100%"),Ci.set(e.container,"position","relative");var t=Ll(cg(function(){e.setReadOnly(e.readOnlyOnInit()),o.enter()},e.translate));e.alloy.add(t);var o=Zp(e,{show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}});return{setReadOnly:e.setReadOnly,refreshStructure:o.refreshStructure,enter:o.enter,exit:o.exit,destroy:O.noop}}},ev=tinymce.util.Tools.resolve("tinymce.EditorManager"),tv=function(n){var e=ft(n.settings,"skin_url").fold(function(){return ev.baseURL+"/skins/lightgray"},function(n){return n});return{content:e+"/content.mobile.min.css",ui:e+"/skin.mobile.min.css"}},ov=function(n,e,t){n.system().broadcastOn([xr.formatChanged()],{command:e,state:t})},rv=function(o,n){var e=M.keys(n.formatter.get());pn.each(e,function(e){n.formatter.formatChanged(e,function(n){ov(o,e,n)})}),pn.each(["ul","ol"],function(t){n.selection.selectorChanged(t,function(n,e){ov(o,t,n)})})},iv=(O.constant(["x-small","small","medium","large","x-large"]),function(n){var e=function(){n._skinLoaded=!0,n.fire("SkinLoaded")};return function(){n.initialized?e():n.on("init",e)}}),uv=O.constant("toReading"),av=O.constant("toEditing");yr.add("mobile",function(C){return{getNotificationManagerImpl:function(){return{open:O.identity,close:O.noop,reposition:O.noop,getArgs:O.identity}},renderUI:function(n){var e=tv(C);0==(!1===C.settings.skin)?(C.contentCSS.push(e.content),br.DOM.styleSheetLoader.load(e.ui,iv(C))):iv(C)();var t,o,r,i,u,a,c,s,f,l,d,m,g,p,v=function(){C.fire("scrollIntoView")},h=Kn.fromTag("div"),b=Dn.detect().os.isAndroid()?(s=v,f=ip({classes:[mi.resolve("android-container")]}),l=Sg(),d=el.api(),m=Cg.makeEditSwitch(d),g=Cg.makeSocket(),p=Pg(O.noop,s),f.add(l.wrapper()),f.add(g),f.add(p.component()),{system:O.constant(f),element:f.element,init:function(n){d.set(fg.produce(n))},exit:function(){d.run(function(n){n.exit(),ad.remove(g,m)})},setToolbarGroups:function(n){var e=l.createGroups(n);l.setGroups(e)},setContextToolbar:function(n){var e=l.createGroups(n);l.setContextToolbar(e)},focusToolbar:function(){l.focus()},restoreToolbar:function(){l.restoreToolbar()},updateMode:function(n){Cg.updateMode(g,m,n,f.root())},socket:O.constant(g),dropup:O.constant(p)}):(t=v,o=ip({classes:[mi.resolve("ios-container")]}),r=Sg(),i=el.api(),u=Cg.makeEditSwitch(i),a=Cg.makeSocket(),c=Pg(function(){i.run(function(n){n.refreshStructure()})},t),o.add(r.wrapper()),o.add(a),o.add(c.component()),{system:O.constant(o),element:o.element,init:function(n){i.set(nv.produce(n))},exit:function(){i.run(function(n){ad.remove(a,u),n.exit()})},setToolbarGroups:function(n){var e=r.createGroups(n);r.setGroups(e)},setContextToolbar:function(n){var e=r.createGroups(n);r.setContextToolbar(e)},focusToolbar:function(){r.focus()},restoreToolbar:function(){r.restoreToolbar()},updateMode:function(n){Cg.updateMode(a,u,n,o.root())},socket:O.constant(a),dropup:O.constant(c)}),y=Kn.fromDom(n.targetNode);Ie.after(y,h),function(n,e){Ie.append(n,e.element());var t=Ae.children(e.element());pn.each(t,function(n){e.getByDom(n).each(je)})}(h,b.system());var w=n.targetNode.ownerDocument.defaultView,x=_d(w,{onChange:function(){b.system().broadcastOn([xr.orientationChanged()],{width:Kd(w)})},onReady:O.noop}),S=function(n,e,t,o){!1===o&&C.selection.collapse();var r=T(n,e,t);b.setToolbarGroups(!0===o?r.readOnly:r.main),C.setMode(!0===o?"readonly":"design"),C.fire(!0===o?uv():av()),b.updateMode(o)},T=function(n,e,t){var o=n.get(),r={readOnly:o.backToMask.concat(e.get()),main:o.backToMask.concat(t.get())};return r},k=function(n,e){return C.on(n,e),{unbind:function(){C.off(n)}}};return C.on("init",function(){b.init({editor:{getFrame:function(){return Kn.fromDom(C.contentAreaContainer.querySelector("iframe"))},onDomChanged:function(){return{unbind:O.noop}},onToReading:function(n){return k(uv(),n)},onToEditing:function(n){return k(av(),n)},onScrollToCursor:function(e){return C.on("scrollIntoView",function(n){e(n)}),{unbind:function(){C.off("scrollIntoView"),x.destroy()}}},onTouchToolstrip:function(){t()},onTouchContent:function(){var n,e=Kn.fromDom(C.editorContainer.querySelector("."+mi.resolve("toolbar")));(n=e,hr(n).bind(function(n){return b.system().getByDom(n).toOption()})).each(Gn),b.restoreToolbar(),t()},onTapContent:function(n){var e=n.target();"img"===ie.name(e)?(C.selection.select(e.dom()),n.kill()):"a"===ie.name(e)&&b.system().getByDom(Kn.fromDom(C.editorContainer)).each(function(n){rr.isAlpha(n)&&wr(e.dom())})}},container:Kn.fromDom(C.editorContainer),socket:Kn.fromDom(C.contentAreaContainer),toolstrip:Kn.fromDom(C.editorContainer.querySelector("."+mi.resolve("toolstrip"))),toolbar:Kn.fromDom(C.editorContainer.querySelector("."+mi.resolve("toolbar"))),dropup:b.dropup(),alloy:b.system(),translate:O.noop,setReadOnly:function(n){S(c,a,u,n)},readOnlyOnInit:function(){return!1}});var t=function(){b.dropup().disappear(function(){b.system().broadcastOn([xr.dropupDismissed()],{})})},n={label:"The first group",scrollable:!1,items:[Xc.forToolbar("back",function(){C.selection.collapse(),b.exit()},{})]},e={label:"Back to read only",scrollable:!1,items:[Xc.forToolbar("readonly-back",function(){S(c,a,u,!0)},{})]},o=Vd(b,C),r=Hd(C.settings,o),i={label:"The extra group",scrollable:!1,items:[]},u=ir([{label:"the action group",scrollable:!0,items:r},i]),a=ir([{label:"The read only mode group",scrollable:!0,items:[]},i]),c=ir({backToMask:[n],backToReadOnly:[e]});rv(b,C)}),{iframeContainer:b.socket().element().dom(),editorContainer:b.element().dom()}}}})}();