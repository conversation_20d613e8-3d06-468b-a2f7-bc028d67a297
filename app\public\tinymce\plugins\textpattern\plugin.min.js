!function(){"use strict";var r=function(t){var e=t,n=function(){return e};return{get:n,set:function(t){e=t},clone:function(){return r(n())}}},t=tinymce.util.Tools.resolve("tinymce.PluginManager"),n=function(e){return{setPatterns:function(t){e.set(t)},getPatterns:function(){return e.get()}}},e=[{start:"*",end:"*",format:"italic"},{start:"**",end:"**",format:"bold"},{start:"***",end:"***",format:["bold","italic"]},{start:"#",format:"h1"},{start:"##",format:"h2"},{start:"###",format:"h3"},{start:"####",format:"h4"},{start:"#####",format:"h5"},{start:"######",format:"h6"},{start:"1. ",cmd:"InsertOrderedList"},{start:"* ",cmd:"InsertUnorderedList"},{start:"- ",cmd:"InsertUnorderedList"}],a=function(t){return t.textpattern_patterns!==undefined?t.textpattern_patterns:e},o=tinymce.util.Tools.resolve("tinymce.util.Delay"),i=tinymce.util.Tools.resolve("tinymce.util.VK"),g=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),h=tinymce.util.Tools.resolve("tinymce.util.Tools"),m=function(t,e){for(var n=0;n<t.length;n++)if(0===e.indexOf(t[n].start)&&(!t[n].end||e.lastIndexOf(t[n].end)===e.length-t[n].end.length))return t[n]},c=function(t,e,n,r){var a,o,i,s,l,d,f=t.sort(function(t,e){return t.start.length>e.start.length?-1:t.start.length<e.start.length?1:0});for(o=0;o<f.length;o++)if((a=f[o]).end!==undefined&&(s=a,l=n,d=r,e.substr(l-s.end.length-d,s.end.length)===s.end)&&0<n-r-(i=a).end.length-i.start.length)return a},s=function(t,e,n){if(!1!==e.collapsed){var r=e.startContainer,a=r.data,o=!0===n?1:0;if(3===r.nodeType){var i=c(t,a,e.startOffset,o);if(i!==undefined){var s=a.lastIndexOf(i.end,e.startOffset-o),l=a.lastIndexOf(i.start,s-i.end.length);if(s=a.indexOf(i.end,l+i.start.length),-1!==l){var d=document.createRange();d.setStart(r,l),d.setEnd(r,s+i.end.length);var f=m(t,d.toString());if(!(i===undefined||f!==i||r.data.length<=i.start.length+i.end.length))return{pattern:i,startOffset:l,endOffset:s}}}}}},l=function(t,e,n){var r=t.selection.getRng(!0),a=s(e,r,n);if(a)return function(a,o,i,t){var s=h.isArray(i.pattern.format)?i.pattern.format:[i.pattern.format];if(0!==h.grep(s,function(t){var e=a.formatter.get(t);return e&&e[0].inline}).length)return a.undoManager.transact(function(){var t,e,n,r;t=o,e=i.pattern,n=i.endOffset,r=i.startOffset,(t=0<r?t.splitText(r):t).splitText(n-r+e.end.length),t.deleteData(0,e.start.length),t.deleteData(t.data.length-e.end.length,e.end.length),o=t,s.forEach(function(t){a.formatter.apply(t,{},o)})}),o}(t,r.startContainer,a)},d={patternFromRng:s,applyInlineFormatSpace:function(t,e){return l(t,e,!0)},applyInlineFormatEnter:function(t,e){return l(t,e,!1)},applyBlockFormat:function(t,e){var n,r,a,o,i,s,l,d,f,c,u;if(n=t.selection,r=t.dom,n.isCollapsed()&&(l=r.getParent(n.getStart(),"p"))){for(f=new g(l,l);i=f.next();)if(3===i.nodeType){o=i;break}if(o){if(!(d=m(e,o.data)))return;if(a=(c=n.getRng(!0)).startContainer,u=c.startOffset,o===a&&(u=Math.max(0,u-d.start.length)),h.trim(o.data).length===d.start.length)return;d.format&&(s=t.formatter.get(d.format))&&s[0].block&&(o.deleteData(0,d.start.length),t.formatter.apply(d.format,{},o),c.setStart(a,u),c.collapse(!0),n.setRng(c)),d.cmd&&t.undoManager.transact(function(){o.deleteData(0,d.start.length),t.execCommand(d.cmd)})}}}},f=function(t,e,n){for(var r=0;r<t.length;r++)if(n(t[r],e))return!0},u={handleEnter:function(t,e){var n,r;(n=d.applyInlineFormatEnter(t,e))&&((r=t.dom.createRng()).setStart(n,n.data.length),r.setEnd(n,n.data.length),t.selection.setRng(r)),d.applyBlockFormat(t,e)},handleInlineKey:function(t,e){var n,r,a,o,i;(n=d.applyInlineFormatSpace(t,e))&&(i=t.dom,r=n.data.slice(-1),/[\u00a0 ]/.test(r)&&(n.deleteData(n.data.length-1,1),a=i.doc.createTextNode(r),i.insertAfter(a,n.parentNode),(o=i.createRng()).setStart(a,1),o.setEnd(a,1),t.selection.setRng(o)))},checkCharCode:function(t,e){return f(t,e,function(t,e){return t.charCodeAt(0)===e.charCode})},checkKeyCode:function(t,e){return f(t,e,function(t,e){return t===e.keyCode&&!1===i.modifierPressed(e)})}},p=function(e,n){var r=[",",".",";",":","!","?"],a=[32];e.on("keydown",function(t){13!==t.keyCode||i.modifierPressed(t)||u.handleEnter(e,n.get())},!0),e.on("keyup",function(t){u.checkKeyCode(a,t)&&u.handleInlineKey(e,n.get())}),e.on("keypress",function(t){u.checkCharCode(r,t)&&o.setEditorTimeout(e,function(){u.handleInlineKey(e,n.get())})})};t.add("textpattern",function(t){var e=r(a(t.settings));return p(t,e),n(e)})}();