$(function () {
    var $_pages = $(".serverpage");
    var $_uls = $(".server1>ul");
    $_pages.click(function () {
        var index = $(this).index();
        $_uls.eq(index).css({
            "display": "block"
        }).siblings(".server1>ul").css("display", "none");
    })

    $(".serverTabLi").click(function () {
        $(this).addClass("liActive").siblings(".serverTabLi").removeClass("liActive");
    })

    $(".serverpage").click(function () {
        $(this).addClass("pageActive").siblings(".serverpage").removeClass("pageActive");
    })
})