<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        其他内容
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row v-loading="loading">
                            <el-col :span="24" style="text-align:right;padding-bottom: 10px">
                                <a  v-if="hasEditPurview==1"   href="/other/add" style="color:#FFF;" class="el-button el-button--primary el-button--mini">添加</a>
                            </el-col>

                            <el-table :data="tableList.list" stripe style="width: 100%">
                                <el-table-column prop="title" label="名称">
                                </el-table-column>
                                <el-table-column prop="id" label="链接地址">
                                    <template slot-scope="scope">
                                        <a :href="'<%- view_url %>/overview/'+scope.row.alias" target="_blank"><%- view_url %>/overview/{{scope.row.alias}}</a>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="id" label="移动端链接地址">
                                    <template slot-scope="scope">
                                        <!-- <a :href="'<%- view_url %>/single/'+scope.row.alias" target="_blank"></a> -->
                                        <%- mview_url %>/overview?page={{scope.row.alias}}
                                    </template>
                                </el-table-column>
                                <el-table-column fixed="right" label="操作" width="160" fixed="right">
                                    <template slot-scope="scope">
                                        <el-button-group>
                                            <a  v-if="hasEditPurview==1"   :href="'/other/edit/'+scope.row.id" style="color:#FFF;" class="el-button el-button--primary el-button--mini">编辑</a>
                                            <el-button  v-if="hasEditPurview==1"   type="danger" size="mini" @click='_del(scope.row.id,scope.row.title, scope.row.alias)'>删除</el-button>
                                        </el-button-group>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <el-pagination @current-change="handleCurrentChange" background :current-page.sync="form.page" :page-size="form.limit"
                                layout="total, prev, pager, next" :total="tableList.total" style="padding:10px 0;text-align: right;">
                            </el-pagination>
                        </el-row>
                    </el-main>
            </el-container>
    </el-container>
    <script>
    var main = new Vue({
        el: '#Main',
        data: {
            tableList: {
                list: <%- list %>,
                total: <%- total %>,
            },
            form: {
                title: '',
                page:1,
                limit: 10,
            },
            loading: false,
            logs: <%- logs %>,
            logshow: false,
			hasCheckedPurview:1,
			hasPubviewPurview: 1,
			hasEditPurview:1
        },
        methods: {
            handleCurrentChange(r) {
                this.form.page = r;
                this.getList();
            },
            getList(){
                var that = this;
                
                that.loading = true;
                var params = this.form;
                params._csrf = '<%- csrf %>';
                axios.post('/other/list/get', params)
                    .then(function(result) {
                        that.loading = false;
                        if (result.data.success) {
                            that.tableList = result.data.data;
                        }else{
                            that.$message.error('获取列表出错');
                        }
                    });
            },
            _del(id,name, alias) {
                var that = this;
                this.$confirm('是否删除'+name+'？', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                    that.loading = true;
                    const pageLink = '<%- view_url %>' + '/overview/'+ alias
                    
                    axios.post('/other/delete', { id: id,pageLink, _csrf: '<%- csrf %>' })
                    .then(function(result) {
                        if (result.data.success) {
                            that._default();
                            window.location.reload();
                        }
                    });
                }).catch(e =>{
                    return e;
                });
            },
            _default() {
                this.form.title = '';
                this.form.page = 1;
            },
            showLog(){
                if(this.logs.list.length > 1){
                    this.logshow = !this.logshow;
                }
            },
            logPageChange(page){
                axios.post('/logslist',{page:page,model:'其他内容',limit:10}).then(res => {
                    let data = res.data;
                    if(data.success){
                        this.logs = data.data;
                    }
                    
                })
            },
        },
        mounted(){
            this.$nextTick(function(){
                document.getElementById('preLoading').style.display = 'none';
            });
        }
    });
    </script>
    <% include footer.html %>