// var swiper = new Swiper('.swiper-container', {
//     navigation: {
//         nextEl: '.swiper-button-next',
//         prevEl: '.swiper-button-prev',
//     },
//     pagination: {
//         el: '.swiper-pagination',
//         clickable: true,
//     },
//     speed:1000,
//     loop: false,
//     autoplay: {
//         delay: 5000,
//         disableOnInteraction: false,
//     },
//     effect : 'fade',
//     fadeEffect: {
//         crossFade: true,
//     }
// });

// var scontainer = $('.swiper-container')[0];
// scontainer.onmouseenter = function () {
//     swiper.autoplay.stop();
// };
// scontainer.onmouseleave = function () {
//     swiper.autoplay.start();
// };

// 点击banner提示收藏
$('.n_banner .bannerLi1:last').on('click', function () {
    addFavorite();
})
// $('.banner .bannerLi1').eq(1).on('click', function () {
//     addFavorite();
// })
function addFavorite() {
    if (window.sidebar && window.sidebar.addPanel) { // Mozilla Firefox Bookmark
        window.sidebar.addPanel(title, url, '');
    } else if (window.external && ('AddFavorite' in window.external)) { // IE Favorite
        window.external.AddFavorite(url, title);
    } else if (window.opera && window.print) { // Opera Hotlist
        this.title = title;
        return true;
    } else { // webkit - safari/chrome
        alert('请使用 ' + (navigator.userAgent.toLowerCase().indexOf('mac') != -1 ? 'Command' : 'CTRL') + ' + D 加入收藏');
    }
}
$(function(){
    $('#desShow>li').on('mouseover',function(){
        $(this).stop().animate({
            width: 353
        });

        var $title = $(this).find('.liTitle'),
            $info = $(this).find('.liInfo'),
            $time = $(this).find('.liTime'),
            $btn = $(this).find('.learnMore');

        $title.stop().animate({
            opacity: 1,
            top: -20,
        }).css('height','auto');
        $info.before($time.css({'text-align':'left'}).css({'padding-top':0})).stop().animate({'padding-top':0}).css('text-align','left');
        $btn.stop().css('display','block').fadeIn();

        $(this).siblings('li').each(function(){
                $(this).stop().removeClass('big').animate({
                width: 240
            });

            var $title = $(this).find('.liTitle'),
                $info = $(this).find('.liInfo'),
                $time = $(this).find('.liTime'),
                $btn = $(this).find('.learnMore');

            $title.stop().animate({
                opacity: 0,
                top: 0,
                height: 0,
            });
            $info.after($time.css({'text-align':'center'}).css({'padding-top':0})).stop().animate({'padding-top':60}).css('text-align','center');
            $btn.stop().fadeOut();
        });
    });

    $(".homeConCon5Li").on('mouseover',function () {
        $(this).children('a').children('img:first').stop().fadeOut().siblings('img').stop().fadeIn();
        $(this).siblings('li').each(function(){
            $(this).children('a').children('img:first').stop().fadeIn().siblings('img').stop().fadeOut();
        });
    }).on('mouseout',function(){
        $(this).children('a').children('img:first').stop().fadeIn().siblings('img').stop().fadeOut();
    });

    $(".homeConCon4Li").hover(function () {
        var $this = $(this),$c = $this.children(".learnMore");
        $c.animate({opacity:1,bottom:120})
    },function () {
        var $this = $(this),$c = $this.children(".learnMore");
        $c.animate({opacity:0,bottom:40})

    })

    $(".title").each(function(){
        var $that = $(this);
        $that.click(function () {
            var $this = $(this);
            var index = $this.index();
            var width = $this.width() + 50;
            var cw = $(document).width();

            var left = $this.position().left;
            $that.parent().children(".titleBor").css("width",width).animate({left:left});

            $this.addClass("active").siblings(".title").removeClass("active");

            var $c = $(this).parent().next().children();
            var $l = $(this).siblings('span.spanL').children('span.spanL-word');

            $c.eq(index).show().siblings().hide();
            $l.eq(index).show().siblings('span.spanL-word').hide();
        });
    })
});

// 新版首页楼层
$('.homeCon-head li').on('click', function() {
    alert()
    var index = $(this).index()
    $(this).addClass("active").siblings().removeClass('active');
    $(this).closest('.homeCon-head').siblings('.homeCon8-body').find('.homeCon8-item').eq(index).addClass('active').siblings().removeClass("active")
    $(this).closest('.homeCon-head').find('.more').eq(index).addClass('active').siblings().removeClass("active")
})

// 新版自定义楼层2
$('.homeCon7 li').hover(function () {
    $(this).addClass('active')
}, function () {
    $(this).removeClass('active')
})