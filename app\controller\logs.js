'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo; 
class LogsController extends Controller {
  async listview() {
    const { ctx } = this;

    await ctx.render('logs_list', {
      site_title: _info.site_title,
      page_title: '操作日志',
      active: '13',
      csrf: ctx.csrf,
      userInfo: ctx.session.userInfo,
    });
  }

  async list() {
    const { ctx } = this;
    const params = ctx.request.body;
    const list = await ctx.service.logs.listAll(params);

    ctx.body = {
      success: true,
      data: list
    };
  }
}

module.exports = LogsController;