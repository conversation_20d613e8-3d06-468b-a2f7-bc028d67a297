<% include header.html %>
  <el-container id="Main" v-loading="loading">
    <% include eheader.html %>
      <el-container>
        <% include eside.html %>
         <iframe :src="console_url + '/orderLine'" style="width: 100%; border: none;"></iframe>
      </el-container>
  </el-container>

  <script>
    var main = new Vue({
      el: '#Main',
      data: {
        loading: false,
        portal_url: '<%- portal_url %>',
        console_url: '<%- console_url %>'
      },
      methods: {},
      mounted() {
        this.$nextTick(() => {
          document.getElementById('preLoading').style.display = 'none';
        })
      }
    });
  </script>

  <style>
  </style>
  <% include footer.html %>