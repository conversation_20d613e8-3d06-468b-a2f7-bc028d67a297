'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo; 
const { env } = require('../../config/info').siteInfo;
class NewsController extends Controller {
  async typeList() {
    const {
      ctx
    } = this;

    const typeList = await ctx.service.news.typeList();
    const serviceList = await ctx.service.catalog.getSercataList();
    const tradeList = await ctx.service.catalog.getTradeList();
    const logs = await ctx.service.logs.listAll({
      model: '新闻中心-新闻类别',
      page: 1,
      limit: 10
    });

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;
    let hasEditPurview = 1;

    if (this.ctx.session.userInfo.type != 1) {
      let objService = await ctx.service.admin.getDataPurviewByService(this.ctx.session.userInfo.name);
      let objTrade = await ctx.service.admin.getDataPurviewByTrade(this.ctx.session.userInfo.name);

      if (objService.checked != 1 && objTrade.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (objService.publish != 1 && objTrade.publish != 1) {
        hasPubviewPurview = 0;
      }
      if (objService.edit != 1 && objTrade.edit != 1) {
        hasEditPurview = 0;
      }
    }

    await ctx.render('news_type', {
      site_title: _info.site_title,
      page_title: '新闻类别',
      active: '5-2',
      list: JSON.stringify(typeList.list),
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
      hasEditPurview: hasEditPurview
    });
  }

  async addType() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;

    const result = await ctx.service.news.addType(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async getType() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const info = await ctx.service.news.typeGet(params);

    ctx.body = {
      success: true,
      data: info.info
    };
  }

  async typeDelete() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const result = await ctx.service.news.typeDelete(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async typeEdit() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const result = await ctx.service.news.typeEdit(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async newsList() {

    const {
      ctx
    } = this;

    const params = {
      type: 0,
      page: 1,
      is_publish: 1
    }

    const typeList = await ctx.service.news.typeList();

    let serviceList;
    let tradeList;
    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      params.allowServiceList = paramsData.ids;
      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      params.allowTradeList = paramsData.ids;
      tradeList = await ctx.service.catalog.getTradeList(paramsData);
    } else {
      serviceList = await ctx.service.catalog.getSercataList();
      tradeList = await ctx.service.catalog.getTradeList();
    }

    const result = await ctx.service.news.getList(params);

    result.list.some(item => {
      item.is_publish == 1 ? item.is_publish = true : item.is_publish = false;
    });

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;
    let hasEditPurview = 1;

    if (this.ctx.session.userInfo.type != 1) {
      let objService = await ctx.service.admin.getDataPurviewByService(this.ctx.session.userInfo.name);
      let objTrade = await ctx.service.admin.getDataPurviewByTrade(this.ctx.session.userInfo.name);

      if (objService.checked != 1 && objTrade.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (objService.publish != 1 && objTrade.publish != 1) {
        hasPubviewPurview = 0;
      }
      if (objService.edit != 1 && objTrade.edit != 1) {
        hasEditPurview = 0;
      }
    }

    await ctx.render('news_list', {
      site_title: _info.site_title,
      page_title: '新闻列表',
      active: '5-1',
      list: JSON.stringify(result.list),
      newsType: JSON.stringify(typeList.list),
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      total: result.total,
      csrf: ctx.csrf,
      type: 'list',
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
      hasEditPurview: hasEditPurview,
      buIds: this.ctx.session.buIds ? JSON.stringify(this.ctx.session.buIds) : JSON.stringify([])
    });
  }

  async newsDraftList() {
    const {
      ctx
    } = this;

    const params = {
      type: 0,
      page: 1,
      is_publish: 0
    };
    const typeList = await ctx.service.news.typeList();

    let serviceList;
    let tradeList;
    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      params.allowServiceList = paramsData.ids;
      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      params.allowTradeList = paramsData.ids;
      tradeList = await ctx.service.catalog.getTradeList(paramsData);
    } else {
      serviceList = await ctx.service.catalog.getSercataList();
      tradeList = await ctx.service.catalog.getTradeList();
    }

    const result = await ctx.service.news.getList(params);


    result.list.some(item => {
      item.is_publish == 1 ? item.is_publish = true : item.is_publish = false;
    });

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;
    let hasEditPurview = 1;
    if (this.ctx.session.userInfo.type != 1) {
      let objService = await ctx.service.admin.getDataPurviewByService(this.ctx.session.userInfo.name);
      let objTrade = await ctx.service.admin.getDataPurviewByTrade(this.ctx.session.userInfo.name);


      if (objService.checked != 1 && objTrade.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (objService.publish != 1 && objTrade.publish != 1) {
        hasPubviewPurview = 0;
      }
      if (objService.edit != 1 && objTrade.edit != 1) {
        hasEditPurview = 0;
      }
    }

    await ctx.render('news_list', {
      site_title: _info.site_title,
      page_title: '新闻草稿箱',
      active: '5-3',
      list: JSON.stringify(result.list),
      newsType: JSON.stringify(typeList.list),
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      total: result.total,
      csrf: ctx.csrf,
      type: 'draft',
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
      hasEditPurview: hasEditPurview,
      buIds: this.ctx.session.buIds ? JSON.stringify(this.ctx.session.buIds) : JSON.stringify([])
    });
  }

  async getList() {

    const {
      ctx
    } = this;

    const params = ctx.request.body;

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      params.allowServiceList = paramsData.ids;


      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      params.allowTradeList = paramsData.ids;
    }


    const result = await ctx.service.news.getList(params);

    result.list.some(item => {
      item.is_publish == 1 ? item.is_publish = true : item.is_publish = false;
    });

    ctx.body = {
      success: true,
      data: result
    };
  }

  async newsAdd() {
    const {
      ctx
    } = this;

    let serviceList = null;
    let tradeList = null;

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      tradeList = await ctx.service.catalog.getTradeList(paramsData);
    } else {
      serviceList = await ctx.service.catalog.getSercataList();
      tradeList = await ctx.service.catalog.getTradeList();
    }
    const typeList = await ctx.service.news.typeList();


    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }

      if (dataids.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (dataids.publish != 1) {
        hasPubviewPurview = 0;
      }
    }

    await ctx.render('news_add', {
      site_title: _info.site_title,
      ticMallHost: env[this.app.config.env].ticMallHost,
      page_title: '添加新闻',
      active: '5-1',
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      typeList: JSON.stringify(typeList.list),
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview
    });
  }

  async newsEdit() {
    const {
      ctx
    } = this;

    const id = ctx.params.id;

    var checkflag = await ctx.service.admin.checkDataCanOperator('news', id);

    if (!checkflag) {
      await ctx.render('error', {});
      return;
    }
    let serviceList = null;
    let tradeList = null;

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      tradeList = await ctx.service.catalog.getTradeList(paramsData);
    } else {
      serviceList = await ctx.service.catalog.getSercataList();
      tradeList = await ctx.service.catalog.getTradeList();
    }


    const typeList = await ctx.service.news.typeList();
    const logs = await ctx.service.logs.listAll({
      type: 'news',
      id: id,
      page: 1,
      limit: 10
    });

    const detail = await ctx.service.news.getDetail(id);
    if (detail.detail.content) {
      detail.detail.content = detail.detail.content.replace(/\n/g, '').replace(/"/g, '\\\"').replace(/'/g, '\\\'')
    }

    let tradeCataArr = [],
      serviceCataArr = [];
    detail.detail.tradeCata.forEach(item => {
      tradeCataArr.push(item.id);
    });

    detail.detail.serviceCata.forEach(item => {
      serviceCataArr.push(item.id);
    });

    let active = '5-1';
    if (!detail.detail.is_publish) {
      active = '5-3';
    }
    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }

      if (dataids.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (dataids.publish != 1) {
        hasPubviewPurview = 0;
      }
    }

    let checkflowResult = await ctx.service.checkflow.getDetail('news', id);
    if (checkflowResult == null) {
      checkflowResult = {};
    }

    await ctx.render('news_edit', {
      site_title: _info.site_title,
      ticMallHost: env[this.app.config.env].ticMallHost,
      page_title: '编辑新闻',
      active: active,
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      typeList: JSON.stringify(typeList.list),
      tradeCataArr: JSON.stringify(tradeCataArr),
      serviceCataArr: JSON.stringify(serviceCataArr),
      form: detail.detail,
      id: id,
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
      logs: JSON.stringify(logs),
      checkflowResult: JSON.stringify(checkflowResult)
    });
  }

  async newsSave() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;

    if (params.id && parseInt(params.id) > 0) {
      //检查老数据是否有权限
      var checkflag = await ctx.service.admin.checkDataCanOperator('news', params.id);
      if (!checkflag) {
        ctx.body = {
          fail: true,
          needReLogin: 1,
          loginUrl: '/login',
          msg: '请重新登录'
        };
        return;
      }
    }

    var trade_flag = await this.ctx.service.admin.checkUserTradeIsAllow(params.tradeList);
    var service_flag = await this.ctx.service.admin.checkUserServiceIsAllow(params.serviceList);

    if (!trade_flag || !service_flag) //检查提交过来的数据是否有权限
    {
      ctx.body = {
        fail: true,
        needReLogin: 1,
        loginUrl: '/login',
        msg: '请重新登录'
      };
      return;
    }

    const result = await ctx.service.news.newsAdd(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null,
        id: result.id
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async newsDelete() {
    const {
      ctx
    } = this;

    const id = ctx.request.body.id;
    const pageLink = ctx.request.body.pageLink;

    var checkflag = await ctx.service.admin.checkDataCanOperator('news', id);
    if (!checkflag) {
      ctx.body = {
        fail: true,
        data: null
      };
      return;
    }

    const result = await ctx.service.news.newsDelete(id, pageLink);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async newsUpdatePublish() {
    const {
      ctx
    } = this;
    const id = ctx.request.body.id,
      is_publish = ctx.request.body.is_publish;

    var checkflag = await ctx.service.admin.checkDataCanOperator('news', id);
    if (!checkflag) {
      ctx.body = {
        fail: true,
        data: null
      };
      return;
    }
    const result = await ctx.service.news.updatePublish({
      id: id,
      is_publish: is_publish
    });

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async drop() {
    const {
      ctx
    } = this;
    const id = ctx.request.body.id,
      type = ctx.request.body.type;

    let is_publish = type == 'list' ? 0 : 1;

    for (let i of id) {
      var checkflag = await ctx.service.admin.checkDataCanOperator('news', i);
      if (!checkflag) {
        ctx.body = {
          fail: true,
          data: null
        };
        return;
      }
      let result = await ctx.service.news.updatePublish({
        id: i,
        is_publish: is_publish
      });
    }

    ctx.body = {
      success: true,
      data: null
    };
  }

}

module.exports = NewsController;