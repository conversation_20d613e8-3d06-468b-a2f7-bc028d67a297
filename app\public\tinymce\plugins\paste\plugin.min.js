!function(){"use strict";var o=function(e){var t=e,n=function(){return t};return{get:n,set:function(e){t=e},clone:function(){return o(n())}}},t=tinymce.util.Tools.resolve("tinymce.PluginManager"),s=function(e){return!(!/(^|[ ,])powerpaste([, ]|$)/.test(e.settings.plugins)||!t.get("powerpaste")||("undefined"!=typeof window.console&&window.console.log&&window.console.log("PowerPaste is incompatible with Paste plugin! Remove 'paste' from the 'plugins' option."),0))},l=function(e,t){return{clipboard:e,quirks:t}},f=function(e,t,n,r){return e.fire("PastePreProcess",{content:t,internal:n,wordContent:r})},d=function(e,t,n,r){return e.fire("PastePostProcess",{node:t,internal:n,wordContent:r})},u=function(e,t){return e.fire("PastePlainTextToggle",{state:t})},n=function(e,t){return e.fire("paste",{ieFake:t})},m={shouldPlainTextInform:function(e){return e.getParam("paste_plaintext_inform",!0)},shouldBlockDrop:function(e){return e.getParam("paste_block_drop",!1)},shouldPasteDataImages:function(e){return e.getParam("paste_data_images",!1)},shouldFilterDrop:function(e){return e.getParam("paste_filter_drop",!0)},getPreProcess:function(e){return e.getParam("paste_preprocess")},getPostProcess:function(e){return e.getParam("paste_postprocess")},getWebkitStyles:function(e){return e.getParam("paste_webkit_styles")},shouldRemoveWebKitStyles:function(e){return e.getParam("paste_remove_styles_if_webkit",!0)},shouldMergeFormats:function(e){return e.getParam("paste_merge_formats",!0)},isSmartPasteEnabled:function(e){return e.getParam("smart_paste",!0)},isPasteAsTextEnabled:function(e){return e.getParam("paste_as_text",!1)},getRetainStyleProps:function(e){return e.getParam("paste_retain_style_properties")},getWordValidElements:function(e){return e.getParam("paste_word_valid_elements","-strong/b,-em/i,-u,-span,-p,-ol,-ul,-li,-h1,-h2,-h3,-h4,-h5,-h6,-p/div,-a[href|name],sub,sup,strike,br,del,table[width],tr,td[colspan|rowspan|width],th[colspan|rowspan|width],thead,tfoot,tbody")},shouldConvertWordFakeLists:function(e){return e.getParam("paste_convert_word_fake_lists",!0)},shouldUseDefaultFilters:function(e){return e.getParam("paste_enable_default_filters",!0)}},r=function(e,t,n){var r,a,i;"text"===t.pasteFormat.get()?(t.pasteFormat.set("html"),u(e,!1)):(t.pasteFormat.set("text"),u(e,!0),i=e,!1===n.get()&&m.shouldPlainTextInform(i)&&(a="Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.",(r=e).notificationManager.open({text:r.translate(a),type:"info"}),n.set(!0))),e.focus()},c=function(e,n,t){e.addCommand("mceTogglePlainTextPaste",function(){r(e,n,t)}),e.addCommand("mceInsertClipboardContent",function(e,t){t.content&&n.pasteHtml(t.content,t.internal),t.text&&n.pasteText(t.text)})},v=tinymce.util.Tools.resolve("tinymce.Env"),h=tinymce.util.Tools.resolve("tinymce.util.Delay"),y=tinymce.util.Tools.resolve("tinymce.util.Tools"),a=tinymce.util.Tools.resolve("tinymce.util.VK"),e="x-tinymce/html",i="\x3c!-- "+e+" --\x3e",b={mark:function(e){return i+e},unmark:function(e){return e.replace(i,"")},isMarked:function(e){return-1!==e.indexOf(i)},internalHtmlMime:function(){return e}},g=tinymce.util.Tools.resolve("tinymce.html.Entities"),p=function(e){return e.replace(/\r?\n/g,"<br>")},x=function(e,t,n){var r=e.split(/\n\n/),a=function(e,t){var n,r=[],a="<"+e;if("object"==typeof t){for(n in t)t.hasOwnProperty(n)&&r.push(n+'="'+g.encodeAllRaw(t[n])+'"');r.length&&(a+=" "+r.join(" "))}return a+">"}(t,n),i="</"+t+">",o=y.map(r,function(e){return e.split(/\n/).join("<br />")});return 1===o.length?o[0]:y.map(o,function(e){return a+e+i}).join("")},P={isPlainText:function(e){return!/<(?:\/?(?!(?:div|p|br|span)>)\w+|(?:(?!(?:span style="white-space:\s?pre;?">)|br\s?\/>))\w+\s[^>]+)>/i.test(e)},convert:function(e,t,n){return t?x(e,t,n):p(e)},toBRs:p,toBlockElements:x},w=tinymce.util.Tools.resolve("tinymce.html.DomParser"),T=tinymce.util.Tools.resolve("tinymce.html.Node"),_=tinymce.util.Tools.resolve("tinymce.html.Schema"),C=tinymce.util.Tools.resolve("tinymce.html.Serializer");function D(t,e){return y.each(e,function(e){t=e.constructor===RegExp?t.replace(e,""):t.replace(e[0],e[1])}),t}var k={filter:D,innerText:function(t){var n=_(),r=w({},n),a="",i=n.getShortEndedElements(),o=y.makeMap("script noscript style textarea video audio iframe object"," "),s=n.getBlockElements();return t=D(t,[/<!\[[^\]]+\]>/g]),function e(t){var n=t.name,r=t;if("br"!==n)if(i[n]&&(a+=" "),o[n])a+=" ";else{if(3===t.type&&(a+=t.value),!t.shortEnded&&(t=t.firstChild))for(;e(t),t=t.next;);s[n]&&r.next&&(a+="\n","p"===n&&(a+="\n"))}else a+="\n"}(r.parse(t)),a},trimHtml:function(e){return e=D(e,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/gi,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,function(e,t,n){return t||n?"\xa0":" "}],/<br class="Apple-interchange-newline">/g,/<br>$/i])},createIdGenerator:function(e){var t=0;return function(){return e+t++}},isMsEdge:function(){return-1!==navigator.userAgent.indexOf(" Edge/")}};function R(t){var n,e;return e=[/^[IVXLMCD]{1,2}\.[ \u00a0]/,/^[ivxlmcd]{1,2}\.[ \u00a0]/,/^[a-z]{1,2}[\.\)][ \u00a0]/,/^[A-Z]{1,2}[\.\)][ \u00a0]/,/^[0-9]+\.[ \u00a0]/,/^[\u3007\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d]+\.[ \u00a0]/,/^[\u58f1\u5f10\u53c2\u56db\u4f0d\u516d\u4e03\u516b\u4e5d\u62fe]+\.[ \u00a0]/],t=t.replace(/^[\u00a0 ]+/,""),y.each(e,function(e){if(e.test(t))return!(n=!0)}),n}function E(e){var i,o,s=1;function n(e){var t="";if(3===e.type)return e.value;if(e=e.firstChild)for(;t+=n(e),e=e.next;);return t}function l(e,t){if(3===e.type&&t.test(e.value))return e.value=e.value.replace(t,""),!1;if(e=e.firstChild)do{if(!l(e,t))return!1}while(e=e.next);return!0}function t(t,n,r){var a=t._listLevel||s;a!==s&&(a<s?i&&(i=i.parent.parent):(o=i,i=null)),i&&i.name===n?i.append(t):(o=o||i,i=new T(n,1),1<r&&i.attr("start",""+r),t.wrap(i)),t.name="li",s<a&&o&&o.lastChild.append(i),s=a,function e(t){if(t._listIgnore)t.remove();else if(t=t.firstChild)for(;e(t),t=t.next;);}(t),l(t,/^\u00a0+/),l(t,/^\s*([\u2022\u00b7\u00a7\u25CF]|\w+\.)/),l(t,/^\u00a0+/)}for(var r=[],a=e.firstChild;null!=a;)if(r.push(a),null!==(a=a.walk()))for(;void 0!==a&&a.parent!==e;)a=a.walk();for(var u=0;u<r.length;u++)if("p"===(e=r[u]).name&&e.firstChild){var c=n(e);if(/^[\s\u00a0]*[\u2022\u00b7\u00a7\u25CF]\s*/.test(c)){t(e,"ul");continue}if(R(c)){var f=/([0-9]+)\./.exec(c),d=1;f&&(d=parseInt(f[1],10)),t(e,"ol",d);continue}if(e._listLevel){t(e,"ul",1);continue}i=null}else o=i,i=null}function M(n,r,a,i){var o,s={},e=n.dom.parseStyle(i);return y.each(e,function(e,t){switch(t){case"mso-list":(o=/\w+ \w+([0-9]+)/i.exec(i))&&(a._listLevel=parseInt(o[1],10)),/Ignore/i.test(e)&&a.firstChild&&(a._listIgnore=!0,a.firstChild._listIgnore=!0);break;case"horiz-align":t="text-align";break;case"vert-align":t="vertical-align";break;case"font-color":case"mso-foreground":t="color";break;case"mso-background":case"mso-highlight":t="background";break;case"font-weight":case"font-style":return void("normal"!==e&&(s[t]=e));case"mso-element":if(/^(comment|comment-list)$/i.test(e))return void a.remove()}0!==t.indexOf("mso-comment")?0!==t.indexOf("mso-")&&("all"===m.getRetainStyleProps(n)||r&&r[t])&&(s[t]=e):a.remove()}),/(bold)/i.test(s["font-weight"])&&(delete s["font-weight"],a.wrap(new T("b",1))),/(italic)/i.test(s["font-style"])&&(delete s["font-style"],a.wrap(new T("i",1))),(s=n.dom.serializeStyle(s,a.name))||null}var S={preProcess:function(e,t){return m.shouldUseDefaultFilters(e)?function(r,e){var t,a;(t=m.getRetainStyleProps(r))&&(a=y.makeMap(t.split(/[, ]/))),e=k.filter(e,[/<br class="?Apple-interchange-newline"?>/gi,/<b[^>]+id="?docs-internal-[^>]*>/gi,/<!--[\s\S]+?-->/gi,/<(!|script[^>]*>.*?<\/script(?=[>\s])|\/?(\?xml(:\w+)?|img|meta|link|style|\w:\w+)(?=[\s\/>]))[^>]*>/gi,[/<(\/?)s>/gi,"<$1strike>"],[/&nbsp;/gi,"\xa0"],[/<span\s+style\s*=\s*"\s*mso-spacerun\s*:\s*yes\s*;?\s*"\s*>([\s\u00a0]*)<\/span>/gi,function(e,t){return 0<t.length?t.replace(/./," ").slice(Math.floor(t.length/2)).split("").join("\xa0"):""}]]);var n=m.getWordValidElements(r),i=_({valid_elements:n,valid_children:"-li[p]"});y.each(i.elements,function(e){e.attributes["class"]||(e.attributes["class"]={},e.attributesOrder.push("class")),e.attributes.style||(e.attributes.style={},e.attributesOrder.push("style"))});var o=w({},i);o.addAttributeFilter("style",function(e){for(var t,n=e.length;n--;)(t=e[n]).attr("style",M(r,a,t,t.attr("style"))),"span"===t.name&&t.parent&&!t.attributes.length&&t.unwrap()}),o.addAttributeFilter("class",function(e){for(var t,n,r=e.length;r--;)n=(t=e[r]).attr("class"),/^(MsoCommentReference|MsoCommentText|msoDel)$/i.test(n)&&t.remove(),t.attr("class",null)}),o.addNodeFilter("del",function(e){for(var t=e.length;t--;)e[t].remove()}),o.addNodeFilter("a",function(e){for(var t,n,r,a=e.length;a--;)if(n=(t=e[a]).attr("href"),r=t.attr("name"),n&&-1!==n.indexOf("#_msocom_"))t.remove();else if(n&&0===n.indexOf("file://")&&(n=n.split("#")[1])&&(n="#"+n),n||r){if(r&&!/^_?(?:toc|edn|ftn)/i.test(r)){t.unwrap();continue}t.attr({href:n,name:r})}else t.unwrap()});var s=o.parse(e);return m.shouldConvertWordFakeLists(r)&&E(s),e=C({validate:r.settings.validate},i).serialize(s)}(e,t):t},isWordContent:function(e){return/<font face="Times New Roman"|class="?Mso|style="[^"]*\bmso-|style='[^'']*\bmso-|w:WordDocument/i.test(e)||/class="OutlineElement/.test(e)||/id="?docs\-internal\-guid\-/.test(e)}},F=function(e,t){return{content:e,cancelled:t}},I=function(e,t,n,r){var a,i,o,s,l,u,c=f(e,t,n,r);return e.hasEventListeners("PastePostProcess")&&!c.isDefaultPrevented()?(a=e,i=c.content,o=n,s=r,l=a.dom.create("div",{style:"display:none"},i),u=d(a,l,o,s),F(u.node.innerHTML,u.isDefaultPrevented())):F(c.content,c.isDefaultPrevented())},O=function(e,t,n){var r=S.isWordContent(t),a=r?S.preProcess(e,t):t;return I(e,a,n,r)},A=function(e,t){return e.insertContent(t,{merge:m.shouldMergeFormats(e),paste:!0}),!0},B=function(e){return/^https?:\/\/[\w\?\-\/+=.&%@~#]+$/i.test(e)},H=function(e){return B(e)&&/.(gif|jpe?g|png)$/.test(e)},N=function(e,t,n){return!(!1!==e.selection.isCollapsed()||!B(t)||(a=t,i=n,(r=e).undoManager.extra(function(){i(r,a)},function(){r.execCommand("mceInsertLink",!1,a)}),0));var r,a,i},L=function(e,t,n){return!!H(t)&&(a=t,i=n,(r=e).undoManager.extra(function(){i(r,a)},function(){r.insertContent('<img src="'+a+'">')}),!0);var r,a,i},$=function(e,t){var n,r;!1===m.isSmartPasteEnabled(e)?A(e,t):(n=e,r=t,y.each([N,L,A],function(e){return!0!==e(n,r,A)}))},W=function(e,t,n){var r=n||b.isMarked(t),a=O(e,b.unmark(t),r);!1===a.cancelled&&$(e,a.content)},j=function(e,t){t=e.dom.encode(t).replace(/\r\n/g,"\n"),t=P.convert(t,e.settings.forced_root_block,e.settings.forced_root_block_attrs),W(e,t,!1)},V=function(e){var t={};if(e){if(e.getData){var n=e.getData("Text");n&&0<n.length&&-1===n.indexOf("data:text/mce-internal,")&&(t["text/plain"]=n)}if(e.types)for(var r=0;r<e.types.length;r++){var a=e.types[r];try{t[a]=e.getData(a)}catch(i){t[a]=""}}}return t},z=function(e,t){return t in e&&0<e[t].length},K=function(e){return z(e,"text/html")||z(e,"text/plain")},U=function(e,t,n,r){var a=k.createIdGenerator("mceclip");t&&(e.selection.setRng(t),t=null);var i,o,s,l,u,c,f,d=n.result,m=-1!==(o=(i=d).indexOf(","))?i.substr(o+1):null,g=a(),p=e.settings.images_reuse_filename&&r.name?(s=e,l=r.name,(u=l.match(/([\s\S]+?)\.(?:jpeg|jpg|png|gif)$/i))?s.dom.encode(u[1]):null):g,v=new Image;if(v.src=d,c=e.settings,f=v,!c.images_dataimg_filter||c.images_dataimg_filter(f)){var h,y=e.editorUpload.blobCache,b=void 0;(h=y.findFirst(function(e){return e.base64()===m}))?b=h:(b=y.create(g,r,m,p),y.add(b)),W(e,'<img src="'+b.blobUri()+'">',!1)}else W(e,'<img src="'+d+'">',!1)},G=function(o,s,l){var e="paste"===s.type?s.clipboardData:s.dataTransfer;function t(e){var t,n,r,a=!1;if(e)for(t=0;t<e.length;t++)if(n=e[t],/^image\/(jpeg|png|gif|bmp)$/.test(n.type)){var i=n.getAsFile?n.getAsFile():n;(r=new window.FileReader).onload=U.bind(null,o,l,r,i),r.readAsDataURL(i),s.preventDefault(),a=!0}return a}if(o.settings.paste_data_images&&e)return t(e.items)||t(e.files)},X=function(e){return a.metaKeyPressed(e)&&86===e.keyCode||e.shiftKey&&45===e.keyCode},q=function(c,f,d){var m,g=0;function p(e,t,n,r){var a,i;z(e,"text/html")?a=e["text/html"]:(a=f.getHtml(),r=r||b.isMarked(a),f.isDefaultContent(a)&&(n=!0)),a=k.trimHtml(a),f.remove(),i=!1===r&&P.isPlainText(a),a.length&&!i||(n=!0),n&&(a=z(e,"text/plain")&&i?e["text/plain"]:k.innerText(a)),f.isDefaultContent(a)?t||c.windowManager.alert("Please use Ctrl+V/Cmd+V keyboard shortcuts to paste contents."):n?j(c,a):W(c,a,r)}c.on("keydown",function(e){function t(e){X(e)&&!e.isDefaultPrevented()&&f.remove()}if(X(e)&&!e.isDefaultPrevented()){if((m=e.shiftKey&&86===e.keyCode)&&v.webkit&&-1!==navigator.userAgent.indexOf("Version/"))return;if(e.stopImmediatePropagation(),g=(new Date).getTime(),v.ie&&m)return e.preventDefault(),void n(c,!0);f.remove(),f.create(),c.once("keyup",t),c.once("paste",function(){c.off("keyup",t)})}}),c.on("paste",function(e){var t,n,r,a=(new Date).getTime(),i=(t=c,n=V(e.clipboardData||t.getDoc().dataTransfer),k.isMsEdge()?y.extend(n,{"text/html":""}):n),o=(new Date).getTime()-a,s=(new Date).getTime()-g-o<1e3,l="text"===d.get()||m,u=z(i,b.internalHtmlMime());m=!1,e.isDefaultPrevented()||(r=e.clipboardData,-1!==navigator.userAgent.indexOf("Android")&&r&&r.items&&0===r.items.length)?f.remove():K(i)||!G(c,e,f.getLastRng()||c.selection.getRng())?(s||e.preventDefault(),!v.ie||s&&!e.ieFake||z(i,"text/html")||(f.create(),c.dom.bind(f.getEl(),"paste",function(e){e.stopPropagation()}),c.getDoc().execCommand("Paste",!1,null),i["text/html"]=f.getHtml()),z(i,"text/html")?(e.preventDefault(),u||(u=b.isMarked(i["text/html"])),p(i,s,l,u)):h.setEditorTimeout(c,function(){p(i,s,l,u)},0)):f.remove()})},Y=function(e){return e.dom.get("mcepastebin")},Z=function(e,t){return t===e},J=function(r){var e=o(null),a="%MCEPASTEBIN%";return{create:function(){return function(e,t,n){var r,a,i=e.dom,o=e.getBody(),s=e.dom.getViewPort(e.getWin()).y,l=20;t.set(e.selection.getRng());var u=t.get();if(e.inline&&(a=e.selection.getScrollContainer())&&0<a.scrollTop&&(s=a.scrollTop),u.getClientRects){var c=function(e){var t,n,r,a=e.startContainer;if((t=e.getClientRects()).length)return t[0];if(e.collapsed&&1===a.nodeType){for(r=a.childNodes[u.startOffset];r&&3===r.nodeType&&!r.data.length;)r=r.nextSibling;if(r)return"BR"===r.tagName&&(n=i.doc.createTextNode("\ufeff"),r.parentNode.insertBefore(n,r),(e=i.createRng()).setStartBefore(n),e.setEndAfter(n),t=e.getClientRects(),i.remove(n)),t.length?t[0]:void 0}}(u);if(c)l=s+(c.top-i.getPos(o).y);else{l=s;var f=u.startContainer;f&&(3===f.nodeType&&f.parentNode!==o&&(f=f.parentNode),1===f.nodeType&&(l=i.getPos(f,a||o).y))}}r=e.dom.add(e.getBody(),"div",{id:"mcepastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: absolute; top: "+l+"px; width: 10px; height: 10px; overflow: hidden; opacity: 0"},n),(v.ie||v.gecko)&&i.setStyle(r,"left","rtl"===i.getStyle(o,"direction",!0)?65535:-65535),i.bind(r,"beforedeactivate focusin focusout",function(e){e.stopPropagation()}),r.focus(),e.selection.select(r,!0)}(r,e,a)},remove:function(){return function(e,t){if(Y(e)){for(var n=void 0,r=t.get();n=e.dom.get("mcepastebin");)e.dom.remove(n),e.dom.unbind(n);r&&e.selection.setRng(r)}t.set(null)}(r,e)},getEl:function(){return Y(r)},getHtml:function(){return function(n){var t,e,r,a,i,o=function(e,t){e.appendChild(t),n.dom.remove(t,!0)};for(e=y.grep(n.getBody().childNodes,function(e){return"mcepastebin"===e.id}),t=e.shift(),y.each(e,function(e){o(t,e)}),r=(a=n.dom.select("div[id=mcepastebin]",t)).length-1;0<=r;r--)i=n.dom.create("div"),t.insertBefore(i,a[r]),o(i,a[r]);return t?t.innerHTML:""}(r)},getLastRng:function(){return e.get()},isDefault:function(){return e=a,n=Y(r),(t=n)&&"mcepastebin"===t.id&&Z(e,n.innerHTML);var e,t,n},isDefaultContent:function(e){return Z(a,e)}}},Q=function(n,e){var t=J(n);return n.on("preInit",function(){return q(o=n,t,e),void o.parser.addNodeFilter("img",function(e,t,n){var r,a=function(e){e.attr("data-mce-object")||s===v.transparentSrc||e.remove()};if(!o.settings.paste_data_images&&(r=n).data&&!0===r.data.paste)for(var i=e.length;i--;)(s=e[i].attributes.map.src)&&(0===s.indexOf("webkit-fake-url")?a(e[i]):o.settings.allow_html_data_urls||0!==s.indexOf("data:")||a(e[i]))});var o,s}),{pasteFormat:e,pasteHtml:function(e,t){return W(n,e,t)},pasteText:function(e){return j(n,e)},pasteImageData:function(e,t){return G(n,e,t)},getDataTransferItems:V,hasHtmlOrText:K,hasContentType:z}},ee=function(){},te=function(e,t,n){if(r=e,!1!==v.iOS||r===undefined||"function"!=typeof r.setData||!0===k.isMsEdge())return!1;try{return e.clearData(),e.setData("text/html",t),e.setData("text/plain",n),e.setData(b.internalHtmlMime(),t),!0}catch(a){return!1}var r},ne=function(e,t,n,r){te(e.clipboardData,t.html,t.text)?(e.preventDefault(),r()):n(t.html,r)},re=function(s){return function(e,t){var n=b.mark(e),r=s.dom.create("div",{contenteditable:"false","data-mce-bogus":"all"}),a=s.dom.create("div",{contenteditable:"true"},n);s.dom.setStyles(r,{position:"fixed",top:"0",left:"-3000px",width:"1000px",overflow:"hidden"}),r.appendChild(a),s.dom.add(s.getBody(),r);var i=s.selection.getRng();a.focus();var o=s.dom.createRng();o.selectNodeContents(a),s.selection.setRng(o),setTimeout(function(){s.selection.setRng(i),r.parentNode.removeChild(r),t()},0)}},ae=function(e){return{html:e.selection.getContent({contextual:!0}),text:e.selection.getContent({format:"text"})}},ie=function(e){var t,n;e.on("cut",(t=e,function(e){!1===t.selection.isCollapsed()&&ne(e,ae(t),re(t),function(){setTimeout(function(){t.execCommand("Delete")},0)})})),e.on("copy",(n=e,function(e){!1===n.selection.isCollapsed()&&ne(e,ae(n),re(n),ee)}))},oe=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),se=function(e,t){return oe.getCaretRangeFromPoint(t.clientX,t.clientY,e.getDoc())},le=function(e,t){e.focus(),e.selection.setRng(t)},ue=function(o,s,l){m.shouldBlockDrop(o)&&o.on("dragend dragover draggesture dragdrop drop drag",function(e){e.preventDefault(),e.stopPropagation()}),m.shouldPasteDataImages(o)||o.on("drop",function(e){var t=e.dataTransfer;t&&t.files&&0<t.files.length&&e.preventDefault()}),o.on("drop",function(e){var t,n;if(n=se(o,e),!e.isDefaultPrevented()&&!l.get()){t=s.getDataTransferItems(e.dataTransfer);var r,a=s.hasContentType(t,b.internalHtmlMime());if((s.hasHtmlOrText(t)&&(!(r=t["text/plain"])||0!==r.indexOf("file://"))||!s.pasteImageData(e,n))&&n&&m.shouldFilterDrop(o)){var i=t["mce-internal"]||t["text/html"]||t["text/plain"];i&&(e.preventDefault(),h.setEditorTimeout(o,function(){o.undoManager.transact(function(){t["mce-internal"]&&o.execCommand("Delete"),le(o,n),i=k.trimHtml(i),t["text/html"]?s.pasteHtml(i,a):s.pasteText(i)})}))}}}),o.on("dragstart",function(e){l.set(!0)}),o.on("dragover dragend",function(e){m.shouldPasteDataImages(o)&&!1===l.get()&&(e.preventDefault(),le(o,se(o,e))),"dragend"===e.type&&l.set(!1)})},ce=function(e){var t=e.plugins.paste,n=m.getPreProcess(e);n&&e.on("PastePreProcess",function(e){n.call(t,t,e)});var r=m.getPostProcess(e);r&&e.on("PastePostProcess",function(e){r.call(t,t,e)})};function fe(t,n){t.on("PastePreProcess",function(e){e.content=n(t,e.content,e.internal,e.wordContent)})}function de(e,t){if(!S.isWordContent(t))return t;var n=[];y.each(e.schema.getBlockElements(),function(e,t){n.push(t)});var r=new RegExp("(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*(<\\/?("+n.join("|")+")[^>]*>)(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*","g");return t=k.filter(t,[[r,"$1"]]),t=k.filter(t,[[/<br><br>/g,"<BR><BR>"],[/<br>/g," "],[/<BR><BR>/g,"<br>"]])}function me(e,t,n,r){if(r||n)return t;var u,a=m.getWebkitStyles(e);if(!1===m.shouldRemoveWebKitStyles(e)||"all"===a)return t;if(a&&(u=a.split(/[, ]/)),u){var c=e.dom,f=e.selection.getNode();t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,function(e,t,n,r){var a=c.parseStyle(c.decode(n)),i={};if("none"===u)return t+r;for(var o=0;o<u.length;o++){var s=a[u[o]],l=c.getStyle(f,u[o],!0);/color/.test(u[o])&&(s=c.toHex(s),l=c.toHex(l)),l!==s&&(i[u[o]]=s)}return(i=c.serializeStyle(i,"span"))?t+' style="'+i+'"'+r:t+r})}else t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3");return t=t.replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,function(e,t,n,r){return t+' style="'+n+'"'+r})}function ge(n,e){n.$("a",e).find("font,u").each(function(e,t){n.dom.remove(t,!0)})}var pe=function(e){var t,n;v.webkit&&fe(e,me),v.ie&&(fe(e,de),n=ge,(t=e).on("PastePostProcess",function(e){n(t,e.node)}))},ve=function(e){return function(){return e}},he=(ve(!1),ve(!0),function(i){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];for(var o=new Array(arguments.length-1),n=1;n<arguments.length;n++)o[n-1]=arguments[n];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];var a=o.concat(n);return i.apply(null,a)}}),ye=function(e,t,n){var r=n.control;r.active("text"===t.pasteFormat.get()),e.on("PastePlainTextToggle",function(e){r.active(e.state)})},be=function(e,t){var n=he(ye,e,t);e.addButton("pastetext",{active:!1,icon:"pastetext",tooltip:"Paste as text",cmd:"mceTogglePlainTextPaste",onPostRender:n}),e.addMenuItem("pastetext",{text:"Paste as text",selectable:!0,active:t.pasteFormat,cmd:"mceTogglePlainTextPaste",onPostRender:n})};t.add("paste",function(e){if(!1===s(e)){var t=o(!1),n=o(!1),r=o(m.isPasteAsTextEnabled(e)?"text":"html"),a=Q(e,r),i=pe(e);return be(e,a),c(e,a,t),ce(e),ie(e),ue(e,a,n),l(a,i)}})}();