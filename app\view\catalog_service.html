<% include header.html %>
  <el-container id="Main">
    <% include eheader.html %>
      <el-container>
        <% include eside.html %>
          <el-main>
            <el-row>
              <el-col :span="18">
                <el-breadcrumb separator-class="el-icon-arrow-right">
                  <el-breadcrumb-item>
                    <a href='/'>首页</a>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    分类配置
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    服务分类
                  </el-breadcrumb-item>
                </el-breadcrumb>
              </el-col>
              <el-col :span="6" style="text-align:right">
                <el-button size="small" @click="_expands(1)" v-if="expands.length == 0">展开所有</el-button>
                <el-button size="small" @click="_expands(0)" v-else>收起</el-button>
                <el-button v-if="onlyEditPurview !=1" type="primary" size="small" @click="_addCata(0)">添加分类</el-button>
              </el-col>
            </el-row>
            <el-row v-loading="loading">
              <el-col :span="24">
                <el-table :data="tableList.list" style="width: 100%" :expand-row-keys="expands" :row-key="getRowKeys">
                  <el-table-column type="expand" width="50">
                    <template slot-scope="props">
                      <el-table :data="props.row.child" style="width: 100%">
                        <el-table-column prop="name" label="名称" width="240">
                          <template slot-scope="scope">
                            <span v-if="!scope.row.editModel">{{scope.row.name}}</span>
                            <el-input v-model.trim="scope.row.name" v-else style="width:200px">
                              <el-button slot="append" @click="_saveRow(scope.row.id,scope.$index,props.$index)">保存
                              </el-button>
                            </el-input>
                          </template>
                        </el-table-column>
                        <el-table-column prop="sort" label="排序" width="100">
                          <template slot-scope="scope">
                            <el-button type="" size="mini" icon="el-icon-upload2" style="padding:6px"
                              @click="_sortUp(scope.$index,props.$index)" v-if="scope.$index != 0"></el-button>
                            <el-button type="" size="mini" icon="el-icon-download" style="padding:6px"
                              @click="_sortDown(scope.$index,props.$index)" v-if="scope.$index != props.row.clen">
                            </el-button>
                          </template>
                        </el-table-column>
                        <el-table-column prop="id" label="链接地址">
                          <template slot-scope="scope">
                            <a :href="'<%- view_url %>/service/' + props.row.alias  + '/#/'+ scope.row.id"
                              target="_blank">
                              <%- view_url %>/service/{{props.row.alias}}/#/{{scope.row.id}}
                            </a>
                          </template>
                        </el-table-column>
                        <el-table-column label="显示/隐藏" width="100">
                          <template slot-scope="scope">
                            <el-switch v-if="onlyEditPurview != 1" v-model="scope.row.is_navi" active-color="#13ce66"
                              @change="_changeShow(scope.row.id,scope.row.is_navi)"></el-switch>

                            <el-switch v-else disabled v-model="scope.row.is_navi" active-color="#13ce66"
                              @change="_changeShow(scope.row.id,scope.row.is_navi)"></el-switch>
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="400">
                          <template slot-scope="scope">
                            <a href="javascript:;" @click='_editRow(scope.row.id,scope.$index,props.$index)'>编辑</a>
                            <a href="javascript:;" v-if="onlyEditPurview != 1"
                              @click='_del(scope.row.id,props.row.id,scope.row.name, props.row.alias)'>删除</a>
                            <a :href="'/catalog/service/skus/'+scope.row.id"
                              target="_blank">归类SKU({{scope.row.count}})</a>

                          </template>
                        </el-table-column>
                      </el-table>
                      <el-col :span="24" style="padding:10px 0;">
                        <el-button size="mini" v-if="onlyEditPurview !=1" type="primary"
                          @click="_addCata(1,props.row.id)">添加子类</el-button>
                      </el-col>

                    </template>
                  </el-table-column>
                  <el-table-column prop="name" label="名称" width="240">
                    <template slot-scope="scope">
                      <strong>{{scope.row.name}}</strong>
                    </template>
                  </el-table-column>
                  <el-table-column prop="sort" label="排序" width="100">
                    <template slot-scope="scope">
                      <el-button type="" size="mini" icon="el-icon-arrow-up" circle style="padding:6px"
                        @click="_sortUp(scope.$index)"></el-button>
                      <el-button type="" size="mini" icon="el-icon-arrow-down" circle style="padding:6px"
                        @click="_sortDown(scope.$index)"></el-button>
                    </template>
                  </el-table-column>
                  <el-table-column prop="alias" label="链接地址">
                    <template slot-scope="scope">
                      <a :href="'<%- view_url %>/service/' + scope.row.alias+'/'" target="_blank">链接</a>
                    </template>
                  </el-table-column>
                  <el-table-column label="显示/隐藏" width="100">
                    <template slot-scope="scope">
                      <el-switch disabled v-if="onlyEditPurview != 1" v-model="scope.row.is_show" active-color="#13ce66"
                        @change="_changeShow(scope.row.id,scope.row.is_show)"></el-switch>
                      <el-switch v-else disabled v-model="scope.row.is_show" active-color="#13ce66"
                        @change="_changeShow(scope.row.id,scope.row.is_show)"></el-switch>
                    </template>
                  </el-table-column>
                  <el-table-column label="出现在导航" width="100">
                    <template slot-scope="scope">
                      <el-switch v-if="onlyEditPurview != 1" v-model="scope.row.is_navi" active-color="#13ce66"
                        @change="_changeNavi(scope.row.id,scope.row.is_navi)"></el-switch>
                      <el-switch v-else disabled v-model="scope.row.is_navi" active-color="#13ce66"
                        @change="_changeNavi(scope.row.id,scope.row.is_navi)"></el-switch>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150" fixed="right">
                    <template slot-scope="scope">
                      <el-button type="primary" size="mini" @click='_edit(scope.row.id)'>编辑</el-button>
                      <el-button v-if="onlyEditPurview != 1" type="danger" size="mini"
                        @click='_del(scope.row.id,2,scope.row.name, scope.row.alias)'>删除</el-button>
                    </template>
                  </el-table-column>
                  <el-table-column label="" width="300" fixed="right">
                    <template slot-scope="scope">
                      <a :href="'/catalog/service/skus/'+scope.row.id" target="_blank">归类SKU({{scope.row.count}})</a>
                      &nbsp;<a :href="'/catalog/service/solution/'+ scope.row.id" target="_blank">归类解决方案({{ scope.row.solutionCount }})</a><br>
                      <a :href="'/case/list?serviceId='+scope.row.id" target="_blank">查看关联资讯({{scope.row.caseCount}})</a>
                      &nbsp;<a :href="'/resource/list?serviceId='+scope.row.id" target="_blank">查看关联资料({{scope.row.resourceCount}})</a>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
            <div class="logshow-wrapper" v-if="logs.list.length > 0">
              <el-row class="logshow" :class="{on: logshow}">
                <span class="icon"><i class="el-icon-arrow-up" @click="showLog"></i></span>
                <ul>
                  <li v-for="(item, index) of logs.list" :key='item'>{{item.time}}, {{item.user_name}}, {{item.log}}
                  </li>
                </ul>
                <el-pagination @current-change="logPageChange" :current-page.sync="logs.page" :page-size="logs.limit"
                  layout="total, prev, pager, next" :total="logs.total">
                </el-pagination>
              </el-row>
            </div>
          </el-main>
      </el-container>
      <el-dialog title="新增分类" :visible.sync="dialogFormVisible" width="480px">
        <el-form :model="form" label-width="90px" :rules="rules" ref="rform">
          <el-form-item label="分类名称" prop="name">
            <el-input v-model="form.name" auto-complete="off"></el-input>
          </el-form-item>
          <el-form-item label="咨询名称">
            <el-input v-model="form.en_name"></el-input>
          </el-form-item>
          <el-form-item label="英文/别名" prop="alias" v-if="topCata">
            <el-input v-model="form.alias" auto-complete="off"></el-input>
          </el-form-item>
          <el-form-item label="分类短名" prop="short" v-if="topCata">
            <el-input v-model="form.short" auto-complete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="addCata()">确 定</el-button>
        </div>
      </el-dialog>
  </el-container>
  <script>
    var main = new Vue({
      el: '#Main',
      data: {
        tableList: {
          list: <%- list %>,
          total: 0,
          curr: 0,
          limit: 10,
          length: <%- length %>
        },
        form: {
          id: 0,
          name: '',
          alias: '',
          short: '',
          pid: 2,
        },
        topCata: true,
        loading: false,
        dialogFormVisible: false,
        rules: {
          name: [
            { required: true, message: '请输入名称', trigger: 'blur' },
            { min: 2, max: 16, message: '需为2-16位字符', trigger: 'blur' },
            { validator: nameCheck, trigger: 'blur' },
          ],
          alias: [
            { required: true, message: '请输入别名', trigger: 'blur' },
            { min: 2, max: 32, message: '需为2-32位字符', trigger: 'blur' },
            { validator: nameCheck, trigger: 'blur' },
            { validator: engCheck, trigger: 'blur' },
          ],
          short: [
            { required: true, message: '请输入短名', trigger: 'blur' },
            { min: 2, message: '需为2位以上字符', trigger: 'blur' },
            { validator: nameCheck, trigger: 'blur' }
          ]
        },
        onlyEditPurview: '<%= onlyEditPurview %>',
        getRowKeys(row) {
          return row.id;
        },
        expands: [],
        logs: <%- logs %>,
        logshow: false,
      },
      methods: {
        addCata() {
          var that = this;
          var params = this.form;
          params._csrf = '<%- csrf %>';
          this.$refs['rform'].validate((valid) => {
            if (valid) {
              that.loading = true;
              axios.post('/catalog/trade/add', params)
                .then(function (result) {
                  if (result.data.success) {
                    that.dialogFormVisible = false;
                    var data = result.data.data;
                    if (params.pid == 2) {
                      that.tableList.list.forEach((item, i) => {
                        if (item.zindex == 'last') {
                          item.zindex = 'normal';
                        }
                      });
                      that.tableList.list.push({ alias: data.alias, child: [], clen: -1, id: data.id, is_navi: true, is_show: true, name: data.name, sort_num: data.sort_num, zindex: 'last' });
                    } else {
                      var pitem;
                      that.tableList.list.forEach((item, i) => {
                        if (item.id == params.pid) {
                          pitem = item;
                        }
                      });
                      pitem.child.push({ alias: data.alias, child: [], clen: -1, id: data.id, is_navi: true, is_show: true, name: data.name, sort_num: data.sort_num, editModel: false });
                    }
                    that.loading = false;
                    that.initLogs();
                  } else {
                    that.$message.error('名称/别名不能重复');
                    that.loading = false;
                  }
                });
            }
          });
        },
        _expands(i) {
          if (i == 1) {
            this.tableList.list.forEach(item => {
              this.expands.push(item.id)
            })
          } else {
            this.expands = new Array();
          }
        },
        resetForm() {
          this.$refs['rform'].resetFields();
        },
        _edit(id) {
          window.location.href = "/catalog/service/" + id;
        },
        _del(id, pid, name, alias) {
          var that = this;
          let pageLink = ''
          if (pid === 2) {
            // 子级
            pageLink = '<%- view_url %>' + '/service/' + alias + '/'
          } else {
            // 父级
            pageLink = '<%- view_url %>' + '/service/' + alias + '/#/' + pid
          }
          this.$confirm('是否删除' + name + '？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            that.loading = true;
            axios.post('/catalog/trade/delete', { id: id, _csrf: '<%- csrf %>', pageLink })
              .then(function (result) {
                if (result.data.success) {
                  that._default();
                  if (pid == 2) {
                    that.tableList.list.forEach(function (item, i) {
                      if (item.id == id) {
                        that.tableList.list.splice(i, 1);
                      }
                    });
                    that._setIndex();
                  } else {
                    that.tableList.list.forEach(function (item, i) {
                      if (item.id == pid) {
                        item.child.forEach(function (item2, i2) {
                          if (item2.id == id) {
                            item.child.splice(i2, 1);
                          }
                        })
                      }
                    });
                  }
                  //window.location.reload();

                  that.loading = false;
                  that.initLogs();
                }
              });
          }).catch(e => {
            return e;
          });
        },
        _changeShow(id, value) {
          var that = this;
          that.loading = true;
          axios.post('/catalog/trade/changeValue', { id: id, value: value, type: 'is_navi', _csrf: '<%- csrf %>' })
            .then(function (result) {
              if (result.data.success) {
                that.loading = false;
                that.initLogs();
              }
            });
        },
        _changeNavi(id, value) {
          var that = this;
          that.loading = true;
          axios.post('/catalog/trade/changeValue', { id: id, value: value, type: 'is_navi', _csrf: '<%- csrf %>' })
            .then(function (result) {
              if (result.data.success) {
                that.loading = false;
                that.initLogs();
              }
            });
        },
        _sortUp(i, pid) {
          if (i == 0) {
            return;
          }
          var that = this;
          that.loading = true;
          var data = this.tableList.list;
          if (pid || pid == 0) {
            axios.post('/catalog/trade/changeSort', { id: [data[pid].child[i].id, data[pid].child[i - 1].id], sort: [data[pid].child[i].sort_num, data[pid].child[i - 1].sort_num], type: 'up', _csrf: '<%- csrf %>' })
              .then(function (result) {
                if (result.data.success) {
                  that.loading = false;
                  var tempOption = data[pid].child[i - 1];
                  var tempSort = data[pid].child[i - 1].sort_num;
                  data[pid].child[i - 1].sort_num = data[pid].child[i].sort_num;
                  data[pid].child[i].sort_num = tempSort;
                  Vue.set(data[pid].child, i - 1, data[pid].child[i]);
                  Vue.set(data[pid].child, i, tempOption);
                  that._setIndex();
                  that.initLogs();
                }
              });
          } else {
            axios.post('/catalog/trade/changeSort', { id: [data[i].id, data[i - 1].id], sort: [data[i].sort_num, data[i - 1].sort_num], type: 'up', _csrf: '<%- csrf %>' })
              .then(function (result) {
                if (result.data.success) {
                  that.loading = false;
                  var tempOption = data[i - 1];
                  var tempSort = data[i - 1].sort_num;
                  data[i - 1].sort_num = data[i].sort_num;
                  data[i].sort_num = tempSort;
                  Vue.set(data, i - 1, data[i]);
                  Vue.set(data, i, tempOption);
                  that._setIndex();
                  that.initLogs();
                }
              });
          }

        },
        _sortDown(i, pid) {
          if (i == this.tableList.list.length - 1) {
            return;
          }
          var that = this;
          that.loading = true;
          var data = this.tableList.list;
          if (pid || pid == 0) {
            axios.post('/catalog/trade/changeSort', { id: [data[pid].child[i].id, data[pid].child[i + 1].id], sort: [data[pid].child[i].sort_num, data[pid].child[i + 1].sort_num], type: 'down', _csrf: '<%- csrf %>' })
              .then(function (result) {
                if (result.data.success) {
                  that.loading = false;
                  var tempOption = data[pid].child[i + 1];
                  var tempSort = data[pid].child[i + 1].sort_num;
                  data[pid].child[i + 1].sort_num = data[pid].child[i].sort_num;
                  data[pid].child[i].sort_num = tempSort;
                  Vue.set(data[pid].child, i + 1, data[pid].child[i]);
                  Vue.set(data[pid].child, i, tempOption);

                  that._setIndex();
                  that.initLogs();
                }
              });
          } else {
            axios.post('/catalog/trade/changeSort', { id: [data[i].id, data[i + 1].id], sort: [data[i].sort_num, data[i + 1].sort_num], type: 'down', _csrf: '<%- csrf %>' })
              .then(function (result) {
                if (result.data.success) {
                  that.loading = false;
                  var tempOption = data[i + 1];
                  var tempSort = data[i + 1].sort_num;
                  data[i + 1].sort_num = data[i].sort_num;
                  data[i].sort_num = tempSort;
                  Vue.set(data, i + 1, data[i]);
                  Vue.set(data, i, tempOption);
                  that._setIndex();
                  that.initLogs();
                }
              });

          }
        },
        _addCata(i, id) {
          this.dialogFormVisible = true;
          var that = this;
          setTimeout(function () {
            that.resetForm();
          }, 100);

          if (i == 0) {
            this.form.pid = 2;
            this.topCata = true;
            this.form.alias = '';
            this.form.short = '';
          } else if (i == 1) {
            this.form.pid = id;
            this.topCata = false;
            this.form.alias = 'subclass';
          }
        },
        _default() {
          this.form.id = 0;
          this.form.name = '';
          this.form.alias = '';
          this.form.short = '';
        },
        _setIndex() {
          this.tableList.list.forEach((item, i) => {
            if (i == 0) {
              item.zindex = 'first';
            } else if (i == this.tableList.list.length - 1) {
              item.zindex = 'last';
            }
          });
        },
        _editRow(id, i, i2) {
          this.tableList.list[i2].child[i].editModel = true;
        },
        _saveRow(id, i, i2) {
          var $_row = this.tableList.list[i2].child[i]
          var name = $_row.name;
          if (!name || name == '') {
            this.$message.error('名称不能为空');
            return;
          }

          var repeatCheck = false;
          this.tableList.list[i2].child.forEach((item, ci) => {
            if (item.name == name && ci != i) {
              repeatCheck = true;
            }
          });

          if (repeatCheck) {
            this.$message.error('已存在相同的名称');
            return;
          }

          var id = id;
          var that = this;
          axios.post('/catalog/saveRow', { id: id, name: name, _csrf: '<%- csrf %>' })
            .then(function (result) {
              if (result.data.success) {
                $_row.editModel = false;
                that.initLogs();
              }
            });
        },
        showLog() {
          if (this.logs.list.length > 1) {
            this.logshow = !this.logshow;
          }
        },
        logPageChange(page) {
          axios.post('/logslist', { page: page, model: '服务分类', limit: 10, _csrf: '<%- csrf %>' }).then(res => {
            let data = res.data;
            if (data.success) {
              this.logs = data.data;
            }

          })
        },
        initLogs() {
          axios.post('/logslist', { page: 1, model: '服务分类', limit: 10, _csrf: '<%- csrf %>' }).then(res => {
            let data = res.data;
            if (data.success) {
              this.logs = data.data;
            }

          })
        },
        qryRelCaseCount() {
          axios.post('/case/relCount/qry', { parentCatalogId: 2}).then(res => {
            let data = res.data;
            if (data.resultCode === '0') {
              let cntData = data.data;
              this.tableList.list.map(item => {
                let foundItem = cntData.find(dataItem => dataItem.id === item.id);
                this.$set(item, 'caseCount', foundItem ? foundItem.count : 0);
                return item;
              });
            }
          })
        },
        qryRelResourceCount() {
          axios.post('/resource/relCount/qry', { parentCatalogId: 2}).then(res => {
            let data = res.data;
            if (data.resultCode === '0') {
              let cntData = data.data;
              this.tableList.list.map(item => {
                let foundItem = cntData.find(dataItem => dataItem.id === item.id);
                this.$set(item, 'resourceCount', foundItem ? foundItem.count : 0);
                return item;
              });
            }
          })
        }
      },
      mounted() {
        var that = this;
        this.$nextTick(function () {
          document.getElementById('preLoading').style.display = 'none';
          that.qryRelCaseCount();
          that.qryRelResourceCount();
          console.log(that.tableList)
        });
      }
    });
  </script>
  <% include footer.html %>