<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        <% if(form.is_publish){ %>
                                        <a href='/news/list'>新闻列表</a>
                                        <% }else{ %>
                                        <a href='/news/draft'>草稿箱</a>
                                        <% } %>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        编辑新闻
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row>
                            <h3 class="stit">基本属性</h3>
                            <el-form :model="form" label-width="120px" :rules="rules" ref="form">
                                <el-form-item label="类型">
                                    <el-select v-model="form.type" placeholder="请选择" @change="cataInfo">
                                        <el-option  v-for="item in types" :key="item.id" :label="item.name" :value="item.id">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="所属行业">
                                    <el-select multiple v-model="form.tradeList" placeholder="请选择" style="width:100%">
                                        <el-option v-for="item in tradeList"  :value="item.id" :key="item.id" :label="item.name"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="所属服务">
                                    <el-select multiple v-model="form.serviceList" placeholder="请选择" style="width:100%">
                                        <el-option v-for="item in serviceList" :value="item.id" :key="item.id" :label="item.name"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="BU">
                                    <el-select placeholder="请选择" v-model="form.bu_id" style="width:100%" clearable>
                                        <el-option v-for="item of buList" :key="item.buId" :label="item.buName" :value="item.buId">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="别名" prop="alias">
                                    <el-input v-model:trim="form.alias"></el-input>
                                </el-form-item>
                                <el-form-item label="页面链接">
                                    <el-input readonly :value="'<%- view_url %>/news/' + form.alias + '/detail-' + form.id +'.html'" v-if='form.alias'></el-input>
                                    <!-- <el-input readonly :value="'<%- view_url %>/news/detail-' + form.id +'.html'" v-else></el-input> -->
                                </el-form-item>
                                <el-form-item label="移动端页面链接">
                                    <el-input readonly :value="'<%- mview_url %>/news/detail?id=' + form.id"></el-input>
                                </el-form-item>
                            </el-form>
                            <hr>
                            <h3 class="stit">新闻内容</h3>
                            <el-form :model="form" label-width="120px">
                                <el-row>
                                    <el-col :span="20">
                                        <el-form-item label="名称" prop="name">
                                            <el-input v-model:trim="form.title"></el-input>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="4">
                                        <el-form-item label="分类置顶" label-width="80px">
                                            <el-switch v-model="form.is_top" active-color="#13ce66"></el-switch>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-form-item label="内容">
                                    <textarea v-model="form.content" cols="60" rows="5" class="el-input__inner" style="height:600px" id="Content"></textarea>
                                </el-form-item>
                                <el-form-item label="作者" prop="name">
                                    <el-input v-model:trim="form.author"></el-input>
                                </el-form-item>
                                <el-form-item label="发布时间">
                                    <el-date-picker v-model="form.gmt_publish_time" type="datetime" placeholder="选择日期时间">
                                    </el-date-picker>
                                </el-form-item>
                                <el-form-item label="关键字">
                                    <el-input v-model:trim="form.seo_text"></el-input>
                                </el-form-item>
                                <el-form-item label="标签">
                                    <el-tag :key="tag" v-for="tag in form.tag" closable :disable-transitions="false" @close="handleClose(tag)" style="margin-right:10px"> {{tag}} </el-tag>
                                    <el-input class="input-new-tag" v-if="inputVisible" v-model="inputValue" ref="saveTagInput" size="small" @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm" style="width:120px"></el-input>
                                    <el-button v-else class="button-new-tag" size="small" @click="showInput">+ New Tag</el-button>
                                </el-form-item>
                            </el-form>

                            <hr>
                            <h3 class="stit">SEO设置-PC端</h3>
                            <el-row :gutter="20">
                                <el-form label-width="120px">
                                    <el-form-item label="Title">
                                        <el-input v-model="form.page_title" auto-complete="off" maxlength="50"></el-input>
                                    </el-form-item>
                            
                                    <el-form-item label="Keywords">
                                        <el-input v-model="form.page_keywords" auto-complete="off" maxlength="50"></el-input>
                                    </el-form-item>
                            
                                    <el-form-item label="Description">
                                        <el-input v-model="form.page_description" auto-complete="off" maxlength="200"></el-input>
                                    </el-form-item>
                                </el-form>
                            </el-row>
                            <hr>
                            <h3 class="stit">SEO设置-移动端<el-button size="small" style="margin-left: 20px;" @click="handleCopyTDK" type="primary">同PC设置</el-button></h3>
                            <el-row :gutter="20">
                                <el-form label-width="120px">
                                    <el-form-item label="mTitle">
                                        <el-input v-model="form.mpage_title" auto-complete="off" maxlength="50"></el-input>
                                    </el-form-item>
                            
                                    <el-form-item label="mKeywords">
                                        <el-input v-model="form.mpage_keywords" auto-complete="off" maxlength="50"></el-input>
                                    </el-form-item>
                            
                                    <el-form-item label="mDescription">
                                        <el-input v-model="form.mpage_description" auto-complete="off" maxlength="200"></el-input>
                                    </el-form-item>
                                </el-form>
                            </el-row>
                            <hr>
                            <el-col :span="24" style="background: #39F;padding:12px 0;color:#FFF">
                                <el-col :span="12" style="padding-left:12px;">
                                    <el-button @click="_cancel">返回列表</el-button>
                                    <el-button @click="saveDraft">保存为草稿</el-button>
                                </el-col>
                                <el-col :span="12" style="text-align:right;padding-right:12px;">
                                    <el-button type="" @click="preview"> 预 览 </el-button>
                                    <el-button type="success"  v-if="hasPubviewPurview == 1 || hasCheckedPurview == 1" @click='onSubmitDraft'>保存</el-button>
                                    <el-button type="success"  v-if=" hasPubviewPurview != 1"  @click='_sendcheckDialog'>保存并送审</el-button>
									<el-button type="success"   v-if=" hasPubviewPurview == 1"  @click='onSubmit'>保存并发布</el-button> 
									<el-button type="danger"   v-if="checkflowResult.id && hasCheckedPurview == 1 && checkflowResult.tran_status==0"  @click='_checkFailedUI'>退回</el-button> 
                                </el-col>
                            </el-col>
                        </el-row>
							
							<el-dialog
							  title="送审提示"
							  :visible.sync="dialogVisible"
							  width="30%">
							  <div>您正在送审</div>
							  <h3 style="text-indent: 20px;">{{currentNewsTitle}}</h3>
							  <div>请选择审批用户分组：</div>
								  <el-select v-model="selectGroup" placeholder="请选择">
									  <el-option
									    v-for="item in checkGroup"
									    :key="item.id"
									    :label="item.name"
									    :value="item.id">
									</el-option>
								  </el-select>	
												
							  <span slot="footer" class="dialog-footer">
								 <el-button @click="dialogVisible = false">取 消</el-button>
								  <el-button type="primary" @click="_sendcheck()">确 定</el-button>
							  </span>
							</el-dialog>
							
							
							<el-dialog
								  title="审批提示"
								  :visible.sync="failedDialogVisible"
								  width="30%">
								  <div>确定以下内容审批退回？</div>
								  <h3 style="text-indent: 20px;">{{currentNewsTitle}}</h3>
								  <div>退回原因</div>
									 
									 <el-input
									  type="textarea"
									  :rows="2"
									  maxlength="100"
									  placeholder="请输入内容"
									  v-model="tran_note">
									</el-input>
													
								  <span slot="footer" class="dialog-footer">
									 <el-button @click="failedDialogVisible = false">取 消</el-button>
									  <el-button type="primary" @click="_checkFailed()">确 定</el-button>
								  </span>
							</el-dialog>
							
                        <div class="logshow-wrapper" v-if="logs.list.length > 0">
                        <el-row class="logshow" :class="{on: logshow}">
                            <span class="icon"><i class="el-icon-arrow-up" @click="showLog"></i></span>
                            <ul>
                                <li v-for="(item, index) of logs.list" :key='index'>{{item.time}}, {{item.user_name}}, {{item.log}}</li>
                            </ul>
                            <el-pagination
                              @current-change="logPageChange"
                              :current-page.sync="logs.page"
                              :page-size="logs.limit"
                              layout="total, prev, pager, next"
                              :total="logs.total">
                            </el-pagination>
							
							
                        </el-row>
						
						
						
                        </div>
                    </el-main>
            </el-container>
    </el-container>
    <script src="/static/tinymce/tinymce.min.js"></script>
    <script>
    var ai = 0, bi = 0;
    var tinyConfig = {
        height: 600,
        language: 'zh_CN',
        menubar: false,
        plugins: 'advlist autolink link image lists charmap print preview code textcolor table paste media',
        toolbar: 'undo redo | formatselect fontsizeselect bold italic underline forecolor backcolor  | alignleft aligncenter alignright alignjustify superscript subscript | bullist numlist outdent indent | removeformat | image media link table | code preview',
        fontsize_formats: '12px 14px 16px 18px 20px 24px 36px',
        file_browser_callback_types: 'image',
        // images_upload_url: '<%- ticMallHost %>ticCenter/business/api.v0.platform/fileUpload/uploadOss',
        images_reuse_filename: true,
        // images_upload_base_path: '/',
        relative_urls: false,
        branding: false,
        width: '100%',
        images_upload_handler: function (blobInfo, success, failure) {
            let that = this
            let fd = new FormData()
            let file = blobInfo.blob();
            fd.append('file', file, file.name)
            fd.append('systemID', 5);
            let config = {
                headers: { 'Content-Type': 'multipart/form-data' }
            }
            axios.post('<%- ticMallHost %>ticCenter/business/api.v0.platform/fileUpload/uploadOss', fd, config)
                .then(function (result) {
                    if (result.data.resultCode === '0') {
                        success(result.data.data.fileName)
                    } else {
                        that.$message.success(result.data.res);
                        failure()
                    }
                });
        }
    };
    var main = new Vue({
        el: '#Main',
        data: {
            form: {
                id: <%- id %>,
                title: '<%- form.title %>',
                alias: '<%- form.alias || "article" %>',
                bu_id: '<%- form.bu_id %>',
                tradeList: <%- tradeCataArr %>,
                serviceList: <%- serviceCataArr %>,
                content: '<%- form.content %>',
                seo_text: '<%- form.seo_text %>',
                author: '<%- form.author %>',
                gmt_publish_time: '<%- form.gmt_publish_time %>',
                tag: '<%- form.tag %>',
                is_publish: <%- form.is_publish %>,
                is_top: <%- form.is_top %>,
                type: <%- form.catalog_id %>,
                page_title:'<%- form.page_title %>',
                page_keywords:'<%- form.page_keywords %>',
                page_description:'<%- form.page_description %>',
                mpage_title: '<%- form.mpage_title %>',
                mpage_keywords: '<%- form.mpage_keywords %>',
                mpage_description: '<%- form.mpage_description %>',
                catalog: ''
            },
            tradeList: <%- tradeList %>,
            serviceList: <%- serviceList %>,
            types: <%- typeList %>,
            loading: false,
            tradeDialog: false,
            serviceDialog: false,
            treeset: {
                label: 'name',
                children: 'child'
            },
            inputVisible: false,
            inputValue: '',
            rules: {
                name: [
                    { required: true, message: '请输入名称', trigger: 'blur' },
                    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
                    { validator: nameCheck, trigger: 'blur' },
                ],
            },
            logs: <%- logs %>,
            logshow: false,
			hasPubviewPurview:<%- hasPubviewPurview %>,
			hasCheckedPurview:<%- hasCheckedPurview %>,
			checkGroup:[],
			selectGroup:null,
			currentNewsTitle:'',
			currentId:0,
			loading: false,
			check: true,
			checkIds:[],
			dialogVisible:false,
			failedDialogVisible:false,
			tran_note:'',
			checkflowResult:<%- checkflowResult %>,
            buList: [] // bu列表数据
        },
        methods: {
            handleCopyTDK(){
                this.form.mpage_title = this.form.page_title;
                this.form.mpage_keywords = this.form.page_keywords;
                this.form.mpage_description = this.form.page_description;
            },
			_sendcheck(row)
			{
				var that = this;
				that.loading = true;
				var objData={data_type:'news', data_id:that.currentId,data_name:that.currentNewsTitle,receive_groupid:that.selectGroup};
	
				if(!this.selectGroup){
					that.loading = false;
				    this.$message.error('请选择审批用户分组');
				    return;
				}
				
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        var loading = this.$loading({
                            lock: true,
                            text: 'Loading',
                            spinner: 'el-icon-loading',
                            background: 'rgba(255, 255, 255, 0.7)'
                        });
                        
                        var params = that.form;
                        params.is_publish = 0;
                        params.content = tinymce.get('Content').getContent();
                        params._csrf = '<%- csrf %>';
                        axios.post('/news/save', that.form)
                            .then(function(result) {
                                loading.close();
                                if (result.data.success) {
                                   
								   axios.post('/checkflow/apply', objData)
								   .then(function(result) {
								   	if (result.data.success) {
								   		that.loading = false;
								   		that.dialogVisible = false;
								   		 if (result.data.success) {
								   		     that.$message.success('送审成功');
											  window.location.reload();
								   			 
								   		}else{
								   		      if(result.data.needReLogin==1)
								   		    {
								   		    	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
								   		    			window.location.href = result.data.loginUrl;
								   		    	}});
								   		    }
								   		    else
								   		    {
								   		    	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
								   		    			window.location.reload();
								   		    	}});
								   		    }
								   		}
								   	}
								   });
								   
                                }else{
                                    that.$message.error(result.data.msg);
                                }
                            });
                    } else {
                        return false;
                    }
                });
			},
			_sendcheckDialog()
			{
				
				var that = this;

				that.loading = true;
				that.currentId=0;
				that.selectGroup=null;
				
				var postdata= {type:'news', byIdSelect:'1',trade:that.form.tradeList,service:that.form.serviceList  };
			
				axios.post('/role/getRoleList',postdata)
				.then(function(result) {
					if (result.data.success) {
						;
						that.loading = false;
						that.dialogVisible = true;
						that.currentId=that.form.id;
						that.currentNewsTitle=that.form.title;
						that.checkGroup=result.data.data;
					}
					else{
					    that.$message.error('加载错误');
					}
				});
			},
            showInput() {
                this.inputVisible = true;
                this.$nextTick(_ => {
                    this.$refs.saveTagInput.$refs.input.focus();
                });
            },
            handleInputConfirm() {
                let inputValue = this.inputValue;
                if (inputValue) {
                    this.form.tag.push(inputValue);
                }
                this.inputVisible = false;
                this.inputValue = '';
            },
            handleClose(tag) {
                this.form.tag.splice(this.form.tag.indexOf(tag), 1);
            },
            saveDraft(){
                var that = this;
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        var loading = this.$loading({
                            lock: true,
                            text: 'Loading',
                            spinner: 'el-icon-loading',
                            background: 'rgba(255, 255, 255, 0.7)'
                        });
                        
                        var params = that.form;
                        params.is_publish = 0;
                        params.content = tinymce.get('Content').getContent();
                        params._csrf = '<%- csrf %>';
                        axios.post('/news/save', that.form)
                            .then(function(result) {
                                loading.close();
      
                                if (result.data.success) {
                                    //window.location.href = '/sku/service'
                                    window.location.reload();
                                    that.$message({
                                        message: '保存成功。',
                                        type: 'success'
                                    });
                                }else{
                                      if(result.data.needReLogin==1)
                                    {
                                    	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
                                    			window.location.href = result.data.loginUrl;
                                    	}});
                                    }
                                    else
                                    {
                                    	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
                                    			window.location.reload();
                                    	}});
                                    }
                                }
                            });
                    } else {
                        return false;
                    }
                });
            },
			_checkFailedUI()
			{
				var that = this;
				that.currentNewsTitle=that.form.title;
				that.currentNewsId=that.form.id;
				that.failedDialogVisible = true;
			},
			_checkFailed()
			{
				var that = this;
				
				 this.$confirm('确定以下内容审批退回吗？<br/>'+that.currentNewsTitle, '审批提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning',
				  dangerouslyUseHTMLString:true
				}).then(() => {
					that.loading = true;
					
					axios.post('/dashboard/checkoperator', { id: that.checkflowResult.id,tran_note:that.tran_note,name:that.currentNewsTitle,tran_status:20, _csrf: '' })
					.then(function(result) {
						if (result.data.success) {
							window.location.reload();
						}
						else
						{
							  if(result.data.needReLogin==1)
							{
								that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
										window.location.href = result.data.loginUrl;
								}});
							}
							else
							{
								that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
										window.location.reload();
								}});
							}
						}
					});
				}).catch(e =>{
                    return e;
				});
			},
			onSubmitDraft(){
			    var that = this;
			    if(!this.form.type){
			        this.$message.error('请选择新闻类型');
			        return;
			    }
			    if(!this.form.title){
			        this.$message.error('请输入新闻名称');
			        return;
                }
                if (!this.form.bu_id) {
                    this.$message.error('请选择BU');
                    return;
                }
			    this.$refs['form'].validate((valid) => {
			        if (valid) {
			            var loading = this.$loading({
			                lock: true,
			                text: 'Loading',
			                spinner: 'el-icon-loading',
			                background: 'rgba(255, 255, 255, 0.7)'
			            });
			
			            var params = that.form;
			            params.is_publish = 0;
			            params.content = tinymce.get('Content').getContent();
			            params._csrf = '<%- csrf %>';
                        params.pageLink = '<%- view_url %>' + '/news/' + that.form.alias + '/detail-' + that.form.id +'.html';
			            axios.post('/news/save', that.form)
			                .then(function(result) {
			                    loading.close();
			                    loading.close();
			                    if (result.data.success) {
			                        //window.location.href = '/sku/service'
			                        window.location.reload();
			                        that.$message({
			                            message: '保存成功。',
			                            type: 'success'
			                        });
			                    }else{
			                          if(result.data.needReLogin==1)
			                        {
			                        	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
			                        			window.location.href = result.data.loginUrl;
			                        	}});
			                        }
			                        else
			                        {
			                        	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
			                        			window.location.reload();
			                        	}});
			                        }
			                    }
			                });
			        } else {
			            return false;
			        }
			    });
			},
            onSubmit(){
                // 别名传递到solr
                let catalogArr = []
                this.tradeList.forEach(i => {
                    this.form.tradeList.forEach(j => {
                        if (i.id === j) {
                            catalogArr.push(i.alias)
                        }
                    })
                })
                this.serviceList.forEach(i => {
                    this.form.serviceList.forEach(j => {
                        if (i.id === j) {
                            catalogArr.push(i.alias)
                        }
                    })
                })
                this.form.catalog = catalogArr.join(',')
                var that = this;
                if(!this.form.type){
                    this.$message.error('请选择新闻类型');
                    return;
                }
                if(!this.form.title){
                    this.$message.error('请输入新闻名称');
                    return;
                }
                if (!this.form.bu_id) {
                    this.$message.error('请选择BU');
                    return;
                }
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        var loading = this.$loading({
                            lock: true,
                            text: 'Loading',
                            spinner: 'el-icon-loading',
                            background: 'rgba(255, 255, 255, 0.7)'
                        });

                        var params = that.form;
                        params.is_publish = 1;
                        params.content = tinymce.get('Content').getContent();
                        params._csrf = '<%- csrf %>';
                        params.pageLink = '<%- view_url %>/news/' + that.form.alias + '/detail-' + that.form.id +'.html';
                        axios.post('/news/save', that.form)
                            .then(function(result) {
                                loading.close();
                                loading.close();
                                if (result.data.success) {
                                    //window.location.href = '/sku/service'
                                    // window.location.reload();
                                    that.$message({
                                        message: '保存成功。',
                                        type: 'success'
                                    });
                                }else{
                                     if(result.data.needReLogin==1)
                                   {
                                   	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
                                   			window.location.href = result.data.loginUrl;
                                   	}});
                                   }
                                   else
                                   {
                                   	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
                                   			window.location.reload();
                                   	}});
                                   }
                                }
                            });
                    } else {
                        return false;
                    }
                });
            },
            _default() {
                this.form.id = 0;
                this.form.name = '';
                this.form.alias = '';
            },
            _cancel(){
                <% if(form.is_publish){ %>
                window.location = '/news/list/';
                <% }else{ %>
                window.location = '/news/draft/';
                <% } %>
            },
            preview(){
                var that = this;
                
                that.content = tinymce.get('Content').getContent();
                that.types.forEach(item => {
                    if(item.id == that.form.type){
                        that.form.catalog_name = item.name;
                    }
                });
                var params = that.form;
                params._csrf = '<%- csrf %>';
                axios.post('/preview/prenews',params).
                    then(function(result){
                        if(result.data.success){
                            window.open('/preview/prenews/'+result.data.data);
                        }
                    });
            },
            cataInfo(v){
                this.types.forEach(item => {
                    if(item.id ==v){
                       this.form.catalog_name = item.name;
                    }
                })
            },
            showLog(){
                if(this.logs.list.length > 1){
                    this.logshow = !this.logshow;
                }
            },
            logPageChange(page){
                axios.post('/logslist',{page:page,type:'news',id:<%- id %>,limit:10,_csrf:'<%- csrf %>'}).then(res => {
                    let data = res.data;
                    if(data.success){
                        this.logs = data.data;
                    }
                    
                })
            },
            // 获取bu列表
            qryBuList() {
                var that = this;
                axios.post('/setting/buManage/qry', {}).
                    then(function (result) {
                        if (result.data.resultCode === '0') {
                            that.buList = result.data.data.items
                        } else {
                            that.$message.error(result.data.resultMsg);
                        }
                    });
            }
        },
        mounted(){
            this.$nextTick(function(){
                tinyConfig.selector = '#Content';
                tinymce.init(tinyConfig);

                var that = this;

                if(this.form.tag){
                    this.form.tag = this.form.tag.split(',');
                }else{
                     this.form.tag = [];
                }

                document.getElementById('preLoading').style.display = 'none';
                this.qryBuList()
                this.form.bu_id = this.form.bu_id > 0 ? Number(this.form.bu_id) : ''
            });
        }
    });
    </script>
    <% include footer.html %>