'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const { env } = require('../../config/info').siteInfo;
const request = require('request'),
  moment = require('moment');

class TicketController extends Controller {
  async list() {
    const {
      ctx
    } = this;

    // const result = await ctx.service.ticket.list({
    //     page: 1
    // });

    // result.list.forEach(item => {
    //     item.time = moment(item.gmt_modify).format('YYYY-MM-DD HH:mm:ss');
    // });
    await ctx.render('ticket', {
      site_title: _info.site_title,
      page_title: '反馈中心',
      active: '11',
      list: [],
      total: 0,
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mviewUrl: _info.mviewUrl,
      userInfo: ctx.session.userInfo,
    });
  }

  async getList() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const result = await ctx.service.ticket.list(params);

    result.list.forEach(item => {
      item.time = moment(item.gmt_modify).format('YYYY-MM-DD HH:mm:ss');
    });
    ctx.body = {
      success: true,
      data: {
        list: result.list,
        total: result.total
      }
    };
  }

  async post() {
    const {
      ctx
    } = this;
    const {
      id
    } = ctx.request.body;

    const result = await ctx.service.external.ticketResend({
      id
    }, ctx.headers);
    ctx.body = {
      success: result.success,
      data: result.resultMsg
    };
  }

  async getFields() {
    return;
  }
}

module.exports = TicketController;