{"name": "tcms", "code": "project_tic", "version": "24.11.26", "description": "在线商城PC端客服端管理系统", "author": "Ken<PERSON>@sgs.com", "private": true, "dependencies": {"await-stream-ready": "^1.0.1", "axios": "^0.18.0", "crypto": "^1.0.1", "egg": "^2.2.1", "egg-mysql": "^3.0.0", "egg-scripts": "^2.5.0", "@sentry/cli": "1.62.0", "egg-sentry": "^1.0.0", "egg-view": "^2.1.0", "egg-view-ejs": "^2.0.0", "file-type": "^7.6.0", "jimp": "^0.2.28", "koa-multer": "^1.0.2", "multer": "^1.3.0", "mysql": "^2.18.1", "npm": "^6.0.0", "read-chunk": "^2.1.0", "stream-wormhole": "^1.0.3"}, "devDependencies": {"autod": "^3.0.1", "autod-egg": "^1.0.0", "egg-bin": "^4.3.5", "egg-ci": "^1.8.0", "egg-mock": "^3.14.0", "eslint": "^4.11.0", "eslint-config-egg": "^6.0.0", "excel-export": "^0.5.1", "webstorm-disable-index": "^1.2.0"}, "engines": {"node": ">=8.9.0"}, "scripts": {"start": "egg-scripts start --daemon --title=egg-server-tcms --port 9001", "dev": "egg-scripts start --daemon --title=egg-server-tcms --env=dev --port 9001", "uat": "egg-scripts start --daemon --title=egg-server-tcms --env=uat --port 9001", "test": "egg-scripts start --daemon --title=egg-server-tcms --env=test --port 9001", "gray": "egg-scripts start --daemon --title=egg-server-tcms --env=gray --port 9001", "prod": "egg-scripts start --daemon --title=egg-server-tcms --env=prod --port 9001", "prodGray": "egg-scripts start --daemon --title=egg-server-tcms --env=prodGray --port 9001", "local": "egg-bin dev --port 9001 --env=local", "stop": "egg-scripts stop --title=egg-server-tcms", "debug": "egg-bin debug --port 9001", "test-local": "egg-bin test", "cov": "egg-bin cov", "lint": "eslint .", "ci": "npm run lint && npm run cov", "autod": "autod"}, "ci": {"version": "8"}, "repository": {"type": "git", "url": ""}, "license": "MIT"}