'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const { env } = require('../../config/info').siteInfo;
const moment = require('moment');

class AdminfreezeController extends Controller {

  //用户列表
  async adminfreezeList() {

    const { ctx } = this;
    const roleList = await ctx.service.role.getList({ is_effect: 1, is_delete: 0 });

    const params = { page: 1, is_delete: 1 };
    params.type = 2;
    const result = await ctx.service.admin.getList(params);

    result.list.some(item => {
      item.gmt_create = moment(item.gmt_create).format('YYYY-MM-DD HH:mm:ss');
      if (item.delete_time == null) {
        item.delete_time = '';
      }
      else {
        item.delete_time = moment(item.delete_time).format('YYYY-MM-DD HH:mm:ss');
      }
    });

    await ctx.render('adminfreeze_list', {
      site_title: _info.site_title,
      page_title: '冻结用户列表',
      active: '14-4',
      list: JSON.stringify(result.list),
      roleList: JSON.stringify(roleList.list),
      total: result.total,
      csrf: ctx.csrf,
      type: 'list',
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
    });
  }

  async getList() {

    const { ctx } = this;

    const params = ctx.request.body;
    params.type = 2;
    params.is_delete = 1;

    const result = await ctx.service.admin.getList(params);
    result.list.some(item => {
      item.gmt_create = moment(item.gmt_create).format('YYYY-MM-DD HH:mm:ss');
      if (item.delete_time == null) {
        item.delete_time = '';
      }
      else {
        item.delete_time = moment(item.delete_time).format('YYYY-MM-DD HH:mm:ss');
      }
    });
    ctx.body = { success: true, data: result };
  }

  async adminfreezeActive() {
    const { ctx } = this;

    const id = ctx.request.body.id;
    const result = await ctx.service.admin.adminActive(id);

    if (result.success) {
      ctx.body = { success: true, data: null };
    } else {
      ctx.body = { fail: true, data: null };
    }
  }

}

module.exports = AdminfreezeController;