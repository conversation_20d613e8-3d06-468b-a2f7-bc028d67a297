.videoBox{
    width: 100%;height: 425px;
    background: url(../images/banner3.jpg);
    margin-top: 100px;
    position: relative;
}
.videoWord1{
    color: #ffffff;
    font-size: 26px;
    font-weight: bold;
    left: 0;
    width: 100%;
    margin-left: 0;
    top: 100px;
    position: absolute;
	text-align:center;
}
.videoWord2{
    font-size: 41px;color: white;
    width: 100%;
    position: absolute;
    left: 0;
    top: 160px;
	text-align:center;
}
.videoWord3{
    font-size: 17px;
    color: #ffffff;
    width: 100%;
    position: absolute;
    left: 0;
    top: 230px;
	text-align:center;
}
.deepen{
    width: 0;height: 100%;
    background: #cc5500;
    position: absolute;
}
.videoBox .learnMore{
    width: 170px;
    opacity: 1;
    left: 50%;
    top: 316px;
    margin-left: -88px;
    position: absolute;
}
.learnMore:hover .deepen{
    width: 100%;transition: .4s;
}
.homeConBox{
    width: 100%;height: auto;
}
.homeCon1{
    width: 1226px;height: 640px;
    margin: 0 auto;
}
.homeContitle{
    width: 100%;height: 22px;
    font-size: 16px;
    padding: 24px 0;
    border-bottom: 3px solid #f2f2f2;
    position: relative;
    margin-bottom: 30px;
}
.homeContitle span{
    font-size: 16px;
    display: inline-block;
    width: auto;height: 22px;
    text-align: center;
    padding: 0 25px;
    cursor: pointer;
}
.homeContitle .title:hover{
    color: rgb(254,102,3);
}
.homeContitle .spanL{
    padding: 0;
    float: right;
}
.spanL .spanL-word{
    float: left;
    font-size: 14px;
    line-height: 22px;
    padding: 0;
    margin-right: 5px;

    cursor: pointer;
}
.spanL .spanL-word a{
    color: rgb(254,102,3);
}
.spanL-icon{
    width: 0;
    height: 0;
    border-top: 6px solid transparent;
    border-left: 6px solid rgb(254,102,3);
    border-bottom: 6px solid transparent;
    border-right: 6px solid transparent;
    float: right;
    margin-top: 6px;
}
.active{
    color: rgb(254,102,3);
    font-weight: bold;
}
.titleBor{
    width: 114px;height: 3px;background: rgb(254,102,3);
    position: absolute;
    top: 70px;left: 0;
}
.homeConCon1{
    width: 100%;height: 528px;

}
.homeConConL{
    width: 284px;height: 498px;
    float: left;
    cursor: pointer;
}
.homeConConL:hover{
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    transition: .4s;
    transform: translate3d(0, -3px, 0);
}
.homeConConL img{
    width: 100%;height: 100%;
    background-size: contain;
}
.homeConConR{
    width: 912px;height: 498px;
    float: right;
}
.homeConConR ul{
    width: 942px;height: 100%;
    margin: 0;padding: 0;
}
.homeConConRLi{
    width: 284px;height: 234px;float: left;
    margin-right: 30px;
    background: #fafafa;
    margin-bottom: 30px;
    cursor: pointer;
    list-style: none;
}
.homeConConRLi-img{
    width: 284px;height: 142px;
    margin-bottom: 20px;
}
.homeConConRLi-word1{
    text-align: center;
    font-size: 17px;
    color: #252525;
    margin-bottom: 5px;
}
.homeConConRLi-word2{
    text-align: center;
    font-size: 13px;
    color: #999999;
}
.homeCon2{
    width: 100%;height: 514px;
    background: #fafafa;
}
.homeCon2Box{
    width: 1226px;height: 100%;
    margin: 0 auto;
}
.homeConCon2{
    width: 100%;height: 370px;
}
.homeConCon2 ul{
    width: 1254px;height: 100%;
    cursor: pointer;
    margin: 0;padding: 0;
}
.homeConCon2Li{
    width: 390px;height: 100%;
    float: left;background: white;
    margin-right: 28px;
    list-style: none;
}
.homeConCon2Li-img{
    width: 390px;height: 192px;
    margin-bottom: 36px;
}
.homeConCon2Li-word1{
    font-size: 17px;
    color: #000000;
    text-align: center;
    margin-bottom: 20px;
}
.homeConCon2Li-borderBox{
    width: 100%;
    height: 3px;
    position: relative;
    margin-bottom: 20px;
}
.homeConCon2Li-border{
    width: 36px;
    height: 3px;
    background-color: #f1f1f1;
    position: absolute;
    left: 50%;top: 0;
    margin-left: -18px;
}
.homeConCon2Li-word2{
    font-size: 13px;
    width: 285px;
    color: #999999;
    margin-left: 52px;
    text-align: center;
    line-height: 1.7;
}
.homeCon3{
    background: white;
    height: 514px;
}
.homeCon4{
    background: #fafafa;
    height: 550px;
}
.over:hover{
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    transition: .4s;
    transform: translate3d(0, -3px, 0);
}
.homeConCon4Li{
    position: relative;
    height: 400px;
}
.homeConCon4Li-word1{
    font-weight: bold;
    font-size: 23px;
    color: white;
    position: absolute;
    left: 25px;top: 41px;
}
.homeConCon4Li-word2{
    width: 340px;
    font-size: 15px;
    color: #ffffff;
    position: absolute;
    left: 25px;
    top: 86px;
    line-height: 1.6;
}
.homeConCon2Li-img2{
    position: absolute;
    left: 0;top: 0;
    width: 390px;
    height: 400px;
}
.homeConCon2Li-img1{
    position: absolute;
}
.over:hover .homeConConRLi-word1{
    color: rgb(254,102,3);
}
.over:hover .homeConConRLi-word2{
    color: #000000;
}
.over:hover .homeConCon2Li-word1{
    color: rgb(254,102,3);
}
.over:hover .homeConCon2Li-word2{
    color: #000000;
}
.learnMore{
    width: 157px;height: 46px;
    background: rgb(254,102,3);
    position: absolute;
    left: 25px;bottom: 40px;
    opacity: 0;
    cursor: pointer;
}
.learnMore-word{
    font-size: 16px;color: white;
    line-height: 44px;
    position: absolute;
    left: 28px;
}
.learnMore-icon{
    position: absolute;
    width: 24px;height: 24px;
    background: url(../images/learnmore.png);
    top: 10px;right: 28px;
}
.homeConCon5Li{
    width: 284px;height: 123px;
    margin-right: 30px;
    float: left;
    /*background: #ccc;*/
	position:relative;
	padding-top: 177px;
    list-style: none;
}
.homeConCon5Li:hover .homeConCon2Li-word1{
    color: rgb(254,102,3);
    transition: .4s;
}
.homeConCon5Li:hover .homeConCon2Li-word2{
    color: #000000;
    transition: .4s;
}
.homeCon5 ul{
    width: 1256px;
}
.homeConCon5Li-img{
    width: 122px;height: 122px;
    /*background: url(../images/minsuxuan.png);*/
    margin-left: 81px;
	position:absolute;
	top: 10px;
}
.homeConCon5Li-word1{
    font-size: 18px;color: #000000;
    text-align: center;
    margin-bottom: 21px;
}
.homeConCon5Li-word2{
    width: 232px;
    margin-left: 27px;
    font-size: 14px;
    color: #999999;
    text-align: center;
    line-height: 1.6;
}
.descrip{
    width: 100%;height: 418px;
    background: url(../images/banner3.jpg);
    overflow: hidden;
}
.descripBox{
    width: 1226px;
    height: 240px;
    margin: 93px auto 0;
    overflow: visible;
}
.descripLi.big{
    width: 353px;
    height: 100%;
    border-right: 1px solid #ffffff;
    position: relative;
}
.descripBox ul{
    width: 1262px;
    height: 100%;
    position: relative;
    padding: 0;margin: 0;
}
.bigBox .liTitle{
    font-size: 36px;
    color: white;
    width:100%;
    position: absolute;
    top: -20px;
}
.bigBox .liTime{
    font-size: 13px;
    color: #bcb5b5;
    padding: 0 0 40px;
}
.bigBox .liInfo{
    font-size: 14px;
    color: #e3dfdd;
    padding-bottom: 60px;
    padding-top: 60px;
    text-align: center;
}
.bigBox .learnMore{
    opacity: 1;
}
.descripLi{
    width: 240px;
    height: 100%;
    float: left;
    border-right: 1px solid white;
    position: relative;
    padding: 0 20px;
    overflow: visible!important;
    list-style: none;
}
.descripLiLast{
    border-right: 0;
}
.smallBox{
    /*opacity: 0;*/
}
.smallBox .liInfo{
    font-size: 17px;
    color: #ffffff;
    width: 232px;
    text-align: center;
    position: absolute;
    left: 50%;
    margin-left: -116px;
    top: 44px;
}
.smallBox .liTime{
    width: 100px;
    color: white;
    font-size: 14px;
    position: absolute;
    left: 50%;
    margin-left: -50px;
    top: 155px;
}
.descripLi .liTitle{
    opacity: 0;
}
.descripLi .learnMore{
    display: none;
}
.descripLi.big .liTitle{
    opacity: 1;
}
.descripLi.big .learnMore{
    display: block;
}
.descripLi .liTime{
    display: block;
    width: 100%;
    text-align: center;
}
.descripLi.big .liTime{
    text-align: left;
    padding-top: 40px;
}
.descripLi .learnMore{
    position: relative;
    top:0;
    left: 0;
}
.descripLi.big .bigBox .liInfo{
    padding-top: 0;
    text-align: left;
}

