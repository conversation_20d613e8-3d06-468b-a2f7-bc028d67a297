<% include header.html %>
  <el-container id="Main">
    <% include eheader.html %>
      <el-container>
        <% include eside.html %>
          <el-main>
            <el-row>
              <el-col :span="24">
                <el-breadcrumb separator-class="el-icon-arrow-right">
                  <el-breadcrumb-item>
                    <a href='/'>首页</a>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    SKU配置
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    解决方案
                  </el-breadcrumb-item>
                </el-breadcrumb>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form :model="form" :inline="true">
                  <el-form-item label="服务">
                    <el-select v-model="form.service" placeholder="请选择" clearable>
                      <el-option v-for="item in serviceList" :key="item.id" :label="item.name" :value="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="行业">
                    <el-select v-model="form.trade" placeholder="请选择" clearable>
                      <el-option v-for="item in tradeList" :key="item.id" :label="item.name" :value="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="BU">
                    <el-select placeholder="请选择" v-model="form.bu_id" clearable>
                      <el-option v-for="item of buList" :key="item.buId" :label="item.buName" :value="item.buId">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="订购">
                    <el-select v-model="form.is_buy" placeholder="请选择" clearable style="width:120px">
                      <el-option :key="true" label="可订购" :value="true"></el-option>
                      <el-option :key="false" label="不可订购" :value="false"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="上下架">
                    <el-select v-model="form.is_use" placeholder="请选择" clearable style="width:120px">
                      <el-option :key="true" label="上架" :value="true"></el-option>
                      <el-option :key="false" label="下架" :value="false"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="名称">
                    <el-input v-model="form.name" placeholder="服务名称" clearable></el-input>
                  </el-form-item>
                  <el-form-item label="统计时间">
                    <el-date-picker style="width: 240px;" v-model="form.qryDate" value-format="yyyy-MM-dd"
                      type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
                    </el-date-picker>
                  </el-form-item>
                </el-form>
              </el-col>
              <el-col :span="24" style="text-align:right">
                <el-button @click='handleExport' type="primary">导出</el-button>
                <el-button @click=_default>重置</el-button>
                <el-button type="primary" @click="_filter">查询</el-button>
                <!-- <el-button  v-if="hasEditPurview==1"   size="small" @click='_delMulti' :disabled="check">批量删除</el-button> -->
                <a v-if="onlyEditPurview != 1" href="/sku/solution/add" style="color:#FFF;"
                  class="el-button el-button--primary" >添加</a>
              </el-col>
            </el-row>
            <el-row v-loading="loading">
              <el-col :span="24">
                <el-table :data="tableList.items" style=" width:99.9%;" @selection-change="change">
                  <el-table-column type="selection" width="50" :selectable='checkboxInit'>
                  </el-table-column>
                  <el-table-column width="50">
                    <template slot-scope="scope">
                      <img :src="scope.row.mThumbImg || scope.row.mCoverImg" style="width:40px" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="name" label="名称" width="240">
                    <template slot-scope="scope">
                      <div class="name">
                        <span>{{scope.row.name}}</span>
                        <i v-if='scope.row.isCopy'>复</i>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="buName" label="BU" width="80">
                  </el-table-column>
                  <el-table-column prop="isUse" label="上下架" width="80">
                    <template slot-scope="scope">
                      <span v-if="scope.row.isUse == 1">上架</span>
                      <span v-else style="color:#900">下架</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="alias" label="链接地址" style="white-space: nowrap;width='50' ">
                    <template slot-scope="scope" v-if="scope.row.id">
                      <a :href="'<%- view_url %>/sku/'+ scope.row.alias +'/'+scope.row.id" target="_blank">链接</a>
                    </template>
                  </el-table-column>
                  <el-table-column prop="service"  label="所属服务" width="120">
                    <!-- <template slot-scope="scope">
                      <el-tooltip placement="top" effect="light" popper-class="spopper">
                        <div slot="content">
                          <h3 style="margin:0;padding-bottom:10px">所属服务</h3>
                          <el-col :span="24" v-for="(item, index) in scope.row.serviceList" :key='index'
                            style="padding:5px 0">{{item}}</el-col>
                        </div>
                        <span
                          style="overflow:hidden;white-space: nowrap;text-overflow: ellipsis;">{{scope.row.serviceList[0]}}</span>
                      </el-tooltip>
                    </template> -->
                  </el-table-column>
                  <el-table-column prop="industry"  label="关联行业" width="120">
                    <!-- <template slot-scope="scope">
                      <el-tooltip placement="top" effect="light" popper-class="spopper">
                        <div slot="content">
                          <h3 style="margin:0;padding-bottom:10px">关联行业</h3>
                          <el-col :span="24" v-for="(item, index) in scope.row.tradeList" :key='index'
                            style="padding:5px 0">{{item}}</el-col>
                        </div>
                        <span
                          style="overflow:hidden;white-space: nowrap;text-overflow: ellipsis;">{{scope.row.tradeList[0]}}</span>
                      </el-tooltip>
                    </template> -->
                  </el-table-column>
                  <el-table-column prop="isBuy" label="订购" width="48">
                    <template slot-scope="scope">
                      <span v-if="scope.row.isBuy == 1">是</span>
                      <span v-else style="color:#900">否</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="lastModify" label="最后编辑">
                  </el-table-column>
                  <el-table-column prop="publishTime" label="最后编辑时间" width="106">
                  </el-table-column>
                  <el-table-column label="UV" prop="uv"></el-table-column>
                  <el-table-column label="PV" prop="pv"></el-table-column>
              </el-table-column>
                  <el-table-column label="操作" width="160" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.row.is_use == 1">
                        <a v-if="onlyEditPurview != 1" class="el-button el-button--primary el-button--mini"
                          style="color:#fff" :href="'/sku/solution/' + scope.row.id">编辑</a>
                        <el-button v-if="onlyEditPurview != 1" type="danger" size="mini"
                          @click='_del(scope.row.id,scope.row.name)'>删除</el-button>
                      </div>
                      <div v-else>
                        <div v-if="scope.row.tran_result==null">
                          <el-button-group style="margin-bottom:10px;" v-if="onlyEditPurview != 1">
                            <el-button v-if="hasEditPurview==1 && hasPubviewPurview==0" type="danger" size="mini"
                              @click='_sendcheckDialog(scope.row)'>送审</el-button>
                            </span>

                            <el-button-group v-if="onlyEditPurview != 1">
                              <a v-if="onlyEditPurview != 1" class="el-button el-button--primary el-button--mini"
                                style="color:#fff" :href="'/sku/solution/' + scope.row.id">编辑</a>
                              <el-button v-if="onlyEditPurview != 1" type="danger" size="mini"
                                @click='_del(scope.row.id,scope.row.name)'>删除</el-button>
                            </el-button-group>
                        </div>
                        <div v-else-if="scope.row.tran_result.tran_status==0">
                          <a v-if="onlyEditPurview != 1" class="el-button el-button--primary el-button--mini"
                            style="color:#fff" :href="'/sku/solution/' + scope.row.id">编辑</a>
                          <el-button type="danger" size="mini">送审中</el-button>
                        </div>

                        <div v-else-if="scope.row.tran_result.tran_status==10">
                          <a v-if="onlyEditPurview != 1" class="el-button el-button--primary el-button--mini"
                            style="color:#fff" :href="'/sku/solution/' + scope.row.id">编辑</a>
                          <el-button v-if="onlyEditPurview != 1" type="danger" size="mini"
                            @click='_del(scope.row.id,scope.row.name)'>删除</el-button>
                          </el-button-group>
                        </div>

                        <div v-else>
                          <el-button-group style="margin-bottom:10px;" v-if="onlyEditPurview != 1">
                            <el-button v-if="hasEditPurview==1 && hasPubviewPurview==0" type="danger" size="mini"
                              @click='_sendcheckDialog(scope.row)'>送审</el-button>
                            </span>

                            <el-button-group v-if="onlyEditPurview != 1">
                              <a v-if="onlyEditPurview != 1" class="el-button el-button--primary el-button--mini"
                                style="color:#fff" :href="'/sku/service/' + scope.row.id">编辑</a>
                              <el-button v-if="onlyEditPurview != 1" type="danger" size="mini"
                                @click='_del(scope.row.id,scope.row.name)'>删除</el-button>
                            </el-button-group>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination @current-change="handleCurrentChange" background 
                  :page-size="10" layout="total, prev, pager, next" :total="tableList.totalNum"
                  style="padding:10px 0;text-align: right;">
                </el-pagination>
              </el-col>
            </el-row>
            <el-dialog title="送审提示" :visible.sync="dialogVisible" width="30%">
              <div>您正在送审</div>
              <h3 style="text-indent: 20px;">{{currentNewsTitle}}</h3>
              <div>请选择审批用户分组：</div>
              <el-select v-model="selectGroup" placeholder="请选择">
                <el-option v-for="item in checkGroup" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>

              <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="_sendcheck()">确 定</el-button>
              </span>
            </el-dialog>
          </el-main>
      </el-container>
      <el-dialog title="新增分类" :visible.sync="dialogFormVisible">
        <el-form :model="form">
          <el-form-item label="分类名称" label-width="120px">
            <el-input v-model="form.name" auto-complete="off" style="width:360px"></el-input>
          </el-form-item>
          <el-form-item label="英文/别名" label-width="120px">
            <el-input v-model="form.alias" auto-complete="off" style="width:360px"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="addCata()">确 定</el-button>
        </div>
      </el-dialog>
      <!-- <el-dialog title="复制SKU" :visible.sync="copyDialog.visibale" width="600px">
        <el-form :model="form" label-width="80px" :rules="rules">
          <el-form-item label="名称">
            <el-input v-model='copyDialog.name' placeholder="请输入名称..."></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-checkbox v-model='copyDialog.value.tradeList'>复制所属行业</el-checkbox>
          </el-form-item>
          <el-form-item label="">
            <el-checkbox v-model='copyDialog.value.serviceList'>复制所属服务</el-checkbox>
          </el-form-item>
          <el-form-item label="">
            <el-checkbox v-model='copyDialog.value.bu_id'>复制BU</el-checkbox>
          </el-form-item>
          <el-form-item label="">
            <el-checkbox v-model='copyDialog.value.is_use'>复制上下架</el-checkbox>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="copyDialog.visibale = false">取 消</el-button>
          <el-button type="primary" @click='copy'>确 定</el-button>
        </span>
      </el-dialog> -->
  </el-container>

  <script>
    var main = new Vue({
      el: '#Main',
      data: {
        tradeIds: '<%- tradeIds %>',
        tableList: {
          items: [],
          totalNum: 0,
        },
        form: {
          service: '',
          trade: '',
          is_use: '',
          is_buy: '',
          name: '',
          page: 1,
          bu_id: '',
          qryDate: []
        },
        loading: false,
        dialogFormVisible: false,
        tradeList: <%- tradeList %>,
        serviceList: <%- serviceList %>,
        check: true,
        checkIds: [],
        selectGroup: null,
        currentNewsTitle: '',
        currentId: 0,
        dialogVisible: false,
        checkGroup: [],
        hasCheckedPurview: <%- hasCheckedPurview %>,
        hasPubviewPurview: <%- hasPubviewPurview %>,
        hasEditPurview: <%- hasEditPurview %>,
        onlyEditPurview: <%- onlyEditPurview %>,
        buList: [],
        copyDialog: {
          visibale: false,
          name: '',
          value: {
            serviceList: false,
            tradeList: false,
            bu_id: false,
            is_use: false
          }
        }, // 复制弹窗
        copyRow: {},
        buIds: <%- buIds || '' %>
      },
      methods: {
        checkboxInit(row, index) {
          //如果正在审批中不能删除
          return (row.tran_result && row.tran_result.tran_status == 0) ? 0 : 1;
        },
        _sendcheck(row) {
          var that = this;
          that.loading = true;
          var objData = { data_type: 'sku_solution', data_id: that.currentId, data_name: that.currentNewsTitle, receive_groupid: that.selectGroup };

          if (!this.selectGroup) {
            that.loading = false;
            this.$message.error('请选择审批用户分组');
            return;
          }
          axios.post('/checkflow/apply', objData)
            .then(function (result) {
              if (result.data.success) {
                that.loading = false;
                that.dialogVisible = false;
                if (result.data.success) {
                  that.$message.success('送审成功');
                  that._filter();
                } else {
                  if (result.data.needReLogin == 1) {
                    that.$alert(result.data.msg, '提示', {
                      confirmButtonText: '确定', callback: action => {
                        window.location.href = result.data.loginUrl;
                      }
                    });
                  }
                  else {
                    that.$alert(result.data.msg, '提示', {
                      confirmButtonText: '确定', callback: action => {
                        window.location.reload();
                      }
                    });
                  }
                }
              }
            });
        },
        _sendcheckDialog(row) {
          var that = this;
          that.loading = true;
          that.currentId = 0;
          that.selectGroup = null;

          axios.post('/role/getRoleList', { type: 'sku_solution', id: row.id })
            .then(function (result) {
              if (result.data.success) {
                that.loading = false;
                that.dialogVisible = true;
                that.currentId = row.id;
                that.currentNewsTitle = row.name;
                that.checkGroup = result.data.data;
              }
              else {
                that.$message.error('加载错误');
              }
            });
        },
        handleCurrentChange(v) {
          this.form.page = v;
          this._filter()
        },
        _edit(id) {
        },
        _del(id, name) {
          var that = this;
          this.$confirm('是否删除' + name + '？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            that.loading = true;
                  <% if (bigType == 'sku') { %>
              url = '/sku/service/delete';
                  <% } else { %>
              url = '/sku/solution/delete';
                  <% } %>
              axios.post(url, { id: id, _csrf: '<%- csrf %>' })
                .then(function (result) {
                  if (result.data.success) {
                    that._default();
                    window.location.reload();
                  }
                });
          }).catch(e => {
            return e;
          });
        },
        _delMulti() {
          var that = this;
          this.$confirm('是否删除已选择的SKU？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            that.loading = true;
                  <% if (bigType == 'sku') { %>
              url = '/sku/service/delete';
                  <% } else { %>
              url = '/sku/solution/delete';
                  <% } %>
              axios.post(url, { id: that.checkIds, _csrf: '<%- csrf %>' })
                .then(function (result) {
                  if (result.data.success) {
                    that._default();
                    window.location.reload();
                  }
                  else {
                    if (result.data.needReLogin == 1) {
                      that.$alert(result.data.msg, '提示', {
                        confirmButtonText: '确定', callback: action => {
                          window.location.href = result.data.loginUrl;
                        }
                      });
                    }
                    else {
                      that.$alert(result.data.msg, '提示', {
                        confirmButtonText: '确定', callback: action => {
                          window.location.reload();
                        }
                      });
                    }
                  }
                });
          }).catch(e => {
            return e;
          });
        },
        _filter() {
          console.log(this.form)
          const params = {
            name: this.form.name,
            isUse: this.form.is_use ? 1 : this.form.is_use === '' ? '' : 0,
            isBuy: this.form.is_buy ? 1 : this.form.is_buy === '' ? '' : 0,
            buId: this.form.bu_id,
            catalogId: this.form.type,
            catalogIds: [],
            type: 2, // 1、 服务，2、解决方案
            // gmtCreate: this.form.create_time,
            qryDate:  '',
            ids: this.tradeIds,
            buIds: this.buIds,
            pageNum: this.form.page,
            pageRow: 10,
          }
          if (this.form.qryDate) params.qryDate = this.form.qryDate.length ? `${this.form.qryDate[0]} 00:00:00/${this.form.qryDate[1]} 23:59:59` : ''
          if (this.form.service) params.catalogIds.push(this.form.service)
          if (this.form.trade) params.catalogIds.push(this.form.trade)

          const that = this
          axios.post('/sku/list/qry', params)
            .then(function (result) {
              
              that.loading = false;
              that.tableList = result.data.data;
            });
        },
        change(val) {
          var that = this;
          that.checkIds = [];
          val.forEach(function (item, i) {
            that.checkIds.push(item.id);
          });

          if (that.checkIds.length == 0) {
            that.check = true;
          } else {
            that.check = false;
          }
        },
        _default() {
          this.form = {
            service: '',
            trade: '',
            is_use: '',
            is_buy: '',
            name: '',
            page: 1,
          }
          // this._filter()
        },
        // 获取bu列表
        qryBuList() {
          var that = this;
          axios.post('/setting/buManage/qry', {}).
            then(function (result) {
              if (result.data.resultCode === '0') {
                that.buList = result.data.data.items
              } else {
                that.$message.error(result.data.resultMsg);
              }
            });
        },
        // 复制
        handleCopy(row) {
          this.copyDialog.visibale = true
          this.copyRow = row
          this.copyDialog.name = `复制 - ${this.copyRow.name}`
        },
        copy() {
          var loading = this.$loading({
            lock: true,
            text: '复制中，请等待...',
            spinner: 'el-icon-loading',
            background: 'rgba(255, 255, 255, 0.7)'
          });
          axios.post('/sku/getSkuDetail', { id: this.copyRow.id })
            .then((res) => {
              
              if (res.data.success) {
                const cloneForm = JSON.parse(JSON.stringify(res.data.data))
                cloneForm.catalogTrade = []
                cloneForm.catalogService = []
                cloneForm.recommends = []
                cloneForm.skuCases = []
                cloneForm.skuResource = []
                cloneForm.skuFloor = []
                cloneForm.name = this.copyDialog.name;
                Object.keys(this.copyDialog.value).forEach(v1 => {
                  // 没有勾选的选项重置为空值
                  if (!this.copyDialog.value[v1]) {
                    if (v1 === 'serviceList' || v1 === 'tradeList') {
                      cloneForm[v1] = []
                    } else if (v1 === 'is_use') {
                      cloneForm[v1] = false
                    } else {
                      cloneForm.bu_id = 0
                    }
                  }
                })
                delete cloneForm.id
                // 新增sku
                let params = Object.assign(this.copyRow, cloneForm)
                params.name = this.copyDialog.name
                axios.post('/sku/solution/new', params)
                  .then((result) => {
                    if (result.data.success) {
                      window.location.href = `/sku/solution/${result.data.data.id}`
                    } else {
                      loading.close();
                      this.$message.error(result.data.msg)
                    }
                  });
              } else {
                loading.close();
                this.$message.error(result.data.msg)
              }
            });

        },
        // 导出数据excle
        handleExport() {
          console.log(this.form)
          // todo
          const params = {
            name: this.form.name,
            isUse: this.form.is_use ? 1 : this.form.is_use === '' ? '' : 0,
            isBuy: this.form.is_buy ? 1 : this.form.is_buy === '' ? '' : 0,
            buId: this.form.bu_id,
            catalogId: this.form.type,
            catalogIds: [],
            type: 2, // 1、 服务，2、解决方案
            // gmtCreate: this.form.create_time,
            qryDate:  '',
            ids: this.tradeIds,
            buIds: this.buIds,
          }
          if (this.form.qryDate) params.qryDate = this.form.qryDate.length ? `${this.form.qryDate[0]} 00:00:00/${this.form.qryDate[1]} 23:59:59` : ''
          if (this.form.service) params.catalogIds.push(this.form.service)
          if (this.form.trade) params.catalogIds.push(this.form.trade)
          axios.post('/export/excle', params).then(res1 => {
            axios({
              method: 'post',
              url: res1.data.host + '/ticMall/business/api.v1.mgmt/sku/exp',
              data: params,
              headers: {
                'Content-Type': 'application/json',
                pid: res1.data.pid,
                pcode: res1.data.pcode,
                timestamp: res1.data.timestamp,
                sign: res1.data.sign,
                frontUrl: window.location.origin,
              },
              responseType: "blob",
            }).then(res => {
              let data = res.data;
              let filename = ''
              //从response的headers中获取filename, 后端response.setHeader("Content-disposition", "attachment; filename=xxxx.docx") 设置的文件名;
              var contentDisposition = res.headers["content-disposition"];
              if (contentDisposition) {
                var patt = new RegExp("filename=([^;]+\\.[^\\.;]+);*");
                var result = patt.exec(contentDisposition);
                filename = result ? decodeURIComponent(result[1]) : filename;
              }
              var blob = new Blob([res.data], {
                type: "application/actet-stream;charset=utf-8",
              });
              var downloadElement = document.createElement("a");
              var href = window.URL.createObjectURL(blob); //创建下载的链接
              downloadElement.style.display = "none";
              downloadElement.href = href;
              downloadElement.download = decodeURI(filename).replace(
                /\+/g,
                " "
              );
              document.body.appendChild(downloadElement);
              downloadElement.click();
              setTimeout(() => {
                document.body.removeChild(downloadElement);
                window.URL.revokeObjectURL(href);
              }, 1000);
            })
          })
        }
      },
      mounted() {
        this.$nextTick(function () {
          document.getElementById('preLoading').style.display = 'none';
          this.qryBuList()
          this._filter()
        });
      }
    });
  </script>

  <style>
    .name {
      position: relative;
    }

    .name i {
      position: absolute;
      right: 0;
      top: 0;
      color: #fff;
      font-size: 12px;
      background: #F56C6C;
      display: block;
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      border-radius: 50%;
      font-style: normal;
    }
  </style>
  <% include footer.html %>