'use strict';
const Controller = require('egg').Controller;
class CheckflowController extends Controller {
  //申请提交审核
  async apply() {
    const { ctx } = this;
    const params = ctx.request.body;
    const result = await ctx.service.checkflow.checkflowAdd(params);

    if (result.success) {
      ctx.body = { success: true, data: null, id: result.id };
    } else {
      ctx.body = { fail: true, data: null };
    }
  }
}

module.exports = CheckflowController;