<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        页面来源分析
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="20">
                                访问页面: {{form.url}}
                            </el-col>

                            <el-col :span="4" style="text-align: right;">
                                <a href="/analysis/url"><el-button type="primary" size="mini">返回</el-button></a>
                            </el-col>
                        </el-row>
                        <el-row v-loading="loading">
                            <el-table :data="tableList.list" stripe style="width: 100%">
                                <el-table-column prop="source" label="站内站外">
                                </el-table-column>
                                <el-table-column prop="from" label="来源">
                                </el-table-column>
                                <el-table-column prop="ip" label="IP">
                                </el-table-column>
                                <el-table-column prop="code" label="错误码">
                                </el-table-column>
                                <el-table-column prop="gmt_create" label="访问时间">
                                    <template slot-scope="scope">
                                        {{moment(scope.row.gmt_create)}}
                                    </template>
                                </el-table-column>
                            </el-table>
                            <el-pagination @current-change="handleCurrentChange" background :current-page.sync="form.page" :page-size="form.limit"
                                layout="total, prev, pager, next" :total="tableList.total" style="padding:10px 0;text-align: right;">
                            </el-pagination>
                        </el-row>
                    </el-main>
            </el-container>
    </el-container>
    <script>
    var main = new Vue({
        el: '#Main',
        data: {
            tableList: {
                list: <%- list %>,
                total: <%- total %>,
            },
            form: {
                url: '<%- url %>',
                page:1,
                limit: 10,
            },
            loading: false,
        },
        methods: {
            handleCurrentChange(r) {
                this.getList();
            },
            getList(){
                var that = this;
                
                that.loading = true;
                var params = this.form;
                params._csrf = '<%- csrf %>';
                axios.post('/analysis/entryListFrom', params)
                    .then(function(result) {
                        that.loading = false;
                        if (result.data.success) {
                            result.data.data.list.forEach(function(item){
                                item.from = decodeURI(item.from);
                            })
                            that.tableList = result.data.data;
                        }else{
                            that.$message.error('获取列表出错');
                        }
                    });
            },
            moment(date){
                return moment(date).format('YYYY-MM-DD HH:mm:ss');
            }
        },
        mounted(){
            this.$nextTick(function(){
                document.getElementById('preLoading').style.display = 'none';
            });
        }
    });
    </script>
    <% include footer.html %>