'use strict';

const Service = require('egg').Service;
const moment = require('moment');

class OtherService extends Service {
    async getList(params) {
        const { app } = this;
        const page = params.page || 1,
            limit = params.limit || 10;
        const total = await app.mysql.count('other', { is_delete: 0 })
        const list = await app.mysql.select('other', {
            columns: ['id', 'title', 'alias'],
            where: { is_delete: 0 },
            orders: [['gmt_modify', 'desc']],
            limit: limit,
            offset: (page - 1) * limit,
        });

        return { list, total };
    }

    async getDetail(id) {
        const { app } = this;
        const detail = await app.mysql.get('other', { id: id });

        if (detail.content) {
            detail.content = detail.content.replace(/\n/g, '').replace(/\.\.\/\.\.\/static/g, '/static').replace(/\.\.\/static/g, '/static');
        }

        return { detail }
    }

    async getDetailByAlias(id) {
        const { app } = this;
        const detail = await app.mysql.get('other', { alias: id });

        return { detail }
    }

    async otherEdit(params) {
        const { app } = this;
        const gmt_create = await moment().format('YYYY-MM-DD HH:mm:ss');
        let row = {
            id: params.id,
            title: params.title,
            alias: params.alias,
            content: params.content,
            banner_img: params.banner_img,
            m_banner_img: params.m_banner_img,
            banner_text: params.banner_text,
            banner_description: params.banner_description,
            seo_text: params.seo_text,
            page_title: params.page_title,
            page_keywords: params.page_keywords,
            page_description: params.page_description,
            mpage_title: params.mpage_title,
            mpage_keywords: params.mpage_keywords,
            mpage_description: params.mpage_description
        };

        let result = '', msg = '';
        if (params.id == 0) {
            row.gmt_create = gmt_create;
            const check_a = await app.mysql.get('other', { title: params.title, is_delete: 0 });
            const check_b = await app.mysql.get('other', { alias: params.alias, is_delete: 0 });

            if (check_a) {
                msg = '已存在相同的名称';
                return { msg };
            } else if (check_b) {
                msg = '已存在相同的英文名';
                return { msg };
            } else {
                result = await app.mysql.beginTransactionScope(async conn => {
                    const insertResult = await conn.insert('other', row);

                    await conn.insert('logs', {
                        user_name: this.ctx.session.userInfo.name,
                        other_id: insertResult.insertId,
                        log: '添加' + params.title,
                        model: '其他内容',
                        name: params.title
                    });

                    return { success: insertResult.insertId };
                }, this.ctx);

                // const success = result.insertId;
                return { success: result.success };
            }

        } else {
            const check_a = await app.mysql.query('SELECT id FROM other WHERE title="' + params.title + '" AND is_delete=0 AND id!=' + params.id);
            const check_b = await app.mysql.query('SELECT id FROM other WHERE alias="' + params.alias + '" AND is_delete=0 AND id!=' + params.id);

            const orgInfo = await app.mysql.get('other', { id: params.id });

            let changeValue = [];
            if (orgInfo.title != params.title) {
                changeValue.push('修改基本属性-名称');
            }

            if (orgInfo.alias != params.alias) {
                changeValue.push('修改基本属性-英文名称');
            }

            if (orgInfo.content != params.content) {
                changeValue.push('修改内容-内容');
            }

            if (orgInfo.banner_img != params.banner_img) {
                changeValue.push('修改内容-Banner图片');
            }

            if (orgInfo.m_banner_img != params.m_banner_img) {
                changeValue.push('修改内容-移动端Banner图片');
            }

            if (orgInfo.banner_text != params.banner_text) {
                changeValue.push('修改内容-Banner标题');
            }

            if (orgInfo.banner_description != params.banner_description) {
                changeValue.push('修改内容-Banner文案');
            }

            if (orgInfo.seo_text != params.seo_text) {
                changeValue.push('修改内容-关键字');
            }

            if (params.page_title != orgInfo.page_title) {
                changeValue.push('修改SEO设置-Title');
            }

            if (params.page_keywords != orgInfo.page_keywords) {
                changeValue.push('修改SEO设置-Keywords');
            }

            if (params.page_description != orgInfo.page_description) {
                changeValue.push('修改SEO设置-Description');
            }

            if (params.mpage_title != orgInfo.mpage_title) {
                changeValue.push('修改移动端SEO设置-Title');
            }

            if (params.mpage_keywords != orgInfo.mpage_keywords) {
                changeValue.push('修改移动端SEO设置-Keywords');
            }

            if (params.mpage_escription != orgInfo.mpage_escription) {
                changeValue.push('修改移动端SEO设置-Description');
            }

            if (check_a.length > 0) {
                msg = '已存在相同的名称';
                return { msg };
            } else if (check_b.length > 0) {
                msg = '已存在相同的英文名';
                return { msg };
            } else {
                result = await app.mysql.beginTransactionScope(async conn => {
                    await conn.update('other', row);

                    await conn.insert('logs', {
                        user_name: this.ctx.session.userInfo.name,
                        other_id: params.id,
                        log: changeValue.join(','),
                        model: '其他内容',
                        name: params.title
                    });

                    return { success: true };
                }, this.ctx);

                if (result.success && row.alias === 'aboutSGS') {
                    const cndParams = {
                        fileUrls: [params.pageLink]
                    }
                    await this.ctx.service.external.CDNRefresh(cndParams);
                }
                return { success: result.success };
            }
        }
    }

    async otherDelete(id, pageLink) {
        const { app } = this;
        const row = {
            id: id,
            is_delete: 1,
        }

        const result = await app.mysql.beginTransactionScope(async conn => {
            await conn.update('other', row);

            const info = await conn.get('other', { id: id });
            await conn.insert('logs', {
                user_name: this.ctx.session.userInfo.name,
                other_id: row.id,
                log: '删除' + info.title,
                model: '其他内容',
                name: info.title
            });

            return { success: true };
        }, this.ctx);

        // if (result.success) {
        //     const cndParams = {
        //         fileUrls: [pageLink]
        //     }
        //     await this.ctx.service.external.CDNRefresh(cndParams);
        // }
        return { success: result.success };
    }
}

module.exports = OtherService;
