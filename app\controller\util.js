async function getHost(env, ctx) {
  let host = ''
  if (env === 'prod' || env === 'prodGray') {
    host = 'https://ticgate.sgs.net';
  } else if (env === 'UAT') {
    host = 'https://ticgateuat.sgs.net';
  } else if (env === 'gray') {
    host = 'https://ticgateuat.sgs.net';
  } else if (env === 'dev') {
    host = 'https://ticgatedev.sgs.net';
  } else if (env === 'test') {
    host = 'https://ticgatetest.sgs.net';
  } else if (env === 'local') {
    //host = 'https://ticgateuat.sgs.net';
    //host = 'https://ticgatetest.sgs.net';
    host = 'http://localhost:8001';
  } else {
    host = 'https://ticgateuat.sgs.net';
  }
  return host
}

module.exports = {
  getHost,
}
