'use strict';

const Service = require('egg').Service;
const moment = require('moment');
const request = require('request');
const querystring = require('querystring');
const crypto = require('crypto');
const _siteInfo = require('../../config/info').siteInfo;
const axios = require('axios');
const solrUrl = _siteInfo.solrUrl,
  solrPstr = _siteInfo.solrPstr,
  solrPid = _siteInfo.solrPid;
const { env } = require('../../config/info').siteInfo;

class AdminService extends Service {


  //获取列表
  async getList(params) {
    if (!params) {
      params = {};
    }

    const {
      app
    } = this;
    let query = ['1=1'];

    if (params && params.name) {
      query.push('name LIKE "%' + params.name + '%"');
    }

    if (params && params.role_id) {
      //数据库存储格式如下：[1,2]

      if (params.role_id != '-1') //-1表示无分组
      {

        var role_id = "," + params.role_id + ",";
        query.push("CONCAT(',',REPLACE(REPLACE(roleid_json,'[',''),']',''),',') like '%" + role_id + "%'");
      } else {
        var role_id = "," + params.role_id + ",";
        query.push("(roleid_json='' or roleid_json is null or roleid_json='[]')");
      }
    }

    if (params && params.type) {
      query.push('type=' + params.type);
    }

    if (params && params.is_delete != null) {
      query.push('is_delete=' + params.is_delete);
    }

    let page = params.page || 1,
      limit = params.limit || 10;
    let start = (page - 1) * limit;

    //id，用户名称，用户创建时间，用户分组名称，分组创建时间，分组状态
    const total = await app.mysql.query('SELECT id from admin  WHERE ' + query.join(' AND '));

    const list = await app.mysql.query('SELECT admin.id,admin.name,admin.gmt_create,admin.roleid_json,admin.type,admin.delete_time from admin admin where ' + query.join(' AND ') + ' order by id asc LIMIT ' + start + ',' + limit);

    for (let item of list) {

      if (item.roleid_json == null) {
        item.roleList = [];
      } else {
        let roleJsonObj = JSON.parse(item.roleid_json);
        if (roleJsonObj.length == 0) {
          item.roleList = [];
          continue;
        }
        let role_ids = [];
        for (var i = 0; i < roleJsonObj.length; i++) {
          role_ids.push(roleJsonObj[i]);
        }

        let roleList = await this.app.mysql.select('role', {
          where: {
            id: role_ids
          }
        });
        item.roleList = [];

        for (var i = 0; i < roleList.length; i++) {

          var roleItem = roleList[i];
          var roleObj = {
            "id": roleItem.id,
            "name": roleItem.name,
            "is_effect": roleItem.is_effect,
            "role_gmt_create": moment(roleItem.gmt_create).format('YYYY-MM-DD HH:mm:ss')
          };

          item.roleList.push(roleObj);
        }

      }
    }


    return {
      list: list,
      total: total.length,
      page: page,
      limit: limit
    }
  }

  async login(params) {
    const {
      app
    } = this;

    const detail = await app.mysql.get('admin', {
      name: params.name
    });

    if (detail == null) {
      return null;
    }
    if (params.deleteflag == -1 && detail.is_delete == 1) {
      return -1;
    } else if (detail.is_delete == 1) {
      return null;
    }
    /*
    if(detail.name!="admin" && detail.roleid_json!=null)
    {
      var role_id=JSON.parse(detail.roleid_json);
      if(role_id==null || role_id.length==0)
      {
        return null;
      }
    	
      const total = await app.mysql.query('SELECT id from role  WHERE id in (' + role_id.join(',')+') and is_delete=0 and is_effect=1');
      if(total.length==0)
      {
        return null;
      }
      return detail;
    }
    */
    if (detail.type == 1) {
      let password = crypto.createHash('md5').update(params.pwd).digest('hex');
      if (password == detail.pwd) {
        return detail;
      }
    } else {
      return detail;
    }
    return null;
  }

  async getDetail(id) {
    const {
      app
    } = this;

    const detail = await app.mysql.get('admin', {
      id: id
    });

    if (detail != null) {
      if (detail.roleid_json == null) {
        detail.roleid_json = [];
      }
    }
    return detail;
  }

  async getByName(name) {
    const {
      app
    } = this;

    const detail = await app.mysql.get('admin', {
      name: name
    });
    return detail;
  }

  async checkNameIsExists(name, id) {
    const {
      app
    } = this;

    var query = ['is_delete=0'];
    query.push("name='" + name + "'");
    if (id != null && id > 0) {
      query.push("id!=" + id);
    }
    const total = await app.mysql.query('SELECT id from admin  WHERE ' + query.join(' AND '));
    return total.length > 0;
  }

  /*关于数据权限
  1.根据用户id获取行业和服务的列表
  2.在保存数据时判断提交的行业和服务数据是否有权限
  3.查看编辑数据的时候判断用户是否能看这条数据
  */
  //检测用户服务是否允许
  async checkUserServiceIsAllow(services) {
    const {
      app
    } = this;

    if (this.ctx.session.userInfo.type == 1) {
      return true;
    }
    if (services == null || services.length == 0) {
      return true;
    }

    var oldservices = await this.ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');

    if (oldservices.edit == 0) {
      return false;
    }

    for (let item of services) {
      if (oldservices.ids.indexOf(item) == -1) {
        return false;
      }
    }
    return true;
  }

  //检测用户行业是否允许
  async checkUserTradeIsAllow(trades) {
    const {
      app
    } = this;

    if (this.ctx.session.userInfo.type == 1) {
      return true;
    }
    if (trades == null || trades.length == 0) {
      return true;
    }

    var oldcatalog = await this.ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');

    if (oldcatalog.edit == 0) {
      return false;
    }

    for (let item of trades) {
      if (oldcatalog.ids.indexOf(item) == -1) {
        return false;
      }
    }
    return true;
  }

  //检查数据是否能操作
  //type:news,case,resource,service,solution

  async checkDataCanOperator(type, id) {
    const {
      app
    } = this;

    if (this.ctx.session.userInfo.type == 1) {
      return true;
    }
    id = parseInt(id);

    var serviceList = [];
    var tradeList = [];
    if (type == 'news' || type == 'case' || type == 'resource') {
      let columnkey = {
        'news': 'news_id',
        'case': 'case_id',
        'resource': 'resource_id'
      };
      let sql = 'select * from catalog_relation where ' + columnkey[type] + '=' + id;

      const list = await app.mysql.query(sql);

      for (let index in list) {
        var item = list[index];
        if (item.catalog_type == 1) //行业
        {
          tradeList.push(item.catalog_id);
        } else if (item.catalog_type == 2) //服务
        {
          serviceList.push(item.catalog_id);
        }
      }

    } else if (type == "solution") {
      let sql = 'select * from catalog_relation where sku_id=' + id + '  and catalog_type is not null';
      const list = await app.mysql.query(sql);

      for (let index in list) {
        var item = list[index];
        if (item.catalog_type == 1) //行业
        {
          tradeList.push(item.catalog_id);
        } else if (item.catalog_type == 2) //服务
        {
          serviceList.push(item.catalog_id);
        }
      }
    } else if (type == "sku") {
      let sql = 'select catalog.id as catalog_id,catalog.parent_id as catalog_type  from catalog_relation join catalog on  catalog_relation.catalog_id=catalog.id where catalog_relation.sku_id=' + id + ' and catalog_relation.catalog_type is null;';
      const list = await app.mysql.query(sql);

      for (let index in list) {
        var item = list[index];
        if (item.catalog_type == 1) //行业
        {
          tradeList.push(item.catalog_id);
        } else if (item.catalog_type == 2) //服务
        {
          serviceList.push(item.catalog_id);
        }
      }
    }

    let objService = await this.ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
    let objTrade = await this.ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');

    if (objService.edit == 1 || objTrade.edit == 1) //有编辑权限
    {

      var trade_flag = await this.ctx.service.admin.checkUserTradeIsAllow(tradeList);

      var service_flag = await this.ctx.service.admin.checkUserServiceIsAllow(serviceList);

      return trade_flag && service_flag;
    } else {
      return false;
    }
  }

  async adminAdd(params) {


    const {
      app
    } = this;

    const gmt_create = await moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
    const last_modify = this.ctx.session.userInfo.name;

    var admin_create_id = this.ctx.session.userInfo.id;
    var roleid_json = JSON.stringify(params.role_id);


    if (!params.type) {
      params.type = 2; //默认新增为普通用户，普通用户不需要密码
    }

    const row = {
      name: params.name,
      type: params.type,
      real_name: '',
      roleid_json: roleid_json,
      admin_modify_id: admin_create_id,
      is_delete: 0
    }


    if (params.type == 1 && !params.id) {
      params.pwd = crypto.createHash('md5').update(params.pwd).digest("hex");
      row.pwd = params.pwd;
    }



    let result = '',
      sid = 0;
    let oldObjectData = [];
    let changeValue = [];

    var needUpdateApiData = false;
    if (params.id && params.id != 0) {

      sid = params.id;
      row.id = params.id;

      oldObjectData = await this.app.mysql.get('admin', {
        id: sid
      }); //原始对象

      if (params.name != oldObjectData.name) {
        changeValue.push('修改用户-名称');
      }

      if (params.type != oldObjectData.type) {
        changeValue.push('修改用户-类型');
      }

      if (params.pwd != oldObjectData.pwd) {
        changeValue.push('修改用户-密码');
      }


      if (params.real_name != oldObjectData.real_name) {
        changeValue.push('修改用户-姓名');
      }

      if (roleid_json != oldObjectData.roleid_json) {
        changeValue.push('修改用户-组');
        needUpdateApiData = true;
      }

      result = await app.mysql.beginTransactionScope(async conn => {
        await conn.update('admin', row);

        if (changeValue.length > 0) {
          await conn.insert('logs', {
            user_name: this.ctx.session.userInfo.name,
            admin_id: sid,
            log: changeValue.join(','),
            model: '用户',
            name: row.title
          });
        }

        return {
          success: true
        };
      }, this.ctx);

    } else {

      row.gmt_create = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
      row.admin_create_id = admin_create_id;


      result = await app.mysql.beginTransactionScope(async conn => {

        const insertResult = await conn.insert('admin', row);

        sid = insertResult.insertId;

        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          admin_id: sid,
          log: '创建' + row.name,
          model: '用户',
          name: row.name
        });

        return {
          success: true
        };
      }, this.ctx);
    }

    if (needUpdateApiData) {
      await this.service.admin.updateAdminCloudData(params.id);
    }
    return {
      success: result.success,
      id: sid
    };
  }

  async adminSavePassword(params) {
    const {
      app
    } = this;

    const gmt_create = await moment(new Date()).format('YYYY-MM-DD HH:mm:ss');

    var admin_create_id = this.ctx.session.userInfo.id;

    let password = crypto.createHash('md5').update(params.newpassword).digest('hex');

    const row = {
      id: params.id,
      pwd: password
    }

    let result = '',
      sid = 0;
    let oldObjectData = [];
    let changeValue = [];
    if (params.id && params.id != 0) {
      sid = params.id;
      oldObjectData = await this.app.mysql.select('admin', {
        where: {
          id: sid
        }
      }); //原始对象

      changeValue.push('修改用户-密码');

      result = await app.mysql.beginTransactionScope(async conn => {
        await conn.update('admin', row);

        if (changeValue.length > 0) {
          await conn.insert('logs', {
            user_name: this.ctx.session.userInfo.name,
            admin_id: sid,
            log: changeValue.join(','),
            model: '用户',
            name: oldObjectData.name
          });
        }

        return {
          success: true
        };
      }, this.ctx);

    }
    return {
      success: result.success,
      id: sid
    };
  }

  async adminDelete(id) {
    const {
      app
    } = this;
    let delete_time = await moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
    const result = await app.mysql.beginTransactionScope(async conn => {

      if (typeof id == 'object') {
        for (let item of id) {

          await conn.update('admin', {
            id: item,
            is_delete: 1,
            delete_time: delete_time
          });
          const info = await this.app.mysql.get('admin', {
            id: item
          });
          await conn.insert('logs', {
            user_name: this.ctx.session.userInfo.name,
            admin_id: item,
            log: '删除' + info.name,
            model: '用户中心',
            name: info.name
          });

        }
      } else {
        const info = await this.app.mysql.get('admin', {
          id: id
        });
        await conn.update('admin', {
          id: id,
          is_delete: 1,
          delete_time: delete_time
        });
        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          admin_id: id,
          log: '删除' + info.name,
          model: '用户中心',
          name: info.name
        });

      }

      return {
        success: true
      };
    }, this.ctx);


    if (typeof id == 'object') {

      for (let item of id) {

        await this.service.admin.updateAdminCloudData(item);
      }
    } else {
      await this.service.admin.updateAdminCloudData(id);
    }
    return {
      success: result.success
    };
  }

  async updateAdminCloudData(userid) {
    const {
      app
    } = this;


    const loginResult = await app.mysql.get('admin', {
      id: userid
    });

    var user_name = loginResult.name;

    var apidata = {};

    apidata.menu = await this.service.admin.getMenuPurviewFromDB(user_name);

    if (apidata.menu == null) {
      apidata.menu = [];
    }

    apidata.serviceData = await this.service.admin.getDataPurviewByServiceFromDB(user_name);
    apidata.tradeData = await this.service.admin.getDataPurviewByTradeFromDB(user_name);


    apidata.menu = JSON.stringify(apidata.menu);
    apidata.serviceData = JSON.stringify(apidata.serviceData);
    apidata.tradeData = JSON.stringify(apidata.tradeData);
    apidata.roleid_json = loginResult.roleid_json;

    apidata.userDelete = loginResult.is_delete;
    apidata.buIds = await this.service.admin.getDataPurviewByBuIdsFromDB(loginResult.roleid_json);
    // 保存分配配置的权限后，写入到缓存
    this.service.admin.setCache(apidata, user_name);
  }

  async adminActive(id) {
    const {
      app
    } = this;

    const result = await app.mysql.beginTransactionScope(async conn => {

      if (typeof id == 'object') {
        for (let item of id) {

          await conn.update('admin', {
            id: item,
            is_delete: 0
          });
          const info = await this.app.mysql.get('admin', {
            id: item
          });
          await conn.insert('logs', {
            user_name: this.ctx.session.userInfo.name,
            admin_id: item,
            log: '激活' + info.name,
            model: '用户中心',
            name: info.name
          });

        }
      } else {
        const info = await this.app.mysql.get('admin', {
          id: id
        });
        await conn.update('admin', {
          id: id,
          is_delete: 0
        });
        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          admin_id: id,
          log: '激活' + info.name,
          model: '用户中心',
          name: info.name
        });
      }

      return {
        success: true
      };
    }, this.ctx);

    // const success = result.affectedRows > 0;
    // 激活之后刷新redius用户数据
    await this.service.admin.updateAdminCloudData(id);
    return {
      success: result.success
    };
  }


  async getMenuPurviewFromDB(admName) {

    //这里需要改一下，如果是超级用户不用做处理
    const {
      app
    } = this;
    if (admName == "admin") {
      return null;
    }
    var query = [];

    const detail = await app.mysql.get('admin', {
      name: admName
    });

    if (detail.roleid_json == null) {
      return null;
    }

    detail.roleid_json = JSON.parse(detail.roleid_json);
    if (detail.roleid_json.length == 0) {
      return null;
    }

    const effectRoleIDs = await app.mysql.query('SELECT id from role  WHERE id in (' + detail.roleid_json.join(',') + ') and is_delete=0 and is_effect=1');

    if (effectRoleIDs.length == 0) {
      return null;
    }
    detail.roleid_json = [];

    for (let effectRoleIDItem in effectRoleIDs) {
      detail.roleid_json.push(effectRoleIDs[effectRoleIDItem].id);
    }

    let sql = "select id,function_value,role_id,concat(',',menu_id,',',ifnull((select parent_id from menu where id=role_menu_purview.menu_id),''),',') as ids from role_menu_purview where  role_id in (" + detail.roleid_json.join(',') + ')';


    let allPurview = await app.mysql.query(sql);

    sql = "select * from menu ";
    let menuResult = await app.mysql.query(sql);

    let adminMenu = {};
    for (let menuItem of menuResult) {
      for (let purviewItem of allPurview) {
        if (purviewItem.ids.indexOf(',' + menuItem.id + ',') >= 0 && (purviewItem.function_value.indexOf("开启") >= 0 || purviewItem.function_value.indexOf("编辑+归类") >= 0 || purviewItem.function_value.indexOf("查看+导出") >= 0)) {
          if (!adminMenu[menuItem.id]) adminMenu[menuItem.id] = [];
          adminMenu[menuItem.id].push(purviewItem.function_value);
        }
      }
    }
    return adminMenu;
  }

  async getMenuPurview(admName) {

    var result = await this.service.admin.getCache(admName);

    return result.data.menu;
  }

  async getMenuPurview2(admName, cmd) {
    var result = await this.service.admin.getCache(admName);

    var result = result.data.menu;
    var newresult = [];
    for (var itemIndex in result) {
      var item = result[itemIndex];

      if (item.toString().indexOf(cmd) >= 0) {
        newresult.push(itemIndex);
      }
    }

    return newresult;
  }

  async getDataPurviewByServiceFromDB(admName) {

    const {
      app
    } = this;

    var query = [];

    const detail = await app.mysql.get('admin', {
      name: admName
    });

    if (detail.roleid_json == null) {
      return {
        look: 0,
        edit: 0,
        publish: 0,
        checked: 0
      };
    }

    detail.roleid_json = JSON.parse(detail.roleid_json);
    if (detail.roleid_json.length == 0) {
      return {
        look: 0,
        edit: 0,
        publish: 0,
        checked: 0
      };
    }

    const effectRoleIDs = await app.mysql.query('SELECT id from role  WHERE id in (' + detail.roleid_json.join(',') + ') and is_delete=0 and is_effect=1');

    if (effectRoleIDs.length == 0) {
      return {
        look: 0,
        edit: 0,
        publish: 0,
        checked: 0
      };
    }

    detail.roleid_json = [];

    for (let effectRoleIDItem in effectRoleIDs) {
      detail.roleid_json.push(effectRoleIDs[effectRoleIDItem].id);
    }

    let sql = "select GROUP_CONCAT(replace(REPLACE(ifnull(ids,''),'[',''),']','')) as ids,GROUP_CONCAT(replace(REPLACE(ifnull(look,''),'[',''),']','')) as look,GROUP_CONCAT(replace(REPLACE(ifnull(edit,''),'[',''),']','')) as edit,GROUP_CONCAT(replace(REPLACE(ifnull(publish,''),'[',''),']','')) as publish,GROUP_CONCAT(replace(REPLACE(ifnull(checked,''),'[',''),']','')) as checked from  role_data_purview where rule_name in ('服务分类权限') and role_id in (" + detail.roleid_json.join(',') + ")";


    let allPurview = await app.mysql.query(sql);
    if (allPurview.length == 0) {
      return {
        look: 0,
        edit: 0,
        publish: 0,
        checked: 0
      };
    }
    let idsStr = '[]';
    if (allPurview[0]['ids']) {
      idsStr = '[' + allPurview[0]['ids'] + ']';
    }
    let ids = JSON.parse(idsStr);
    if (ids.length == 0) {
      return {
        look: 0,
        edit: 0,
        publish: 0,
        checked: 0
      };
    }
    let data = {};

    data.look = (',' + allPurview[0]['look'] + ',').indexOf(',1,') >= 0 ? 1 : 0;
    data.edit = (',' + allPurview[0]['edit'] + ',').indexOf(',1,') >= 0 ? 1 : 0;
    data.publish = (',' + allPurview[0]['publish'] + ',').indexOf(',1,') >= 0 ? 1 : 0;
    data.checked = (',' + allPurview[0]['checked'] + ',').indexOf(',1,') >= 0 ? 1 : 0;

    data.ids = ids;

    return data;
  }

  async getDataPurviewByTradeFromDB(admName) {
    const {
      app
    } = this;

    var query = [];

    const detail = await app.mysql.get('admin', {
      name: admName
    });

    if (detail.roleid_json == null) {
      return {
        look: 0,
        edit: 0,
        publish: 0,
        checked: 0
      };
    }

    detail.roleid_json = JSON.parse(detail.roleid_json);
    if (detail.roleid_json.length == 0) {
      return {
        look: 0,
        edit: 0,
        publish: 0,
        checked: 0
      };
    }

    const effectRoleIDs = await app.mysql.query('SELECT id from role  WHERE id in (' + detail.roleid_json.join(',') + ') and is_delete=0 and is_effect=1');

    if (effectRoleIDs.length == 0) {
      return {
        look: 0,
        edit: 0,
        publish: 0,
        checked: 0
      };
    }

    detail.roleid_json = [];

    for (let effectRoleIDItem in effectRoleIDs) {
      detail.roleid_json.push(effectRoleIDs[effectRoleIDItem].id);
    }

    let sql = "select GROUP_CONCAT(replace(REPLACE(ifnull(ids,''),'[',''),']','')) as ids,GROUP_CONCAT(replace(REPLACE(ifnull(look,''),'[',''),']','')) as look,GROUP_CONCAT(replace(REPLACE(ifnull(edit,''),'[',''),']','')) as edit,GROUP_CONCAT(replace(REPLACE(ifnull(publish,''),'[',''),']','')) as publish,GROUP_CONCAT(replace(REPLACE(ifnull(checked,''),'[',''),']','')) as checked from  role_data_purview where rule_name in ('行业分类权限') and role_id in (" + detail.roleid_json.join(',') + ")";


    let allPurview = await app.mysql.query(sql);
    if (allPurview.length == 0) {
      return {
        look: 0,
        edit: 0,
        publish: 0,
        checked: 0
      };
    }
    let idsStr = '[]';
    if (allPurview[0]['ids']) {
      idsStr = '[' + allPurview[0]['ids'] + ']';
    }
    // let idsStr='['+allPurview[0]['ids']+']';
    let ids = JSON.parse(idsStr);
    if (ids.length == 0) {
      return {
        look: 0,
        edit: 0,
        publish: 0,
        checked: 0
      };
    }
    let data = {};

    data.look = (',' + allPurview[0]['look'] + ',').indexOf(',1,') >= 0 ? 1 : 0;
    data.edit = (',' + allPurview[0]['edit'] + ',').indexOf(',1,') >= 0 ? 1 : 0;
    data.publish = (',' + allPurview[0]['publish'] + ',').indexOf(',1,') >= 0 ? 1 : 0;
    data.checked = (',' + allPurview[0]['checked'] + ',').indexOf(',1,') >= 0 ? 1 : 0;

    data.ids = ids;

    return data;
  }

  async getDataPurviewByService(admName) {

    const {
      app
    } = this;

    var data = await this.service.admin.getCache(admName);

    var json = data.data.serviceData;
    if (json.ids == null || json.ids[0] == null) {
      if (!json.ids) {
        json.ids = [];
      }
      json.ids[0] = -1; //构造一个不存在的id;不然拼接sql会错误
    }

    return json;

  }

  async getDataPurviewByTrade(admName) {
    const {
      app
    } = this;

    var data = await this.service.admin.getCache(admName);

    var json = data.data.tradeData;
    if (json.ids == null || json.ids[0] == null) {
      if (!json.ids) {
        json.ids = [];
      }
      json.ids[0] = -1; //构造一个不存在的id;不然拼接sql会错误
    }

    return json;
  }

  async getDataPurview(user_name, cmd) {
    if (cmd == "服务分类权限") {
      var result = await this.service.admin.getDataPurviewByService(user_name);
      return result;
    } else if (cmd == "行业分类权限") {
      var result = await this.service.admin.getDataPurviewByTrade(user_name);
      return result;
    }
  }

  // 根绝角色id查询bu数据集合
  async getDataPurviewByBuIdsFromDB(roles) {
    const {
      app
    } = this;
    let buIds = []
    const roleIds = roles && JSON.parse(roles).join(',')
    if (roleIds) {
      const roleData = await app.mysql.query(`select distinct bu_ids, role_id  from role_data_purview  where role_id IN (${roleIds})`)
      roleData.forEach(v => {
        console.log('v.bu_ids', v.bu_ids)
        if (v.bu_ids) {
          v.bu_ids.split(',').forEach(v1 => {
            console.log('v.bu_ids', v1)
            if (!buIds.includes(v1)) buIds.push(v1)
          })
        }
      })
    }
    return buIds;
  }


  async getServerEmail(admName) {
    const querystring = require('querystring');

    var result = await axios.post(_siteInfo.userEmailRequestApi, querystring.stringify({
      name: admName
    }), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }).then(res => {

      let total = res.data.total;
      if (total == '1') {
        return res.data.rows.email;
      }
      return null;

    }).catch(err => {
      return null;
    });

    return result;
  }

  async getEmail(admName) {
    // 获取内网的email
    let result = await axios.post(env[this.app.config.env].userInfoApi, {
      'regionAccount': admName
    }).then(res => {
      if (res.data.records) {
        return res.data.rows[0].email
      } else {
        return null
      }
    }).catch(err => {
      return null
    });
    // 获取外网的email
    let resultOut = await axios.post(_siteInfo.userInfoOutApi, {
      'name': admName
    }).then(res => {
      if (res.data.records) {
        const tempArr = res.data.rows.filter(item => {
          return item.name == admName
        })
        return tempArr.length ? tempArr[0].email : null
      } else {
        return null
      }
    }).catch(err => {
      return null
    });
    // 以内网为主
    return result ? result : resultOut
  }

  async sendFailedEmail(params) {
    const {
      app
    } = this;

    var timestamp1 = new Date().getTime();
    var partnerCode = 'pid.leads';
    var partnerValidatorCode = 'saUqq8AHsS7kQH8s';

    var data = {
      buType: 'ALL',
      projectName: 'TIC_CMS',
      businessType: '600002',
      mailType: '1',
      orderNo: '',
      sendMail: params.email, //<EMAIL>
      item: {
        title: params.title
      }
    };
    data = JSON.stringify(data);
    var sign = (partnerCode + partnerValidatorCode).toUpperCase();
    sign = crypto.createHash('md5').update(sign).digest('hex');
    sign = sign.toUpperCase();
    sign = data + sign + timestamp1;
    sign = crypto.createHash('md5').update(sign).digest('hex');
    var myheader = {
      'Content-Type': 'application/json',
      'Content-Length': data.length,
      'pid': partnerCode,
      'sign': sign,
      'timestamp': timestamp1
    };
    var result = await axios.post(env[this.app.config.env].userEmailSendApi, data, {
      headers: myheader
    }).then(res => {
      return res.data;

    }).catch(err => {
      return null;
    });
    return result;
  }

  async sendEmail(params) {
    const {
      app
    } = this;
    const querystring = require('querystring');
    var timestamp1 = new Date().getTime();
    var partnerCode = 'pid.leads';
    var partnerValidatorCode = 'saUqq8AHsS7kQH8s';
    var data = {
      buType: 'ALL',
      projectName: 'TIC_CMS',
      businessType: '600001',
      mailType: '1',
      orderNo: '',
      sendMail: params.email, //<EMAIL>
      item: {
        title: params.title
      }
    };
    data = JSON.stringify(data);
    var sign = (partnerCode + partnerValidatorCode).toUpperCase();
    sign = crypto.createHash('md5').update(sign).digest('hex');
    sign = sign.toUpperCase();
    sign = data + sign + timestamp1;
    sign = crypto.createHash('md5').update(sign).digest('hex');
    var myheader = {
      'Content-Type': 'application/json',
      'Content-Length': data.length,
      'pid': partnerCode,
      'sign': sign,
      'timestamp': timestamp1
    };
    var result = await axios.post(env[this.app.config.env].userEmailSendApi, data, {
      headers: myheader
    }).then(res => {
      return res.data;
    }).catch(err => {
      return null;
    });
    return result;
  }

  async setCache(apiData, user_name) {
    if (!user_name) {
      user_name = this.ctx.session.userInfo.name;
    }
    const {
      app
    } = this;
    var timestamp1 = new Date().getTime();
    var partnerCode = 'pid.leads';
    var partnerValidatorCode = 'saUqq8AHsS7kQH8s';
    var data = {
      account: user_name,
      system: 'TICCMS',
      privilege: apiData
    };

    data = JSON.stringify(data);
    var apirequestcontent = "【" + user_name + '】设置redis数据,请求参数是：\r\n' + data;
    var sign = (partnerCode + partnerValidatorCode).toUpperCase();
    sign = crypto.createHash('md5').update(sign).digest('hex');
    sign = sign.toUpperCase();
    sign = data + sign + timestamp1;
    sign = crypto.createHash('md5').update(sign).digest('hex');
    var myheader = {
      'Content-Type': 'application/json',
      'Content-Length': data.length,
      'pid': partnerCode,
      'sign': sign,
      'timestamp': timestamp1
    };
    var result = await axios.post(env[this.app.config.env].apiSetCacheApi, data, {
      headers: myheader
    }).then(res => {
      apirequestcontent += "\r\n返回结果是：" + JSON.stringify(res.data) + "\r\n";
      this.ctx.logger.error(apirequestcontent);
      return res.data;
    }).catch(err => {
      return null;
    });
    return result;
  }

  async getCache(user_name) {
    if (!user_name) {
      user_name = this.ctx.session.userInfo.name;
    }
    if (this.ctx.session.apiData && user_name == this.ctx.session.userInfo.name) {
      return this.ctx.session.apiData;
    }
    const {
      app
    } = this;
    var timestamp1 = new Date().getTime();
    var partnerCode = 'pid.leads';
    var partnerValidatorCode = 'saUqq8AHsS7kQH8s';
    var data = {
      account: user_name,
      system: 'TICCMS'
    };
    data = JSON.stringify(data);
    var apirequestcontent = "【" + user_name + '】获取redis数据,请求参数是：\r\n' + data;
    //MD5(body数据 +大写(MD5(大写(合作方编号+合作方验证码))) + timestamp)
    var sign = (partnerCode + partnerValidatorCode).toUpperCase();
    sign = crypto.createHash('md5').update(sign).digest('hex');
    sign = sign.toUpperCase();
    sign = data + sign + timestamp1;
    sign = crypto.createHash('md5').update(sign).digest('hex');
    var myheader = {
      'Content-Type': 'application/json',
      'pid': partnerCode,
      'sign': sign,
      'timestamp': timestamp1
    };
    var result = await axios.post(env[this.app.config.env].apiGetCacheApi, data, {
      headers: myheader
    }).then(res => {
      apirequestcontent += "\r\n返回结果是：" + JSON.stringify(res.data) + "\r\n";
      this.ctx.logger.error(apirequestcontent);
      this.ctx.session.apiData = res.data;
      this.ctx.session.buIds = res.data.data.buIds;
      return res.data;

    }).catch(err => {
      return null;
    });
    return result;
  }
}

module.exports = AdminService;