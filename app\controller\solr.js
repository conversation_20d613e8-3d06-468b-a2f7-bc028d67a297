'use strict';

const Controller = require('egg').Controller;
const moment = require('moment');
const request = require('request');
const crypto = require('crypto');
const _siteInfo = require('../../config/info').siteInfo;
const solrUrl = _siteInfo.solrUrl,
  solrPstr = _siteInfo.solrPstr,
  solrPid = _siteInfo.solrPid;
const { env } = require('../../config/info').siteInfo;

class SolrController extends Controller {
  async renew() {
    const { ctx } = this;
    const params = ctx.request.query;
    const type = params.type,
      page = params.page || 1,
      limit = params.limit || 100;

    let str = '<html><body>';
    str += '<p>类别: <a href="/solr/renew?type=sku&page=1">服务</a> | <a href="/solr/renew?type=solution&page=1">解决方案</a> | <a href="/solr/renew?type=news&page=1">新闻</a> | <a href="/solr/renew?type=case&page=1">案例</a> | <a href="/solr/renew?type=resource&page=1">资源</a> | <a href="/solr/renew?type=delete" style="color:#F00">同步删除数据</a></p>';
    if (type == 'sku') {
      str += '<h3>当前类别: 服务</h3>';
    } else if (type == 'solution') {
      str += '<h3>当前类别: 解决方案</h3>';
    } else if (type == 'news') {
      str += '<h3>当前类别: 新闻</h3>';
    } else if (type == 'case') {
      str += '<h3>当前类别: 案例</h3>';
    } else if (type == 'resource') {
      str += '<h3>当前类别: 资源</h3>';
    }

    let list = {};
    if (type == 'sku' || type == 'solution') {
      // list = await ctx.service.sku.getList({ bigType: type, page: page, limit: limit, is_use: true, is_delete: false });
      list = await ctx.service.sku.getList({ bigType: type, page: page, limit: limit });
      let options = {};
      for (let item of list.list) {
        if (item.is_use) {
          let detail = await ctx.service.sku.getInfo(item.id);
          detail = detail.detail;

          let tradeList = item.tradeList;
          let serviceList = item.serviceList;

          let newtList = new Array(),
            newsList = new Array();
          if (type == 'sku') {
            for (let s of tradeList) {
              s = s.split('-');
              newtList.push(s[0]);
            }

            for (let s of serviceList) {
              s = s.split('-');
              newsList.push(s[0]);
            }

            tradeList = newtList;
            serviceList = newsList;
          }

          let solrContent = [detail.description];
          for (let item of detail.content) {
            solrContent.push(item.title + ' ' + item.content);
          }

          for (let item of detail.qa) {
            solrContent.push(item.question + '' + item.answer);
          }

          solrContent = solrContent.join(' ').replace(/<.*?>/g, "").replace(/\n/g, '');
          let catalogs = []
          item.catalog.forEach(item => {
            catalogs.push(item.alias)
          })
          let row = {
            id: 'S_' + detail.id,
            title: detail.name,
            alias: detail.alias,
            sub_title: detail.sub_title,
            summary: detail.description.replace(/<.*?>/g, "").replace(/\n/g, ''),
            content: solrContent,
            type: '1',
            buy: detail.is_buy,
            industry: tradeList.join(','),
            service: serviceList.join(','),
            imgUrl: detail.thumb_img,
            mImgUrl: detail.m_thumb_img || detail.thumb_img,
            catalog: catalogs.join(','),
            createDate: moment(item.gmt_create).format('YYYY/MM/DD')
          }
          str += '<p style="font-size:12px;color:#666">' + JSON.stringify(row) + '</p><hr>';

          /* start */
          options = {
            url: env[this.app.config.env].solrUrl + '/add',
            method: 'POST',
            headers: {
              pid: solrPid,
              timestamp: new Date().getTime(),
            },
            json: true,
            body: row
          }
        } else {
          let row = {
            id: 'S_' + item.id,
          }

          str += '<p style="font-size:12px;color:#666"><span style="color:#F00">下架：</strong>' + JSON.stringify(row) + '</p><hr>';

          options = {
            url: env[this.app.config.env].solrUrl + '/del',
            method: 'POST',
            headers: {
              pid: solrPid,
              timestamp: new Date().getTime(),
            },
            json: true,
            body: row
          }
        }

        let parr = JSON.stringify(options.body);
        parr += solrPstr;
        parr = crypto.createHash('md5').update(parr).digest("hex");
        options.headers.sign = parr;

        request(options, function (error, response, body) {
          if (error || response.statusCode > 400) {
            return;
          }
        });

        /* end */
      }
    } else if (type == 'news') {
      list = await ctx.service.news.getList({ page: page, limit: limit });

      for (let item of list.list) {
        let options = {};
        if (item.is_publish == 1) {
          let detail = await ctx.service.news.getDetail(item.id);
          detail = detail.detail;

          const cReg = new RegExp(/(<p>)[^>]*>([\s\S]*?)(?=<\/p>)/gi);
          let newDetail = detail.content.match(cReg);

          let outSolrCon = '';
          if (newDetail) {
            for (let item2 of newDetail) {
              item2 = item2.replace(/<[^>]*>/g, "");
              if (item2.trim() != '' && outSolrCon == '') {
                outSolrCon = item2;
              }
            }
          } else {
            outSolrCon = detail.content.replace(/<[^>]*>/g, "");
          }

          let tradeList = item.tradeList;
          let serviceList = item.serviceList;
          let catalogs = []
          item.catalog.forEach(item => {
            catalogs.push(item.alias)
          })
          let row = {
            id: 'N_' + detail.id,
            title: detail.title,
            alias: detail.alias,
            sub_title: '',
            // summary: outSolrCon,
            summary: '',
            content: detail.content.replace(/<[^>]*>/g, ""),
            type: '3',
            // buy: detail.is_buy,
            industry: tradeList.join(','),
            service: serviceList.join(','),
            imgUrl: detail.thumb_url,
            mImgUrl: detail.thumb_url || detail.thumb_img,
            catalog: catalogs.join(','),
            createDate: moment(item.gmt_create).format('YYYY/MM/DD')
          }

          str += '<p style="font-size:12px;color:#666">' + JSON.stringify(row) + '</p><hr>';

          /* start */
          options = {
            url: env[this.app.config.env].solrUrl + '/add',
            method: 'POST',
            headers: {
              pid: solrPid,
              timestamp: new Date().getTime(),
            },
            json: true,
            body: row
          }
        } else {
          let row = {
            id: 'N_' + item.id,
          }

          str += '<p style="font-size:12px;color:#666"><span style="color:#F00">草稿箱：</strong>' + JSON.stringify(row) + '</p><hr>';

          options = {
            url: env[this.app.config.env].solrUrl + '/del',
            method: 'POST',
            headers: {
              pid: solrPid,
              timestamp: new Date().getTime(),
            },
            json: true,
            body: row
          }
        }

        let parr = JSON.stringify(options.body);
        parr += solrPstr;
        parr = crypto.createHash('md5').update(parr).digest("hex");
        options.headers.sign = parr;

        request(options, function (error, response, body) {
          if (error || response.statusCode > 400) {
            return;
          }
        });
        /* end */
      }
    } else if (type == 'case') {
      list = await ctx.service.case.getList({ page: page, limit: limit });
      const typeList = await ctx.service.case.typeList();

      for (let item of list.list) {
        let options = {};
        if (item.is_publish == 1) {
          let detail = await ctx.service.case.getDetail(item.id);
          detail = detail.detail;

          const cReg = new RegExp(/(<p>)[^>]*>([\s\S]*?)(?=<\/p>)/gi);
          let newDetail = detail.content.match(cReg);

          let outSolrCon = '';
          if (newDetail) {
            for (let item2 of newDetail) {
              item2 = item2.replace(/<[^>]*>/g, "");
              if (item2.trim() != '' && outSolrCon == '') {
                outSolrCon = item2;
              }
            }
          } else {
            outSolrCon = detail.content.replace(/<[^>]*>/g, "");
          }

          let tradeList = item.tradeList;
          let serviceList = item.serviceList;
          let catalogs = []
          item.catalog.forEach(item => {
            catalogs.push(item.alias)
          })
          let catalogType = ''
          typeList.list.forEach(v => {
            if (v.id === detail.catalog_id) {
              catalogType = v.alias
            }
          })
          // console.log('typeList.list.find(v => v.id === detail.catalog_id).alias', typeList.list.find(v => v.id === detail.catalog_id), typeList.list.find(v => v.id === detail.catalog_id).alias)
          let row = {
            id: 'C_' + detail.id,
            title: detail.title,
            alias: detail.alias,
            sub_title: '',
            content: detail.content.replace(/<[^>]*>/g, ""),
            type: '2',
            buy: detail.is_buy,
            industry: tradeList.join(','),
            service: serviceList.join(','),
            imgUrl: detail.thumb_img,
            mImgUrl: detail.m_thumb_img,
            catalog: catalogs.join(','),
            subType: detail.name,
            createDate: moment(item.gmt_create).format('YYYY/MM/DD'),
            catalogType
          }

          str += '<p style="font-size:12px;color:#666">' + JSON.stringify(row) + '</p><hr>';

          /* start */
          options = {
            url: env[this.app.config.env].solrUrl + '/add',
            method: 'POST',
            headers: {
              pid: solrPid,
              timestamp: new Date().getTime(),
            },
            json: true,
            body: row
          }
        } else {
          let row = {
            id: 'C_' + item.id,
          }

          str += '<p style="font-size:12px;color:#666"><span style="color:#F00">草稿箱：</strong>' + JSON.stringify(row) + '</p><hr>';

          options = {
            url: env[this.app.config.env].solrUrl + '/del',
            method: 'POST',
            headers: {
              pid: solrPid,
              timestamp: new Date().getTime(),
            },
            json: true,
            body: row
          }
        }

        let parr = JSON.stringify(options.body);
        parr += solrPstr;
        parr = crypto.createHash('md5').update(parr).digest("hex");
        options.headers.sign = parr;

        request(options, function (error, response, body) {
          if (error || response.statusCode > 400) {
            return;
          }
        });
        /* end */
      }
    } else if (type == 'resource') {
      list = await ctx.service.resource.getList({ page: page, limit: limit });

      for (let item of list.list) {
        let options = {};
        if (item.is_publish == 1) {
          let detail = await ctx.service.resource.getDetail(item.id);
          detail = detail.detail;

          let tradeList = item.tradeList;
          let serviceList = item.serviceList;

          let summary = detail.path.split('/');
          summary = summary[summary.length - 1];
          let row = {
            id: 'R_' + detail.id,
            title: detail.title,
            alias: detail.alias,
            // summary: summary,
            summary: '',
            content: '',
            url: detail.path,
            type: '4',
            // buy: 0,
            industry: tradeList.join(','),
            service: serviceList.join(',')
          }

          str += '<p style="font-size:12px;color:#666">' + JSON.stringify(row) + '</p><hr>';

          /* start */
          options = {
            url: env[this.app.config.env].solrUrl + '/add',
            method: 'POST',
            headers: {
              pid: solrPid,
              timestamp: new Date().getTime(),
            },
            json: true,
            body: row
          }
        } else {
          let row = {
            id: 'R_' + item.id,
          }

          str += '<p style="font-size:12px;color:#666"><span style="color:#F00">草稿箱：</strong>' + JSON.stringify(row) + '</p><hr>';

          options = {
            url: env[this.app.config.env].solrUrl + '/del',
            method: 'POST',
            headers: {
              pid: solrPid,
              timestamp: new Date().getTime(),
            },
            json: true,
            body: row
          }
        }

        let parr = JSON.stringify(options.body);
        parr += solrPstr;
        parr = crypto.createHash('md5').update(parr).digest("hex");
        options.headers.sign = parr;

        request(options, function (error, response, body) {
          if (error || response.statusCode > 400) {
            return;
          }
        });
        /* end */
      }
    } else if (type == 'delete') {
      const delSku = await this.app.mysql.select('sku', { columns: ['id'], where: { is_delete: 1 } });
      const delNews = await this.app.mysql.select('news', { columns: ['id'], where: { is_delete: 1 } });
      const delCase = await this.app.mysql.select('cases', { columns: ['id'], where: { is_delete: 1 } });
      const delResource = await this.app.mysql.select('resource', { columns: ['id'], where: { is_delete: 1 } });

      let rows = [];
      for (let item of delSku) {
        let row = { id: 'S_' + item.id };
        rows.push(row);
      }

      for (let item of delNews) {
        let row = { id: 'N_' + item.id };
        rows.push(row);
      }

      for (let item of delCase) {
        let row = { id: 'C_' + item.id };
        rows.push(row);
      }

      for (let item of delResource) {
        let row = { id: 'R_' + item.id };
        rows.push(row);
      }

      for (let item of rows) {
        str += '<p style="font-size:12px;color:#666"><span style="color:#F00">删除：</strong>' + JSON.stringify(item) + '</p><hr>';
        let options = {
          url: env[this.app.config.env].solrUrl + '/del',
          method: 'POST',
          headers: {
            pid: solrPid,
            timestamp: new Date().getTime(),
          },
          json: true,
          body: item
        }

        let parr = JSON.stringify(options.body);
        parr += solrPstr;
        parr = crypto.createHash('md5').update(parr).digest("hex");
        options.headers.sign = parr;

        request(options, function (error, response, body) {
          if (error || response.statusCode > 400) {
            return;
          }
        });
      }
    }

    if (type) {
      let totalPage = Math.ceil(list.total / limit);
      if (type != 'delete') {
        str += '<p>共' + list.total + '条数据，' + totalPage + '页，当前第' + page + '页。';
      }

      if (page < totalPage) {
        str += '<a href="/solr/renew?type=' + type + '&page=' + (Number(page) + 1) + '">处理下一页</a></p>';
      } else {
        str += '当前类别处理完毕</p>';
      }
    } else {
      str += '<p>请先选择类别</p>';
    }


    ctx.body = str + '</body></html>';
    // ctx.body = params;
  }
};

module.exports = SolrController;
