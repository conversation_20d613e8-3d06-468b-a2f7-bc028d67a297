.serverBox{
    width: 100%;
    background: rgb(250,250,250);
    height: 1000px;
    margin: 0 auto;
}
.serverBox1{
    width: 1226px;height: 100%;
    margin: 0 auto;
    position: relative;
    padding-top: 30px;
}

.serverTab{
    width: 100%;height: 62px;
    position: relative;
}
.serverTabUl{
    width: 1221px;height: 100%;
    border-bottom: 3px solid rgb(242,242,242);
    padding: 0 0px 0px 5px;
}
.serverTabLi{
    width: auto;
    padding-right: 24px;
    border-right: 3px solid rgb(242,242,242);
    float: left;
    font-size: 16px;
    color: rgb(51,51,51);
    margin-right: 24px;
    margin-bottom: 10px;
    cursor: pointer;
    list-style: none;
}
.serverTabLi:nth-child(8){
    /*border-right: 0;*/

}
.serverTabLi:last-child{
    border-right: 0;
}
/*.serverTabLi:hover{*/
    /*color: rgb(254,102,3);*/
/*}*/
.serverAlls{
    width: 104px;height: 21px;
    /*position: absolute;*/
    /*right: 0;*/
    /*top: 24px;*/
    cursor: pointer;
}
.serverAllWord{
    width: auto;height: 100%;line-height: 21px;
    color: rgb(254,102,3);
    font-size: 16px;
    /*margin-left: 32px;*/
    float: left;
}
.serverAllIcon{
    /*position: relative;*/
    height: 0px;
    width: 0px;
    border-top: 4px solid transparent;
    border-left: 4px solid rgb(254,102,3);
    border-bottom: 4px solid transparent;
    float: left;
    margin-left: 6px;
    margin-top: 8px;
}
.serverLists{
    width: 100%;
    height: auto;
    margin-top: 20px;
}
.serverListsUl{
    width: 100%;
    height: 100%;
}
.serverListLi{
    width: 284px;float: left;
    height: 250px;
    margin-right: 30px;

}
.serverListLi:nth-child(4n){
    margin-right: 0;
}
.serverCon.server1{
    height: auto;
    position: relative;
    margin-top: 50px;
    width: 100%;
}
.serverpages{
    width: auto;height: 30px;
    position: absolute;
    bottom: 56px;
    right: 0;
    padding: 0;
    margin: 0;
}
.serverpage{
    width: 40px;
    height: 30px;
    background: rgb(242,242,242);
    margin-right: 5px;
    float: left;
    text-align: center;
    line-height: 30px;
    font-size: 12px;
    border: 1px solid rgb(242,242,242);
    cursor: pointer;
    list-style: none;
}
.pageActive{
    background: rgb(254,102,3);
    color: white;
    border: 1px solid rgb(254,102,3);
}
.serverpage.pageActive:hover{
    background: rgb(254,102,3);
    border: 1px solid rgb(254,102,3);
    color: white;
}
.serverpage:hover{
    background: white;
    border: 1px solid rgb(254,102,3);
    color: #000000;
}
.server1Active{

}
.liActive{
    color: rgb(254,102,3);
}