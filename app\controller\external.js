const Controller = require('egg').Controller;

class ExternalControll extends Controller {
  async uploadImg() {
    const {
      ctx
    } = this;
    // const formData = ctx.getFileStream();
    console.log(ctx.request.body);
    // const parts = ctx.multipart();
    console.log('got %d files', ctx.request.files.length);
    const params = ctx.request.body;
    const result = await ctx.service.external.uploadImg(params, ctx.headers);

    ctx.body = result;
  }

  // 工单列表查询
  async ticketQry() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.ticketQry(params, ctx.headers);

    ctx.body = result;
  }

  // 日志查询
  async logQry() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.logQry(params, ctx.headers);

    ctx.body = result;
  }

  // 工单列表查询
  async qryCategory() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.qryCategory(params, ctx.headers);

    ctx.body = result;
  }

  // 复制案例
  async copyCase() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.copyCase(params, ctx.headers);

    ctx.body = result;
  }

  // 导出excle
  async exportExcle() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.exportExcle(params, ctx.headers);

    ctx.body = result;
  }

  // 合作案例（资讯列表）列表(服务端)
  async qryCaseList() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.qryCaseList(params, ctx.headers);

    ctx.body = result;
  }

  // 查询关联资讯数量
  async qryRelCaseCount() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.qryRelCaseCount(params, ctx.headers);

    ctx.body = result;
  }

  // 解决方案列表(管理端)
  async qrySkuList() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.qrySkuList(params, ctx.headers);

    ctx.body = result;
  }
  // 资源访问列表
  async qryResourceList() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.qryResourceList(params, ctx.headers);

    ctx.body = result;
  }

  // 查询关联资料数量
  async qryRelResourceCount() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.qryRelResourceCount(params, ctx.headers);

    ctx.body = result;
  }

  // 新闻案例列表(服务端)
  async qryNewsList() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.qryNewsList(params, ctx.headers);

    ctx.body = result;
  }
}
module.exports = ExternalControll;