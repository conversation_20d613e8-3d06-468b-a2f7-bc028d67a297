'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo; 
class HotWordController extends Controller {
  async view() {
    const { ctx } = this;
    await ctx.render('hot_word', {
      site_title: _info.site_title,
      page_title: '热门关键词',
      active: '12-3',
      csrf: ctx.csrf,
      userInfo: ctx.session.userInfo,
    });
  }

  async getList() {
    const { ctx } = this;
    const params = ctx.request.body;
    const list = await ctx.service.hotWord.getList(params);
    ctx.body = {
      success: true,
      data: list
    };
  }

  async insert() {
    const { ctx } = this;
    const params = ctx.request.body;
    const data = await ctx.service.hotWord.insert(params);
    ctx.body = {
      success: true,
      data: data
    };
  }

  async edit() {
    const { ctx } = this;
    const params = ctx.request.body;
    const data = await ctx.service.hotWord.edit(params);
    ctx.body = {
      success: true,
      data: data
    };
  }
  async del() {
    const { ctx } = this;
    const params = ctx.request.body;
    const data = await ctx.service.hotWord.del(params);
    ctx.body = {
      success: true,
      data: data
    };
  }

}

module.exports = HotWordController;