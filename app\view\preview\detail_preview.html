<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="description" content="<%- detail.seo_text %>">
    <title> <%- detail.name %> - SGS</title>
    <link rel="stylesheet" href="/static/preview/css/homepage.css">
    <link rel="stylesheet" href="/static/preview/css/detail.css">
    <link rel="stylesheet" href="/static/preview/css/detail1.css">
    <script src="/static/preview/js/jquery.min.js"></script>

    <script src="/static/preview/js/homepage.js"></script>
    <script src="/static/preview/js/detail.js"></script>
</head>
<body>
<div class="box">
    <div class="message">
        <div class="messageIcon"></div>
    </div>

    <div class="note">
        在线客服
    </div>
</div>
<div class="headBox">
    <div class="head">
        <div class="sgs">SGS Online</div>
        <ul class="headUl">
            <li class="headLi first">
                <span class="headLi-first-icon"></span>
                <span class="headLi-first-word">购物车( 0 )</span>
            </li>
            <li class="headLi">我的订单</li>
            <li class="headLi">个人中心</li>
            <li class="headLi"></li>
            <li class="headLi">您好，欢迎来到 SGS 商城</li>
        </ul>
    </div>
</div>


<div class="nav detailNav">
    <div class="navBox">
        <div class="navImg">
            <img src="/static/preview/images/logo.png" alt="">
        </div>
        <ul class="navUl">
                <li class="navLi">首页</li>
                <li class="navLi s">
                    您的行业
                    <ul class="drag">
                        <div class="navBor">

                        </div>

                        <% if(tradeNavi && tradeNavi.length > 0){ %>
                        <% for (var item of tradeNavi){ %>
                        <li class="dragLi">

                            <div class="dragLi-word"><%- item.name %></div>
                        </li>
                        <% } %>
                        <% } %>
                        
                        <div class="dragAngel">

                        </div>
                    </ul>
                </li>
                <li class="navLi t">我们的服务
                    <ul class="drag">
                        <div class="navBor">

                        </div>
                        <% if(serviceNavi && serviceNavi.length > 0){ %>
                            <% for (var item of serviceNavi){ %>
                            <li class="dragLi">
    
                                <div class="dragLi-word"><%- item.name %></div>
                            </li>
                            <% } %>
                            <% } %>
                        <div class="dragAngel">

                        </div>
                    </ul>
                </li>
            </ul>

        <div class="navSearchBox">
            <form action="">
                <div class="navSearchIcon">
                    <div class="navSearchIconIcon">

                    </div>
                    <!--<img src="./images/1-3.jpg" alt="" class="">-->
                </div>
                <input type="text" class="navSearch">
            </form>

        </div>
    </div>
</div>
<div class="location detailLoca">
        <ul>
            <li class="locationLi">首页</li>
            <li class="locationLi icon"></li>
            <li class="locationLi"><%- detail.name %></li>
        </ul>
    </div>
<div class="detailBox">
    <div class="detailbannerBox">
        <div class="detailbanner">
            <img src="<%- detail.cover_img %>" />
        </div>
        <div class="bannerInfo">
            <div style="min-height:224px">
            <div class="word1"><%- detail.name %></div>
            <div class="word2"><%- detail.sub_title %></div>
            <div class="word3" style="min-height: 0"><% if(detail.description){ %><%- detail.description.replace(/\n/g,'<br>') %><% } %></div>
            

            <% if(detail.type == 2){ %>
            <div class="word-day">
                <!--<div class="word-day-icon"></div>-->
                <% if(detail.course.time){ %>
                <div class="word-day-word">开课时间：<% for(var item of detail.course.time){ %> <span style="display:inline-block;white-space: nowrap"><%- item %></span> <% } %></div>
                <% } %>
                <% if(detail.course.area){ %>
                <div class="word-day-word">开课城市：<% for(var item of detail.course.area){ %> <span style="display:inline-block;white-space: nowrap"><%- item %></span> <% } %></div>
                <% } %>
                <% if(detail.course.days){ %>
                <div class="word-day-word">课程天数：<%- detail.course.days %>天</div>
                <% } %>
            </div>
            <% } %>
            </div>
            <% if(!detail.buy_url){ %>
            <div class="word5">
                <a href="/preview/ticket" style="color:#fe6603" target="_blank">
                <div class="word5-1"></div>
                <div class="word5-2">咨询报价</div>
                </a>
            </div>
            <% }else{ %>
            <div class="zixun-dinggou">
                <a href="/preview/ticket" style="color:#fe6603" target="_blank">
                <div class="zixun">咨询报价</div>
                </a>
                <div class="dinggou">
                    <div class="deepen"></div>
                    <div class="dinggouword"><a href="<%- detail.buy_url %>" style="color:#FFF" target="_blank">在线下单</a></div>

                </div>
            </div>
            <% } %>
        </div>
    </div>

    
    <% if(detail.sContent){ %>
    <% for (var item of detail.sContent){ %>
    <div class="serverConBox">
        <% if(!item.hide_title){ %>
        <div class="serverTitleBox">
            <div class="serverCon" style="float:left;width:auto;"><%- item.title %></div>
        </div>
        <% } %>
        <div class="rtfc" style="clear:both;width:100%;height:auot;overflow:hidden;font-size:14px;line-height:2"><%- item.content %></div>
    </div>
    <% } %>
    <% } %>

    <div class="serverConBox">
        <div class="serverTitleBox" style="margin-bottom:25px">
            <div class="serverCon">常见问题</div>
        </div>
        
        <% if(detail.faq){ %>
        <% for (var item of detail.faq){ %>
        <div class="questionBox" style="clear:both;overflow:hidden;">
            <div class="question-word1"><%- item.question %></div>
            <div class="question-word2 rtfc" style="font-size: 14px;line-height: 2"><%- item.answer %></div>
            <div class="question-icon"></div>
        </div>
        <% } %>
        <% } %>
    
    </div>

    <div class="recoment">
        <div class="square"></div>
        <div class="recoment-word1">为您推荐</div>
        <!--<div class="recoment-word2">换一批</div>
        <div class="recoment-icon"></div>-->
        <ul class="recomentUl">
            <li class="recomentLi">
                <div class="recomentLi-img">
                    <img src="" alt="">
                </div>
                <div class="recomentLi-word"></div>
                </a>
            </li>

            <li class="recomentLi">
                <div class="recomentLi-img">
                    <img src="" alt="">
                </div>
                <div class="recomentLi-word"></div>
                </a>
            </li>

            <li class="recomentLi">
                <div class="recomentLi-img">
                    <img src="" alt="">
                </div>
                <div class="recomentLi-word"></div>
                </a>
            </li>
        </ul>
    </div>

</div>
<div class="footerBox">
    <div class="footer">
        <div class="footer-top">
            <div class="line">

            </div>
            <div class="footer-top-title">
                <ul>
                    <li class="footer-top-title-li">帮助中心</li>
                    <li class="footer-top-title-li">我们的服务</li>
                    <li class="footer-top-title-li">关于我们</li>
                    <li class="footer-top-title-li footer-top-title-li-four">联系客服</li>
                    <li class="footer-top-title-li footer-top-title-li-last">关注我们
                        <div class="weibo">
                            <div class="weibo-img"></div>
                            <span class="weibo-word">官方微博</span>
                        </div>
                        <div class="weixin">
                            <div class="weixin-img"></div>
                            <span class="weixin-word">公众号</span>
                        </div>

                        <div class="erweima">
                            <div class="weiboer">

                            </div>
                            <div class="weixiner">

                            </div>
                            <div class="weixinerword">扫描二维码</div>
                            <div class="weixinerword1">关注SGS官方公众号</div>
                            <div class="weiboerword">扫描二维码</div>
                            <div class="weiboerword1">关注SGS智慧生活</div>
                        </div>


                    </li>
                </ul>

                <ul class="help">
                    <li class="helpLi">新手指南</li>
                    <li class="helpLi">服务条款</li>
                    <li class="helpLi">常见问题</li>
                    <li class="helpLi">订购流程</li>
                </ul>

                <ul class="ourServer">
                    <li class="helpLi">测试</li>
                    <li class="helpLi">检验</li>
                    <li class="helpLi">审核</li>
                    <li class="helpLi">认证</li>
                    <li class="helpLi">培训</li>
                </ul>
                <ul class="aboutUs">
                    <li class="helpLi">关于SGS</li>
                    <li class="helpLi">SGS官网</li>
                    <li class="helpLi">新闻中心</li>

                </ul>

                <ul class="connect">
                    <li class="helpLi">邮箱：<EMAIL></li>
                    <li class="helpLi">服务热线：400-666-888</li>
                </ul>


            </div>
        </div>
        <div class="footer-bottom">
            © 2017 SGS SA
        </div>
    </div>
</div>
</body>
</html>