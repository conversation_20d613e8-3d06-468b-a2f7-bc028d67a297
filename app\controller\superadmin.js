'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const { env } = require('../../config/info').siteInfo;
const crypto = require('crypto');
const moment = require('moment');

class SuperadminController extends Controller {

  //用户列表
  async superadminList() {

    const { ctx } = this;
    const roleList = await ctx.service.role.getList({ is_effect: 1, is_delete: 0 });

    const params = { page: 1, is_delete: 0 };
    params.type = 1;
    const result = await ctx.service.admin.getList(params);

    result.list.some(item => {
      item.gmt_create = moment(item.gmt_create).format('YYYY-MM-DD HH:mm:ss');
    });

    await ctx.render('superadmin_list', {
      site_title: _info.site_title,
      page_title: '超级用户配置',
      active: '14-3',
      list: JSON.stringify(result.list),
      roleList: JSON.stringify(roleList.list),
      total: result.total,
      csrf: ctx.csrf,
      type: 'list',
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
    });
  }

  async getList() {

    const { ctx } = this;

    const params = ctx.request.body;
    params.type = 1;
    params.is_delete = 0;

    const result = await ctx.service.admin.getList(params);
    result.list.some(item => {
      item.gmt_create = moment(item.gmt_create).format('YYYY-MM-DD HH:mm:ss');
    });
    ctx.body = { success: true, data: result };
  }

  async superadminAdd() {
    const { ctx } = this;

    const roleList = await ctx.service.role.getList({ is_effect: 1, is_delete: 0 });

    await ctx.render('superadmin_add', {
      site_title: _info.site_title,
      page_title: '添加超级用户',
      active: '14-3',
      roleList: JSON.stringify(roleList.list),
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
    });
  }

  async superadminEdit() {
    const { ctx } = this;

    const id = ctx.params.id;

    const roleList = await ctx.service.role.getList({ is_effect: 1, is_delete: 0 });


    const detail = await ctx.service.admin.getDetail(id);

    const logs = await ctx.service.logs.listAll({ type: 'admin', id: id, page: 1, limit: 10 });

    await ctx.render('superadmin_edit', {
      site_title: _info.site_title,
      page_title: '编辑超级用户',
      active: '14-3',
      roleList: JSON.stringify(roleList.list),
      form: detail,
      id: id,
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs)
    });
  }

  async superadminSave() {
    const { ctx } = this;

    const params = ctx.request.body;
    params.type = 1;

    const flag = await ctx.service.admin.checkNameIsExists(params.name, params.id);
    if (flag) {
      ctx.body = { fail: true, data: null, msg: "超级用户名称重复" };
      return;
    }
    const result = await ctx.service.admin.adminAdd(params);

    if (result.success) {
      ctx.body = { success: true, data: null, id: result.id };
    } else {
      ctx.body = { fail: true, data: null };
    }
  }

  async superadminEditPassword() {
    const { ctx } = this;

    const id = ctx.params.id;

    await ctx.render('superadmin_password', {
      site_title: _info.site_title,
      page_title: '修改密码',
      active: '14-3',
      id: id,
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo
    });
  }

  async superadminSavePassword() {

    const { ctx } = this;

    const params = ctx.request.body;

    const adminInfo = await ctx.service.admin.getDetail(params.id);

    const origin_pwd = crypto.createHash('md5').update(params.pwd).digest("hex");

    if (adminInfo.pwd != origin_pwd) {
      ctx.body = { fail: true, data: null, msg: '原始密码不正确' };
      return;
    }

    const result = await ctx.service.admin.adminSavePassword(params);

    if (result.success) {
      ctx.body = { success: true, data: null };
    } else {
      ctx.body = { fail: true, data: null };
    }
  }


  async superadminDelete() {
    const { ctx } = this;

    const id = ctx.request.body.id;
    const result = await ctx.service.admin.adminDelete(id);

    if (result.success) {
      ctx.body = { success: true, data: null };
    } else {
      ctx.body = { fail: true, data: null };
    }
  }

}

module.exports = SuperadminController;