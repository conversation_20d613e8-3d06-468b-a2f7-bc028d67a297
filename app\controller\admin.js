'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const { env } = require('../../config/info').siteInfo;
const crypto = require('crypto');
const moment = require('moment');
const axios = require('axios');

class AdminController extends Controller {

  //用户列表
  async adminList() {

    const { ctx } = this;
    const roleList = await ctx.service.role.getList({ is_effect: 1, is_delete: 0, limit: 10000 });

    const params = { page: 1, is_delete: 0 };
    params.type = 2;
    const result = await ctx.service.admin.getList(params);

    result.list.some(item => {
      item.gmt_create = moment(item.gmt_create).format('YYYY-MM-DD HH:mm:ss');
    });

    await ctx.render('admin_list', {
      site_title: _info.site_title,
      page_title: '用户列表',
      active: '14-1',
      list: JSON.stringify(result.list),
      roleList: JSON.stringify(roleList.list),
      total: result.total,
      csrf: ctx.csrf,
      type: 'list',
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
    });
  }

  async getList() {

    const { ctx } = this;

    const params = ctx.request.body;
    params.type = 2;
    params.is_delete = 0;

    const result = await ctx.service.admin.getList(params);
    result.list.some(item => {
      item.gmt_create = moment(item.gmt_create).format('YYYY-MM-DD HH:mm:ss');
    });
    ctx.body = { success: true, data: result };
  }

  async adminAdd() {
    const { ctx } = this;

    const roleList = await ctx.service.role.getList({ is_effect: 1, is_delete: 0, limit: 10000 });

    await ctx.render('admin_add', {
      site_title: _info.site_title,
      page_title: '添加用户',
      active: '14-1',
      roleList: JSON.stringify(roleList.list),
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
    });
  }

  async adminEdit() {
    const { ctx } = this;

    const id = ctx.params.id;

    let roleList = null;

    let roleid_json = [];
    const detail = await ctx.service.admin.getDetail(id);

    if (!detail.roleid_json || detail.roleid_json == '[]') {
      roleid_json = [];
    }
    else {
      roleid_json = JSON.parse(detail.roleid_json)
    }
    roleList = await ctx.service.role.getRoleScopeByAdmin(roleid_json);

    const logs = await ctx.service.logs.listAll({ type: 'admin', id: id, page: 1, limit: 10 });

    await ctx.render('admin_edit', {
      site_title: _info.site_title,
      page_title: '编辑用户',
      active: '14-1',
      roleList: JSON.stringify(roleList),
      form: detail,
      id: id,
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs)
    });
  }

  async adminSave() {
    const { ctx } = this;

    const params = ctx.request.body;

    const flag = await ctx.service.admin.checkNameIsExists(params.name, params.id);
    if (flag) {
      ctx.body = { fail: true, data: null, msg: "用户名称重复" };
      return;
    }

    const querystring = require('querystring');
    await axios.post(_info.userNameCheckApi, querystring.stringify({ name: params.name }), { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }).then(res => {

      if (res.data && res.data.total == 0) {
        ctx.body = { fail: true, data: null, msg: "sgs账号不存在" };
        return;
      }
    }).catch(err => {
      ctx.body = { fail: true, data: null, msg: "网络错误" };
      return;
    });

    const result = await ctx.service.admin.adminAdd(params);

    if (result.success) {
      ctx.body = { success: true, data: null, id: result.id };
    } else {
      ctx.body = { fail: true, data: null };
    }
  }



  async adminSavePassword() {

    const { ctx } = this;

    const params = ctx.request.body;

    const adminInfo = await ctx.service.admin.getDetail(params.id);

    const origin_pwd = crypto.createHash('md5').update(params.pwd).digest("hex");

    if (adminInfo.pwd != origin_pwd) {
      ctx.body = { fail: true, data: null, msg: '原始密码不正确' };
      return;
    }

    const result = await ctx.service.admin.adminSavePassword(params);

    if (result.success) {
      ctx.body = { success: true, data: null };
    } else {
      ctx.body = { fail: true, data: null };
    }
  }


  async adminDelete() {
    const { ctx } = this;

    const id = ctx.request.body.id;

    let deleteFlag = await ctx.service.checkflow.checkAdminCanDelete(id);

    if (deleteFlag == 0) {
      ctx.body = { fail: true, data: null, msg: '有未处理的数据,无法删除' };
      return;
    }

    const result = await ctx.service.admin.adminDelete(id);

    if (result.success) {
      ctx.body = { success: true, data: null };
    } else {
      ctx.body = { fail: true, data: null, msg: '系统错误' };
    }
  }

}

module.exports = AdminController;