'use strict';

const Service = require('egg').Service;
const moment = require('moment');

class SettingService extends Service {
  async labListQry() {
    const list = await this.app.mysql.select('laboratory', {
      // columns: ['id', 'alias', 'name', 'is_delete', 'is_copy', 'type', 'create_time', 'modify_time'],
      orders: [
        ['id', 'desc']
      ],
      where: { is_delete: 0 },
    });
    list.forEach(v => {
      v.create_time = moment(v.create_time).format('YYYY-MM-DD HH:mm:ss')
      v.modify_time = moment(v.modify_time).format('YYYY-MM-DD HH:mm:ss')
      v.seo = JSON.parse(v.seo)
    })
    return { list };
  }

  async labListDetail(id) {
    const {
      app
    } = this;
    const detail = await app.mysql.get('laboratory', {
      id
    });
    return { detail };
  }

  async labSave(params) {
    const {
      app
    } = this;

    const row = {
      alias: params.alias,
      name: params.name,
      type: params.type,
      content: JSON.stringify(params.content),
      seo: JSON.stringify(params.seo),
      modify_time: moment().format('YYYY-MM-DD HH:mm:ss'),
      is_delete: params.is_delete,
      is_copy: params.is_copy,
      is_publish: params.is_publish
    };

    const options = {
      where: {
        id: params.id
      }
    };
    const result = await this.app.mysql.update('laboratory', row, options);
    if (result.affectedRows === 1) {
      // 保存成功推送CDN刷新
      // const cndParams = {
      //   fileUrls: [params.pageLink]
      // }
      // await this.ctx.service.external.CDNRefresh(cndParams);
      return {
        success: true
      }
    } else {
      return {
        success: false
      }
    }
  }

  async labAdd(params) {
    const {
      app
    } = this;

    const row = {
      alias: params.alias,
      name: params.name,
      type: params.type,
      is_delete: params.is_delete || 0,
      is_publish: params.is_publish || 0,
      is_copy: params.is_copy || 0,
      content: JSON.stringify(params.content),
      seo: JSON.stringify(params.seo),
      create_time: moment().format('YYYY-MM-DD HH:mm:ss'),
      modify_time: moment().format('YYYY-MM-DD HH:mm:ss'),
    };
    const qryByAlias = await this.app.mysql.select('laboratory', {
      where: {
        alias: row.alias,
        type: row.type
      }
    });
    if (!qryByAlias.length) {
      const result = await app.mysql.insert('laboratory', row);
      if (result.affectedRows === 1) {
        return {
          success: true,
          id: result.insertId
        }
      } else {
        return {
          success: false
        }
      }
    } else {
      return {
        success: false,
        msg: '别名不能重复'
      }
    }
  }

  async labDel(params) {
    const {
      app
    } = this;
    const { id } = params
    const result = await app.mysql.query(`UPDATE laboratory SET is_delete=1 WHERE id=${id}`);
    if (!result || result.length == 0) {
      return {
        success: false
      }
    } else {
      // 保存成功推送CDN刷新
      // const cndParams = {
      //   fileUrls: [params.pageLink]
      // }
      // await this.ctx.service.external.CDNRefresh(cndParams);
      // return {
      //   success: true
      // }
    }
  }
}

module.exports = SettingService;