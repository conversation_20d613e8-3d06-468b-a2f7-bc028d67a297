<% include header.html %>
<div class="myLocation">
    <div class="myLocationBox">
        <div class="myLocation-word1"><a href="">首页</a></div>
        <div class="myLocation-icon1"></div>
        <div class="myLocation-word2"><a href="">咨询留言</a></div>
    </div>
</div>

<div class="SearchBox">
    <div class="searchleftBox">
        <div class="messageCon">
            <div class="messageConT">咨询内容</div>
            <div class="messageConS">请尽量详细描述您的需求，以便我们能更好得回复您的咨询</div>
        </div>

        <div class="tip">
            带（<span class="mustStar">*</span>）为必填项
        </div>

        <div class="selectBox">
            <div class="select">
                <div class="selectT">咨询类型<span class="mustStar">*</span></div>
                <div class="selectC">
                    <label class="form-label" style="margin-top:0px;height: 50px">
                        <select class="filter-select" id="type" style="width: 260px;">
                            <!--<option value="0">无</option>-->
                            <option value="服务询价">服务询价</option>
                            <option value="业务咨询">业务咨询</option>
                            <option value="建议反馈">建议反馈</option>
                            <option value="其他">其他</option>
                        </select>
                    </label>
                </div>
                <p class="error">*必填字段</p>
            </div>
            <div class="select">
                <div class="selectT">您的行业<span class="mustStar">*</span></div>
                <div class="selectC">
                    <label class="form-label" style="margin-top:0px;height: 50px">
                        <select class="filter-select" id="trade" style="width: 260px;">
                            <option value="0">无</option>
                            <% if(tradeNavi && tradeNavi.length > 0){ %>
                            <% for (var item of tradeNavi){ %>
                            <option value="<%- item.id %>"><%- item.name %></option>
                            <% } %>
                            <% } %>
                        </select>
                    </label>
                </div>
                <p class="error">*必填字段</p>
            </div>
            <div class="select">
                <div class="selectT">服务类型<span class="mustStar">*</span></div>
                <div class="selectC">
                    <label class="form-label" style="margin-top:0px;height: 50px">
                        <select class="filter-select" id="service" style="width: 260px;">
                            <option value="0">无</option>
                            <% if(serviceNavi && serviceNavi.length > 0){ %>
                            <% for (var item of serviceNavi){ %>
                            <option value="<%- item.id %>"><%- item.name %></option>
                            <% } %>
                            <% } %>
                        </select>
                    </label>
                </div>
                <p class="error">*必填字段</p>
            </div>
        </div>

        <div class="content">
            <div class="selectT">咨询内容<span class="mustStar">*</span></div>
            <textarea id="textarea" placeholder="请详细描述您的需求" style="overflow-y: auto"></textarea>
            <p class="error">*必填字段</p>

        </div>

        <div class="yoursInfo">
            <div class="yoursT">您的信息</div>
            <div class="yoursInfoC">
                <div class="yoursInfoCC">
                    <div class="selectT">姓名<span class="mustStar">*</span></div>
                    <input type="text" class="selectC" placeholder="请输入您的姓名" id="customer">
                    <p class="error">*必填字段</p>
                </div>
                <div class="yoursInfoCC">
                    <div class="selectT">电话<span class="mustStar">*</span></div>
                    <input type="text" class="selectC" placeholder="请输入您的电话" id="phone">
                    <p class="error">*必填字段</p>
                </div>
                <div class="yoursInfoCC">
                    <div class="selectT">邮箱<span class="mustStar">*</span></div>
                    <input type="text" class="selectC" placeholder="请输入您的电子邮箱" id="email">
                    <p class="error">*必填字段</p>
                </div>
                <div class="yoursInfoCC" style="position: relative">
                    <div class="selectT">所在省份<span class="mustStar">*</span></div>
                    <input type="text" class="selectC country" placeholder="请输入所在省份，支持中文/拼音/简拼查找" id="provice">
                    <p class="error">*必填字段</p>
                    <div class="provice" style="position: absolute">
                        <p class="citys">

                            <a href="javascript:;" data-jp="ah" data-qp="anhui" data-value="123" data-name="安徽省">安徽省</a>

                            <a href="javascript:;" data-jp="bj" data-qp="beijing" data-value="107" data-name="北京市">北京市</a>

                            <a href="javascript:;" data-jp="cq" data-qp="chongqing" data-value="110" data-name="重庆市">重庆市</a>

                            <a href="javascript:;" data-jp="fj" data-qp="fujian" data-value="126" data-name="福建省">福建省</a>

                            <a href="javascript:;" data-jp="gs" data-qp="gansu" data-value="117" data-name="甘肃省">甘肃省</a>

                            <a href="javascript:;" data-jp="gd" data-qp="guangdong" data-value="130" data-name="广东省">广东省</a>

                            <a href="javascript:;" data-jp="gx" data-qp="guangxi" data-value="130" data-name="广西壮族自治区">广西壮族自治区</a>

                            <a href="javascript:;" data-jp="gz" data-qp="guizhou" data-value="134" data-name="贵州省">贵州省</a>

                            <a href="javascript:;" data-jp="hn" data-qp="hainan" data-value="132" data-name="海南省">海南省</a>

                            <a href="javascript:;" data-jp="hb" data-qp="hebei" data-value="121" data-name="河北省">河北省</a>

                            <a href="javascript:;" data-jp="hlj" data-qp="heilongjiang" data-value="111" data-name="黑龙江省">黑龙江省</a>

                            <a href="javascript:;" data-jp="hn" data-qp="henan" data-value="120" data-name="河南省">河南省</a>

                            <a href="javascript:;" data-jp="hb" data-qp="hubei" data-value="128" data-name="湖北省">湖北省</a>

                            <a href="javascript:;" data-jp="hn" data-qp="hunan" data-value="129" data-name="湖南省">湖南省</a>

                            <a href="javascript:;" data-jp="js" data-qp="jiangsu" data-value="124" data-name="江苏省">江苏省</a>

                            <a href="javascript:;" data-jp="jx" data-qp="jiangxi" data-value="127" data-name="江西省">江西省</a>

                            <a href="javascript:;" data-jp="jl" data-qp="jilin" data-value="112" data-name="吉林省">吉林省</a>

                            <a href="javascript:;" data-jp="jn" data-qp="liaoning" data-value="113" data-name="辽宁省">辽宁省</a>

                            <a href="javascript:;" data-jp="nmg" data-qp="neimenggu" data-value="114" data-name="内蒙古自治区">内蒙古自治区</a>

                            <a href="javascript:;" data-jp="nx" data-qp="ningxia" data-value="116" data-name="宁夏回族自治区">宁夏回族自治区</a>

                            <a href="javascript:;" data-jp="qh" data-qp="qinghai" data-value="137" data-name="青海省">青海省</a>

                            <a href="javascript:;" data-jp="sd" data-qp="shandong" data-value="122" data-name="山东省">山东省</a>

                            <a href="javascript:;" data-jp="sh" data-qp="shanghai" data-value="108" data-name="上海市">上海市</a>

                            <a href="javascript:;" data-jp="sx" data-qp="shanxi" data-value="118" data-name="山西省">山西省</a>

                            <a href="javascript:;" data-jp="sx" data-qp="shanxi" data-value="119" data-name="陕西省">陕西省</a>

                            <a href="javascript:;" data-jp="sc" data-qp="sichuan" data-value="135" data-name="四川省">四川省</a>

                            <a href="javascript:;" data-jp="tj" data-qp="tianjin" data-value="109" data-name="天津市">天津市</a>

                            <a href="javascript:;" data-jp="xj" data-qp="xinjiang" data-value="115" data-name="新疆维吾尔自治区">新疆维吾尔自治区</a>

                            <a href="javascript:;" data-jp="xz" data-qp="xizang" data-value="136" data-name="西藏自治区">西藏自治区</a>

                            <a href="javascript:;" data-jp="yn" data-qp="yunnan" data-value="133" data-name="云南省">云南省</a>

                            <a href="javascript:;" data-jp="zj" data-qp="zhejiang" data-value="125" data-name="浙江省">浙江省</a>

                        </p>

                    </div>
                </div>
                <div class="yoursInfoCC">
                    <div class="selectT">企业名称</div>
                    <input type="text" class="selectC1" placeholder="请输入企业名称" id="company">
                    <p class="error">*必填字段</p>
                </div>
                <div class="yoursInfoCC" style="display: none">
                    <div class="selectT">验证码<span class="mustStar">*</span></div>
                    <input type="text" class="selectC" placeholder="请输入验证码" id="code">
                    <p class="error">*必填字段</p>
                    <div class="code"></div>
                    <div class="re"></div>
                </div>
            </div>
        </div>

        <div class="messFooter">
            <div class="accept">
                <div class="acceptIcon active"></div>
                <div class="acceptWord">
                    我接受<a href="" class="privacy">隐私政策</a>
                </div>
            </div>
            <p class="error" style="text-align: center">*请先阅读隐私政策并选择是否同意</p>
            <button class="submit">提交</button>
        </div>
    </div>

    <div class="searchrightBox">
        <div class="searchrightBox1">
            <div class="your">
                <div class="yours">
                    <span class="your-word">您的行业</span>
                    <span class="dragbut">
                        <img src="/static/preview/images/jianhao.png" alt="" class="dragbut-icon">
                    </span>
                </div>

                <div class="your-cons first">
                    <div class="yourcon">
                        <ul>
                            <% if(tradeNavi && tradeNavi.length > 0){ %>
                            <% for (var item of tradeNavi){ %>
                            <li class="yourconLi">
                                <a href="/preview/industry/<%- item.alias %>/"><%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="your">
                <div class="yours">
                    <span class="your-word">我们的服务</span>
                    <span class="dragbut">
                        <img src="/static/preview/images/jiahao.png" alt="" class="dragbut-icon">
                    </span>
                </div>

                <div class="your-cons">
                    <div class="yourcon">
                        <ul>
                            <% if(serviceNavi && serviceNavi.length > 0){ %>
                            <% for (var item of serviceNavi){ %>
                            <li class="yourconLi">
                                <a href="/preview/service/<%- item.alias %>/"><%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="your">
                <div class="yours">
                    <span class="your-word">热点服务</span>
                    <span class="dragbut">
                        <img src="/static/preview/images/jiahao.png" alt="" class="dragbut-icon">
                    </span>
                </div>

                <div class="your-cons">
                    <div class="yourcon">
                        <ul>
                            <% if(hot && hot.length > 0){ %>
                            <% for (var item of hot){ %>
                            <li class="yourconLi">
                                <a href="/preview/sku/<%- item.id %>/"><%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<script src="/static/preview/js/select2.full.min.js"></script>
<script>
    $(function () {

        $('.citys a').click(function () {

            var con = $(this).data('name');
            $('.country').val(con);
            $('.provice').css({'display':'none'});
        })

        // $('.country').blur(function () {
        //     $('.provice').css({'display':'none'});
        // })

        $(document).click(function (e) {
            e=e || window.event;
            var o=e.target || e.srcElement;
            if($(o).attr('class')!='selectC country'){
                $('.provice').css({'display':'none'});

            }
        })

        $('.country').focus(function () {
            $('.provice').css({'display':'block'});

        })
        


        $('.country').bind('input propertychange',function () {
            var $_this = $(this);
            if($(this).val()!=''){
                $('.citys a').css({'width':'100%',"display":'none'});
                $('.citys a').each(function (index,value) {

                    if($(value).data('qp').indexOf($_this.val())>=0){

                        $(value).css({"display":'block'});
                    }else if ($(value).data('name').indexOf($_this.val())>=0){
                        $(value).css({"display":'block'});

                    }else if($(value).data('jp').indexOf($_this.val())>=0){
                        $(value).css({"display":'block'});

                    }

                })
            }else {
                $('.citys a').css({'width':'129px','display':'inline-block'});
            }

        })
        
        var $select = $(".select");
        $('.filter-select').select2({minimumResultsForSearch: -1});

        $(".acceptIcon").click(function () {
            var classNames = $(this).attr('class').split(' ');
            if(classNames.indexOf("active") > -1){
                $(this).removeClass('active');

            }else if(classNames.indexOf("active") < 0){
                $(this).addClass('active');
                $('.submit').siblings('.error').css({'visibility':'hidden'});
            }
        })

        $('.select2-selection--single').click(function () {
            $(this).css({'borderColor':'#eeeeee'}).parents('.selectC').siblings('.error').css({'visibility':'hidden'});
        })

        $('#textarea').focus(function () {
            $(this).css({'borderColor':'#eeeeee'}).siblings('.error').css({'visibility':'hidden'});
        })

        $('input.selectC').focus(function () {
            $(this).css({'borderColor':'#eeeeee'}).siblings('.error').css({'visibility':'hidden'});
        })

        $('.submit').click(function () {

            var $choice = $('.acceptIcon');
            if($choice.attr('class').indexOf('active')<0){
                $choice.parents('.accept').siblings('.error').css({'visibility':'visible'});
            }

            var $s = $('.select2-selection__rendered');
            $s.each(function (index,value) {
                if($(value).text() == '无'){
                    $(value).parents('.select2-selection--single').css({'borderColor':'#e50000'}).parents('.selectC').siblings('.error').css({'visibility':'visible'});

                }
            })

            var textCon = $('#textarea').val();
            if(textCon == ''){
                $('#textarea').css({'borderColor':'#e50000'}).siblings('.error').css({'visibility':'visible'});

            }

            var $input = $('input.selectC');
            $input.each(function (index,value) {
                if($(value).val() == ''){
                    $(value).css({'borderColor':'#e50000'}).siblings('.error').css({'visibility':'visible'});
                }
            });

            var errLen = 0;
            $('p.error').each(function(){
                if($(this).css('visibility') != 'hidden'){
                    errLen += 1;
                }
            })

            if(errLen == 1){
                var params = {
                    type: $('#type').val(),
                    trade: $('#trade').val(),
                    tradeName: $('#trade>option:selected').text(),
                    service: $('#service').val(),
                    serviceName: $('#service>option:selected').text(),
                    content:  $('#textarea').val(),
                    customer: $('#customer').val().trim(),
                    phone: $('#phone').val().trim(),
                    email: $('#email').val().trim(),
                    provice: $('#provice').val().trim(),
                    company: $('#company').val().trim(),
                    _csrf: '<%- csrf %>',
                }

                $.post('/ticket/post',params,function(r){
                    if(r.success){
                        alert('提交成功!');
                        window.location.reload();
                    }else{
                        alert('提交失败，请重试或刷新。');
                    }
                })
            }
        })

        var $_yours = $(".yours");

        $_yours.click(function () {

            var $_con = $(this).siblings(".your-cons");

            // for(var i=0;i<$_drag.length;i++){
            //     if($_drag.eq(i).attr("src") == "./images/"){
            //
            //     }
            // }

            if($_con.css("height")=="0px"){
                $_con.animate({"height":486}).end().parents(".your").siblings(".your").children(".your-cons").animate({"height":0});
                $(this).find(".dragbut-icon").attr("src",'/static/preview/images/jianhao.png');
                $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(0).attr("src","/static/preview/images/jiahao.png");
                $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(1).attr("src","/static/preview/images/jiahao.png")
            }else{
                $(this).find(".dragbut-icon").attr("src",'/static/preview/images/jiahao.png');
                $_con.animate({"height":0});
            }
        })

    })
</script>

<% include footer.html %>