<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        <% if(form.is_publish){ %>
                                        <a href='/resource/list'>资料列表</a>
                                        <% }else{ %>
                                        <a href='/resource/draft'>草稿箱</a>
                                        <% } %>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        编辑资源
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row>
                            <h3 class="stit">基本属性</h3>
                            <el-form :model="form" label-width="120px">
                                <el-form-item label="类型">
                                    <el-select v-model="form.type" placeholder="请选择">
                                        <el-option  v-for="item in types" :key="item.id" :label="item.name" :value="item.id">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="所属行业">
                                    <el-select multiple v-model="form.tradeList" placeholder="请选择" style="width:100%">
                                        <el-option v-for="item in tradeList"  :value="item.id" :key="item.id" :label="item.name"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="所属服务">
                                     <el-select multiple v-model="form.serviceList" placeholder="请选择" style="width:100%">
                                        <el-option v-for="item in serviceList"  :value="item.id" :key="item.id" :label="item.name"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="BU">
                                    <el-select placeholder="请选择" v-model="form.bu_id" style="width:100%" clearable>
                                        <el-option v-for="item of buList" :key="item.buId" :label="item.buName" :value="item.buId">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-form>
                            <hr>
                            <h3 class="stit">资源内容</h3>
                            <el-form :model="form" label-width="120px">
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="名称">
                                            <el-input v-model:trim="form.title"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-form-item label="类型">
                                    <el-radio-group v-model="form.up_type">
                                        <el-radio :label="1">文件上传</el-radio>
                                        <el-radio :label="2">文件链接</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <template v-if="form.up_type == 1">
                                <el-form-item label="上传文件">
                                    <el-upload class="upload-demo" :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                                        :data='uploadData' :show-file-list="false" :on-progress="handleProgress" :on-change="handleChange"
                                        :on-success="(response, file, fileList) => { return handleAvatarSuccess(response, file, fileList)}" name="file">
                                        <el-button v-if="!fileupload" size="small" type="primary">点击上传</el-button>
                                        <el-button v-else size="small" type="primary" :loading="true">上传中，请稍候…</el-button>
                                        <span slot="tip" class="el-upload__tip" style="padding-left:20px;"></span>
                                    </el-upload>
                                </el-form-item>
                                <el-form-item label="文件地址" v-if="form.path != ''">
                                     <el-input v-model:trim="form.path" readonly></el-input>
                                </el-form-item>
                                </template>
                                <template v-else>
                                <el-form-item label="文件地址">
                                    <el-input v-model:trim="form.path"></el-input>
                                </el-form-item>
                                </template>
                                </template>
                                <el-form-item label="发布时间">
                                    <el-date-picker v-model="form.gmt_publish_time" type="datetime" placeholder="选择日期时间" default-time="12:00:00">
                                    </el-date-picker>
                                </el-form-item>
                                <el-form-item label="关键字">
                                    <el-input v-model:trim="form.seo_text"></el-input>
                                </el-form-item>
                                <el-form-item label="标签">
                                    <el-tag :key="tag" v-for="tag in form.tag" closable :disable-transitions="false" @close="handleClose(tag)" style="margin-right:10px"> {{tag}} </el-tag>
                                    <el-input class="input-new-tag" v-if="inputVisible" v-model="inputValue" ref="saveTagInput" size="small" @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm" style="width:120px"></el-input>
                                    <el-button v-else class="button-new-tag" size="small" @click="showInput">+ New Tag</el-button>
                                </el-form-item>
                                <el-form-item label="会员权限">
                                    <el-switch v-model="form.is_public" active-color="#13ce66" active-value="0" inactive-value="1"></el-switch>
                                </el-form-item>
                            </el-form>

                            <hr>
                            <el-col :span="24" style="background: #39F;padding:12px 0;color:#FFF">
                                <el-col :span="12" style="padding-left:12px;">
                                    <el-button @click="_cancel">返回列表</el-button>
                                    <el-button @click="saveDraft">保存为草稿</el-button>
                                </el-col>
                                <el-col :span="12" style="text-align:right;padding-right:12px;">
									<el-button type="success"  v-if="hasPubviewPurview == 1 || hasCheckedPurview == 1" @click='saveDraft'>保存</el-button>
									<el-button type="success"   v-if=" hasPubviewPurview != 1"   @click='_sendcheckDialog'>保存并送审</el-button>
									<el-button type="success"   v-if=" hasPubviewPurview == 1"  @click='onSubmit'>保存并发布</el-button> 
									<el-button type="danger"   v-if="checkflowResult.id && hasCheckedPurview == 1 && checkflowResult.tran_status==0"  @click='_checkFailedUI'>退回</el-button> 
                                </el-col>
                            </el-col>
                        </el-row>
						
						<el-dialog
						  title="送审提示"
						  :visible.sync="dialogVisible"
						  width="30%">
						  <div>您正在送审</div>
						  <h3 style="text-indent: 20px;">{{currentNewsTitle}}</h3>
						  <div>请选择审批用户分组：</div>
							  <el-select v-model="selectGroup" placeholder="请选择">
								  <el-option
								    v-for="item in checkGroup"
								    :key="item.id"
								    :label="item.name"
								    :value="item.id">
								</el-option>
							  </el-select>	
											
						  <span slot="footer" class="dialog-footer">
							 <el-button @click="dialogVisible = false">取 消</el-button>
							  <el-button type="primary" @click="_sendcheck()">确 定</el-button>
						  </span>
						</el-dialog>
						
						
						<el-dialog
							  title="审批提示"
							  :visible.sync="failedDialogVisible"
							  width="30%">
							  <div>确定以下内容审批退回？</div>
							  <h3 style="text-indent: 20px;">{{currentNewsTitle}}</h3>
							  <div>退回原因</div>
								 
								 <el-input
								  type="textarea"
								  :rows="2"
								   maxlength="100"
								  placeholder="请输入内容"
								  v-model="tran_note">
								</el-input>
												
							  <span slot="footer" class="dialog-footer">
								 <el-button @click="failedDialogVisible = false">取 消</el-button>
								  <el-button type="primary" @click="_checkFailed()">确 定</el-button>
							  </span>
						</el-dialog>

                        <div class="logshow-wrapper" v-if="logs.list.length > 0">
                        <el-row class="logshow" :class="{on: logshow}">
                            <span class="icon"><i class="el-icon-arrow-up" @click="showLog"></i></span>
                            <ul>
                                <li v-for="(item, index) of logs.list" :key='index'>{{item.time}}, {{item.user_name}}, {{item.log}}</li>
                            </ul>
                            <el-pagination
                              @current-change="logPageChange"
                              :current-page.sync="logs.page"
                              :page-size="logs.limit"
                              layout="total, prev, pager, next"
                              :total="logs.total">
                            </el-pagination>
                        </el-row>
                        </div>
                    </el-main>
            </el-container>
    </el-container>
    <script>
    var main = new Vue({
        el: '#Main',
        data: {
            ticMallHost: '<%- ticMallHost %>',
            uploadData: {
                systemID: 5
            },
            form: {
                id: <%- form.id %>,
                title: `<%- form.title %>`,
                bu_id: '<%- form.bu_id %>',
                tradeList: <%- tradeCataArr %>,
                serviceList: <%- serviceCataArr %>,
                seo_text: '<%- form.seo_text %>',
                path: `<%- form.path %>`,
                gmt_publish_time: '<%- form.gmt_publish_time %>',
                tag: '<%- form.tag %>',
                up_type: <%- form.up_type %>,
                type: <%- form.catalog_id %>,
                is_public: `<%- form.is_public %>`,
                size: `<%- form.size %>`
            },
            tradeList: <%- tradeList %>,
            serviceList: <%- serviceList %>,
            types: <%- typeList %>,
            fileupload: false,
            loading: false,
            tradeDialog: false,
            serviceDialog: false,
            treeset: {
                label: 'name',
                children: 'child'
            },
            inputVisible: false,
            inputValue: '',
            logs: <%- logs %>,
            logshow: false,
			hasPubviewPurview:<%- hasPubviewPurview %>,
			hasCheckedPurview:<%- hasCheckedPurview %>,
			checkGroup:[],
			selectGroup:null,
			currentNewsTitle:'',
			currentId:0,
			loading: false,
			check: true,
			checkIds:[],
			dialogVisible:false,
			failedDialogVisible:false,
			tran_note:'',
			checkflowResult:<%- checkflowResult %>,
            buList: [] // bu列表数据
        },
        methods: {
			_sendcheck(row)
			{
				var that = this;
				if(!this.selectGroup){
					that.loading = false;
				    this.$message.error('请选择审批用户分组');
				    return;
				}
				if(!this.form.type){
				    this.$message.error('请选择资料类型');
				    return;
				}

				if(!this.form.title){
				    this.$message.error('请输入资源名称');
				    return;
                }
                if (!this.form.bu_id) {
                    this.$message.error('请选择BU');
                    return;
                }
				// this.$refs['form'].validate((valid) => {
				//     if (valid) {
				        if(that.form.path == ''){
				            that.$message.error('未上传文件或未添加文件地址');
				            return;
				        }
				        if(that.form.catalog_id == ''){
				            that.$message.error('未选择所属类型');
				            return;
				        }
				        var loading = this.$loading({
				            lock: true,
				            text: 'Loading',
				            spinner: 'el-icon-loading',
				            background: 'rgba(255, 255, 255, 0.7)'
				        });
				
				        var params = that.form;
				        params.is_publish = 0;
				        params._csrf = '<%- csrf %>';
				        axios.post('/resource/save', that.form)
				            .then(function(result) {
				                loading.close();
				                if (result.data.success) {
				                    
				                   var objData={data_type:'res', data_id:that.currentId,data_name:that.currentNewsTitle,receive_groupid:that.selectGroup};
				                   
								   axios.post('/checkflow/apply', objData)
				                   .then(function(result) {
				                   	if (result.data.success) {
				                   		that.loading = false;
				                   		that.dialogVisible = false;
				                   		 if (result.data.success) {
				                   		     that.$message.success('送审成功');
											 window.location.reload();
				                   			 
				                   		}else{
				                   		     if(result.data.needReLogin==1)
				                   		   {
				                   		   	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
				                   		   			window.location.href = result.data.loginUrl;
				                   		   	}});
				                   		   }
				                   		   else
				                   		   {
				                   		   	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
				                   		   			window.location.reload();
				                   		   	}});
				                   		   }
				                   		}
				                   	}
				                   });
								   
				                }else{
				                      if(result.data.needReLogin==1)
				                    {
				                    	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
				                    			window.location.href = result.data.loginUrl;
				                    	}});
				                    }
				                    else
				                    {
				                    	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
				                    			window.location.reload();
				                    	}});
				                    }
				                }
				            });
				    // } else {
				    //     return false;
				    // }
				// });
			},
			_sendcheckDialog()
			{
				
				var that = this;
			
				that.loading = true;
				that.currentId=0;
				that.selectGroup=null;
				
				var postdata= {type:'res', byIdSelect:'1',trade:that.form.tradeList,service:that.form.serviceList  };
							
				axios.post('/role/getRoleList', postdata)
				.then(function(result) {
					if (result.data.success) {
						;
						that.loading = false;
						that.dialogVisible = true;
						that.currentId=that.form.id;
						that.currentNewsTitle=that.form.title;
						that.checkGroup=result.data.data;
					}
					else{
					    that.$message.error('加载错误');
					}
				});
			},
			_checkFailedUI()
			{
				var that = this;
				that.currentNewsTitle=that.form.title;
				that.currentNewsId=that.form.id;
				that.failedDialogVisible = true;
			},
			_checkFailed()
			{
				var that = this;
				
				 this.$confirm('确定以下内容审批退回吗？<br/>'+that.currentNewsTitle, '审批提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning',
				  dangerouslyUseHTMLString:true
				}).then(() => {
					that.loading = true;
					
					axios.post('/dashboard/checkoperator', { id: that.checkflowResult.id,tran_note:that.tran_note,name:that.currentNewsTitle,tran_status:20, _csrf: '' })
					.then(function(result) {
						if (result.data.success) {
							window.location.reload();
						}
						else
						{
							  if(result.data.needReLogin==1)
							{
								that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
										window.location.href = result.data.loginUrl;
								}});
							}
							else
							{
								that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
										window.location.reload();
								}});
							}
						}
					});
				}).catch(e =>{
                    return e;
				});
			},
            showInput() {
                this.inputVisible = true;
                this.$nextTick(_ => {
                    this.$refs.saveTagInput.$refs.input.focus();
                });
            },
			
            handleInputConfirm() {
                let inputValue = this.inputValue;
                if (inputValue) {
                    this.form.tag.push(inputValue);
                }
                this.inputVisible = false;
                this.inputValue = '';
            },
            handleClose(tag) {
                this.form.tag.splice(this.form.tag.indexOf(tag), 1);
            },
            handleChange(file,filelist){
                if(file.size <= 40 * 1024 * 1024){
                    this.fileupload = true;
                }
            },
            handleSuccess(file) {
                this.$message({message:'上传成功',type:'success'});
                this.form.path = file.path;
                
                setTimeout(() =>{
                    this.fileupload = false;
                },100)
            },
            handleProgress(event,file,fileList){
                this.form.size = Number(file.size / 1024 / 1024).toFixed(1);
                if(file.status == 'success'){
                    //
                }
            },
            HandleError(err, file, fileList){
                this.$message({message:'上传失败，请刷新重试',type:'error'});
                this.fileupload = false;
            },
            handleBefore(file){
                if(file.size > 40 * 1024 * 1024){
                    this.$message({message:'文件大小超限制，请更换文件重试',type:'error'});
                    return false;
                }
            },
            handleAvatarSuccess(res, file, fileList) {
                if (res.resultCode === '0') {
                    this.$message({ message: '上传成功', type: 'success' });
                    this.form.path = res.data.fileName;
                    setTimeout(() => {
                        this.fileupload = false;
                    }, 100)
                } else {
                    this.$message.error(res.resultMsg);
                }
            },
            saveDraft(){
                var that = this;
                
                // this.$refs['form'].validate((valid) => {
                    // if (valid) {
                        if(that.form.path == ''){
                            that.$message.error('未上传文件或未添加文件地址');
                            return;
                        }
                        if(that.form.catalog_id == ''){
                            that.$message.error('未选择所属类型');
                            return;
                        }
                        var loading = this.$loading({
                            lock: true,
                            text: 'Loading',
                            spinner: 'el-icon-loading',
                            background: 'rgba(255, 255, 255, 0.7)'
                        });

                        
                        var params = that.form;
                        params.is_publish = 0;
                        params._csrf = '<%- csrf %>';
                        axios.post('/resource/save', that.form)
                            .then(function(result) {
                                loading.close();
                                if (result.data.success) {
                                    //window.location.href = '/sku/service'
                                    window.location.reload();
                                    that.$message({
                                        message: '保存成功。',
                                        type: 'success'
                                    });
                                }else{
                                     if(result.data.needReLogin==1)
                                   {
                                   	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
                                   			window.location.href = result.data.loginUrl;
                                   	}});
                                   }
                                   else
                                   {
                                   	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
                                   			window.location.reload();
                                   	}});
                                   }
                                }
                            });
                    // } else {
                    //     return false;
                    // }
                // });
            },
            onSubmit(){
                var that = this;
                if(!this.form.type){
                    this.$message.error('请选择资料类型');
                    return;
                }
                if(!this.form.title){
                    this.$message.error('请输入资源名称');
                    return;
                }
                if (!this.form.bu_id) {
                    this.$message.error('请选择BU');
                    return;
                }
                // this.$refs['form'].validate((valid) => {
                //     if (valid) {
                        if(that.form.path == ''){
                            that.$message.error('未上传文件或未添加文件地址');
                            return;
                        }
                        if(that.form.catalog_id == ''){
                            that.$message.error('未选择所属类型');
                            return;
                        }
                        var loading = this.$loading({
                            lock: true,
                            text: 'Loading',
                            spinner: 'el-icon-loading',
                            background: 'rgba(255, 255, 255, 0.7)'
                        });

                        var params = that.form;
                        params.is_publish = 1;
                        params._csrf = '<%- csrf %>';
                        axios.post('/resource/save', that.form)
                            .then(function(result) {
                                loading.close();
                                if (result.data.success) {
                                    //window.location.href = '/sku/service'
                                    window.location.reload();
                                    that.$message({
                                        message: '保存成功。',
                                        type: 'success'
                                    });
                                }else{
                                     if(result.data.needReLogin==1)
                                   {
                                   	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
                                   			window.location.href = result.data.loginUrl;
                                   	}});
                                   }
                                   else
                                   {
                                   	that.$alert(result.data.msg,'提示',{confirmButtonText: '确定',callback:action=>{
                                   			window.location.reload();
                                   	}});
                                   }
                                }
                            });
                    // } else {
                    //     return false;
                    // }
                // });
            },
            _default() {
                this.form.id = 0;
                this.form.name = '';
                this.form.alias = '';
            },
            _cancel(){
                <% if(form.is_publish){ %>
                window.location = '/resource/list/';
                <% }else{ %>
                window.location = '/resource/draft/';
                <% } %>
            },
            showLog(){
                if(this.logs.list.length > 1){
                    this.logshow = !this.logshow;
                }
            },
            logPageChange(page){
                axios.post('/logslist',{page:page,type:'resource',id: <%- id %>,limit:10,_csrf:'<%- csrf %>'}).then(res => {
                    let data = res.data;
                    if(data.success){
                        this.logs = data.data;
                    }
                    
                })
            },
            // 获取bu列表
            qryBuList() {
                var that = this;
                axios.post('/setting/buManage/qry', {}).
                    then(function (result) {
                        if (result.data.resultCode === '0') {
                            that.buList = result.data.data.items
                        } else {
                            that.$message.error(result.data.resultMsg);
                        }
                    });
            }
        },
        mounted(){
            this.$nextTick(function(){

                if(this.form.tag){
                    this.form.tag = this.form.tag.split(',');
                }else{
                     this.form.tag = [];
                }

                document.getElementById('preLoading').style.display = 'none';
                this.qryBuList()
                this.form.bu_id = this.form.bu_id > 0 ? Number(this.form.bu_id) : ''
            });
        }
    });
    </script>
    <% include footer.html %>