<% include header.html %>
<el-container id="Main">
    <% include eheader.html %>
    <el-container>
        <% include eside.html %>
        <el-main>
            <el-row>
                <el-col :span="24">
                    <el-breadcrumb separator-class="el-icon-arrow-right">
                        <el-breadcrumb-item>
                            <a href='/'>首页</a>
                        </el-breadcrumb-item>
                        <el-breadcrumb-item>
                            <a href='/case/list'>资讯列表</a>
                        </el-breadcrumb-item>
                        <el-breadcrumb-item>
                            添加案例
                        </el-breadcrumb-item>
                    </el-breadcrumb>
                </el-col>
            </el-row>
            <el-row>
                <h3 class="stit">基本属性</h3>
                <el-form :model="form" label-width="120px" :rules="rules" ref="form">
                    <el-form-item label="类型">
                        <el-select v-model="form.type" placeholder="请选择" @change="cataInfo">
                            <el-option v-for="item in types" :key="item.id" :label="item.name" :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="所属行业">
                        <el-select multiple v-model="form.tradeList" placeholder="请选择" style="width:100%">
                            <el-option v-for="item in tradeList" :value="item.id" :key="item.id" :label="item.name">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="所属服务">
                        <el-select multiple v-model="form.serviceList" placeholder="请选择" style="width:100%">
                            <el-option v-for="item in serviceList" :value="item.id" :key="item.id" :label="item.name">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="BU">
                        <el-select placeholder="请选择" v-model="form.bu_id" style="width:100%" clearable>
                            <el-option v-for="item of buList" :key="item.buId" :label="item.buName" :value="item.buId">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="别名" prop="alias">
                        <el-input v-model:trim="form.alias"></el-input>
                    </el-form-item>
                </el-form>
                <hr>
                <h3 class="stit">案例内容</h3>
                <el-form :model="form" label-width="120px">
                    <el-row>
                        <el-col :span="15">
                            <el-form-item label="名称" prop="name">
                                <el-input v-model:trim="form.title"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="3">
                            <el-form-item label="分类置顶" label-width="80px">
                                <el-switch v-model="form.is_top" active-color="#13ce66"></el-switch>
                            </el-form-item>
                        </el-col>
                        <el-col :span="3">
                            <el-form-item label="首页展示" label-width="80px">
                                <el-switch v-model="form.is_hot" active-value="1" inactive-value="0" active-color="#13ce66"></el-switch>
                            </el-form-item>
                        </el-col>
                        <el-col :span="3">
                          <el-form-item label="会员权限" label-width="80px">
                              <el-switch v-model="form.is_public" active-value="1" inactive-value="0" active-color="#13ce66"></el-switch>
                          </el-form-item>
                      </el-col>
                    </el-row>
                    <el-form-item label="内容">
                        <textarea v-model="form.content" cols="60" rows="5" class="el-input__inner" style="height:600px"
                            id="Content"></textarea>
                    </el-form-item>
                    <el-form-item label="作者" prop="name">
                        <el-input v-model:trim="form.author"></el-input>
                    </el-form-item>
                    <el-form-item label="原创" prop="original">
                        <el-switch v-model="form.original" :active-value='1' :inactive-value='0'>
                        </el-switch>
                    </el-form-item>
                    <el-form-item label="发布时间">
                        <el-date-picker v-model="form.gmt_publish_time" type="datetime" placeholder="选择日期时间"
                            default-time="12:00:00">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="关键字">
                        <el-input v-model:trim="form.seo_text"></el-input>
                    </el-form-item>
                    <el-form-item label="服务推荐">
                        <div>
                          <el-button icon='el-icon-plus' @click='handlerAddRecommend'>添加服务推荐</el-button>
                        </div>
                        <draggable class='card_box' v-model="recommends" group="people" @start="drag=true" @end="drag=false">
                          <el-card :body-style="{ padding: '0px' }" v-for="(item, index) in recommends" :key="item.sku_id">
                            <template v-if='item.sku_id'>
                              <img :src="item.sku_image" class="image">
                              <span>{{ item.sku_name }}</span>
                              <div>{{ item.sub_title }}</div>
                              <div class="bottom clearfix">
                                <el-button type="primary" @click='handlerRecimmendEdit(index)' icon='el-icon-edit' circle>
                                </el-button>
                                <el-button type="danger" @click='handlerRemoveSku(index)' icon='el-icon-delete' circle>
                                </el-button>
                              </div>
                            </template>
                            <template v-else>
                              <el-button icon='el-icon-plus' style='width: 300px; height: 300px;'
                                @click='handlerRecimmendEdit(index)'></el-button>
                            </template>
                          </el-card>
                        </draggable>
                    </el-form-item>
                    <el-form-item label="标签">
                        <el-tag :key="tag" v-for="tag in form.tag" closable :disable-transitions="false"
                            @close="handleClose(tag)" style="margin-right:10px"> {{tag}} </el-tag>
                        <el-input class="input-new-tag" v-if="inputVisible" v-model="inputValue" ref="saveTagInput"
                            size="small" @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm"
                            style="width:120px"></el-input>
                        <el-button v-else class="button-new-tag" size="small" @click="showInput">+ New Tag</el-button>
                    </el-form-item>
                </el-form>
                <hr>
                <h3 class="stit">SEO设置-PC端</h3>
                <el-row :gutter="20">
                    <el-form label-width="120px">
                        <el-form-item label="Title">
                            <el-input v-model="form.page_title" auto-complete="off" maxlength="50"></el-input>
                        </el-form-item>

                        <el-form-item label="Keywords">
                            <el-input v-model="form.page_keywords" auto-complete="off" maxlength="50"></el-input>
                        </el-form-item>

                        <el-form-item label="Description">
                            <el-input v-model="form.page_description" auto-complete="off" maxlength="200"></el-input>
                        </el-form-item>
                    </el-form>
                </el-row>
                <hr>
                <h3 class="stit">SEO设置-移动端<el-button size="small" style="margin-left: 20px;" @click="handleCopyTDK" type="primary">同PC设置</el-button></h3>
                <el-row :gutter="20">
                    <el-form label-width="120px">
                        <el-form-item label="mTitle">
                            <el-input v-model="form.mpage_title" auto-complete="off" maxlength="50"></el-input>
                        </el-form-item>

                        <el-form-item label="mKeywords">
                            <el-input v-model="form.mpage_keywords" auto-complete="off" maxlength="50"></el-input>
                        </el-form-item>

                        <el-form-item label="mDescription">
                            <el-input v-model="form.mpage_description" auto-complete="off" maxlength="200"></el-input>
                        </el-form-item>
                    </el-form>
                </el-row>
                <hr>
                <el-col :span="24" style="background: #39F;padding:12px 0;color:#FFF">
                    <el-col :span="12" style="padding-left:12px;">
                        <el-button @click="_cancel">返回列表</el-button>
                        <el-button @click="saveDraft">保存为草稿</el-button>
                    </el-col>
                    <el-col :span="12" style="text-align:right;padding-right:12px;">
                        <el-button type="" @click="preview"> 预 览 </el-button>
                        <el-button type="success" @click='onSubmitDraft'>保存</el-button>
                        <el-button type="success" v-if=" hasPubviewPurview != 1" @click='_sendcheckDialog'>保存并送审
                        </el-button>
                        <el-button type="success" v-if="hasPubviewPurview == 1" @click='onSubmit'>保存并发布</el-button>
                    </el-col>
                </el-col>
            </el-row>

            <el-dialog title="送审提示" :visible.sync="dialogVisible" width="30%">
                <div>您正在送审</div>
                <h3 style="text-indent: 20px;">{{currentNewsTitle}}</h3>
                <div>请选择审批用户分组：</div>
                <el-select v-model="selectGroup" placeholder="请选择">
                    <el-option v-for="item in checkGroup" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                </el-select>

                <span slot="footer" class="dialog-footer">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="_sendcheck()">确 定</el-button>
                </span>
            </el-dialog>

            <el-dialog title="添加/编辑SKU" :visible.sync="caseDialog.dialogStatus" width="600px">
                <el-row>
                  <el-form :model="form" label-width="70px" :rules="rules">
                    <el-form-item label="URL">
                      <el-col :span='20'>
                        <el-input v-model='caseDialog.sku_url' placeholder="请输入url地址"></el-input>
                      </el-col>
                      <el-col :span='4' style='text-align: right;'>
                        <el-button type="primary" @click='handlerGetSku'>查找</el-button>
                      </el-col>
                    </el-form-item>
                    <template v-if='caseDialog.sku_name'>
                      <el-form-item label="图片">
                        <img :src='caseDialog.sku_image' style="max-width: 510px;" />
                      </el-form-item>
                      <el-form-item label="主标题">
                        <el-input v-model='caseDialog.sku_name' :disabled="true"></el-input>
                      </el-form-item>
                      <el-form-item label="副标题">
                        <el-input v-model='caseDialog.sub_title' :disabled="true"></el-input>
                      </el-form-item>
                    </template>
                  </el-form>
                </el-row>
                <span slot="footer" class="dialog-footer" v-if='caseDialog.sku_name'>
                  <el-button type="primary" @click='handlerSetSku'>确 定</el-button>
                </span>
            </el-dialog>
        </el-main>
    </el-container>
</el-container>
<script src="/static/tinymce/tinymce.min.js"></script>
<script>
    var ai = 0, bi = 0;
    var tinyConfig = {
        height: 600,
        language: 'zh_CN',
        menubar: false,
        plugins: 'advlist autolink link image lists charmap print preview code textcolor table paste media',
        toolbar: 'undo redo | formatselect fontsizeselect bold italic underline forecolor backcolor  | alignleft aligncenter alignright alignjustify superscript subscript | bullist numlist outdent indent | removeformat | image media link table | code preview',
        fontsize_formats: '12px 14px 16px 18px 20px 24px 36px',
        file_browser_callback_types: 'image',
        // images_upload_url: '<%- ticMallHost %>ticCenter/business/api.v0.platform/fileUpload/uploadOss',
        images_reuse_filename: true,
        // images_upload_base_path: '/',
        relative_urls: false,
        branding: false,
        width: '100%',
        images_upload_handler: function(blobInfo, success, failure) {
            let that = this
            let fd = new FormData()
            let file  = blobInfo.blob();
            fd.append('file', file, file.name)
            fd.append('systemID', 5);
            let config = {
               headers: { 'Content-Type': 'multipart/form-data' }
            }
            axios.post('<%- ticMallHost %>ticCenter/business/api.v0.platform/fileUpload/uploadOss', fd, config)
                .then(function (result) {
                    if (result.data.resultCode === '0') {
                        success(result.data.data.fileName)
                    } else {
                        that.$message.success(result.data.res);
                        failure()
                    }
                });
        }
    };
    var main = new Vue({
        el: '#Main',
        data: {
            form: {
                id: 0,
                title: '',
                alias: 'article',
                bu_id: '',
                tradeList: [],
                serviceList: [],
                content: '',
                seo_text: '',
                author: '',
                original: '0',
                gmt_publish_time: '',
                tag: [],
                is_publish: false,
                is_top: false,
                is_hot: '0',
                type: '',
                page_title: '',
                page_keywords: '',
                page_description: '',
                mpage_title: '',
                mpage_keywords: '',
                mpage_description: '',
                catalog: '',
                recommends: [],
            },
            tradeList: <%- tradeList %>,
            serviceList: <%- serviceList %>,
            types: <%- typeList %>,
            loading: false,
            tradeDialog: false,
            serviceDialog: false,
            treeset: {
                label: 'name',
                children: 'child'
            },
            inputVisible: false,
            inputValue: '',
            rules: {
                name: [
                    { required: true, message: '请输入名称', trigger: 'blur' },
                    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
                    { validator: nameCheck, trigger: 'blur' },
                ],
            },
            hasPubviewPurview:<%- hasPubviewPurview %>,
            hasCheckedPurview:<%- hasCheckedPurview %>,
            checkGroup: [],
            selectGroup: null,
            dialogVisible: false,
            currentNewsTitle: '',
            buList: [], // bu列表数据
            caseDialog: {
                dialogStatus: false,
                sku_url: '',
                sku_image: '',
                sku_name: '',
                sub_title: '',
                sku_id: ''
            },
            recommends: [],
        },
        methods: {
            handleCopyTDK(){
                this.form.mpage_title = this.form.page_title;
                this.form.mpage_keywords = this.form.page_keywords;
                this.form.mpage_description = this.form.page_description;
            },
            handlerRecimmendEdit(index) {
                this.recommendIndex = index;
                // 有数据的编辑
                if (this.recommends[index].sku_id) {
                    this.caseDialog.sku_id = this.recommends[index].sku_id
                    this.caseDialog.sku_image = this.recommends[index].sku_image
                    this.caseDialog.sku_name = this.recommends[index].sku_name
                    this.caseDialog.sub_title = this.recommends[index].sub_title;
                    this.caseDialog.sku_url = this.recommends[index].sku_url
                } else {
                    // 无数据的编辑
                    this.caseDialog.sku_id = ''
                    this.caseDialog.sku_image = ''
                    this.caseDialog.sku_name = ''
                    this.caseDialog.sub_title = ''
                    this.caseDialog.sku_url = ''
                }
                this.caseDialog.dialogStatus = true
            },
            // 删除单个推荐
            handlerRemoveSku(index) {
                this.$confirm('是否删除?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.recommends.splice(index, 1);
                }).catch(e => {
                    return e;
                });
            },
            // 添加一个空的占位推荐
            handlerAddRecommend() {
                // const flag = this.recommends.some(item => {
                //     return !item.sku_id;
                // })
                // const flag1 = this.recommends.length < 3
                // if (flag) {}

                if (this.recommends.length < 3) {
                    this.recommends.push({});
                } else {
                    this.$message.error('最多添加3条')
                }
            },
            // 获取到的数据添加到渲染源数组内
            handlerSetSku() {
                const item = {
                    sku_image: this.caseDialog.sku_image,
                    sku_name: this.caseDialog.sku_name,
                    sub_title: this.caseDialog.sub_title,
                    sku_id: this.caseDialog.sku_id,
                    sku_url: this.caseDialog.sku_url
                }
                this.recommends[this.recommendIndex] = item;
                this.caseDialog.dialogStatus = false;
            },
            // 根据url获取数据
            handlerGetSku() {
                if (this.caseDialog.sku_url) {
                    var that = this;
                    axios.post('/homepage/getSku', { skuUrl: that.caseDialog.sku_url, _csrf: '<%- csrf %>' })
                    .then(function (result) {
                        if (result.data.success) {
                            that.caseDialog.sku_image = result.data.data.thumb_img;
                            that.caseDialog.sku_name = result.data.data.name;
                            that.caseDialog.sub_title = result.data.data.sub_title;
                            that.caseDialog.sku_id = result.data.data.id;
                            that.caseDialog = JSON.parse(JSON.stringify(that.caseDialog))
                        } else {
                        that.$message.error(result.data.msg);
                        }
                    }).catch(e => {
                        that.$message.error(e);
                    });
                } else {
                    that.$message.error('请输入url地址。');
                }
            },
            _sendcheck() {
                var that = this;
                this.form.recommends = this.recommends
                if (!this.recommends.every(v => v.sku_url))  {
                    that.loading = false;
                  this.$message.error('服务推荐内容不能为空');
                  return;
                }
                if (!this.selectGroup) {
                  that.loading = false;
                  this.$message.error('请选择审批用户分组');
                  return;
                }
                if (!this.form.tradeList && !this.form.serviceList) {
                  that.loading = false;
                  this.$message.error('请选择所属行业或所属服务');
                  return;
                }
                if (!this.form.type) {
                  this.$message.error('请选择资讯类型');
                  return;
                }
                if (!this.form.title) {
                    this.$message.error('请输入案例名称');
                    return;
                }
                if (!this.form.bu_id) {
                    this.$message.error('请选择BU');
                    return;
                }
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        var loading = this.$loading({
                            lock: true,
                            text: 'Loading',
                            spinner: 'el-icon-loading',
                            background: 'rgba(255, 255, 255, 0.7)'
                        });

                        var params = that.form;
                        params.is_publish = 0;
                        params.content = tinymce.get('Content').getContent();
                        params._csrf = '<%- csrf %>';
                        // 添加类型的别名参数
                        this.form.catalogType =  this.types.find(v => v.id === this.form.type).alias
                        axios.post('/case/save', that.form)
                            .then(function (result) {
                                loading.close();
                                if (result.data.success) {
                                    var url = '/case/edit/backup/' + result.data.id;

                                    var objData = { data_type: 'case', data_id: result.data.id, data_name: that.form.title, receive_groupid: that.selectGroup };
                                    axios.post('/checkflow/apply', objData)
                                        .then(function (result) {
                                            if (result.data.success) {
                                                that.loading = false;
                                                that.dialogVisible = false;
                                                if (result.data.success) {
                                                    that.$message.success('送审成功');
                                                    window.location.href = url;
                                                }
                                                else {
                                                    if (result.data.needReLogin == 1) {
                                                        that.$alert(result.data.msg, '提示', {
                                                            confirmButtonText: '确定', callback: action => {
                                                                window.location.href = result.data.loginUrl;
                                                            }
                                                        });
                                                    }
                                                    else {
                                                        that.$alert(result.data.msg, '提示', {
                                                            confirmButtonText: '确定', callback: action => {
                                                                window.location.reload();
                                                            }
                                                        });
                                                    }
                                                }
                                            }
                                        });

                                } else {
                                    if (result.data.needReLogin == 1) {
                                        that.$alert(result.data.msg, '提示', {
                                            confirmButtonText: '确定', callback: action => {
                                                window.location.href = result.data.loginUrl;
                                            }
                                        });
                                    }
                                    else {
                                        that.$message.error(result.data.msg);
                                    }

                                }
                            });
                    } else {
                        return false;
                    }
                });
            },
            _sendcheckDialog() {
                var that = this;
                that.loading = true;
                that.currentId = 0;
                that.selectGroup = null;

                var postdata = { type: 'case', byIdSelect: '1', trade: that.form.tradeList, service: that.form.serviceList };

                axios.post('/role/getRoleList', postdata)
                    .then(function (result) {
                        if (result.data.success) {
                            that.loading = false;
                            that.dialogVisible = true;
                            that.currentId = that.form.id;
                            that.currentNewsTitle = that.form.title;
                            that.checkGroup = result.data.data;
                        }
                        else {
                            that.$message.error('加载错误');
                        }
                    });
            },
            showInput() {
                this.inputVisible = true;
                this.$nextTick(_ => {
                    this.$refs.saveTagInput.$refs.input.focus();
                });
            },
            handleInputConfirm() {
                let inputValue = this.inputValue;
                if (inputValue) {
                    this.form.tag.push(inputValue);
                }
                this.inputVisible = false;
                this.inputValue = '';
            },
            handleClose(tag) {
                this.form.tag.splice(this.form.tag.indexOf(tag), 1);
            },
            saveDraft() {
                var that = this;
                this.form.recommends = this.recommends
                if (!this.recommends.every(v => v.sku_url))  {
                  this.$message.error('服务推荐内容不能为空');
                  return;
                }
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        var loading = this.$loading({
                            lock: true,
                            text: 'Loading',
                            spinner: 'el-icon-loading',
                            background: 'rgba(255, 255, 255, 0.7)'
                        });

                        var params = that.form;
                        params.is_publish = 0;
                        params.content = tinymce.get('Content').getContent();
                        params._csrf = '<%- csrf %>';
                        axios.post('/case/save', that.form)
                            .then(function (result) {
                                loading.close();
                                loading.close();
                                if (result.data.success) {
                                    //window.location.href = '/sku/service'
                                    that.$message({
                                        message: '保存成功。',
                                        type: 'success'
                                    });
                                } else {

                                    if (result.data.needReLogin == 1) {
                                        that.$alert(result.data.msg, '提示', {
                                            confirmButtonText: '确定', callback: action => {
                                                window.location.href = result.data.loginUrl;
                                            }
                                        });
                                    }
                                    else {
                                        that.$message.error(result.data.msg);
                                    }

                                }
                            });
                    } else {
                        return false;
                    }
                });
            },
            onSubmitDraft() {
                var that = this;
                this.form.recommends = this.recommends
                if (!this.recommends.every(v => v.sku_url))  {
                  this.$message.error('服务推荐内容不能为空');
                  return;
                }
                if (!this.form.type) {
                    this.$message.error('请选择资讯类型');
                    return;
                }
                

                if (!this.form.title) {
                    this.$message.error('请输入案例名称');
                    return;
                }
                if (!this.form.bu_id) {
                    this.$message.error('请选择BU');
                    return;
                }
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        var loading = this.$loading({
                            lock: true,
                            text: 'Loading',
                            spinner: 'el-icon-loading',
                            background: 'rgba(255, 255, 255, 0.7)'
                        });

                        var params = that.form;
                        params.is_publish = 0;
                        params.content = tinymce.get('Content').getContent();
                        params._csrf = '<%- csrf %>';
                        axios.post('/case/save', that.form)
                            .then(function (result) {
                                loading.close();
                                if (result.data.success) {
                                    window.location.href = '/case/edit/backup/' + result.data.id;
                                    that.$message({
                                        message: '保存成功。',
                                        type: 'success'
                                    });
                                } else {
                                    if (result.data.needReLogin == 1) {
                                        that.$alert(result.data.msg, '提示', {
                                            confirmButtonText: '确定', callback: action => {
                                                window.location.href = result.data.loginUrl;
                                            }
                                        });
                                    }
                                    else {
                                        that.$message.error(result.data.msg);
                                    }
                                }
                            });
                    } else {
                        return false;
                    }
                });
            },
            onSubmit() {
                this.form.recommends = this.recommends
                if (!this.recommends.every(v => v.sku_url))  {
                  this.$message.error('服务推荐内容不能为空');
                  return;
                }
                // 别名传递到solr
                let catalogArr = []
                this.tradeList.forEach(i => {
                    this.form.tradeList.forEach(j => {
                        if (i.id === j) {
                            catalogArr.push(i.alias)
                        }
                    })
                })
                this.serviceList.forEach(i => {
                    this.form.serviceList.forEach(j => {
                        if (i.id === j) {
                            catalogArr.push(i.alias)
                        }
                    })
                })
                this.form.catalog = catalogArr.join(',')

                var that = this;

                if (!this.form.type) {
                    this.$message.error('请选择资讯类型');
                    return;
                }
                
                if (!this.form.tradeList.length && !this.form.serviceList.length) {
                  that.loading = false;
                  this.$message.error('请选择所属行业或所属服务');
                  return;
                }

                if (!this.form.title) {
                    this.$message.error('请输入案例名称');
                    return;
                }
                if (!this.form.bu_id) {
                    this.$message.error('请选择BU');
                    return;
                }
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        var loading = this.$loading({
                            lock: true,
                            text: 'Loading',
                            spinner: 'el-icon-loading',
                            background: 'rgba(255, 255, 255, 0.7)'
                        });

                        var params = that.form;
                        params.is_publish = 1;
                        params.content = tinymce.get('Content').getContent();
                        params._csrf = '<%- csrf %>';
                        axios.post('/case/save', that.form)
                            .then(function (result) {
                                loading.close();
                                if (result.data.success) {
                                    window.location.href = '/case/edit/backup/' + result.data.id;
                                    that.$message({
                                        message: '保存成功。',
                                        type: 'success'
                                    });
                                } else {
                                    if (result.data.needReLogin == 1) {
                                        that.$alert(result.data.msg, '提示', {
                                            confirmButtonText: '确定', callback: action => {
                                                window.location.href = result.data.loginUrl;
                                            }
                                        });
                                    }
                                    else {
                                        that.$message.error(result.data.msg);
                                    }
                                }
                            });
                    } else {
                        return false;
                    }
                });
            },
            _default() {
                this.form.id = 0;
                this.form.name = '';
                this.form.alias = '';
            },
            _cancel() {
                window.location = '/case/list/';
            },
            preview() {
                var that = this;

                that.content = tinymce.get('Content').getContent();
                that.types.forEach(item => {
                    if (item.id == that.form.type) {
                        that.form.catalog_name = item.name;
                    }
                });
                var params = that.form;
                params._csrf = '<%- csrf %>';
                axios.post('/preview/precase', params).
                    then(function (result) {
                        if (result.data.success) {
                            window.open('/preview/precase/' + result.data.data);
                        }
                    });
            },
            cataInfo(v) {
                this.types.forEach(item => {
                    if (item.id == v) {
                        this.form.catalog_name = item.name;
                    }
                })
            },
            // 获取bu列表
            qryBuList() {
                var that = this;
                axios.post('/setting/buManage/qry', {}).
                    then(function (result) {
                        if (result.data.resultCode === '0') {
                            that.buList = result.data.data.items
                        } else {
                            that.$message.error(result.data.resultMsg);
                        }
                    });
            }
        },
        mounted() {
            this.$nextTick(function () {
                tinyConfig.selector = '#Content';
                tinymce.init(tinyConfig);

                var that = this;
                that.tradeList.forEach(function (item, i) {
                    if (item.child.length == 0) {
                        that.tradeList.splice(i, 1);
                    }
                });

                that.serviceList.forEach(function (item, i) {
                    if (item.child.length == 0) {
                        that.serviceList.splice(i, 1);
                    }
                });

                document.getElementById('preLoading').style.display = 'none';
                this.qryBuList()
            });
        }
    });
</script>
<style>
    .floor h3 {
      display: inline-block;
    }

    .card_box .el-card {
      float: left;
      width: 300px;
      margin: 10px 10px 0 0;
    }

    .card_box .el-card img {
      width: 300px;
      height: 200px;
    }

    .card_box .el-card span,
    .card_box .el-card div {
      padding: 0 15px;
    }

    .card_box .el-card .bottom {
      text-align: right;
    }

    .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .avatar-uploader .el-upload:hover {
      border-color: #409EFF;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      text-align: center;
    }

    .avatar {
      display: block;
    }

    .box1 .avatar-uploader-icon,
    .box1 .avatar {
      width: 858px;
      height: 360px;
      line-height: 360px;
    }

    .box2 .avatar-uploader-icon,
    .box2 .avatar {
      width: 858px;
      height: 290px;
      line-height: 290px;
    }

    .box3 .avatar-uploader-icon,
    .box3 .avatar {
      width: 284px;
      height: 142px;
      line-height: 142px;
    }

    .box4 .avatar-uploader-icon,
    .box4 .avatar {
      width: 750px;
      height: 360px;
      line-height: 360px;
    }

    .box5 .avatar-uploader-icon,
    .box5 .avatar {
      width: 214px;
      height: 110px;
      line-height: 110px;
    }

    .content_mark {
      z-index: 2;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, .3);
    }
</style>
<% include footer.html %>