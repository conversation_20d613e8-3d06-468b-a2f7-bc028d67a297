<% include header.html %>
<el-container id="Main">
    <% include eheader.html %>
    <el-container>
        <% include eside.html %>
        <el-main>
            <el-row>
                <el-col :span="24">
                    <el-breadcrumb separator-class="el-icon-arrow-right">
                        <el-breadcrumb-item>
                            <a href='/'>首页</a>
                        </el-breadcrumb-item>
                        <el-breadcrumb-item>
                            资料下载统计
                        </el-breadcrumb-item>
                    </el-breadcrumb>
                </el-col>
            </el-row>
            <el-row v-loading="loading">
                <el-col :span="24">
                    <el-form :inline="true" :model="form">
                        <el-form-item>
                            <el-date-picker v-model="form.time" type="datetimerange" range-separator="至"
                                start-placeholder="开始时间" end-placeholder="结束时间" :picker-options="pickerOptions2">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="getList">查询</el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="reset">重置</el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="success" @click="_export">导出</el-button>
                        </el-form-item>
                    </el-form>
                </el-col>
                <el-table :data="tableList.list" stripe style="width: 100%">
                    <el-table-column prop="id" label="序列号">
                    </el-table-column>
                    <el-table-column prop="time" label="下载时间">
                    </el-table-column>
                    <el-table-column prop="file_name" label="资料名称">
                    </el-table-column>
                    <el-table-column prop="file_type" label="资料类型">
                    </el-table-column>
                    <el-table-column prop="group" label="编辑分组">
                    </el-table-column>
                    <el-table-column prop="user_name" label="用户姓名">
                    </el-table-column>
                    <el-table-column prop="user_phone" label="用户电话">
                    </el-table-column>
                    <el-table-column prop="user_email" label="用户E-mail">
                    </el-table-column>
                </el-table>
                <el-pagination @current-change="handleCurrentChange" background :current-page.sync="form.page"
                    :page-size="form.limit" layout="total, prev, pager, next" :total="tableList.total"
                    style="padding:10px 0;text-align: right;">
                </el-pagination>
            </el-row>
        </el-main>
    </el-container>
</el-container>
<script>
    var main = new Vue({
        el: '#Main',
        data: {
            tableList: {
                list: <%- list %>,
                total: <%- total %>,
            },
            form: {
                page: 1,
                limit: 10,
                time: '',
            },
            loading: false,
            pickerOptions2: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
        },
        methods: {
            handleCurrentChange(r) {
                this.getList();
            },
            getList() {
                var that = this;
                that.loading = true;
                var params = this.form;
                params._csrf = '<%- csrf %>';
                axios.post('/analysis/download/qry', params)
                    .then(function (result) {
                        that.loading = false;
                        if (result.data.success) {
                            that.tableList = result.data.data;
                        } else {
                            that.$message.error('获取列表出错');
                        }
                    });
            },
            reset() {
                this.form.time = ''
                this.getList()
            },
            _export() {
                var params = this.form;
                var that = this;
                that.loading = true;
                params._csrf = '<%- csrf %>';
                axios.post('/analysis/download/export', params)
                    .then(function (result) {
                        that.loading = false;
                        if (result.data.success) {
                            window.location = result.data.data;
                        }
                    });
            }
        },
        mounted() {
            this.$nextTick(function () {
                document.getElementById('preLoading').style.display = 'none';
            });
        }
    });
</script>
<% include footer.html %>