'use strict';

const Controller = require('egg').Controller;
const fs = require('fs');
const moment = require('moment');

function subString(str, n) {
  var r = /[^\x00-\xff]/g;
  var m;

  if (str.replace(r, '**').length > n) {
    m = Math.floor(n / 2);

    for (var i = m, l = str.length; i < l; i++) {
      if (str.substr(0, i).replace(r, '**').length >= n) {
        var newStr = str.substr(0, i) + '...';
        return newStr;
      }
    }
  }

  return str;
}


class PreviewController extends Controller {
  async sku() {
    const { ctx } = this;

    const id = ctx.params.id;
    const detail = await ctx.service.sku.getInfo(id);

    if (detail.detail.is_use == 0) {
      ctx.body = '已下架';
      return;
    }

    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();
    const tagAdpt = await ctx.service.sku.tagAdpt(id);

    const class_info = JSON.parse(detail.detail.class_info);
    if (class_info) {
      await ctx.render('preview/detail', {
        detail: detail.detail,
        tradeNavi: tradeNavi.list,
        serviceNavi: serviceNavi.list,
        tagAdpt: tagAdpt.list,
        class_infoTime: class_info.time || '',
        class_infoArea: class_info.area || '',
        class_infoDay: class_info.days || '',
      })
    } else {
      await ctx.render('preview/detail', {
        detail: detail.detail,
        tradeNavi: tradeNavi.list,
        serviceNavi: serviceNavi.list,
        tagAdpt: tagAdpt.list,
      })
    }

  }

  async presku() {
    const { ctx } = this;

    const params = ctx.request.body;

    var str = "",
      range = 8,
      arr = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

    let pos = '';
    for (var i = 0; i < range; i++) {
      pos = Math.round(Math.random() * (arr.length - 1));
      str += arr[pos];
    }

    const path = './tmp/' + str + '.json';
    fs.writeFileSync(path, JSON.stringify(params), 'utf8');

    ctx.body = { success: true, data: str };
  }

  async preskuView() {
    const { ctx } = this;

    const name = ctx.params.name;

    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();

    const detail = fs.readFileSync('./tmp/' + name + '.json', 'utf8');

    await ctx.render('preview/detail_preview', {
      detail: JSON.parse(detail),
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
    })
  }

  async newsList() {
    const { ctx } = this;

    const params = { is_publish: 1 };
    const detail = await ctx.service.news.getList(params);
    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();


    detail.list.forEach(item => {
      item.time = item.time.split(' ')[0];
    });

    await ctx.render('preview/newsList', {
      detail: detail.list,
      total: detail.total,
      page: detail.page,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
    })

  }

  async newsDetail() {
    const { ctx } = this;

    const id = ctx.params.id;
    const detail = await ctx.service.news.getDetail(id);
    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();

    if (detail.detail.is_publish == 0) {
      ctx.body = '新闻不存在';
      return;
    }

    const relate = await ctx.service.news.getNewsRelate(id);
    const otherNews = await ctx.service.news.otherNews(id);

    detail.detail.gmt_publish_time = moment(detail.detail.gmt_publish_time).format('YYYY年MM月DD日');
    detail.detail.catalog_name = await ctx.service.catalog.getCataName(detail.detail.catalog_id);
    detail.detail.name = detail.detail.title;

    otherNews.list.some(item => {
      item.content = subString(item.content.replace(/<.*?>/g, ""), 80);
    });

    await ctx.render('preview/news', {
      detail: detail.detail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      prevNews: relate.prevItem[0],
      nextNews: relate.nextItem[0],
      otherNews: otherNews.list,
    })

  }

  async prenews() {
    const { ctx } = this;

    const params = ctx.request.body;

    var str = "",
      range = 8,
      arr = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

    let pos = '';
    for (var i = 0; i < range; i++) {
      pos = Math.round(Math.random() * (arr.length - 1));
      str += arr[pos];
    }

    const path = './tmp/' + str + '.json';
    fs.writeFileSync(path, JSON.stringify(params), 'utf8');

    ctx.body = { success: true, data: str };
  }

  async prenewsView() {
    const { ctx } = this;

    const name = ctx.params.name;

    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();

    const detail = JSON.parse(fs.readFileSync('./tmp/' + name + '.json', 'utf8'));

    detail.gmt_publish_time = moment(detail.gmt_publish_time).format('YYYY年MM月DD日');
    detail.name = detail.title;
    await ctx.render('preview/news_preview', {
      detail: detail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
    })
  }

  async caseList() {
    const { ctx } = this;

    const params = { is_publish: 1 };
    const detail = await ctx.service.case.getList(params);
    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();

    detail.list.forEach(item => {
      item.time = item.time.split(' ')[0];
    });
    await ctx.render('preview/caseList', {
      detail: detail.list,
      total: detail.total,
      page: detail.page,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
    })

  }

  async caseDetail() {
    const { ctx } = this;

    const id = ctx.params.id;
    const detail = await ctx.service.case.getDetail(id);
    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();

    if (detail.detail.is_publish == 0) {
      ctx.body = '案例不存在';
      return;
    }
    const relate = await ctx.service.case.getCaseRelate(id);
    const otherCase = await ctx.service.case.otherCase(id, detail.detail.catalog_id);

    detail.detail.gmt_publish_time = moment(detail.detail.gmt_publish_time).format('YYYY年MM月DD日');
    detail.detail.catalog_name = await ctx.service.catalog.getCataName(detail.detail.catalog_id);
    detail.detail.name = detail.detail.title;

    otherCase.list.some(item => {
      item.content = subString(item.content.replace(/<.*?>/g, ""), 80);
    });

    await ctx.render('preview/case', {
      detail: detail.detail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      prevCase: relate.prevItem[0],
      nextCase: relate.nextItem[0],
      otherCase: otherCase.list,
    })

  }

  async precase() {
    const { ctx } = this;

    const params = ctx.request.body;

    var str = "",
      range = 8,
      arr = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

    let pos = '';
    for (var i = 0; i < range; i++) {
      pos = Math.round(Math.random() * (arr.length - 1));
      str += arr[pos];
    }

    const path = './tmp/' + str + '.json';
    fs.writeFileSync(path, JSON.stringify(params), 'utf8');

    ctx.body = { success: true, data: str };
  }

  async precaseView() {
    const { ctx } = this;

    const name = ctx.params.name;

    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();

    const detail = JSON.parse(fs.readFileSync('./tmp/' + name + '.json', 'utf8'));

    detail.gmt_publish_time = moment(detail.gmt_publish_time).format('YYYY年MM月DD日');
    detail.name = detail.title;
    await ctx.render('preview/case_preview', {
      detail: detail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
    })
  }

  //catalog
  async tradeDetail() {
    const { ctx } = this;
    const alias = ctx.params.id;

    const detail = await ctx.service.catalog.getCatalogInfoByAlias(alias);

    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();

    await ctx.render('preview/homepage', {
      detail: detail.info,
      banner: detail.banner,
      children: detail.children,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      solution: detail.solution,
      cases: detail.cases,
      resource: detail.resource,
    });

  }

  async tradeSkus() {
    const { ctx } = this;
    const alias = ctx.params.id;
    const params = ctx.request.query;

    const name = params.name || '',
      page = params.page || 1;
    let id = params.id;



    const detail = await ctx.service.catalog.getCatalogInfoByAlias(alias);
    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();

    if (!id || id == '') {
      id = detail.info.id;
    }

    //const skuList = await ctx.service.sku.getSkuByCata({ id: id, page: page, limit: 5,name: name });

    await ctx.render('preview/serverlist', {
      detail: detail.info,
      banner: detail.banner,
      children: detail.children,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      solution: detail.solution,
      //skuList: skuList.list,
      page: page,
      csrf: ctx.csrf,
      id: id,
      name: name,
    });
  }

  async getCataSkus() {
    const { ctx } = this;
    const params = ctx.request.query;
    const id = params.id,
      page = params.page || 1,
      name = params.name || '';

    const skuList = await ctx.service.sku.getSkuByCata({ id: id, page: page, limit: 5, name: name });

    ctx.body = { success: true, data: skuList.list, total: skuList.total, page: skuList.page };
  }

  async precata() {
    const { ctx } = this;

    const params = ctx.request.body;

    var str = "",
      range = 8,
      arr = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

    let pos = '';
    for (var i = 0; i < range; i++) {
      pos = Math.round(Math.random() * (arr.length - 1));
      str += arr[pos];
    }

    const path = './tmp/' + str + '.json';
    fs.writeFileSync(path, JSON.stringify(params), 'utf8');

    ctx.body = { success: true, data: str };
  }

  async precataView() {
    const { ctx } = this;

    const name = ctx.params.name;

    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();

    const detail = fs.readFileSync('./tmp/' + name + '.json', 'utf8');

    const jdetail = JSON.parse(detail);
    const alias = jdetail.form.alias;
    const tdetail = await ctx.service.catalog.getCatalogInfoByAlias(alias);

    await ctx.render('preview/home_preview', {
      detail: jdetail.form,
      banner: jdetail.banner,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      children: tdetail.children,
      solution: tdetail.solution,
      cases: tdetail.cases,
      resource: tdetail.resource,
    })
  }

  async other() {
    const { ctx } = this;

    const alias = ctx.params.alias;

    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();

    const detail = await ctx.service.other.getDetailByAlias(alias);


    await ctx.render('preview/article', {
      detail: detail.detail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list
    })
  }

  async preother() {
    const { ctx } = this;

    const params = ctx.request.body;

    var str = "",
      range = 8,
      arr = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

    let pos = '';
    for (var i = 0; i < range; i++) {
      pos = Math.round(Math.random() * (arr.length - 1));
      str += arr[pos];
    }

    const path = './tmp/' + str + '.json';
    fs.writeFileSync(path, JSON.stringify(params), 'utf8');

    ctx.body = { success: true, data: str };
  }

  async preotherView() {
    const { ctx } = this;

    const name = ctx.params.name;

    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();

    const detail = fs.readFileSync('./tmp/' + name + '.json', 'utf8');

    await ctx.render('preview/article', {
      detail: JSON.parse(detail),
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list
    })
  }

  async prehome() {
    const { ctx } = this;

    const params = ctx.request.body;

    var str = "",
      range = 8,
      arr = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

    let pos = '';
    for (var i = 0; i < range; i++) {
      pos = Math.round(Math.random() * (arr.length - 1));
      str += arr[pos];
    }

    const path = './tmp/' + str + '.json';
    fs.writeFileSync(path, JSON.stringify(params), 'utf8');

    ctx.body = { success: true, data: str };
  }

  async prehomeView() {
    const { ctx } = this;

    const name = ctx.params.name;

    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();

    const detail = fs.readFileSync('./tmp/' + name + '.json', 'utf8');

    let outDetail = JSON.parse(detail);
    outDetail.name = '首页';
    await ctx.render('preview/index', {
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list
    })
  }

  async homepage() {
    const { ctx } = this;

    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();

    const detail = fs.readFileSync('./config/homepage.json', 'utf8');

    let outDetail = JSON.parse(detail);
    outDetail.name = '首页';
    await ctx.render('preview/index', {
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list
    })
  }

  async search() {
    const { ctx } = this;

    const params = ctx.request.query;

    const result = await ctx.service.search.getResult(params);
    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();
    const hotsku = await ctx.service.search.hotsku();

    for (let item of result.list) {
      item.title = item.title.replace(new RegExp(params.q, 'gmi'), '<span class="keyWord">' + params.q + '</span>');
    }

    await ctx.render('preview/search1', {
      list: result.list,
      total: result.total,
      catas: result.catas,
      types: result.types,
      cataTypes: result.cataTypes,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      keyword: params.q,
      page: params.page || 1,
      detail: {
        title: params.q
      },
      type: params.type,
      pid: params.pid,
      hot: hotsku.list,
    })
  }

  async ticket() {
    const { ctx } = this;

    const params = ctx.request.query;

    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();
    const hotsku = await ctx.service.search.hotsku();

    await ctx.render('preview/ticket', {
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      hot: hotsku.list,
      detail: {
        title: '咨询留言'
      },
      csrf: ctx.csrf,
    })
  }
}

module.exports = PreviewController;