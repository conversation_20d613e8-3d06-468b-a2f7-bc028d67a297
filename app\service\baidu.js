const Service = require('egg').Service;
const http = require('http');
const axios = require('axios');
const { env } = require('../../config/info').siteInfo;
class Baidu extends Service {
  async Push(content) {
    const url = env[this.app.config.env].view_url + content
    const token = 'gQTyuw96Vanq3GHY'
    let path = `http://data.zz.baidu.com/urls?site=${url}&token=${token}`
    const options = {
      host: "data.zz.baidu.com",
      path,
      method: "post",
      "User-Agent": "curl/7.12.1",
      headers: {
        "Content-Type": "text/plain",
        "Content-Length": url.length
      }
    };
    const req = http.request(options, res => {
      res.setEncoding("utf8");
      res.on("data", data => {
        this.ctx.logger.error(`百度实时推送返回参数：${data}`, url);
        return {
          data
        };
      });
    });
    req.write(url);
    req.end;
  }

  async PushAxios(content) {
    const url = view_url + content;
    const token = 'gQTyuw96Vanq3GHY'
    let path = `http://data.zz.baidu.com/urls?site=${url}&token=${token}`

    let result = await axios({
      url: 'http://data.zz.baidu.com/urls',
      params: {
        site: url,
        token
      },
      method: 'post',
      headers: {
        "User-Agent": "curl/7.12.1",
        "Content-Type": "text/plain",
        "Content-Length": url.length
      },
    }).then(res => {
      return res.data
    }).catch(err => {
      return err
    });

    return {
      result
    }
  }
}

module.exports = Baidu;