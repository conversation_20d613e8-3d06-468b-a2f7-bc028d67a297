!function(){"use strict";var n,e,t,r,o=function(n){var e=n,t=function(){return e};return{get:t,set:function(n){e=n},clone:function(){return o(t())}}},u=tinymce.util.Tools.resolve("tinymce.PluginManager"),i=function(n){return{isEnabled:function(){return n.get()}}},c=function(n,e){return n.fire("VisualChars",{state:e})},a={"\xa0":"nbsp","\xad":"shy"},f=function(n,e){var t,r="";for(t in n)r+=t;return new RegExp("["+r+"]",e?"g":"")},l=function(n){var e,t="";for(e in n)t&&(t+=","),t+="span.mce-"+n[e];return t},s={charMap:a,regExp:f(a),regExpGlobal:f(a,!0),selector:l(a),charMapToRegExp:f,charMapToSelector:l},d=function(n){return function(){return n}},m={noop:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e]},noarg:function(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t()}},compose:function(t,r){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,arguments))}},constant:d,identity:function(n){return n},tripleEquals:function(n,e){return n===e},curry:function(u){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];for(var i=new Array(arguments.length-1),t=1;t<arguments.length;t++)i[t-1]=arguments[t];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];for(var t=new Array(arguments.length),r=0;r<t.length;r++)t[r]=arguments[r];var o=i.concat(t);return u.apply(null,o)}},not:function(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return!t.apply(null,arguments)}},die:function(n){return function(){throw new Error(n)}},apply:function(n){return n()},call:function(n){n()},never:d(!1),always:d(!0)},h=m.never,v=m.always,p=function(){return g},g=(r={fold:function(n,e){return n()},is:h,isSome:h,isNone:v,getOr:t=function(n){return n},getOrThunk:e=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},or:t,orThunk:e,map:p,ap:p,each:function(){},bind:p,flatten:p,exists:h,forall:v,filter:p,equals:n=function(n){return n.isNone()},equals_:n,toArray:function(){return[]},toString:m.constant("none()")},Object.freeze&&Object.freeze(r),r),y=function(t){var n=function(){return t},e=function(){return o},r=function(n){return n(t)},o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:v,isNone:h,getOr:n,getOrThunk:n,getOrDie:n,or:e,orThunk:e,map:function(n){return y(n(t))},ap:function(n){return n.fold(p,function(n){return y(n(t))})},each:function(n){n(t)},bind:r,flatten:n,exists:r,forall:r,filter:function(n){return n(t)?o:g},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(h,function(n){return e(t,n)})},toArray:function(){return[t]},toString:function(){return"some("+t+")"}};return o},T={some:y,none:p,from:function(n){return null===n||n===undefined?g:y(n)}},w=(Array.prototype.indexOf,undefined,function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var u=n[o];r[o]=e(u,o,n)}return r}),x=function(n,e){for(var t=0,r=n.length;t<r;t++)e(n[t],t,n)},E=(Array.prototype.push,Array.prototype.slice,w),b=x,k=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:m.constant(n)}},N={fromHtml:function(n,e){var t=(e||document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw console.error("HTML does not have a single root node",n),"HTML must have a single root node";return k(t.childNodes[0])},fromTag:function(n,e){var t=(e||document).createElement(n);return k(t)},fromText:function(n,e){var t=(e||document).createTextNode(n);return k(t)},fromDom:k,fromPoint:function(n,e,t){return T.from(n.dom().elementFromPoint(e,t)).map(k)}},C=8,M=9,D=3,O=function(n){return n.dom().nodeName.toLowerCase()},A=function(n){return n.dom().nodeType},S=function(e){return function(n){return A(n)===e}},B=S(1),P=S(D),V=S(M),q={name:O,type:A,value:function(n){return n.dom().nodeValue},isElement:B,isText:P,isDocument:V,isComment:function(n){return A(n)===C||"#comment"===O(n)}},H=function(n){return'<span data-mce-bogus="1" class="mce-'+s.charMap[n]+'">'+n+"</span>"},L=function(n,e){var t=[],r=n.dom(),o=E(r.childNodes,N.fromDom);return b(o,function(n){e(n)&&(t=t.concat([n])),t=t.concat(L(n,e))}),t},R={isMatch:function(n){return q.isText(n)&&q.value(n)!==undefined&&s.regExp.test(q.value(n))},filterDescendants:L,findParentElm:function(n,e){for(;n.parentNode;){if(n.parentNode===e)return n;n=n.parentNode}},replaceWithSpans:function(n){return n.replace(s.regExpGlobal,H)}},_=function(t,n){var r,o,e=R.filterDescendants(N.fromDom(n),R.isMatch);b(e,function(n){var e=R.replaceWithSpans(q.value(n));for(o=t.dom.create("div",null,e);r=o.lastChild;)t.dom.insertAfter(r,n.dom());t.dom.remove(n.dom())})},j=function(e,n){var t=e.dom.select(s.selector,n);b(t,function(n){e.dom.remove(n,1)})},z=_,G=j,W=function(n){var e=n.getBody(),t=n.selection.getBookmark(),r=R.findParentElm(n.selection.getNode(),e);r=r!==undefined?r:e,j(n,r),_(n,r),n.selection.moveToBookmark(t)},F=function(n,e){var t,r=n.getBody(),o=n.selection;e.set(!e.get()),c(n,e.get()),t=o.getBookmark(),!0===e.get()?z(n,r):G(n,r),o.moveToBookmark(t)},I=function(n,e){n.addCommand("mceVisualChars",function(){F(n,e)})},J=tinymce.util.Tools.resolve("tinymce.util.Delay"),K=function(e,t){var r=J.debounce(function(){W(e)},300);!1!==e.settings.forced_root_block&&e.on("keydown",function(n){!0===t.get()&&(13===n.keyCode?W(e):r())})},Q=function(t){return function(n){var e=n.control;t.on("VisualChars",function(n){e.active(n.state)})}};u.add("visualchars",function(n){var e,t=o(!1);return I(n,t),(e=n).addButton("visualchars",{active:!1,title:"Show invisible characters",cmd:"mceVisualChars",onPostRender:Q(e)}),e.addMenuItem("visualchars",{text:"Show invisible characters",cmd:"mceVisualChars",onPostRender:Q(e),selectable:!0,context:"view",prependToContext:!0}),K(n,t),i(t)})}();