'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const moment = require('moment');

function subString(str, n) {
  var r = /[^\x00-\xff]/g;
  var m;

  if (str.replace(r, '**').length > n) {
    m = Math.floor(n / 2);

    for (var i = m, l = str.length; i < l; i++) {
      if (str.substr(0, i).replace(r, '**').length >= n) {
        var newStr = str.substr(0, i) + '...';
        return newStr;
      }
    }
  }

  return str;
}

class searchController extends Controller {
  async list() {
    const { ctx } = this;

    const params = ctx.request.query;

    const result = await ctx.service.search.getResult(params);

    ctx.body = result;
  }

}

module.exports = searchController;
