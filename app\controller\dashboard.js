'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo; 
const { env } = require('../../config/info').siteInfo;
const moment = require('moment');


class DashboardController extends Controller {
  async default() {
    const { ctx } = this;

    // const list = await ctx.service.ticket.list({page:1});

    // list.list.forEach(item => {
    // 	item.time = moment(item.gmt_create).format('YYYY-MM-DD HH:mm:ss');
    // });
    let list = null;

    let checkGroupList = null;
    let sendCheckGroupList = null;

    let serviceList = null;
    let tradeList = null;

    let hasCheckedPurview = 0;//是否有审核权限
    let hasEditChecked = 0;//是否有编辑权限

    let checkDataList = null;//审批数据
    let sendCheckDataList = null;//送审数据
    let activeName = '';


    if (this.ctx.session.userInfo.type != 1) {
      let checkParams = {};

      checkGroupList = await ctx.service.role.getDataCheckedGroup();
      sendCheckGroupList = await ctx.service.role.getDataSendCheckedGroup();

      let objService = await ctx.service.admin.getDataPurviewByService(this.ctx.session.userInfo.name);
      let objTrade = await ctx.service.admin.getDataPurviewByTrade(this.ctx.session.userInfo.name);


      //first:审核
      //second 送审
      if (objService.edit == 1 || objTrade.edit == 1)//如果有编辑权限
      {
        hasEditChecked = 1;
        activeName = 'second';
      }

      if (objService.checked == 1 || objTrade.checked == 1)//如果有审核权限
      {
        hasCheckedPurview = 1;
        activeName = 'first';
      }

      if (1) {
        const paramsService = {};
        const paramsTrade = {};

        paramsService.ids = objService.ids;
        serviceList = await ctx.service.catalog.getSercataList(paramsService);

        paramsTrade.ids = objTrade.ids;
        tradeList = await ctx.service.catalog.getTradeList(paramsTrade);

        checkParams.allowTrade = objTrade.ids;
        checkParams.allowService = objService.ids;
      }

      checkParams.tran_status = '';
      checkParams.needWaitCheckedNum = 1;


      if (this.ctx.session.userInfo.roleid_json) {

        let roleid_json = JSON.parse(this.ctx.session.userInfo.roleid_json);
        checkParams.fixed_receive_groupid = roleid_json;
      }



      checkDataList = await ctx.service.checkflow.getList(checkParams);


      checkDataList.list.forEach(item => {
        let data_type = item.data_type;
        let data_link = env[this.app.config.env].view_url + "/";
        let tran_status = item.tran_status;
        if (data_type == 'news') {
          data_type = '新闻';
          data_link += 'news/detail-' + item.data_id + '.html';
        }
        else if (data_type == 'sku_service') {
          data_type = 'SKU服务';
          data_link += 'sku/' + item.data_id;
        }
        else if (data_type == 'sku_solution') {
          data_type = 'SKU解决方案';
          data_link += 'sku/' + item.data_id;
        }
        else if (data_type == 'cases' || data_type == 'case') {
          data_type = '案例';
          data_link += 'case/detail-' + item.data_id + '.html';
        }
        else if (data_type == 'res') {
          data_type = '资源';
          data_link = item.resourceUrl;
        }
        else {
          data_type = '暂无';
        }
        item.data_type = data_type;
        item.data_link = data_link;
        item.send_admin_time = moment(item.send_admin_time).format('YYYY-MM-DD HH:mm:ss');

        if (tran_status == 0) {
          item.tran_status = '审批中';
          item.tran_admin_time = '';
        }
        else if (tran_status == 20) {
          item.tran_status = '已退回';
          item.tran_admin_time = moment(item.tran_admin_time).format('YYYY-MM-DD HH:mm:ss');
        }

        else if (tran_status == 10) {
          item.tran_status = '已发布';
          item.tran_admin_time = moment(item.tran_admin_time).format('YYYY-MM-DD HH:mm:ss');
        }
      });

      checkParams.tran_status = '';
      checkParams.needWaitCheckedNum = 1;
      checkParams.needFailedNum = 1;
      checkParams.fixed_receive_groupid = null;
      checkParams.send_admin_id = this.ctx.session.userInfo.id;//送审列表只能查询是自己的数据

      sendCheckDataList = await ctx.service.checkflow.getList(checkParams);


      sendCheckDataList.list.forEach(item => {
        let data_type = item.data_type;
        let data_link = env[this.app.config.env].view_url + "/";
        let tran_status = item.tran_status;
        if (data_type == 'news') {
          data_type = '新闻';
          data_link += 'news/detail-' + item.data_id + '.html';
        }
        else if (data_type == 'sku_service') {
          data_type = 'SKU服务';
          data_link += 'sku/' + item.data_id;
        }
        else if (data_type == 'sku_solution') {
          data_type = 'SKU解决方案';
          data_link += 'sku/' + item.data_id;
        }
        else if (data_type == 'cases' || data_type == 'case') {
          data_type = '案例';
          data_link += 'case/detail-' + item.data_id + '.html';
        }
        else if (data_type == 'res') {
          data_type = '资源';
          data_link = item.resourceUrl;

        }
        else {
          data_type = '暂无';
        }

        item.data_type = data_type;
        item.data_link = data_link;
        item.send_admin_time = moment(item.send_admin_time).format('YYYY-MM-DD HH:mm:ss');

        if (tran_status == 0) {
          item.tran_status = '审批中';
          item.tran_admin_time = '';
        }
        else if (tran_status == 20) {
          item.tran_status = '已退回';
          item.tran_admin_time = moment(item.tran_admin_time).format('YYYY-MM-DD HH:mm:ss');
        }

        else if (tran_status == 10) {
          item.tran_status = '已发布';
          item.tran_admin_time = moment(item.tran_admin_time).format('YYYY-MM-DD HH:mm:ss');
        }
      });

    }
    else {
      serviceList = await ctx.service.catalog.getSercataList();
      tradeList = await ctx.service.catalog.getTradeList();
      checkDataList = { list: null, total: 0, waitCheckedNumTotal: 0 };
      sendCheckDataList = { list: null, total: 0, waitCheckedNumTotal: 0, failedCheckedNumTotal: 0 };
    }

    if (list == null) {
      list = {};
    }

    if (tradeList == null) {
      tradeList = {};
      tradeList.list = [];
    }

    if (serviceList == null) {
      serviceList = {};
      serviceList.list = [];
    }


    if (checkDataList == null) {
      checkDataList = {};
      checkDataList.list = [];
      checkDataList.total = 0;
      checkDataList.waitCheckedNumTotal = 0;
    }

    if (sendCheckDataList == null) {
      sendCheckDataList = {};
      sendCheckDataList.list = [];
      sendCheckDataList.waitCheckedNumTotal = 0;
      sendCheckDataList.failedCheckedNumTotal = 0;
      sendCheckDataList.total = 0;
    }


    await ctx.render('dashboard', {
      site_title: _info.site_title,
      page_title: 'Dashboard',
      active: '1',
      view_url: env[this.app.config.env].view_url,
      list: JSON.stringify(list.list),
      csrf: ctx._csrf,
      userInfo: ctx.session.userInfo,
      checkGroupList: JSON.stringify(checkGroupList),
      hasCheckedPurview: hasCheckedPurview,
      hasEditChecked: hasEditChecked,
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      sendCheckGroupList: JSON.stringify(sendCheckGroupList),
      checkDataList: JSON.stringify(checkDataList.list),
      checkDataTotal: checkDataList.total,
      waitCheckedNumTotal: checkDataList.waitCheckedNumTotal,
      sendCheckDataList: JSON.stringify(sendCheckDataList.list),
      sendCheckDataTotal: sendCheckDataList.total,
      sendCheckedListWaitCheckedNumTotal: sendCheckDataList.waitCheckedNumTotal,
      sendCheckedListWFailedNumTotal: sendCheckDataList.failedCheckedNumTotal,
      activeName: activeName
    });
  }

  //获取审批列表
  async getCheckedList() {

    const { ctx } = this;

    const params = ctx.request.body;

    let checkDataList = null;
    if (this.ctx.session.userInfo.type != 1) {
      let checkParams = {};

      let objService = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      let objTrade = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');

      params.allowTrade = objTrade.ids;
      params.allowService = objService.ids;
      params.needWaitCheckedNum = 1;


      if (this.ctx.session.userInfo.roleid_json) {
        let roleid_json = JSON.parse(this.ctx.session.userInfo.roleid_json);
        params.fixed_receive_groupid = roleid_json;
      }

      checkDataList = await ctx.service.checkflow.getList(params);

      checkDataList.list.forEach(item => {
        let data_type = item.data_type;
        let data_link = env[this.app.config.env].view_url + "/";
        let tran_status = item.tran_status;
        if (data_type == 'news') {
          data_type = '新闻';
          data_link += 'news/detail-' + item.data_id + '.html';
        }
        else if (data_type == 'sku_service') {
          data_type = 'SKU服务';
          data_link += 'sku/' + item.data_id;
        }
        else if (data_type == 'sku_solution') {
          data_type = 'SKU解决方案';
          data_link += 'sku/' + item.data_id;
        }
        else if (data_type == 'cases' || data_type == 'case') {
          data_type = '案例';
          data_link += 'case/detail-' + item.data_id + '.html';
        }
        else if (data_type == 'res') {
          data_type = '资源';
          data_link = item.resourceUrl;
        }
        else {
          data_type = '暂无';
        }
        item.data_type = data_type;
        item.data_link = data_link;
        item.send_admin_time = moment(item.send_admin_time).format('YYYY-MM-DD HH:mm:ss');

        if (tran_status == 0) {
          item.tran_status = '审批中';
          item.tran_admin_time = '';
        }
        else if (tran_status == 20) {
          item.tran_status = '已退回';
          item.tran_admin_time = moment(item.tran_admin_time).format('YYYY-MM-DD HH:mm:ss');
        }

        else if (tran_status == 10) {
          item.tran_status = '已发布';
          item.tran_admin_time = moment(item.tran_admin_time).format('YYYY-MM-DD HH:mm:ss');
        }

      });
    }
    else {
      checkDataList = { list: null, total: 0, page: 1, waitCheckedNumTotal: 0 };
    }

    ctx.body = { success: true, data: checkDataList };
  }

  //获取送审列表
  async getSendCheckedList() {

    const { ctx } = this;

    const params = ctx.request.body;
    params.send_admin_id = this.ctx.session.userInfo.id;//送审列表只能查询是自己的数据
    let checkDataList = null;
    if (this.ctx.session.userInfo.type != 1) {
      let checkParams = {};

      let objService = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');

      let objTrade = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');

      params.allowTrade = objTrade.ids;
      params.allowService = objService.ids;
      params.needWaitCheckedNum = 1;
      params.needFailedNum = 1;
      checkDataList = await ctx.service.checkflow.getList(params);

      checkDataList.list.forEach(item => {
        let data_type = item.data_type;
        let data_link = env[this.app.config.env].view_url + "/";
        let tran_status = item.tran_status;
        if (data_type == 'news') {
          data_type = '新闻';
          data_link += 'news/detail-' + item.data_id + '.html';
        }
        else if (data_type == 'sku_service') {
          data_type = 'SKU服务';
          data_link += 'sku/' + item.data_id;
        }
        else if (data_type == 'sku_solution') {
          data_type = 'SKU解决方案';
          data_link += 'sku/' + item.data_id;
        }
        else if (data_type == 'cases' || data_type == 'case') {
          data_type = '案例';
          data_link += 'case/detail-' + item.data_id + '.html';
        }
        else if (data_type == 'res') {
          data_type = '资源';
          data_link = item.resourceUrl;
        }
        else {
          data_type = '暂无';
        }
        item.data_type = data_type;
        item.data_link = data_link;
        item.send_admin_time = moment(item.send_admin_time).format('YYYY-MM-DD HH:mm:ss');

        if (tran_status == 0) {
          item.tran_status = '审批中';
          item.tran_admin_time = '';
        }
        else if (tran_status == 20) {
          item.tran_status = '已退回';
          item.tran_admin_time = moment(item.tran_admin_time).format('YYYY-MM-DD HH:mm:ss');
        }

        else if (tran_status == 10) {
          item.tran_status = '已发布';
          item.tran_admin_time = moment(item.tran_admin_time).format('YYYY-MM-DD HH:mm:ss');
        }
      });
    }
    else {
      checkDataList = { list: null, total: 0, page: 1, waitCheckedNumTotal: 0, failedCheckedNumTotal: 0 };
    }

    ctx.body = { success: true, data: checkDataList };
  }

  async checkflowOperator() {
    const { ctx } = this;

    const params = ctx.request.body;
    const result = await ctx.service.checkflow.checkflowOperator(params);

    if (result.success) {
      ctx.body = { success: true, data: null };
    } else {
      ctx.body = { fail: true, data: null, msg: '数据已被处理' };
    }
  }
};

module.exports = DashboardController;
