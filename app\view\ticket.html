<% include header.html %>
  <el-container id="Main">
    <% include eheader.html %>
      <el-container>
        <% include eside.html %>
          <el-main>
            <el-row>
              <el-col :span="24">
                <el-breadcrumb separator-class="el-icon-arrow-right">
                  <el-breadcrumb-item>
                    <a href='/'>首页</a>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    反馈中心
                  </el-breadcrumb-item>
                </el-breadcrumb>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form :model="form" :inline="true">
                  <!-- <el-form-item label="内容">
                    <el-input v-model.trim="form.content" placeholder="内容" clearable></el-input>
                  </el-form-item> -->

                  <el-form-item label="姓名">
                    <el-input v-model.trim="form.customer" placeholder="姓名" clearable></el-input>
                  </el-form-item>

                  <el-form-item label="手机">
                    <el-input v-model.trim="form.phone" placeholder="手机" clearable></el-input>
                  </el-form-item>

                  <el-form-item label="邮箱">
                    <el-input v-model.trim="form.email" placeholder="邮箱" clearable></el-input>
                  </el-form-item>

                  <el-form-item label="发送状态">
                    <el-select v-model="form.resultCode" placeholder="发送状态" clearable>
                      <el-option v-for="item in resultType" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item>
                    <el-button type="primary" @click="getList(1)">查询</el-button>
                    <el-button @click=_default>重置</el-button>
                  </el-form-item>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>

            <el-row v-loading="loading">
              <el-table :data="tableList.list" stripe style="width: 100%">
                <el-table-column prop="serviceName" label="服务">
                </el-table-column>
                <el-table-column prop="tradeName" label="行业">
                </el-table-column>
                <el-table-column prop="type" label="类型">
                </el-table-column>
                <el-table-column prop="productName" label="产品">
                </el-table-column>
                <!-- <el-table-column prop="destCountry" label="目的国">
                </el-table-column> -->
                <!-- <el-table-column prop="content" label="内容">
                    </el-table-column> -->
                <el-table-column prop="customer" label="客户信息">
                  <template slot-scope="scope">
                    {{scope.row.customer}}
                    <el-tooltip class="item" effect="dark" placement="top-end">
                      <div slot="content">
                        电话:{{scope.row.phone}}<br />
                        邮箱:{{scope.row.email}}<br />
                        省份:{{scope.row.provice}}<br />
                        企业:{{scope.row.company}}<br />
                        IP:{{scope.row.ip}}
                      </div>
                      <i class="el-icon-info"></i>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <!-- <el-table-column prop="phone" label="电话" width="120">
                </el-table-column>
                <el-table-column prop="email" label="邮箱">
                </el-table-column>
                <el-table-column prop="provice" label="省份">
                </el-table-column>
                <el-table-column prop="company" label="企业">
                </el-table-column> -->
                <el-table-column prop="resultCode" label="同步状态">
                  <template slot-scope="scope">
                    <template v-if="scope.row.resultCode == 0">
                      <el-tooltip class="item" effect="dark" placement="top-end">
                        <div slot="content">{{scope.row.resultText}}</div>
                        <span style="color:#F00">失败</span>
                      </el-tooltip>
                      <el-button type="primary" size="mini" @click="resend(scope.row.id)">重发</el-button>
                    </template>
                    <span v-else>成功</span>
                  </template>
                </el-table-column>
                <el-table-column prop="osType" label="来源" width="80">
                  <template slot-scope="scope">
                    <!-- <template v-if="scope.row.osType == 'mobile'">移动端</template>
                    <template v-else>PC</template> -->
                    <template>{{scope.row.osType}}</template>
                    <template v-if="scope.row.frontUrl">
                      /
                      <template v-if="scope.row.frontUrl.includes('sgsonline')">主站</template>
                      <template v-else>副站</template>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column prop="gmtCreate" label="提交时间" width="164">
                </el-table-column>
              </el-table>
              <el-pagination @current-change="handleCurrentChange" background :current-page.sync="form.pageNum"
                :page-size="form.pageRow" layout="total, prev, pager, next" :total="tableList.total"
                style="padding:10px 0;text-align: right;">
              </el-pagination>
            </el-row>
          </el-main>
      </el-container>
  </el-container>
  <script>
    var main = new Vue({
      el: '#Main',
      data: {
        tableList: {
          list: [],
          total: 0,
        },
        form: {
          content: '',
          customer: '',
          phone: '',
          email: '',
          resultCode: '',
          state: '',
          pageNum: 1,
          pageRow: 10,
        },
        resultType: [
          {
            value: 0,
            label: '失败'
          },
          {
            value: 1,
            label: '成功'
          },
        ],
        loading: false,
      },
      methods: {
        handleCurrentChange(r) {
          this.getList();
        },
        getList(pageNum) {
          var that = this;
          that.loading = true;
          var params = this.form;
          params.pageNum = pageNum ? pageNum : params.pageNum
          params._csrf = '<%- csrf %>';
          axios.post('/ticket/qry', params)
            .then(function (result) {
              that.loading = false;
              if (result.data.resultCode) {
                that.tableList.list = result.data.data.items;
                that.tableList.total = result.data.data.totalNum;
              } else {
                that.$message.error('获取列表出错');
              }
            });
        },
        _default() {
          this.form.content = '';
          this.form.customer = '';
          this.form.phone = '';
          this.form.email = '';
        },
        resend(i) {
          var that = this;

          that.loading = true;
          var params = {
            id: i,
            _csrf: '<%- csrf %>',
          }

          axios.post('/ticket/post', params)
            .then(function (result) {
              that.loading = false;
              // window.location.reload();
              that.$message({ message: '已重新发送，请稍候刷新查看结果。', type: 'success' });
            });
        }
      },
      mounted() {
        var that = this
        this.$nextTick(function () {
          document.getElementById('preLoading').style.display = 'none';
          that.getList()
        });
      }
    });
  </script>
  <% include footer.html %>