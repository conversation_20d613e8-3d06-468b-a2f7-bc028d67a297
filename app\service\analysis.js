'use strict';

const Service = require('egg').Service;
const moment = require('moment');

class AnalysisService extends Service {
    async keywordList(params) {
        const {
            app
        } = this;
        const page = params.page || 1,
            limit = 10,
            time = params.time;

        let qarr = [];

        if (time) {
            if (time[0]) {
                qarr.push('gmt_create>="' + moment(time[0]).format('YYYY-MM-DD HH:mm:ss') + '"');
            }

            if (time[1]) {
                qarr.push('gmt_create<"' + moment(time[1]).format('YYYY-MM-DD HH:mm:ss') + '"');
            }
        }

        if (qarr.length > 0) {
            qarr = qarr.join(' AND ');
            qarr = ' WHERE ' + qarr;
        }

        let total = await app.mysql.query('SELECT * FROM analysis_keyword' + qarr);
        const list = await app.mysql.query('SELECT * FROM analysis_keyword' + qarr + ' ORDER BY num DESC LIMIT ' + (page - 1) * limit + ',' + limit);

        total = total.length;
        for (let item of list) {
            item.time = moment(item.gmt_create).format('YYYY-MM-DD HH:mm:ss');
        }

        return {
            list,
            total
        };
    }

    async keywordListExport(params) {
        const {
            app
        } = this;
        const time = params.time;

        let qarr = [];

        if (time) {
            if (time[0]) {
                qarr.push('gmt_create>="' + moment(time[0]).format('YYYY-MM-DD HH:mm:ss') + '"');
            }

            if (time[1]) {
                qarr.push('gmt_create<"' + moment(time[1]).format('YYYY-MM-DD HH:mm:ss') + '"');
            }
        }

        if (qarr.length > 0) {
            qarr = qarr.join(' AND ');
            qarr = ' WHERE ' + qarr;
        }

        const list = await app.mysql.query('SELECT * FROM analysis_keyword' + qarr + ' GROUP BY id ORDER BY num DESC');
        const minDate = await app.mysql.query(`select min(gmt_create) from analysis_keyword`)

        return {
            list,
            time,
            minDate: minDate[0]['min(gmt_create)']
        };
    }

    async entryList(params) {
        const {
            app
        } = this;
        const page = params.page || 1,
            limit = 10,
            time = params.time;

        let qarr = [];

        if (time) {
            if (time[0]) {
                qarr.push('gmt_create>="' + moment(time[0]).format('YYYY-MM-DD HH:mm:ss') + '"');
            }

            if (time[1]) {
                qarr.push('gmt_create<"' + moment(time[1]).format('YYYY-MM-DD HH:mm:ss') + '"');
            }
        }

        if (qarr.length > 0) {
            qarr = qarr.join(' AND ');
            qarr = ' WHERE ' + qarr;
        }

        // let total = await app.mysql.query('SELECT url,COUNT(*) AS len FROM analysis_entry'+qarr+' GROUP BY url');
        // const list = await app.mysql.query('SELECT url,MAX(gmt_create) AS time,COUNT(*) AS len FROM analysis_entry'+qarr+' GROUP BY url ORDER BY COUNT(*) DESC LIMIT ' + (page - 1) * limit + ',' + limit);
        let total = await app.mysql.query('SELECT COUNT(*) AS len FROM analysis_entry' + qarr + ' GROUP BY id');
        const list = await app.mysql.query('SELECT * FROM analysis_entry' + qarr + ' GROUP BY id ORDER BY id DESC LIMIT ' + (page - 1) * limit + ',' + limit);

        for (let item of list) {
            item.time = moment(item.time).format('YYYY-MM-DD HH:mm:ss');
            item.url = decodeURI(item.url);
        }
        total = total.length;
        return {
            list,
            total
        };
    }

    async entryListExport(params) {
        const {
            app
        } = this;
        const time = params.time;

        let qarr = [];

        if (time) {
            if (time[0]) {
                qarr.push('gmt_create>="' + moment(time[0]).format('YYYY-MM-DD HH:mm:ss') + '"');
            }

            if (time[1]) {
                qarr.push('gmt_create<"' + moment(time[1]).format('YYYY-MM-DD HH:mm:ss') + '"');
            }
        }

        if (qarr.length > 0) {
            qarr = qarr.join(' AND ');
            qarr = ' WHERE ' + qarr;
        }


        const list = await app.mysql.query('SELECT * FROM analysis_entry' + qarr + ' GROUP BY id ORDER BY id DESC');

        return {
            list
        };
    }

    async entryListFrom(params) {
        const {
            app
        } = this;
        const page = params.page || 1,
            limit = 10;

        const url = params.url;

        let total = await app.mysql.count('analysis_entry', {
            url: url
        });
        const list = await app.mysql.select('analysis_entry', {
            columns: ['id', 'gmt_create', 'ip', 'source', 'code'],
            where: {
                url: url
            },
            orders: [
                ['gmt_create', 'desc']
            ],
            offset: (page - 1) * limit,
            limit: limit
        });

        for (let item of list) {
            item.time = moment(item.gmt_create).format('YYYY-MM-DD HH:mm:ss');
            item.url = decodeURI(item.url);
        }
        //total = total.length;
        return {
            list,
            total
        };
    }

    // 资源下载统计
    async downloadList(params) {
        const {
            app
        } = this;
        const page = params.page || 1,
            limit = params.limit || 10,
            time = params.time;

        let qarr = [];

        if (time) {
            if (time[0]) {
                qarr.push('create_time>="' + moment(time[0]).format('YYYY-MM-DD HH:mm:ss') + '"');
            }

            if (time[1]) {
                qarr.push('create_time<"' + moment(time[1]).format('YYYY-MM-DD HH:mm:ss') + '"');
            }
        }

        if (qarr.length > 0) {
            qarr = qarr.join(' AND ');
            qarr = ' WHERE ' + qarr;
        }

        let total = await app.mysql.query('SELECT * FROM analysis_download' + qarr);
        const list = await app.mysql.query('SELECT * FROM analysis_download' + qarr + ' ORDER BY create_time DESC LIMIT ' + (page - 1) * limit + ',' + limit);

        total = total.length;
        for (let item of list) {
            item.time = moment(item.create_time).format('YYYY-MM-DD HH:mm:ss');
        }

        return {
            list,
            total
        };
    }
}

module.exports = AnalysisService;