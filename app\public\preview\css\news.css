.myLocation{
    width: 100%;height: 16px;
    margin-top: 100px;
    margin-bottom: 34px;
    padding-top: 20px;
    border-top: 1px solid #eeeeee;
}
.myLocationBox{
    width: 1226px;
    height: 100%;
    margin: 0 auto;
    margin-bottom: 33px;
    /*padding-top: 6px;*/
    /*padding-left: 88px;*/
}
.myLocation-word1{
    width: auto;
    height: 100%;

    font-size: 16px;
    line-height: 16px;
    float: left;
    margin-right: 27px;
}
.myLocation-word1 a{
    color: #000000;
}
.myLocation-word2{
    width: auto;
    height: 100%;

    font-size: 16px;
    line-height: 16px;
    float: left;
}
.myLocation-word2 a{
    color: #000000;
}
.myLocation-icon1{
    width: 16px;
    height: 16px;
    background: url(../images/arrow.png);
    float: left;
    margin-right: 27px;
}
.newsBox{
    width: 1226px;
    margin: 0 auto;
}
.leftBox{
    width: 750px;
    float: left;
    margin-bottom: 20px;
}
.newsTitle{
    font-size: 32px;
    font-weight: bold;
    line-height: 1.6;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
    margin-bottom: 30px;
}
.subhead{
    width: 100%;height: 20px;
    margin-bottom: 38px;
}
.subhead1{
    font-size: 15px;
    color: #999999;
    float: left;
    padding-right: 20px;
    border-right: 1px solid #e7e7e7;
    margin-right: 20px;
}
.subhead2{
    font-size: 15px;
    color: #999999;
    float: left;
}
.con1{
    font-size: 14px;
    line-height: 2;
    color: #000000;
    margin-bottom: 30px;
}
.img1{
    width: 750px;height: 330px;
    margin-bottom: 30px;
    background: url(../images/img1.png);
}
.con2{
    font-size: 14px;
    line-height: 2;
    color: #000000;
    margin-bottom: 30px;
}
.img2{
    width: 750px;height: 330px;
    margin-bottom: 30px;
    background: url(../images/img2.png);
}
.con3{
    font-size: 14px;
    line-height: 2;
    color: #000000;
    margin-bottom: 30px;
}
.img3{
    width: 750px;height: 330px;
    margin-bottom: 30px;
    background: url(../images/img1.png);
}
.rightBox{
    width: 360px;height: auto;
    float: right;
    background: #f8f8f8;
}
.rightBox1{
    width: 295px;height: auto;
    margin: 0 auto;
}
.rela{

    font-size: 18px;
    margin-top: 32px;
    margin-bottom: 25px;
    color: #000000;
}
.rightConBox{
    width: auto;
    padding-bottom: 22px;
    border-bottom: 1px solid #cccccc;
}
.rightCon{

    width: auto;

    padding: 8px 12px;
    height: 22px;
    background-color: #f7f7f7;
    border: 1px solid #e0e0e0;
    margin-right: 20px;
    cursor: pointer;
    color: #000000;
}
.rightCon:hover{
    background: rgb(254,102,2);
    color: white;
}
.rightContitle:hover{
    color: rgb(254,102,2);
}
.rightContitleBox{
    width: 100%;
    height: auto;
    border-bottom: 1px solid #e0e0e0;
}
.rightContitle{
    font-size: 15px;
    color: #000000;
    margin-bottom: 16px;
    margin-top: 26px;
    cursor: pointer;
    display: block;
}
.rightConSubtitle{
    font-size: 14px;
    line-height: 1.64;
    color: #999999;
    padding-bottom: 19px;
}
.rightContitleBox:last-child{
    border-bottom: none;
}
.chapters{
    width: 100%;
}
.lastchapter{
    width: auto;
    float: left;
    cursor: pointer;
}
.lastchapter:hover{
    color: rgb(254,102,2);
}
.nextchapter{
    width: auto;
    float: right;
    cursor: pointer;
}
.nextchapter:hover{
    color: rgb(254,102,2);
}
