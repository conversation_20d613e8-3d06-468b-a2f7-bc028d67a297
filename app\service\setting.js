'use strict';

const Service = require('egg').Service;
const moment = require('moment');

class SettingService extends Service {
    async getRegionList() {
        const list = await this.app.mysql.select('region', {
            columns: ['id', 'name', 'alias'],
            where: { is_delete: 0 },
            orders: [
                ['id', 'desc']
            ]
        });

        return { list };
    }

    async addRegion(name, alias) {
        const {app} = this;
        const gmt_create = moment().format('YYYY-MM-DD HH:mm:ss');
        // const result = await this.app.mysql.insert('region', {
        //     name: name,
        //     alias: alias,
        //     gmt_create: gmt_create
        // });

        // return { result };
        const result = await app.mysql.beginTransactionScope(async conn => {
            await conn.insert('region',{
                name: name,
                alias: alias,
                gmt_create: gmt_create,
            });

            await conn.insert('logs', {
                user_name: this.ctx.session.userInfo.name,
                log: '创建' + name,
                model: '设置-目的国',
                name: name
            });
            return { success: true };
        }, this.ctx);

        return { success: result.success }
    }

    async regionInfo(id) {
        const info = await this.app.mysql.select('region', {
            columns: ['id', 'name', 'alias'],
            where: { is_delete: 0, id: id },
            orders: [
                ['id', 'desc']
            ]
        });

        return { info };
    }

    async regionUpdate(id, name, alias) {
        const {app} = this;
        const row = {
            id: id,
            name: name,
            alias: alias
        };
        // const update = await this.app.mysql.update('region',row);

        // const success = update.affectedRows === 1;

        // return { success };
        const result = await app.mysql.beginTransactionScope(async conn => {
            await conn.update('region',row);

            await conn.insert('logs', {
                user_name: this.ctx.session.userInfo.name,
                log: '修改' + name,
                model: '设置-目的国',
                name: name
            });
            return { success: true };
        }, this.ctx);

        return { success: result.success }
    }

    async regionDelete(id) {
        const {app} = this;
        const row = {
            id: id,
            is_delete: 1
        };
        // const update = await this.app.mysql.update('region',row);

        // const success = update.affectedRows === 1;

        // return { success };
        const result = await app.mysql.beginTransactionScope(async conn => {
            await conn.update('region',row);

            const info = await conn.get('region',{id:id})
            await conn.insert('logs', {
                user_name: this.ctx.session.userInfo.name,
                log: '删除' + info.name,
                model: '设置-目的国',
                name: info.name
            });
            return { success: true };
        }, this.ctx);

        return { success: result.success }
    }

    async getCitysList() {
        const list = await this.app.mysql.select('citys', {
            columns: ['id', 'name'],
            where: { is_delete: 0 },
            orders: [
                ['id', 'desc']
            ]
        });

        return { list };
    }

    async addCity(name) {
        const {app} = this;
        const gmt_create = moment().format('YYYY-MM-DD HH:mm:ss');
        // const result = await this.app.mysql.insert('citys', {
        //     name: name,
        //     gmt_create: gmt_create
        // });

        // return { result };

        const result = await app.mysql.beginTransactionScope(async conn => {
            await conn.insert('citys', {
                name: name,
                gmt_create: gmt_create
            });

            await conn.insert('logs', {
                user_name: this.ctx.session.userInfo.name,
                log: '创建' + name,
                model: '设置-城市',
                name: name
            });
            return { success: true };
        }, this.ctx);

        return { success: result.success }
    }

    async cityInfo(id) {
        const info = await this.app.mysql.select('citys', {
            columns: ['id', 'name'],
            where: { is_delete: 0, id: id },
            orders: [
                ['id', 'desc']
            ]
        });

        return { info };
    }

    async cityUpdate(id, name) {
        const {app} = this;
        const row = {
            id: id,
            name: name,
        };
        // const update = await this.app.mysql.update('citys',row);

        // const success = update.affectedRows === 1;

        // return { success };
        const result = await app.mysql.beginTransactionScope(async conn => {
            await conn.update('citys',row);

            await conn.insert('logs', {
                user_name: this.ctx.session.userInfo.name,
                log: '修改' + name,
                model: '设置-城市',
                name: name
            });
            return { success: true };
        }, this.ctx);

        return { success: result.success }
    }

    async cityDelete(id) {
        const {app} = this;
        const row = {
            id: id,
            is_delete: 1
        };
        // const update = await this.app.mysql.update('citys',row);

        // const success = update.affectedRows === 1;

        // return { success };
        const result = await app.mysql.beginTransactionScope(async conn => {
            await conn.update('citys',row);

            const info = await conn.get('citys',{id:id});
            await conn.insert('logs', {
                user_name: this.ctx.session.userInfo.name,
                log: '删除' + info.name,
                model: '设置-城市',
                name: info.name
            });
            return { success: true };
        }, this.ctx);

        return { success: result.success }
    }
}

module.exports = SettingService;