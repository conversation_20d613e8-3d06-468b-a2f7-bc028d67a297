'use strict';

const Service = require('egg').Service;
const moment = require('moment');

class OperationService extends Service {
    async hotList(params) {
        const {
            app
        } = this;

        let page = params.page || 1,
            limit = params.limit || 10;

        if (!page || page == 'NULL' || page == '') {
            page = 1;
        }

        if (!limit || limit == 'NULL' || limit == '') {
            limit = 10;
        }

        let qFilter = [];

        let query = 'SELECT s.id,s.name,s.alias,s.type,s.is_use,s.thumb_img,s.is_buy,s.gmt_modify,r.sort_num FROM sku s LEFT JOIN catalog_relation r ON s.id=r.sku_id LEFT JOIN catalog c ON r.catalog_id=c.id WHERE s.is_delete=0 AND r.catalog_id=6';
        let cquery = 'SELECT s.id FROM sku s LEFT JOIN catalog_relation r ON s.id=r.sku_id LEFT JOIN catalog c ON r.catalog_id=c.id WHERE s.is_delete=0 AND r.catalog_id=6';
        let total = await this.app.mysql.query(cquery + qFilter);
        let list = await this.app.mysql.query(query + qFilter + ' ORDER BY r.sort_num,s.is_use DESC,s.gmt_modify DESC LIMIT ' + (page - 1) * limit + ',' + limit);
        total = total.length;

        for (let item of list) {
            item.is_use == 0 ? item.is_use = false : item.is_use = true;
            item.is_buy == 0 ? item.is_buy = false : item.is_buy = true;

            item.time = moment(item.gmt_modify).format('YYYY-MM-DD HH:mm:ss');

            let tradeList = [],
                serviceList = [];
            let tradeCata = await this.app.mysql.select('catalog_relation', {
                where: {
                    sku_id: item.id,
                    catalog_type: 1
                }
            });
            let serviceCate = await this.app.mysql.select('catalog_relation', {
                where: {
                    sku_id: item.id,
                    catalog_type: 2
                }
            });


            if (tradeCata && tradeCata.length > 0) {
                for (let ti of tradeCata) {
                    let tradeInfo = await this.app.mysql.get('catalog', {
                        id: ti.catalog_id,
                        is_delete: 0
                    });
                    if (tradeInfo) {
                        let tradeParantInfo = await this.app.mysql.get('catalog', {
                            id: tradeInfo.parent_id,
                            is_delete: 0
                        });
                        if (tradeParantInfo) {
                            tradeList.push(tradeParantInfo.name + '-' + tradeInfo.name);
                        }

                    }

                }
            }

            if (serviceCate && serviceCate.length > 0) {
                for (let si of serviceCate) {
                    let serviceInfo = await this.app.mysql.get('catalog', {
                        id: si.catalog_id,
                        is_delete: 0
                    });
                    if (serviceInfo) {
                        let serviceParantInfo = await this.app.mysql.get('catalog', {
                            id: serviceInfo.parent_id,
                            is_delete: 0
                        });
                        if (serviceParantInfo) {
                            serviceList.push(serviceParantInfo.name + '-' + serviceInfo.name);
                        }

                    }

                }
            }

            item.tradeList = tradeList;
            item.serviceList = serviceList;

            item.last_modify = '测试';
        }

        return {
            list,
            total
        };
    }

    async skuAdd(id) {
        const {
            app,
            ctx
        } = this;
        const gmt_create = moment().format('YYYY-MM-DD HH:mm:ss');

        const result = await app.mysql.query('SELECT id,name FROM sku WHERE id=' + id + ' AND is_delete=0 AND is_use=1');

        if (!result || result.length == 0) {
            return {
                success: false,
                msg: 'SKU不存在或者SKU未上架'
            };
        } else {
            const list = await ctx.service.operation.hotList({
                page: 1,
                limit: 1000
            });
            // url内ID是否存在判断
            let flag = list.list.some(item => {
                return item.id == id
            })

            // 不存在添加，存在返回错误信息
            if (!flag) {
                const addAlias = await app.mysql.insert('catalog_relation', {
                    sku_id: id,
                    catalog_id: 6,
                    gmt_create: gmt_create
                });

                await this.ctx.service.logs.log({
                    log: '添加' + result[0].name,
                    model: '运维内容维护',
                    name: result[0].name
                })
                if (addAlias.insertId) {
                    return {
                        success: true
                    }
                }
            } else {
                return {
                    success: false,
                    msg: 'URL重复'
                }
            }
        }
    }

    async skuDrop(id) {
        const {
            app
        } = this;

        let info;
        if (typeof id != 'object') {
            const result = await app.mysql.delete('catalog_relation', {
                sku_id: id,
                catalog_id: 6
            });
            info = await app.mysql.get('sku', {
                id: id
            });
        } else {
            for (let item of id) {
                const result = await app.mysql.delete('catalog_relation', {
                    sku_id: item,
                    catalog_id: 6
                });
                info = await app.mysql.get('sku', {
                    id: item
                });
            }
        }

        await this.ctx.service.logs.log({
            log: '删除' + info.name,
            model: '运维内容维护',
            name: info.name
        })
        return {
            success: true
        }
    }
}

module.exports = OperationService;