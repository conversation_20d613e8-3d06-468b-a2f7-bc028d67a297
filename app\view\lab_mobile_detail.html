<% include header.html %>
  <el-container id="Main" v-loading="loading">
    <% include eheader.html %>
      <el-container>
        <% include eside.html %>
          <el-main>
            <el-row>
              <el-col :span="24">
                <el-breadcrumb separator-class="el-icon-arrow-right">
                  <el-breadcrumb-item>
                    <a href='/'>首页</a>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    实验室
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    移动端实验室详情
                  </el-breadcrumb-item>
                </el-breadcrumb>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24" class="lab-detail">
                <el-form ref="detail" :model="detail" label-width="120px">
                  <el-form-item label="是否上架">
                    <el-switch v-model="detail.is_publish" :active-value='1' :inactive-value='0'></el-switch>
                  </el-form-item>
                  <el-form-item label="实验室名称">
                    <el-input v-model="detail.name" placeholder="请输入实验室名称..." maxlength='8'></el-input>
                  </el-form-item>
                  <el-form-item label="实验室别名">
                    <el-input v-model="detail.alias" placeholder="请输入实验室别名..."></el-input>
                  </el-form-item>
                  <el-form-item label="预览地址">
                    {{ mview_url + '/lab?alias=' + detail.alias }}
                  </el-form-item>
                  <el-form-item label="资质ICON" class='lab-detail-zizhi'>
                    <div>
                      <el-upload :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                        :data='uploadData' :show-file-list="false"
                        :on-success="(response, file, fileList) => { return handleAvatarSuccess(response, file, fileList, 'icon1')}"
                        :before-upload="beforeAvatarUpload" name="file" accept="image/jpeg,image/png,image/gif">
                        <template v-if='detail.content.icon1'>
                          <img :src='detail.content.icon1' style="width: 50px; height: 50px;" />
                          <span>缩略图1</span>
                        </template>
                        <el-button v-else size="small" type="primary">缩略图1</el-button>
                      </el-upload>
                      <el-upload :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                        :data='uploadData' :show-file-list="false"
                        :on-success="(response, file, fileList) => { return handleAvatarSuccess(response, file, fileList, 'icon2')}"
                        :before-upload="beforeAvatarUpload" name="file" accept="image/jpeg,image/png,image/gif">
                        <template v-if='detail.content.icon2'>
                          <img :src='detail.content.icon2' style="width: 50px; height: 50px;" />
                          <span>缩略图2</span>
                        </template>
                        <el-button v-else size="small" type="primary">缩略图2</el-button>
                      </el-upload>
                    </div>
                  </el-form-item>
                  <el-form-item label="banner配置">
                    <div>
                      <el-upload :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                        :data='uploadData' :show-file-list="false"
                        :on-success="(response, file, fileList) => { return handleAvatarSuccess(response, file, fileList, 'bannerBg')}"
                        :before-upload="beforeAvatarUpload" name="file" accept="image/jpeg,image/png,image/gif">
                        <template v-if='detail.content.bannerBg'>
                          <img :src='detail.content.bannerBg' style="width: 100%;" />
                        </template>
                        <el-button v-else size="small" type="primary">背景图片</el-button>
                      </el-upload>
                    </div>
                  </el-form-item>
                  <el-form-item label="楼层模块">
                    <div v-for='(floor, floorIndex) of detail.content.floors' :key='floorIndex'>
                      <!-- 热门测试项目楼层 -->
                      <div v-if='floor.type === 10'>
                        <h2>
                          热门测试项目楼层
                          <el-button-group style="float: right;">
                            <el-button type="primary" size='small' icon="el-icon-arrow-up"
                              @click='handleMoveUp(floorIndex)' :disabled="!floorIndex">向上</el-button>
                            <el-button type="primary" size='small' icon="el-icon-arrow-down"
                              @click='handleMoveDown(floorIndex)'
                              :disabled="floorIndex === detail.content.floors.length - 1">向下</el-button>
                          </el-button-group>
                        </h2>
                         <div style="margin: 10px 0;">
                          主标题：
                          <el-input v-model='floor.title' placeholder="请输入主标题...."
                            style="width: 300px;"></el-input>
                          是否显示<el-switch v-model="floor.showTitle" :active-value='1'
                            :inactive-value='0'></el-switch>
                        </div>
                        <div style="margin-bottom: 10px;">
                          子标题：
                          <el-input v-model='floor.subTitle' placeholder="请输入子标题...."
                            style="width: 800px;"></el-input>
                          是否显示<el-switch v-model="floor.showSubTitle" :active-value='1'
                            :inactive-value='0'></el-switch>
                        </div>
                        <div>
                          <ul>
                            <li v-for='(v2, index2) of floor.list' :key='index2' >
                              <el-input placeholder="请输入内容..." v-model='v2.title' maxlength='4'></el-input>
                              <el-button size='small' type='danger' @click='handleDelHotItem(floorIndex, index2)'>删除</el-button>
                            </li>
                            <li><el-button size='small' type='warning' @click='handleAddHotItem(floorIndex)'>添加</el-button></li>
                          </ul>
                        </div>
                      </div>
                       <!-- 服务项目涵盖楼层 -->
                       <div v-if='floor.type === 11'>
                        <h2>
                          服务项目涵盖楼层
                          <el-button-group style="float: right;">
                            <el-button type="primary" size='small' icon="el-icon-arrow-up"
                              @click='handleMoveUp(floorIndex)' :disabled="!floorIndex">向上</el-button>
                            <el-button type="primary" size='small' icon="el-icon-arrow-down"
                              @click='handleMoveDown(floorIndex)'
                              :disabled="floorIndex === detail.content.floors.length - 1">向下</el-button>
                          </el-button-group>
                        </h2>
                         <div style="margin: 10px 0;">
                          主标题：
                          <el-input v-model='floor.title' placeholder="请输入主标题...."
                            style="width: 300px;"></el-input>
                          是否显示<el-switch v-model="floor.showTitle" :active-value='1'
                            :inactive-value='0'></el-switch>
                        </div>
                        <div style="margin-bottom: 10px;">
                          子标题：
                          <el-input v-model='floor.subTitle' placeholder="请输入子标题...."
                            style="width: 800px;"></el-input>
                          是否显示<el-switch v-model="floor.showSubTitle" :active-value='1'
                            :inactive-value='0'></el-switch>
                        </div>
                        <div>
                          <ul>
                            <li v-for='(v2, index2) of floor.list' :key='index2' style="display: flex;">
                              <el-upload
                                :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                                :data='uploadData' :show-file-list="false"
                                :on-success="(response, file, fileList) => { return handleAvatarSuccess(response, file, fileList, 'serviceItem', floorIndex, index2)}"
                                :before-upload="beforeAvatarUpload" name="file"
                                accept="image/jpeg,image/png,image/gif">
                                <template v-if='v2.img'>
                                  <img :src='v2.img' style="width: 360px; height: 200px;" />
                                </template>
                                <el-button v-else size="small" type="primary">背景图片</el-button>
                              </el-upload>
                              <el-input v-model='v2.link' placeholder="请输入链接地址..." style="width: 300px"></el-input>
                              <el-button-group style="float: right;">
                                <el-button size='small' type='danger' @click='handleDelServiceItem(floorIndex, index2)'>删除</el-button>
                                <el-button type="primary" size='small' icon="el-icon-arrow-up"
                                  @click='handleMoveServiceItemUp(floorIndex, index2)' :disabled="!index2">向上</el-button>
                                <el-button type="primary" size='small' icon="el-icon-arrow-down"
                                  @click='handleMoveServiceItemDown(floorIndex, index2)'
                                  :disabled="index2 === floor.list.length - 1">向下</el-button>
                              </el-button-group>
                            </li>
                            <li><el-button size='small' type='warning' @click='handleAddServiceItem(floorIndex)'>添加</el-button></li>
                          </ul>
                        </div>
                      </div>
                      <!-- 定制化一站式检测方案楼层 -->
                      <div v-if='floor.type === 12'>
                        <h2>
                          定制化一站式检测方案楼层
                          <el-button-group style="float: right;">
                            <el-button type="primary" size='small' icon="el-icon-arrow-up"
                              @click='handleMoveUp(floorIndex)' :disabled="!floorIndex">向上</el-button>
                            <el-button type="primary" size='small' icon="el-icon-arrow-down"
                              @click='handleMoveDown(floorIndex)'
                              :disabled="floorIndex === detail.content.floors.length - 1">向下</el-button>
                          </el-button-group>
                        </h2>
                         <div style="margin: 10px 0;">
                         <div>
                            整个楼层是否显示：
                            <el-switch v-model="floor.showFloor" :active-value='1'
                              :inactive-value='0'></el-switch>
                         </div>
                          主标题：
                          <el-input v-model='floor.title' placeholder="请输入主标题...."
                            style="width: 300px;"></el-input>
                          是否显示<el-switch v-model="floor.showTitle" :active-value='1'
                            :inactive-value='0'></el-switch>
                        </div>
                        <div style="margin-bottom: 10px;">
                          子标题：
                          <el-input v-model='floor.subTitle' placeholder="请输入子标题...."
                            style="width: 800px;"></el-input>
                          是否显示<el-switch v-model="floor.showSubTitle" :active-value='1'
                            :inactive-value='0'></el-switch>
                        </div>
                        <div>
                          <ul>
                            <li v-for='(v2, index2) of floor.list' :key='index2' style="display: flex;">
                              <el-upload
                                :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                                :data='uploadData' :show-file-list="false"
                                :on-success="(response, file, fileList) => { return handleAvatarSuccess(response, file, fileList, 'plan', floorIndex, index2)}"
                                :before-upload="beforeAvatarUpload" name="file"
                                accept="image/jpeg,image/png,image/gif">
                                <template v-if='v2.img'>
                                  <img :src='v2.img' style="width: 360px; height: 200px;" />
                                </template>
                                <el-button v-else size="small" type="primary">背景图片</el-button>
                              </el-upload>
                              <el-input v-model='v2.link' placeholder="请输入链接地址..." style="width: 300px"></el-input>
                              <el-button-group style="float: right;">
                                <el-button size='small' type='danger' @click='handleDelServiceItem(floorIndex, index2)'>删除</el-button>
                                <el-button type="primary" size='small' icon="el-icon-arrow-up"
                                  @click='handleMoveServiceItemUp(floorIndex, index2)' :disabled="!index2">向上</el-button>
                                <el-button type="primary" size='small' icon="el-icon-arrow-down"
                                  @click='handleMoveServiceItemDown(floorIndex, index2)'
                                  :disabled="index2 === floor.list.length - 1">向下</el-button>
                              </el-button-group>
                            </li>
                            <li><el-button size='small' type='warning' @click='handleAddServiceItem(floorIndex)'>添加</el-button></li>
                          </ul>
                        </div>
                      </div>
                      <!-- 留言表单楼层 -->
                      <div v-if='floor.type === 13'>
                        <h2>
                          留言表单楼层
                          <el-button-group style="float: right;">
                            <el-button type="primary" size='small' icon="el-icon-arrow-up"
                              @click='handleMoveUp(floorIndex)' :disabled="!floorIndex">向上</el-button>
                            <el-button type="primary" size='small' icon="el-icon-arrow-down"
                              @click='handleMoveDown(floorIndex)'
                              :disabled="floorIndex === detail.content.floors.length - 1">向下</el-button>
                          </el-button-group>
                        </h2>
                        <!-- <div style="margin: 10px 0;">
                          主标题：
                          <el-input v-model='floor.title' placeholder="请输入主标题...."
                            style="width: 300px;"></el-input>
                          是否显示<el-switch v-model="floor.showTitle" :active-value='1'
                            :inactive-value='0'></el-switch>
                        </div> -->
                      </div>
                      <!-- 企业动态 -->
                      <div v-if='floor.type === 7'>
                        <h2>
                          企业动态
                          <el-button-group style="float: right;">
                            <el-button type="primary" size='small' icon="el-icon-arrow-up"
                              @click='handleMoveUp(floorIndex)' :disabled="!floorIndex">向上</el-button>
                            <el-button type="primary" size='small' icon="el-icon-arrow-down"
                              @click='handleMoveDown(floorIndex)'
                              :disabled="floorIndex === detail.content.floors.length - 1">向下</el-button>
                          </el-button-group>
                        </h2>
                        <div style="margin: 10px 0;">
                          主标题：
                          <el-input v-model='floor.title' placeholder="请输入主标题...."
                            style="width: 300px;"></el-input>
                          是否显示<el-switch v-model="floor.showTitle" :active-value='1'
                            :inactive-value='0'></el-switch>
                        </div>
                        <div style="margin-bottom: 10px;">
                          子标题：
                          <el-input v-model='floor.subTitle' placeholder="请输入子标题...."
                            style="width: 800px;"></el-input>
                          是否显示<el-switch v-model="floor.showSubTitle" :active-value='1'
                            :inactive-value='0'></el-switch>
                        </div>
                        <div>
                          更多文章链接：
                          <el-input v-model='floor.moreLink' placeholder="请输更多文章链接...."
                            style="width: 300px;"></el-input>
                        </div>
                        <div>
                          <ul>
                            <li v-for='(v2, index2) of floor.list' :key='index2' style='display: flex; align-items: center; margin-bottom: 10px;'>
                              <el-input v-model='v2.link' placeholder="请输入URL..." style="width: 400px;">
                                <template slot="prepend">{{view_url}}</template>
                              </el-input>
                              <el-button @click='handleNewsSearch(v2.link, index2, floorIndex)' type='primary' size='small'>查找
                              </el-button>
                              标题：<el-input v-model='v2.name' placeholder="请输入名称..." style="width: 320px;"></el-input>
                              预览数量：<el-tag>{{v2.visits_num}}</el-tag>
                              <el-button @click='handleNewsDel(floorIndex, index2)' size='small' type='danger'>删除</el-button>
                            </li>
                          </ul>
                          <el-button @click='handleNewsAdd(floorIndex)' size='small' type='warning'>添加</el-button>
                        </div>
                      </div>
                       <!-- 留言表单楼层 -->
                       <div v-if='floor.type === 18'>
                        <h2>
                          留言表单楼层
                          <el-button-group style="float: right;">
                            <el-button type="primary" size='small' icon="el-icon-arrow-up"
                              @click='handleMoveUp(floorIndex)' :disabled="!floorIndex">向上</el-button>
                            <el-button type="primary" size='small' icon="el-icon-arrow-down"
                              @click='handleMoveDown(floorIndex)'
                              :disabled="floorIndex === detail.content.floors.length - 1">向下</el-button>
                          </el-button-group>
                        </h2>
                         <!-- <div style="margin: 10px 0;">
                          主标题：
                          <el-input v-model='floor.title' placeholder="请输入主标题...."
                            style="width: 300px;"></el-input>
                          是否显示<el-switch v-model="floor.showTitle" :active-value='1'
                            :inactive-value='0'></el-switch>
                        </div> -->
                      </div>
                       <!-- 自定义楼层 -->
                       <div v-if='floor.type === 9'>
                        <h2>
                          自定义楼层
                          <el-button-group style="float: right;">
                            <el-button type="primary" size='small' icon="el-icon-arrow-up"
                              @click='handleMoveUp(floorIndex)' :disabled="!floorIndex">向上</el-button>
                            <el-button type="primary" size='small' icon="el-icon-arrow-down"
                              @click='handleMoveDown(floorIndex)'
                              :disabled="floorIndex === detail.content.floors.length - 1">向下</el-button>
                            <el-button type="primary" size='small' icon="el-icon-arrow-down"
                              @click='handleDeleteFloor(floorIndex)'
                              >删除</el-button>
                          </el-button-group>
                        </h2>
                        <div style="margin: 10px 0;">
                          主标题：
                          <el-input v-model='floor.title' placeholder="请输入主标题...."
                            style="width: 300px;"></el-input>
                          是否显示<el-switch v-model="floor.showTitle" :active-value='1'
                            :inactive-value='0'></el-switch>
                        </div>
                        <textarea cols="60" rows="5" class="el-input__inner foolist"  :id="'Editor' + floorIndex"
                          v-model="floor.content" style="height:100px"
                          placeholder="请输入内容．．．"></textarea>
                      </div>
                    </div>
                    <h2>
                      <el-button @click='handleAddtionFloor' type='warning'>添加楼层</el-button>
                    </h2>
                  </el-form-item>
                  <el-form-item label="SEO Title">
                    <el-input v-model="detail.seo.title" placeholder="请输入SEO Title..." maxlength="50"></el-input>
                  </el-form-item>
                  <el-form-item label="SEO Keywords">
                    <el-input v-model="detail.seo.keywords" placeholder="请输入SEO Keywords..." maxlength="50"></el-input>
                  </el-form-item>
                  <el-form-item label="SEO Description">
                    <el-input v-model="detail.seo.description" placeholder="请输入SEO Description..." maxlength="200"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="warning" @click="onSubmit"
                      style='position: fixed; right: 20px; bottom: 20px; z-index: 2;'>发 布</el-button>
                    <el-button type="primary" @click="handleCopy"
                      style='position: fixed; right: 20px; bottom: 80px; z-index: 2;'>复 制</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
            <el-dialog v-loading="loading" title="导航维护" :visible.sync="dialogNavigation" width="700px">
              <el-button type="primary" @click="handleAddtionNavigation">添加导航</el-button>
              </div>
              <el-table :data="detail.content.navigations" style="width: 100%">
                <el-table-column prop="name" label="导航名称">
                  <template slot-scope="scope">
                    <el-input placeholder="请输入导航名称..." v-model='scope.row.name'></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="link" label="链接地址">
                  <template slot-scope="scope">
                    <el-input placeholder="请输入导航链接地址..." v-model='scope.row.link'></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80">
                  <template slot-scope="scope">
                    <el-button @click="handleDeltetNavigation(scope.row, scope.$index)" type="text" size="small">删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="handleSaveNavigation">保存</el-button>
              </div>
            </el-dialog>
            <el-dialog :title="title" :visible.sync="copyDialog.visibale" width="600px">
              <el-form :model="copyDialog" label-width="100px" :rules="rules" ref="form">
                <el-form-item label="实验室名称" prop='name'>
                  <el-input v-model='copyDialog.name' placeholder="请输入实验室名称..."></el-input>
                </el-form-item>
                <el-form-item label="实验室别名" prop='alias'>
                  <el-input v-model='copyDialog.alias' placeholder="请输入实验室别名..."></el-input>
                </el-form-item>
                <!-- <el-form-item label="">
                  <el-checkbox v-model='copyDialog.is_delete'>复制上下架</el-checkbox>
                </el-form-item> -->
              </el-form>
              <span slot="footer" class="dialog-footer">
                <el-button @click="copyDialog.visibale = false">取 消</el-button>
                <el-button type="primary" @click='copy'>确 定</el-button>
              </span>
            </el-dialog>
          </el-main>
      </el-container>
  </el-container>

  <script src="/static/tinymce/tinymce.min.js"></script>
  <script>
    var tinyConfig = {
      height: 600,
      language: 'zh_CN',
      menubar: false,
      plugins: 'advlist autolink link image lists charmap print preview code textcolor table paste media',
      toolbar: 'undo redo | formatselect fontsizeselect bold italic underline forecolor backcolor  | alignleft aligncenter alignright alignjustify superscript subscript | bullist numlist outdent indent | removeformat | image media link table | code preview',
      fontsize_formats: '12px 14px 16px 18px 20px 24px 36px',
      file_browser_callback_types: 'image',
      // images_upload_url: '<%- ticMallHost %>ticCenter/business/api.v0.platform/fileUpload/uploadOss',
      images_reuse_filename: true,
      // images_upload_base_path: '/',
      relative_urls: false,
      branding: false,
      width: '100%',
      images_upload_handler: function (blobInfo, success, failure) {
        let that = this
        let fd = new FormData()
        let file = blobInfo.blob();
        fd.append('file', file, file.name)
        fd.append('systemID', 5);
        let config = {
          headers: { 'Content-Type': 'multipart/form-data' }
        }
        axios.post('<%- ticMallHost %>ticCenter/business/api.v0.platform/fileUpload/uploadOss', fd, config)
          .then(function (result) {
            if (result.data.resultCode === '0') {
              success(result.data.data.fileName)
            } else {
              that.$message.success(result.data.res);
              failure()
            }
          });
      }
    };
    var main = new Vue({
      el: '#Main',
      data: {
        mview_url: '<%- mview_url %>',
        view_url: '<%- view_url %>',
        detail: {
          id: <%- detail.id %>,
          is_publish: <%- detail.is_publish %>,
          name: '<%- detail.name %>',
          alias: '<%- detail.alias %>',
          seo: <%- detail.seo %>,
          content: <%- detail.content %>,
          type: '<%- detail.type %>',
          is_delete: 0
        },
        ticMallHost: '<%- ticMallHost %>',
        uploadData: {
          systemID: 5
        },
        loading: false,
        dialogNavigation: false,
        copyDialog: {
          visibale: false,
          name: '',
          alias: '',
          // is_delete: false
        }, // 复制弹窗
        rules: {
          name: [
            { required: true, message: '请输入实验室名称', trigger: 'blur' },
          ],
          alias: [
            { required: true, message: '请输入实验室别名', trigger: 'blur' },
          ],
        },
        title: ''
      },
      methods: {
        onSubmit() {
          // 获取富文本内容
          this.detail.content.floors.forEach(function (item, i) {
            var thisid = 'Editor' + i;
            if (tinymce.get(thisid)) {
            item.content = tinymce.get(thisid).getContent();
          }
          });
          const that = this
          axios.post('/lab/save', this.detail)
            .then(function (result) {
              if (result.data.success) {
                that.$message.success('保存成功');
                window.location.reload()
              } else {
                that.$message.error(result.data.msg || '系统错误，请稍后重试');
              }
              this.loading = false
            });
        },
        handleCopy() {
          this.copyDialog.visibale = true
          this.copyDialog.name = `复制 - ${this.detail.name}`
          this.copyDialog.alias = `copy - ${this.detail.alias}`
        },
        copy() {
          this.$refs['form'].validate((valid) => {
            if (valid) {
              var loading = this.$loading({
                lock: true,
                text: '复制中，请等待...',
                spinner: 'el-icon-loading',
                background: 'rgba(255, 255, 255, 0.7)'
              });
              const cloneForm = JSON.parse(JSON.stringify(this.detail))
              cloneForm.name = this.copyDialog.name;
              cloneForm.alias = this.copyDialog.alias;
              // if (!this.copyDialog.is_delete) cloneForm.is_delete = 1
              cloneForm.is_copy = 1 // 打上复制标签
              // 新增sku
              axios.post('/lab/addtion', cloneForm)
                .then((result) => {
                  if (result.data.success) {
                    window.location.href = `/labMobile/${result.data.id}`
                  } else {
                    loading.close();
                    this.$message.error(result.data.msg)
                  }
                });
            }
          })
        },
        handleAddtionNavigation() {
          this.detail.content.navigations.push({})
        },
        handleSaveNavigation() {
          this.dialogNavigation = false
        },
        handleDeltetNavigation(row, index) {
          this.$confirm('您确认删除该菜单?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.detail.content.navigations.splice(index, 1)
          }).catch(() => {

          });
        },
        // 上传图片
        handleAvatarSuccess(res, file, fileList, path, floorIndex, currIndex) {
          if (res.resultCode === '0') {
            if (path === 'icon1' || path === 'icon2' || path === 'pic1' || path === 'pic2' || path === 'bannerBg') {
              this.detail.content[path] = res.data.fileName
            } else if (path === 'videoSource') {
              this.detail.content.floors[floorIndex].videoSource = res.data.fileName
            } else if (path === 'floorAboutPic') {
              this.detail.content.floors[floorIndex].floorAboutPic = res.data.fileName
            } else if (path === 'service') {
              this.detail.content.floors[floorIndex].list[currIndex].bg = res.data.fileName
            } else if (path === 'case') {
              this.detail.content.floors[floorIndex].list[currIndex].img = res.data.fileName
            } else if (path === 'serviceItem' || path === 'plan') {
              this.detail.content.floors[floorIndex].list[currIndex].img = res.data.fileName
            }
          } else {
            this.$message.error(res.resultMsg);
          }
        },
        beforeAvatarUpload(file) {
          const isJPG = file.type === 'image/jpeg';
          const isPNG = file.type === 'image/png';
          const isGIF = file.type === 'image/gif';
          const isLt2M = file.size / 1024 / 1024 < 10;

          if (!isJPG && !isPNG && !isGIF) {
            this.$message.error('图片格式错误!');
            return false;
          }
          if (!isLt2M) {
            this.$message.error('上传图片大小不能超过 10MB!');
            return false;
          }
        },
        handleDelService(index2, floorIndex) {
          this.$confirm('您确认删除?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.detail.content.floors[floorIndex].list.splice(index2, 1)
          }).catch(() => {

          });
        },
        handleAddSubService(index2, floorIndex) {
          this.detail.content.floors[floorIndex].list[index2].items.push({})
        },
        // 添加热门测试项目
        handleAddHotItem(floorIndex) {
          if (this.detail.content.floors[floorIndex].list.length < 9) {
            this.detail.content.floors[floorIndex].list.push({
              title: '',
            })
          } else {
            this.$message.error('最多只能添加9个')
          }
        },
        handleDelHotItem(floorIndex, index2) {
          this.$confirm('您确认删除?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.detail.content.floors[floorIndex].list.splice(index2, 1)
          }).catch(() => {

          });
        },
        // 添加服务项目涵盖
        handleAddServiceItem(floorIndex) {
          this.detail.content.floors[floorIndex].list.push({
            img: '',
          })
        },
        handleDelServiceItem(floorIndex, index2) {
          this.$confirm('您确认删除?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.detail.content.floors[floorIndex].list.splice(index2, 1)
          }).catch(() => {

          });
        },
        // 添加服务项目涵盖
        handleAddPlan(floorIndex) {
          this.detail.content.floors[floorIndex].list.push({
            img: '',
          })
        },
        handleAddService(floorIndex) {
          this.detail.content.floors[floorIndex].list.push({
            title: '',
            des: '',
            bg: ''
          })
        },
        handlePlanSearch(link, index, floorIndex) {
          if (link) {
            const that = this
            this.loading = true
            axios.post('/homepage/getSku', { skuUrl: link, _csrf: '<%- csrf %>' })
              .then(result => {
                if (result.data.success) {
                  this.detail.content.floors[floorIndex].list[index].title = result.data.data.name
                  this.detail.content.floors[floorIndex].list[index].bg = result.data.data.thumb_img || result.data.data.cover_img
                  this.detail.content.floors[floorIndex].list[index].des = result.data.data.description
                  this.detail.content.floors[floorIndex].list[index].link = link
                } else {
                  this.$message.error(result.data.msg);
                }
                that.loading = false
              }).catch(e => {
                this.loading = false
                return e;
              });
          }
        },
        handleAddPlan(floorIndex) {
          this.detail.content.floors[floorIndex].list.push({})
        },
        handleDelPlan(index2) {
          this.$confirm('您确认删除?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.detail.content.floors[floorIndex].list.splice(index2, 1)
          }).catch(() => {

          });
        },
        handleNewsSearch(link, index, floorIndex) {
          const that = this
          this.loading = true
          const id = link.substring(link.indexOf('-') + 1, link.lastIndexOf('.'))
          axios.post('/case/dtl', { id, _csrf: '<%- csrf %>' })
            .then(result => {
              if (result.data.success) {
                this.detail.content.floors[floorIndex].list[index].name = result.data.detail.title
                this.detail.content.floors[floorIndex].list[index].visits_num = result.data.detail.hits
                this.detail.content.floors[floorIndex].list[index].link = link
              } else {
                this.$message.error(result.data.msg);
              }
              that.loading = false
            }).catch(e => {
              this.loading = false
              return e;
            });
        },
        handleNewsDel(floorIndex, index2) {
          this.$confirm('您确认删除?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.detail.content.floors[floorIndex].list.splice(index2, 1)
          }).catch(() => {

          });
        },
        handleNewsAdd(floorIndex) {
          this.detail.content.floors[floorIndex].list.push({})
        },
        handleMoveUp(index) {
          this.detail.content.floors[index] = this.detail.content.floors.splice(index - 1, 1, this.detail.content.floors[index])[0];
          this.$nextTick(() => {
            tinyConfig.selector = '.foolist';
            tinymce.init(tinyConfig);
          })
          // this.detail = JSON.parse(JSON.stringify(this.detail))
        },
        handleMoveDown(index) {
          this.detail.content.floors[index] = this.detail.content.floors.splice(index + 1, 1, this.detail.content.floors[index])[0];
          this.$nextTick(() => {
            tinyConfig.selector = '.foolist';
            tinymce.init(tinyConfig);
          })
          // this.detail = JSON.parse(JSON.stringify(this.detail))
        },
        handleMoveServiceItemUp(foolIndex, index) {
          this.detail.content.floors[foolIndex].list[index] = this.detail.content.floors[foolIndex].list.splice(index - 1, 1, this.detail.content.floors[foolIndex].list[index])[0];
        },
        handleMoveServiceItemDown(foolIndex, index) {
          this.detail.content.floors[foolIndex] = this.detail.content.floors[foolIndex].list.splice(index + 1, 1, this.detail.content.floors[foolIndex].list[index])[0];
        },
        // 添加楼层
        handleAddtionFloor() {
          this.detail.content.floors.push({
            type: 9,
            title: '',
            showTitle: 1,
            subTitle: '',
            showSubTitle: 1,
          })

          this.$nextTick(() => {
            tinyConfig.selector = '.foolist';
            tinymce.init(tinyConfig);
          })
          this.detail = JSON.parse(JSON.stringify(this.detail))
        },
        // 删除自定义楼层
        handleDeleteFloor(floorIndex) {
          this.$confirm('您确认删除?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.detail.content.floors.splice(floorIndex, 1)
          }).catch(() => {

          });
        },
        // 区域修改
        handleAddArea(floorIndex) {
          this.detail.content.floors[floorIndex].list.push({
            name: '',
            subList: [
              {
              city: '',
              address: ''
            }]
          })
        },
        handleDelArea(index2, floorIndex) {
          this.$confirm('您确认删除?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.detail.content.floors[floorIndex].list.splice(index2, 1)
          }).catch(() => {

          });
        },
        handleAddSubArea(index2, floorIndex) {
          this.detail.content.floors[floorIndex].list[index2].subList.push({})
        },
        handleDelSubArea(index2, index3, floorIndex) {
          this.$confirm('您确认删除?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.detail.content.floors[floorIndex].list[index2].subList.splice(index3, 1)
          }).catch(() => {

          });
        },
      },
      mounted() {
        this.$nextTick(() => {
          document.getElementById('preLoading').style.display = 'none';
          tinyConfig.selector = '.foolist';
          tinymce.init(tinyConfig);
          this.title = `复制实验室：${window.location.pathname}`
        })
      }
    });
  </script>

  <style>
    .lab-detail {}

    .lab-detail-zizhi {
      display: flex;
      justify-content: start;
    }

    .lab-detail-zizhi button {
      margin-right: 20px;
    }

    .lab-detail-zizhi div {
      display: flex;
    }

    h2 {
      border-top: 1px dashed #ddd;
      padding: 20px 0;
    }
  </style>
  <% include footer.html %>