module.exports = (options) => {
	return async function userCheck(ctx, next) {

		ctx.session.maxAge = 0;

		let userInfo = ctx.session.userInfo;

		if (!ctx.session || !userInfo) {
			ctx.redirect('/');
			return;
		}

		ctx.session.apiData = null;
		let apiData = await ctx.service.admin.getCache(ctx.session.userInfo.username || ctx.session.userInfo.name);
		if (userInfo.type != 1 && (apiData == null || apiData.data == '' || (apiData.data.userDelete && apiData.data.userDelete.toString() == '1'))) {
			ctx.session = null;
			if (ctx.request.method == 'POST') {
				ctx.body = {
					code: 0,
					msg: '请重新登录',
					needReLogin: 1,
					loginUrl: '/login'
				};
				return;
			} else {
				await ctx.render('error', {});
				return;
			}
		} else if (userInfo.type != 1 && apiData.data.roleid_json) {
			apiData.data.roleid_json = apiData.data.roleid_json;
			if (apiData.data.roleid_json.length == 0) {
				ctx.session = null;
				await ctx.render('error', {});
				return;
			}
		}
		if (userInfo.type != 1 && options != '' && options != 'common') {
			if (ctx.request.url.indexOf("/catalog/service/skus/") >= 0) {
				let editskuRule = 0;
				const purviewList = await ctx.service.admin.getMenuPurview2(ctx.session.userInfo.username || ctx.session.userInfo.name, '编辑+归类');
				for (var menuId of purviewList) {
					if (menuId == 3) {
						//3是菜单表里面的服务分类
						editskuRule = 1;
						break;
					}
				}
				if (editskuRule == 0) {
					ctx.body = {
						code: 0,
						msg: '未授权操作。'
					};
					return;
				}
			} else if (ctx.request.url.indexOf("/catalog/trade/skus/") >= 0) {
				let editskuRule = 0;
				const purviewList = await ctx.service.admin.getMenuPurview2(ctx.session.userInfo.username || ctx.session.userInfo.name, '编辑+归类');
				for (var menuId of purviewList) {
					if (menuId == 4) {
						//4是菜单表里面的行业分类
						editskuRule = 1;
						break;
					}
				}
				if (editskuRule == 0) {
					ctx.body = {
						code: 0,
						msg: '未授权操作。'
					};
					return;
				}
			} else {
				var localmenuObj = await ctx.service.admin.getMenuPurview(ctx.session.userInfo.username || ctx.session.userInfo.name);
				var localmenu = [];
				for (var item in localmenuObj) {
					localmenu.push(item);
				}
				ctx.session.userInfo.localMenu = localmenu;
				if (localmenu.indexOf(options) == -1) {
					if (ctx.request.method == 'GET') {
						if (localmenu.length == 0) {
							ctx.session = null;
							await ctx.render('error', {});
							return;
						}
						await ctx.render('error', {});
						return;
					} else if (ctx.request.method == 'POST') {
						ctx.session = null;
						ctx.body = {
							code: 0,
							msg: '请重新登录',
							needReLogin: 1,
							loginUrl: '/login'
						};
						return;
					}
				}
			}
		}
		await next();
		if (ctx.session.userInfo != null) {
			ctx.session.apiData = null;
		}
	};
};