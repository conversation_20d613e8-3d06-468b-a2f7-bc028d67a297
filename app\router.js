'use strict';

/**
 * @param {Egg.Application} app - egg application
 */
module.exports = app => {
  const {
    router,
    controller
  } = app;

  const fs = require('fs');
  fs.stat('./tmp', function (err) {
    if (err) {
      fs.mkdir('./tmp', function (e) { });
    }
  });

  const userCheck = app.middlewares.userCheck('common');
  /*
      ticcms_first_list_classify 分类   ticcms_first_list_skuconf sku配置 
      ticcms_first_list_firstconf 首页配置   ticcms_first_list_news 新闻中心
      ticcms_first_list_explorer 资料下载 ticcms_first_list_case 资讯中心
      ticcms_first_list_other 其他内容 ticcms_first_list_operation 运维
      ticcms_first_list_data 数据统计 ticcms_first_list_coupleBack 反馈
      ticcms_first_list_setting 设置
  */

  const userCheckClassify = app.middlewares.userCheck('ticcms_first_list_classify');
  const userCheckSku = app.middlewares.userCheck('ticcms_first_list_skuconf');
  const userCheckFirstconf = app.middlewares.userCheck('ticcms_first_list_firstconf');
  const userCheckNews = app.middlewares.userCheck('ticcms_first_list_news');
  const userCheckExplorer = app.middlewares.userCheck('ticcms_first_list_explorer');
  const userCheckCase = app.middlewares.userCheck('ticcms_first_list_case');
  const userCheckOther = app.middlewares.userCheck('ticcms_first_list_other');
  const userCheckOperation = app.middlewares.userCheck('ticcms_first_list_operation');
  const userCheckData = app.middlewares.userCheck('ticcms_first_list_data');
  const userCheckCoupleBack = app.middlewares.userCheck('ticcms_first_list_coupleBack');
  const userCheckSettig = app.middlewares.userCheck('ticcms_first_list_setting');
  const userCheckLogs = app.middlewares.userCheck('ticcms_first_list_log');
  const usernoChecked = app.middlewares.userCheck('common');
  const menuPurviewDashboard = app.middlewares.userCheck('1'); //工作台
  const menuPurviewServiceAndTrade = app.middlewares.userCheck("ServicesAndTrade"); //分类配置-服务分类
  const menuPurviewService = app.middlewares.userCheck('3'); //分类配置-服务分类
  const menuPurviewTrade = app.middlewares.userCheck('4'); //分类配置-行业分类
  const menuPurviewSKUService = app.middlewares.userCheck('6'); //SKU配置-服务
  const menuPurviewSKUSolution = app.middlewares.userCheck('7'); //SKU配置-解决方案
  const menuPurviewHomepage = app.middlewares.userCheck('8'); //首页配置
  const menuPurviewNewsList = app.middlewares.userCheck('10'); //新闻中心-新闻列表
  const menuPurviewNewsType = app.middlewares.userCheck('11'); //新闻中心-新闻类型
  const menuPurviewNewsDraft = app.middlewares.userCheck('12'); //新闻中心-草稿箱
  const menuPurviewResourceList = app.middlewares.userCheck('14'); //资料下载-资料列表
  const menuPurviewResourceType = app.middlewares.userCheck('15'); //资料下载-资料类型
  const menuPurviewResourceDraft = app.middlewares.userCheck('16'); //资料下载-草稿箱
  const menuPurviewCaseList = app.middlewares.userCheck('18'); //资讯中心-资料列表
  const menuPurviewCaseType = app.middlewares.userCheck('19'); //资讯中心-资料类型
  const menuPurviewCaseDraft = app.middlewares.userCheck('20'); //资讯中心-草稿箱
  const menuPurviewOther = app.middlewares.userCheck('21'); //其他内容
  const menuPurviewOperator = app.middlewares.userCheck('22'); //运营内容维护
  const menuPurviewAnalysisUrl = app.middlewares.userCheck('24'); //数据统计-页面来源分析
  const menuPurviewAnalysisKeyWord = app.middlewares.userCheck('25'); //数据统计-关键词分析
  const menuPurviewAnalysisRecord = app.middlewares.userCheck('26'); //数据统计-400点击分析
  const menuPurviewAnalysisDownload = app.middlewares.userCheck('40'); //数据统计-400点击分析
  const menuPurviewTicket = app.middlewares.userCheck('27'); //反馈中心
  const menuPurviewSettingRegion = app.middlewares.userCheck('29'); //目的国
  const menuPurviewSettingCitys = app.middlewares.userCheck('30'); //城市
  const menuPurviewLog = app.middlewares.userCheck('31'); //日志权限
  const menuPurviewAdmin = app.middlewares.userCheck('33'); //权限管理-用户配置
  const menuPurviewRole = app.middlewares.userCheck('34'); //权限管理-分组配置
  const menuPurviewSuperAdmin = app.middlewares.userCheck('35'); //权限管理-超级用户配置
  const menuPurviewAdminfreeze = app.middlewares.userCheck('36'); //权限管理-冻结用户
  // const menuPurviewAdminfreeze = app.middlewares.userCheck('37'); //pc首页
  // const menuPurviewAdminfreeze = app.middlewares.userCheck('38'); //移动端首页
  const menuPurviewHotWord = app.middlewares.userCheck('39'); //关键词
  const menuPurviewSettingTradeRelevance = app.middlewares.userCheck('43'); //产品行业映射
  const menuPurviewSettingBuManage = app.middlewares.userCheck('44'); //bu管理
  const menuPurviewSettingIMtool = app.middlewares.userCheck('45'); // IM工具
  const menuPurviewNavigation = app.middlewares.userCheck('46'); // 导航菜单
  const menuPurviewLab = app.middlewares.userCheck('49'); // 实验室
  const menuPurviewCoreSupplier = app.middlewares.userCheck('51'); // 核心供应商
  const menuPurviewOrderLine = app.middlewares.userCheck('52'); // 集合页
  const menuPurviewQuestion = app.middlewares.userCheck('58'); // 问题配置

  router.get('/', controller.passport.check);

  //工作台
  router.get('/dashboard', menuPurviewDashboard, controller.dashboard.default);
  router.post('/dashboard/checkedlist/get', menuPurviewDashboard, controller.dashboard.getCheckedList);
  router.post('/dashboard/sendcheckedlist/get', menuPurviewDashboard, controller.dashboard.getSendCheckedList);
  router.post('/dashboard/checkoperator', menuPurviewDashboard, controller.dashboard.checkflowOperator);
  router.post('/checkflow/apply', usernoChecked, controller.checkflow.apply);

  //管理员
  router.get('/admin/list', menuPurviewAdmin, controller.admin.adminList);
  router.get('/admin/list/get', menuPurviewAdmin, controller.admin.getList);
  router.post('/admin/list/get', menuPurviewAdmin, controller.admin.getList);
  router.get('/admin/add', menuPurviewAdmin, controller.admin.adminAdd);
  router.post('/admin/save', menuPurviewAdmin, controller.admin.adminSave);
  router.get('/admin/edit/:id', menuPurviewAdmin, controller.admin.adminEdit);
  router.post('/admin/delete', menuPurviewAdmin, controller.admin.adminDelete);
  router.get('/adminfreeze/list', menuPurviewAdminfreeze, controller.adminfreeze.adminfreezeList);
  router.get('/adminfreeze/list/get', menuPurviewAdminfreeze, controller.adminfreeze.getList);
  router.post('/adminfreeze/list/get', menuPurviewAdminfreeze, controller.adminfreeze.getList);
  router.post('/adminfreeze/active', menuPurviewAdminfreeze, controller.adminfreeze.adminfreezeActive);
  router.get('/role/list', menuPurviewRole, controller.role.roleList);
  router.get('/role/list/get', menuPurviewRole, controller.role.getList);
  router.post('/role/list/get', menuPurviewRole, controller.role.getList);
  router.get('/role/add', menuPurviewRole, controller.role.roleAdd);
  router.get('/role/edit/:id', menuPurviewRole, controller.role.roleEdit);
  router.post('/role/save', menuPurviewRole, controller.role.roleSave);
  router.post('/role/setRoleEffect', menuPurviewRole, controller.role.roleEffect);
  router.post('/role/delete', menuPurviewRole, controller.role.roleDelete);
  router.get('/role/purview/:id', menuPurviewRole, controller.role.rolePurview);
  router.post('/role/rolePurviewSave', menuPurviewRole, controller.role.rolePurviewSave);
  router.post('/role/getRoleList', usernoChecked, controller.role.roleGetCheckedRoleList);
  router.get('/superadmin/list', menuPurviewSuperAdmin, controller.superadmin.superadminList);
  router.get('/superadmin/editpwd/:id', menuPurviewSuperAdmin, controller.superadmin.superadminEditPassword); //修改密码
  router.post('/superadmin/savepwd', menuPurviewSuperAdmin, controller.superadmin.superadminSavePassword); //修改密码
  router.get('/superadmin/list/get', menuPurviewSuperAdmin, controller.superadmin.getList);
  router.post('/superadmin/list/get', menuPurviewSuperAdmin, controller.superadmin.getList);
  router.get('/superadmin/add', menuPurviewSuperAdmin, controller.superadmin.superadminAdd);
  router.post('/superadmin/save', menuPurviewSuperAdmin, controller.superadmin.superadminSave);
  router.get('/superadmin/edit/:id', menuPurviewSuperAdmin, controller.superadmin.superadminEdit); //edit
  router.post('/superadmin/delete', menuPurviewSuperAdmin, controller.superadmin.superadminDelete); //删除

  //服务
  router.get('/catalog/service', menuPurviewService, controller.catalog.serviceCata); //列表

  //行业
  router.get('/catalog/trade', menuPurviewTrade, controller.catalog.trade); //列表
  router.post('/catalog/trade/add', usernoChecked, controller.catalog.tradeAdd); //添加
  router.post('/catalog/trade/changeValue', usernoChecked, controller.catalog.tradeUpdateSingle); //单项更新
  router.post('/catalog/trade/changeSort', usernoChecked, controller.catalog.tradeSort); //排序
  router.post('/catalog/trade/delete', usernoChecked, controller.catalog.tradeDelete); //删除
  router.post('/catalog/edit', usernoChecked, controller.catalog.tradeSave); //编辑保存
  router.get('/catalog/:type/:id', usernoChecked, controller.catalog.tradeEdit); //编辑

  //还没写
  router.get('/catalog/:bigType/skus/:id', usernoChecked, controller.catalog.catalogSkusList); //归类sku
  router.get('/catalog/:bigType/solution/:id', usernoChecked, controller.catalog.catalogSolutionList); //归类解决方案

  router.post('/sku/service/saveSort', usernoChecked, controller.sku.saveSort); //归类排序
  router.post('/catalog/getCatalogSkus', usernoChecked, controller.catalog.getCatalogSkus2); //列表
  router.post('/catalog/saveRow', usernoChecked, controller.catalog.saveRow); //更名
  router.post('/catalog/defaultSort', usernoChecked, controller.catalog.defaultSort); //默认排序

  //SKU
  router.get('/sku/service', menuPurviewSKUService, controller.sku.skuList); //列表
  router.get('/sku/service/:alias', menuPurviewSKUService, controller.sku.skuAdd); //新增、编辑
  router.post('/sku/service/new', menuPurviewSKUService, controller.sku.skuEdit); //新增
  router.post('/sku/service/edit', menuPurviewSKUService, controller.sku.skuEdit2); //编辑
  router.post('/sku/service/delete', menuPurviewSKUService, controller.sku.skuDelete); //删除
  router.post('/sku/service/changeValue', menuPurviewSKUService, controller.sku.skuChangeValue); //更新
  router.post('/sku/service/listfilter', menuPurviewSKUService, controller.sku.listFilter); //筛选
  router.post('/sku/service/getList', menuPurviewSKUService, controller.sku.getList); //列表
  router.post('/sku/service/drop', menuPurviewSKUService, controller.sku.dropAlias); //解除关联

  //Solution
  router.get('/sku/solution', menuPurviewSKUSolution, controller.sku.solutionList); //列表
  router.get('/sku/solution/:alias', menuPurviewSKUSolution, controller.sku.solutionAdd); //新增、编辑
  router.post('/sku/solution/new', menuPurviewSKUSolution, controller.sku.solutionEdit); //新增
  router.post('/sku/solution/edit', menuPurviewSKUSolution, controller.sku.solutionEdit2); //编辑
  router.post('/sku/solution/delete', menuPurviewSKUSolution, controller.sku.solutionDelete); //删除
  router.post('/sku/solution/changeValue', menuPurviewSKUSolution, controller.sku.solutionChangeValue); //更新
  router.post('/sku/solution/listfilter', menuPurviewSKUSolution, controller.sku.listFilter); //筛选
  router.post('/sku/solution/getList', menuPurviewSKUSolution, controller.sku.getList2); //列表
  router.post('/sku/getSkuDetail', menuPurviewSKUSolution, controller.sku.getSkuDetail); //获取详情
  router.post('/sku/list/qry', controller.external.qrySkuList); //modify multiple

  //设置 目的国
  router.get('/setting/region', menuPurviewSettingRegion, controller.setting.region); //列表
  router.post('/setting/region/add', menuPurviewSettingRegion, controller.setting.regionAdd); //添加
  router.post('/setting/region/info', menuPurviewSettingRegion, controller.setting.regionInfo); //单个信息
  router.post('/setting/region/edit', menuPurviewSettingRegion, controller.setting.regionEdit); //更新
  router.post('/setting/region/delete', menuPurviewSettingRegion, controller.setting.regionDelete); //删除

  //城市
  router.get('/setting/citys', menuPurviewSettingCitys, controller.setting.citys); //列表
  router.post('/setting/citys/add', menuPurviewSettingCitys, controller.setting.cityAdd); //添加
  router.post('/setting/citys/info', menuPurviewSettingCitys, controller.setting.cityInfo); //单个信息
  router.post('/setting/citys/edit', menuPurviewSettingCitys, controller.setting.cityEdit); //更新
  router.post('/setting/citys/delete', menuPurviewSettingCitys, controller.setting.cityDelete); //删除

  // 设置 产品行业映射
  router.get('/setting/tardeRelevance', menuPurviewSettingTradeRelevance, controller.setting.tardeRelevance); // 页面
  router.post('/setting/tardeRelevance/qry', menuPurviewSettingTradeRelevance, controller.setting.tardeRelevanceQry); // 列表
  router.post('/setting/tardeRelevance/add', menuPurviewSettingTradeRelevance, controller.setting.tardeRelevanceAdd); // 添加
  router.post('/setting/tardeRelevance/mod', menuPurviewSettingTradeRelevance, controller.setting.tardeRelevanceMod); // 修改
  router.post('/setting/tardeRelevance/del', menuPurviewSettingTradeRelevance, controller.setting.tardeRelevanceDel); // 删除
  router.post('/setting/tardeRelevance/sort', menuPurviewSettingTradeRelevance, controller.setting.tardeRelevanceSort); // 排序
  router.post('/setting/tardeRelevance/qryTask', menuPurviewSettingTradeRelevance, controller.setting.tardeRelevanceQryTask); // 保存描述信息
  router.post('/setting/tardeRelevance/saveTask', menuPurviewSettingTradeRelevance, controller.setting.tardeRelevanceSaveTask); // 保存描述信息
  router.post('/setting/tardeRelevance/getQuoteAlias', menuPurviewSettingTradeRelevance, controller.setting.getQuoteAlias); // 获取带有别名的服务列表

  // 设置 BU管理
  router.get('/setting/buManage', menuPurviewSettingBuManage, controller.setting.buManage); // 页面
  router.post('/setting/buManage/qry', controller.setting.buManageQry); // 列表
  router.post('/setting/buManage/add', menuPurviewSettingBuManage, controller.setting.buManageAdd); // 添加
  router.post('/setting/buManage/mod', menuPurviewSettingBuManage, controller.setting.buManageModify); // 修改
  router.post('/setting/buManage/del', menuPurviewSettingBuManage, controller.setting.buManageDel); // 删除

  // 设置IM工具
  router.get('/setting/IMtool', menuPurviewSettingIMtool, controller.setting.IMtool); // 页面
  router.post('/setting/IMtool/qry', menuPurviewSettingIMtool, controller.setting.IMtoolQry); // 列表
  router.post('/setting/IMtool/mod', menuPurviewSettingIMtool, controller.setting.IMtoolModify); // 修改

  // 导航菜单
  router.get('/navigation/service', menuPurviewNavigation, controller.navigation.navigationSerive); // 服务导航
  router.get('/navigation/industry', menuPurviewNavigation, controller.navigation.navigationIndustry); // 行业导航
  router.post('/navigation/save', menuPurviewNavigation, controller.navigation.navigationSave); //保存导航
  router.post('/navigation/delete', menuPurviewNavigation, controller.navigation.navigationDelete); //保存导航
  router.post('/navigation/query', menuPurviewNavigation, controller.navigation.navigationQuery); //查询导航

  // 实验室列表
  router.get('/lab/list', menuPurviewLab, controller.lab.labList); // 实验室列表页面
  router.get('/lab/:id', menuPurviewLab, controller.lab.labDetail); // 实验室详情
  router.get('/labMobile/:id', menuPurviewLab, controller.lab.labMobileDetail); // 移动端实验室详情
  router.post('/lab/list/qry', menuPurviewLab, controller.lab.labListQry); // 查询实验室列表
  router.post('/lab/save', menuPurviewLab, controller.lab.labSave); // 保存实验室
  router.post('/lab/addtion', menuPurviewLab, controller.lab.labAdd); // 添加实验室
  router.post('/lab/delete', menuPurviewLab, controller.lab.labDel); // 删除实验室
  router.post('/category/qry', menuPurviewLab, controller.external.qryCategory); // 删除实验室

  //upload
  router.post('/upload/img', userCheck, controller.upload.img); //
  router.post('/upload/imgEditor', userCheck, controller.upload.imgEditor); //
  router.post('/upload/file', userCheck, controller.upload.upFile); //
  router.post('/upload/img/oss', userCheck, controller.external.uploadImg); //上传oss

  //news
  router.get('/news/type', menuPurviewNewsType, controller.news.typeList);
  router.post('/news/type/add', menuPurviewNewsType, controller.news.addType); //add 
  router.post('/news/type/get', menuPurviewNewsType, controller.news.getType); //get 
  router.post('/news/type/delete', menuPurviewNewsType, controller.news.typeDelete); //delete 
  router.post('/news/type/edit', menuPurviewNewsType, controller.news.typeEdit); //edit
  router.get('/news/list', menuPurviewNewsList, controller.news.newsList);
  router.post('/news/list/get', menuPurviewNewsList, controller.news.getList); //list
  router.get('/news/add', menuPurviewNewsList, controller.news.newsAdd); //add
  router.post('/news/save', menuPurviewNewsList, controller.news.newsSave); //add
  router.get('/news/edit/:id', menuPurviewNewsList, controller.news.newsEdit); //edit
  router.post('/news/delete', menuPurviewNewsList, controller.news.newsDelete); //delete
  router.post('/news/updatePublish', menuPurviewNewsList, controller.news.newsUpdatePublish); //update is_publish
  router.get('/news/draft', menuPurviewNewsDraft, controller.news.newsDraftList); // draft list
  router.post('/news/drop', menuPurviewNewsDraft, controller.news.drop); // multi drop
  router.post('/news/list/qry', controller.external.qryNewsList); //modify multiple

  //case
  router.get('/case/type', menuPurviewCaseType, controller.case.typeList);
  router.post('/case/type/add', menuPurviewCaseType, controller.case.addType); //add 
  router.post('/case/type/get', menuPurviewCaseType, controller.case.getType); //get 
  router.post('/case/type/delete', menuPurviewCaseType, controller.case.typeDelete); //delete 
  router.post('/case/type/edit', menuPurviewCaseType, controller.case.typeEdit); //edit
  router.get('/case/list', menuPurviewCaseList, controller.case.caseList);
  router.get('/case/list/backup', menuPurviewCaseList, controller.case.caseListBackup);
  router.post('/case/list/get', menuPurviewCaseList, controller.case.getList); //list
  router.get('/case/add', menuPurviewCaseDraft, controller.case.caseAdd); //add
  router.get('/case/add/backup', menuPurviewCaseDraft, controller.case.caseAddBackup); //add
  router.post('/case/save', menuPurviewCaseDraft, controller.case.caseSave); //add
  router.get('/case/edit/:id', menuPurviewCaseDraft, controller.case.caseEdit); //edit
  router.get('/case/edit/backup/:id', menuPurviewCaseDraft, controller.case.caseEditBackup); //edit
  router.post('/case/delete', menuPurviewCaseDraft, controller.case.caseDelete); //delete
  router.post('/case/updatePublish', menuPurviewCaseDraft, controller.case.caseUpdatePublish); //update is_publish
  router.get('/case/draft', menuPurviewCaseDraft, controller.case.caseDraftList); // draft list
  router.get('/case/draft/backup', menuPurviewCaseDraft, controller.case.caseDraftListBackup); // draft list
  router.post('/case/modify', menuPurviewCaseDraft, controller.case.caseModify); //modify
  router.post('/case/modify/multiple', menuPurviewCaseDraft, controller.case.caseModiyMultiple); //modify multiple
  router.post('/case/dtl', menuPurviewCaseDraft, controller.case.caseDtl); // get detail
  router.post('/case/copy', menuPurviewCaseDraft, controller.external.copyCase);
  router.post('/case/list/qry', controller.external.qryCaseList);
  router.post('/case/qryRelCaseCount', controller.external.qryRelCaseCount);
  // router.post('/case/findCaseForUrl', controller.case.findCaseForUrl);
  

  //resource
  router.get('/resource/type', menuPurviewResourceList, controller.resource.typeList);
  router.post('/resource/type/add', menuPurviewResourceList, controller.resource.addType); //add 
  router.post('/resource/type/get', menuPurviewResourceList, controller.resource.getType); //get 
  router.post('/resource/type/delete', menuPurviewResourceList, controller.resource.typeDelete); //delete 
  router.post('/resource/type/edit', menuPurviewResourceList, controller.resource.typeEdit); //edit
  router.get('/resource/list', menuPurviewResourceList, controller.resource.resourceList);
  router.post('/resource/list/get', menuPurviewResourceList, controller.resource.getList); //list
  router.get('/resource/add', menuPurviewResourceList, controller.resource.resourceAdd); //add
  router.post('/resource/save', menuPurviewResourceList, controller.resource.resourceSave); //add
  router.get('/resource/edit/:id', menuPurviewResourceList, controller.resource.resourceEdit); //edit
  router.post('/resource/delete', menuPurviewResourceList, controller.resource.resourceDelete); //delete
  router.post('/resource/updatePublish', menuPurviewResourceList, controller.resource.resourceUpdatePublish); //update is_publish
  router.get('/resource/draft', menuPurviewResourceDraft, controller.resource.resourceDraftList); // draft list
  router.post('/resource/modify', menuPurviewResourceDraft, controller.resource.resourceModify); //modify
  router.post('/resource/modify/multiple', menuPurviewResourceDraft, controller.resource.resourceModiyMultiple); //modify multiple
  router.post('/resource/list/qry', controller.external.qryResourceList); //modify multiple

  //other
  router.get('/other/list', menuPurviewOther, controller.other.otherList); //list
  router.get('/other/add', menuPurviewOther, controller.other.otherAdd); //add
  router.post('/other/save', menuPurviewOther, controller.other.otherEdit); //add
  router.get('/other/edit/:id', menuPurviewOther, controller.other.otherEditByid); //edit
  router.post('/other/delete', menuPurviewOther, controller.other.otherDelete); //edit
  router.post('/other/list/get', menuPurviewOther, controller.other.getList) // get list

  //operation
  router.get('/operation', menuPurviewOperator, controller.operation.hotList); //list
  router.post('/operation/add', menuPurviewOperator, controller.operation.skuAdd); //add
  router.post('/operation/skuDrop', menuPurviewOperator, controller.operation.skuDrop); // drop
  router.post('/operation/list', menuPurviewOperator, controller.operation.getList); // get list

  //preview
  router.get('/preview/sku/:id', controller.preview.sku);
  router.get('/preview/presku/:name', controller.preview.preskuView);
  router.post('/preview/presku', controller.preview.presku);
  router.get('/preview/news', controller.preview.newsList);
  router.get('/preview/news/detail-:id.html', controller.preview.newsDetail);
  router.get('/preview/prenews/:name', controller.preview.prenewsView);
  router.post('/preview/prenews', controller.preview.prenews);
  router.get('/preview/case', controller.preview.caseList);
  router.get('/preview/case/detail-:id.html', controller.preview.caseDetail);
  router.get('/preview/precase/:name', controller.preview.precaseView);
  router.post('/preview/precase', controller.preview.precase);

  //
  router.get('/preview/(service|industry)/:id', controller.preview.tradeDetail);
  router.get('/preview/(service|industry)/:id/service', controller.preview.tradeSkus);
  router.get('/preview/getCataSkus', controller.preview.getCataSkus);
  router.get('/preview/precata/:name', controller.preview.precataView);
  router.post('/preview/precata', controller.preview.precata);

  //
  router.get('/preview/overview/:alias', controller.preview.other);
  router.post('/preview/preother', controller.preview.preother);
  router.get('/preview/preother/:name', controller.preview.preotherView);
  router.post('/preview/prehome', controller.preview.prehome);
  router.get('/preview/prehome/:name', controller.preview.prehomeView);
  router.get('/preview/search', controller.preview.search);
  router.get('/preview/ticket', controller.preview.ticket);
  router.get('/preview/', controller.preview.homepage);
  router.get('/preview/(index|index.html)', controller.preview.homepage);

  //index page
  router.get('/homepage', menuPurviewHomepage, controller.index.index); //
  router.get('/homepage/mobile', menuPurviewHomepage, controller.index.indexMobile); //
  router.post('/homepage/getSku', controller.index.getSku); //get detail
  router.post('/homepage/getJf', menuPurviewHomepage, controller.index.getJf); //get detail
  router.post('/homepage/getNews', menuPurviewHomepage, controller.index.getNews); //get detail
  router.post('/homepage/save', menuPurviewHomepage, controller.index.save); //save
  router.post('/homepage/saveMobile', menuPurviewHomepage, controller.index.saveMobile); //save

  //search
  router.get('/search', userCheckData, controller.search.list); //

  //ticket
  router.get('/ticket', menuPurviewTicket, controller.ticket.list); //
  router.post('/ticket/post', menuPurviewTicket, controller.ticket.post); //
  router.post('/ticket/list', menuPurviewTicket, controller.ticket.getList); //
  router.get('/ticket/get', menuPurviewTicket, controller.ticket.getFields); //
  router.post('/ticket/qry', menuPurviewTicket, controller.external.ticketQry); //

  //keyword
  router.get('/analysis/keyword', menuPurviewAnalysisKeyWord, controller.analysis.keywordList); //
  router.post('/analysis/keywordList', menuPurviewAnalysisKeyWord, controller.analysis.getKeywordList); //
  router.post('/analysis/keyword_export', menuPurviewAnalysisKeyWord, controller.analysis.keywordExport); //
  router.get('/analysis/url', menuPurviewAnalysisUrl, controller.analysis.entryList); //
  router.post('/analysis/entryList', menuPurviewAnalysisUrl, controller.analysis.getEntryList); //
  router.post('/analysis/entry_export', menuPurviewAnalysisUrl, controller.analysis.entryExport);
  router.get('/analysis/url/detail', menuPurviewAnalysisUrl, controller.analysis.entryListFrom); //
  router.post('/analysis/entryListFrom', menuPurviewAnalysisUrl, controller.analysis.getEntryListFrom); //
  router.get('/analysis/record', menuPurviewAnalysisRecord, controller.record.listview); //
  router.post('/analysis/record', menuPurviewAnalysisRecord, controller.record.list); //
  router.post('/analysis/record_export', menuPurviewAnalysisRecord, controller.record.export); //
  router.get('/record/case', menuPurviewAnalysisRecord, controller.record.case);
  router.get('/record/sku', menuPurviewAnalysisRecord, controller.record.sku);
  router.get('/record/file', menuPurviewAnalysisRecord, controller.record.file);
  router.get('/record/doccheck', menuPurviewAnalysisRecord, controller.record.doccheck);
  router.get('/record/news', menuPurviewAnalysisRecord, controller.record.news);

  // 资料下载
  router.get('/analysis/download', menuPurviewAnalysisDownload, controller.analysis.downloadList);
  router.post('/analysis/download/qry', menuPurviewAnalysisDownload, controller.analysis.getDownloadList);
  router.post('/analysis/download/export', menuPurviewAnalysisDownload, controller.analysis.exportDownloadList);
  router.get('/logs', menuPurviewLog, controller.logs.listview); //
  router.post('/logslist', menuPurviewLog, controller.logs.list); //
  router.post('/log/qry', menuPurviewLog, controller.external.logQry); //

  //passport
  router.all('/login', controller.passport.login);
  router.get('/logout', controller.passport.logout);

  //api v1
  router.get('/v1/cataInfo', controller.view.cataInfo);
  router.get('/v1/index', controller.view.index);
  router.get('/v1/catalog/skus', controller.view.catalogSkus);
  router.get('/v1/catalog/trainskus', controller.view.catalogTrainSkus);
  router.get('/v1/catalog/:alias', controller.view.catalog);
  router.get('/v1/catalog/:alias/service', controller.view.catalog);
  router.get('/v1/sku/:id', controller.view.skuInfo);
  router.get('/v1/sku/:id/:alias', controller.view.skuInfo);
  router.get('/v1/tags/:id', controller.view.tags);
  router.get('/solr/renew', controller.solr.renew);

  // 搜索框热门值
  router.get('/hotWord', menuPurviewHotWord, controller.hotWord.view);
  router.post('/hotWord/list', menuPurviewHotWord, controller.hotWord.getList);
  router.post('/hotWord/insert', menuPurviewHotWord, controller.hotWord.insert);
  router.post('/hotWord/edit', menuPurviewHotWord, controller.hotWord.edit);
  router.post('/hotWord/del', menuPurviewHotWord, controller.hotWord.del);

  // 模板模块
  router.get('/template/sku/list', controller.template.tempSkuListPage);
  router.post('/template/sku/qry', controller.template.tempSkuList);
  router.post('/template/sku/add', controller.template.tempSkuAdd);
  router.post('/template/sku/del', controller.template.tempSkuDel);
  router.post('/template/sku/mod', controller.template.tempSkuMod);
  router.post('/template/sku/dtl', controller.template.tempSkuDtl);

  // 核心供应商
  router.get('/coreSupplier', menuPurviewCoreSupplier, controller.index.coreSupplier);
  router.post('/coreSupplier/save', menuPurviewCoreSupplier, controller.index.coreSupplierSave);

  // 集合页
  router.get('/order/line', menuPurviewOrderLine, controller.orderLine.orderLine);
  router.get('/record/orderOnline', menuPurviewOrderLine, controller.orderLine.recordOrderOnline);

  // 问题配置
  router.get('/question', menuPurviewQuestion, controller.question.question);
  // 导出excle
  router.post('/export/excle', controller.external.exportExcle);
};