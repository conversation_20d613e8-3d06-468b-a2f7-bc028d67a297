<% include header.html %>
  <el-container id="Main" v-loading="loading">
    <% include eheader.html %>
      <el-container>
        <% include eside.html %>
          <el-main>
            <el-row>
              <el-col :span="24">
                <el-breadcrumb separator-class="el-icon-arrow-right">
                  <el-breadcrumb-item>
                    <a href='/'>首页</a>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    导航配置
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    行业导航
                  </el-breadcrumb-item>
                </el-breadcrumb>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-tabs type="border-card" v-model="activeName" @tab-click="handleClick">
                  <el-tab-pane :label="v1.name" v-for='(v1, i1) of navigations' :key='i1'>
                    <el-tabs tab-position="left" v-if='v1.lstSub.length'>
                      <el-tab-pane :label="v2.name" v-for='(v2, i2) of v1.lstSub' :key='i2' class="second">
                        <el-button type='primary' @click="handleAddtion(i1, i2, -1, v2.id)">新增菜单</el-button>
                        <div class="third" v-if='v2.lstNavi.length'>
                          <el-card class="box-card" v-for='(v3, i3) of v2.lstNavi' :key='i3'
                            style='margin-bottom: 10px; font-size: 12px; position: relative;'>
                            <el-form label-width="150px">
                              <el-form-item label="名称：">
                                {{v3.showName}}
                              </el-form-item>
                              <el-form-item label="服务导航名称：">
                                <el-input style="width: 400px;" placeholder="请输入服务导航名称" v-model='v3.showName'>
                                </el-input>
                              </el-form-item>
                              <el-form-item label="详情URL：" v-if='v3.linkUrl'>
                                <el-input disabled style="width: 400px; margin-right: 10px;" placeholder="请输入详情URL"
                                  v-model='v3.linkUrl'>
                                  <template slot="prepend" v-if="!v3.linkUrl.includes('http')">{{view_url}}</template>
                                </el-input>
                              </el-form-item>
                              <el-form-item label="是否热门：">
                                <el-checkbox v-model='v3.isHot' :true-label='1' :false-label='0'></el-checkbox>
                              </el-form-item>
                              <el-form-item label="导航类型：">
                                <el-radio-group v-model="v3.showType">
                                  <el-radio :label="2">普通</el-radio>
                                  <el-radio :label="1">标题</el-radio>
                                  <el-radio :label="3">广告位</el-radio>
                                </el-radio-group>
                              </el-form-item>
                            </el-form>
                            <el-button-group style='position: absolute; top: 10px; right: 10px;'>
                              <el-button type="primary" icon="el-icon-plus" @click='handleAddtion(i1, i2, i3, v2.id)'>
                              </el-button>
                              <el-button type="primary" icon="el-icon-edit" @click='handleEdit(i1, i2, i3, v3)'></el-button>
                              <el-button type="primary" icon="el-icon-top" @click='handleMoveUp(i1, i2, i3)'
                                :disabled="!i3"></el-button>
                              <el-button type="primary" icon="el-icon-bottom" @click='handleMoveDown(i1, i2, i3)'
                                :disabled="i3 === navigations[i1].lstSub[i2].lstNavi.length - 1"></el-button>
                              <el-button type="primary" icon="el-icon-delete"
                                @click='handleDelete(i1, i2, i3, v3.configId)'></el-button>
                            </el-button-group>
                          </el-card>
                        </div>
                      </el-tab-pane>
                    </el-tabs>
                  </el-tab-pane>
                </el-tabs>
              </el-col>
            </el-row>
            <el-dialog v-loading="loading" title="添加/编辑三级导航" :visible.sync="dialogVisible" width="700px">
              <el-form ref="form" :model="form" label-width="120px" :rules="rules">
                <el-form-item label="详情URL：" prop="linkUrl">
                  <el-input style="width: 456px; margin-right: 10px;" placeholder="请输入详情URL" v-model='form.linkUrl'>
                    <!-- <template slot="prepend">{{view_url}}</template> -->
                  </el-input>
                  <el-button type='primary' @click='handleSearch(form.linkUrl)'>查找</el-button>
                  <p style="padding: 0; margin: 0; height: 20px; font-size: 12px; color: #f00;">
                    如需配置站内资源，请直接输入域名后面地址。例如:/sku/product/889</p>
                </el-form-item>
                <el-form-item label="名称：" prop="showName">
                  <el-input placeholder="请输入名称" v-model='form.showName'></el-input>
                </el-form-item>
                <el-form-item label="简介：">
                  <el-input placeholder="请输入简介" v-model='form.showSummary' type='textarea'></el-input>
                </el-form-item>
                <el-form-item label="导航类型：">
                  <el-radio-group v-model="form.showType">
                    <el-radio :label="2">普通</el-radio>
                    <el-radio :label="1">标题</el-radio>
                    <el-radio :label="3">广告位</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="图片：" v-if='form.showType === 3'>
                  <el-upload :data='uploadData'
                    :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                    :show-file-list="false" :on-success="handleShopSuccess" :before-upload="beforeAvatarUpload"
                    name="file">
                    <img :src='form.imgUrl' v-if='form.imgUrl' style="max-width: 500px;" />
                    <el-button v-else size="small" type="primary">上传</el-button>
                  </el-upload>
                </el-form-item>
                <template v-if='form.showType === 3'>
                  <el-form-item label="广告位按钮：">
                    <el-switch v-model='form.isBuy' :active-value='0' :inactive-value='1' active-text='在线咨询'
                      inactive-text='立即订购'></el-switch>
                  </el-form-item>
                  <el-form-item label="订购商品链接：" v-if='form.isBuy'>
                    <el-input style="width: 400px;" placeholder="请输入订购商品链接" v-model='form.buyUrl'></el-input>
                  </el-form-item>
                </template>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="handleSave" :disabled='btnLoading'>保存</el-button>
              </div>
            </el-dialog>
            <el-button @click='handleSubmit' style="position: fixed; z-index: 2; left: 180px; bottom: 10px;"
              type='danger'>保存导航</el-button>
          </el-main>
      </el-container>
  </el-container>

  <script>
    var main = new Vue({
      el: '#Main',
      data: {
        loading: false,
        logs: <%- logs %>,
        navigations: <%- navigations %>,
        logshow: false,
        view_url: '<%- view_url %>',
        ticMallHost: '<%- ticMallHost %>',
        uploadData: { systemId: 5 },
        logParam: {
          operatorType: 8,
          operatorId: '',
          pageRow: 10,
          pageNum: 1
        },
        dialogVisible: false,
        form: {
          isNew: '', //是否最新	
          isHot: 0, //是否热点	
          sortNum: 0, //排序ID，越小越前面	
          imgUrl: '', //图片地址	
          isBold: '', //是否加粗	
          fontSize: '', //字体大小	
          fontColor: '', //字体颜色	
          linkUrl: '', //链接地址	
          showSummary: '', //显示简介	
          showName: '', //显示名称	
          showType: 2, //显示方式，1标题,2文字内容,3图文内容	
          businessId: '', //业务ID	
          businessType: 1, //业务类型,1SKU,2NEWS,3CASES,4OTHER	
          catalogId: '', //类目ID
          isBuy: 0, // 广告位是否可订购
          buyUrl: '' // 可订购的链接地址
        },
        rules: {
          showName: [
            { required: true, message: '请输入名称', trigger: 'blur' },
          ],
          linkUrl: [
            { required: true, message: '请输入详情URL', trigger: 'blur' },
          ]
        },
        lelve1: 0,
        lelve2: 0,
        lelve3: 0,
        submitParams: [],
        activeName: '0',
        isEdit: false,
        btnLoading: false
      },
      methods: {
        showLog() {
          if (this.logs.list.length > 1) {
            this.logshow = !this.logshow;
          }
        },
        qryLog(page) {
          this.logParam.pageNum = page
          this.loading = true
          axios.post('/log/qry', this.logParam)
            .then(res => {
              if (res.data.resultCode === '0') {
                this.logs = {
                  list: res.data.data.items,
                  total: res.data.data.totalNum
                };
              }
              this.loading = false
            }).catch(e => {
              this.loading = false
              this.$message.error(e);
            });
        },
        handleAddtion(i1, i2, i3, catalogId) {
          this.form = {
            isNew: '', //是否最新	
            isHot: 0, //是否热点	
            sortNum: 0, //排序ID，越小越前面	
            imgUrl: '', //图片地址	
            isBold: '', //是否加粗	
            fontSize: '', //字体大小	
            fontColor: '', //字体颜色	
            linkUrl: '', //链接地址	
            showSummary: '', //显示简介	
            showName: '', //显示名称	
            showType: 2, //显示方式，1标题,2文字内容,3图文内容	
            businessId: '', //业务ID	
            businessType: 1, //业务类型,1SKU,2NEWS,3CASES,4OTHER	
            catalogId: catalogId, //类目ID
            isBuy: 0, // 广告位是否可订购
            buyUrl: '' // 可订购的链接地址
          }

          this.lelve1 = i1
          this.lelve2 = i2
          this.lelve3 = i3
          this.isEdit = false
          this.dialogVisible = true
        },
        handleSave() {
          var that = this
          this.$refs['form'].validate((valid) => {
            if (valid) {
              this.btnLoading = true
              that.loading = true
              // buinessid 必须为int类型
              if (this.form.linkUrl !== '' && !this.form.linkUrl.includes('http')) {
                var buinessid = this.form.linkUrl.substr(this.form.linkUrl.lastIndexOf('/') + 1, this.form.linkUrl.length)
                if (isNaN(Number(buinessid))) {
                  // 非数字时提示URL错误
                  this.$message.error('URL错误（包含非数字字符）')
                  this.btnLoading = false
                  that.loading = false
                  return // 中断后续操作
                } else {
                  this.form.businessId = Number(buinessid)
                }
              } else {
                this.form.businessId = 0
              }
              // 新增
              if (!this.isEdit) {
                if (this.navigations[this.lelve1].lstSub[this.lelve2].lstNavi) {
                  if (this.lelve3 === -1) {
                    this.navigations[this.lelve1].lstSub[this.lelve2].lstNavi.unshift(this.form)
                  } else {
                    this.navigations[this.lelve1].lstSub[this.lelve2].lstNavi.splice(this.lelve3 + 1, 0, this.form)
                  }
                } else {
                  this.navigations[this.lelve1].lstSub[this.lelve2].lstNavi = [this.form]
                }
              } else {
                // 编辑
                this.navigations[this.lelve1].lstSub[this.lelve2].lstNavi[this.lelve3] = this.form
              }
              this.refreshData()
              setTimeout(() => {
                that.loading = false
                this.btnLoading = false
                this.dialogVisible = false
              }, 100);

              // axios.post('/homepage/getSku', { skuUrl: this.form.linkUrl, _csrf: '<%- csrf %>' })
              //   .then(result => {
              //     if (result.data.success) {
              //       this.form.businessId = this.form.linkUrl.substr(this.form.linkUrl.lastIndexOf('/') + 1, this.form.linkUrl.length)
              //       // 新增
              //       if (!this.isEdit) {
              //         if (this.navigations[this.lelve1].lstSub[this.lelve2].lstNavi) {
              //           if (this.lelve3 === -1) {
              //             this.navigations[this.lelve1].lstSub[this.lelve2].lstNavi.unshift(this.form)
              //           } else {
              //             this.navigations[this.lelve1].lstSub[this.lelve2].lstNavi.splice(this.lelve3 + 1, 0, this.form)
              //           }
              //         } else {
              //           this.navigations[this.lelve1].lstSub[this.lelve2].lstNavi = [this.form]
              //         }
              //       } else {
              //         // 编辑
              //         this.navigations[this.lelve1].lstSub[this.lelve2].lstNavi[this.lelve3] = this.form
              //       }
              //       this.refreshData()
              //     } else {
              //       this.$message.error(result.data.msg);
              //     }
              //     setTimeout(() => {
              //       that.loading = false
              //       this.btnLoading  = false
              //       this.dialogVisible = false
              //     }, 100);
              //   }).catch(e => {
              //     this.loading = false
              //     this.btnLoading  = false
              //     return e;
              //   });
            } else {
              console.log('error submit!!');
              return false;
            }
          });
        },
        handleEdit(i1, i2, i3, v3) {
          this.form = JSON.parse(JSON.stringify(v3))
          this.lelve1 = i1
          this.lelve2 = i2
          this.lelve3 = i3
          this.isEdit = true
          this.dialogVisible = true
        },
        handleMoveUp(i1, i2, i3) {
          this.navigations[i1].lstSub[i2].lstNavi[i3] = this.navigations[i1].lstSub[i2].lstNavi.splice(i3 - 1, 1, this.navigations[i1].lstSub[i2].lstNavi[i3])[0];
        },
        handleMoveDown(i1, i2, i3) {
          this.navigations[i1].lstSub[i2].lstNavi[i3] = this.navigations[i1].lstSub[i2].lstNavi.splice(i3 + 1, 1, this.navigations[i1].lstSub[i2].lstNavi[i3])[0];
        },
        handleDelete(i1, i2, i3, configId) {
          this.$confirm('您确认删除该条记录?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
          }).then(() => {
            if (configId) {
              this.loading = true
              axios.post('/navigation/delete', { configId })
                .then(result => {
                  if (result.data.resultCode === '0') {
                    this.deleteCatch(i1, i2, i3)
                    this.$message.success('删除成功');
                  } else {
                    this.$message.error(result.data.resultMsg);
                  }
                  this.loading = false
                }).catch(e => {
                  this.loading = false
                  return e;
                });
            } else {
              this.deleteCatch(i1, i2, i3)
            }
          }).catch(() => {

          });
        },
        // 静态删除
        deleteCatch(i1, i2, i3) {
          this.navigations[i1].lstSub[i2].lstNavi.splice(i3, 1);
        },
        handleSearch(link) {
          const that = this
          this.loading = true
          axios.post('/homepage/getSku', { skuUrl: link, _csrf: '<%- csrf %>' })
            .then(result => {
              if (result.data.success) {
                this.form.showName = result.data.data.name
                this.form.businessId = result.data.data.id
                this.form.imgUrl = result.data.data.thumb_img || result.data.data.cover_img
                this.form.showSummary = result.data.data.description
                this.form.linkUrl = link
              } else {
                this.$message.error(result.data.msg);
              }
              setTimeout(() => {
                that.loading = false
              }, 100);
            }).catch(e => {
              this.loading = false
              return e;
            });
        },
        handleClick(tab, event) {
          console.log(tab, event)
        },
        handleSubmit() {
          this.submitParams = []
          // 3级菜单打平为传入参数
          this.navigations.forEach((v1, index) => {
            // if (index == this.activeName) {
            if (v1.lstSub && v1.lstSub.length) {
              v1.lstSub.forEach(v2 => {
                if (v2.lstNavi && v2.lstNavi.length) {
                  v2.lstNavi.forEach((v3, i3) => {
                    if (v3.showName) {
                      // 3级菜单的排序sortNum重赋值
                      v3.sortNum = i3
                      this.submitParams.push(v3)
                    }
                  })
                }
              })
            }
            // }
          })
          this.loading = true
          console.log(this.activeName)
          console.log(this.submitParams)

          axios.post('/navigation/save', this.submitParams)
            .then(result => {
              if (result.data.resultCode === '0') {
                this.$message.success('保存成功');
                this.navigationQuery()
              } else {
                this.loading = false
                this.$message.error(result.data.resultMsg);
              }
            }).catch(e => {
              this.loading = false
              return e;
            });
        },
        handleShopSuccess(res, file) {
          if (res.resultCode === '0') {
            this.form.imgUrl = res.data.fileName;
          } else {
            this.$message.error(res.resultMsg);
          }
          this.form = JSON.parse(JSON.stringify(this.form))
        },
        beforeAvatarUpload(file) {
          const isJPG = file.type === 'image/jpeg';
          const isPNG = file.type === 'image/png';
          const isGIF = file.type === 'image/gif';
          const isLt2M = file.size / 1024 / 1024 < 10;

          if (!isJPG && !isPNG && !isGIF) {
            this.$message.error('图片格式错误!');
            return false;
          }
          if (!isLt2M) {
            this.$message.error('上传图片大小不能超过 10MB!');
            return false;
          }
        },
        refreshData() {
          this.navigations = JSON.parse(JSON.stringify(this.navigations))
        },
        navigationQuery() {
          axios.post('/navigation/query', { catalogId: 1, _csrf: '<%- csrf %>' })
            .then(result => {
              
              if (result.data.length) {
                this.navigations = result.data
              }
              setTimeout(() => {
                this.loading = false
              }, 100);
            }).catch(e => {
              this.loading = false
              return e;
            });
        }
      },
      watch: {
        "form.showType": {
          handler(val) {
            if (val === 1) {
              this.rules.linkUrl[0].required = false;
            } else {
              this.rules.linkUrl[0].required = true;
            }
          },
          immediate: true
      }
    },
      mounted() {
        this.$nextTick(() => {
          document.getElementById('preLoading').style.display = 'none';
          // this.qryLog()
          // 3级为空的时候创建一条空数据
          // this.navigations.forEach(v1 => {
          //   if (v1.lstSub && v1.lstSub.length) {
          //     v1.lstSub.forEach(v2 => {
          //       if (!v2.lstNavi) {
          //         v2.lstNavi = [{}]
          //       } 
          //     })
          //   }
          // })
        })
      }
    });
  </script>
  <style>
  .el-radio input[aria-hidden="true"] {
  display: none !important;
  }

  .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
  box-shadow: none !important;
  }
  </style>
  <% include footer.html %>