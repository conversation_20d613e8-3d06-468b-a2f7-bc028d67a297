'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const { env } = require('../../config/info').siteInfo;

class TemplateController extends Controller {
  // sku模板列表页面
  async tempSkuListPage() {
    const {
      ctx
    } = this;
    await ctx.render('temp_sku_list', {
      site_title: _info.site_title,
      ticMallHost: env[this.app.config.env].ticMallHost,
      page_title: 'SKU楼层模板',
      active: '15-1',
      csrf: ctx.csrf,
      userInfo: ctx.session.userInfo,
    });
  }
  // sku模板列表接口
  async tempSkuList() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.template.tempSkuList(params);

    ctx.body = result;
  }
  // 模板被使用的文章
  // async tempRelevanceQry() {
  //     const {
  //         ctx
  //     } = this;
  //     const params = ctx.request.body;
  //     const result = await ctx.service.template.tempRelevanceQry(params);

  //     ctx.body = result;
  // }
  // sku模板添加
  async tempSkuAdd() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.template.tempSkuAdd(params);

    ctx.body = result;
  }
  // sku模板删除
  async tempSkuDel() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.template.tempSkuDel(params);

    ctx.body = result;
  }
  // sku模板编辑
  async tempSkuMod() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.template.tempSkuMod(params);

    ctx.body = result;
  }
  // sku模板详情
  async tempSkuDtl() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.template.tempSkuDtl(params);

    ctx.body = result;
  }
}

module.exports = TemplateController;