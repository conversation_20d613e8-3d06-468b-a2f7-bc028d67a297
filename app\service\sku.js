'use strict';

const Service = require('egg').Service;
const moment = require('moment');
const request = require('request');
const crypto = require('crypto');
const _siteInfo = require('../../config/info').siteInfo;
const solrUrl = _siteInfo.solrUrl,
  solrPstr = _siteInfo.solrPstr,
  solrPid = _siteInfo.solrPid;
const { env } = require('../../config/info').siteInfo;
class SkuService extends Service {
  async getList(params) {
    let sid = params.service,
      tid = params.trade,
      is_buy = params.is_buy,
      is_use = params.is_use,
      name = params.name || '',
      page = params.page || 1,
      cid = [],
      bigType = params.bigType || 'sku',
      limit = params.limit || 30,
      bu_id = params.bu_id;

    if (!page || page == 'NULL') {
      page = 1;
    }

    let qFilter = [];
    if (sid && sid != '') {

      if (typeof sid == 'object') {
        for (let item of sid) {
          cid.push(item);
        }
      } else {
        cid.push(sid);
      }
    }

    if (tid && tid != '') {

      if (typeof tid == 'object') {
        for (let item of tid) {
          cid.push(item);
        }
      } else {
        cid.push(tid);
      }
    }


    if (typeof is_buy == 'boolean') {
      if (is_buy == false) {
        qFilter.push('s.is_buy=0');
      }

      if (is_buy == true) {
        qFilter.push('s.is_buy=1');
      }
    }

    if (typeof is_use == 'boolean') {
      if (is_use == false) {
        qFilter.push('s.is_use=0');
      }

      if (is_use == true) {
        qFilter.push('s.is_use=1');
      }
    }

    if (bu_id) {
      qFilter.push('s.bu_id=' + bu_id);
    }

    if (name && name != '') {
      qFilter.push('s.name LIKE "%' + name + '%"');
    }

    if (bigType == 'sku') {
      qFilter.push('s.type IN (1,2)');
    } else if (bigType == 'solution') {
      qFilter.push('s.type IN (3,4)');
    }

    if (cid.length == 1) {
      qFilter.push('(c.parent_id=' + cid + ' or c.id=' + cid + ')');
    } else if (cid.length > 1) {
      cid = cid.join(',');
      qFilter.push('(c.parent_id IN (' + cid + ') or c.id in (' + cid + ')' + ')');
    }

    let baseCategoryIds_service = [];
    let baseCategoryIds_trade = []


    if (params && params.allowServiceList != null && params.allowServiceList.length > 0) {

      for (let idvalue of params.allowServiceList) {
        baseCategoryIds_service.push(idvalue);
      }
    }

    if (params && params.allowTradeList != null && params.allowTradeList.length > 0) {
      for (let idvalue of params.allowTradeList) {
        baseCategoryIds_trade.push(idvalue);
      }
    }

    if (baseCategoryIds_service.length == 0 && this.ctx.session.userInfo && this.ctx.session.userInfo.type == 2) {
      qFilter.push('1=2');
    }

    if (baseCategoryIds_trade.length == 0 && this.ctx.session.userInfo && this.ctx.session.userInfo.type == 2) {
      qFilter.push('1=2');
    }

    qFilter.push(' s.is_delete=0 AND c.is_delete=0 ');

    let query = 'SELECT s.*, b.BU_NAME as bu_name FROM sku s LEFT JOIN catalog_relation r ON s.id=r.sku_id LEFT JOIN catalog c ON r.catalog_id=c.id LEFT JOIN BU b ON s.bu_id=b.BU_ID';

    let cquery = 'SELECT count(distinct(s.id)) as countval FROM sku s LEFT JOIN catalog_relation r ON s.id=r.sku_id LEFT JOIN catalog c ON r.catalog_id=c.id  ';

    if (baseCategoryIds_trade.length > 0) {
      if (bigType == 'solution') {
        query += " left join (select sku_id from catalog_relation where catalog_type=1 and sku_id is not null and catalog_id not in (" + baseCategoryIds_trade.join(',') + ")) tmptrade on s.id=tmptrade.sku_id";

        cquery += " left join (select sku_id from catalog_relation where catalog_type=1 and sku_id is not null and catalog_id not in (" + baseCategoryIds_trade.join(',') + ")) tmptrade on s.id=tmptrade.sku_id";

        qFilter.push('tmptrade.sku_id is null');
      } else //服务
      {
        query += " left join (select catalog_relation.sku_id from catalog_relation join catalog on catalog_relation.catalog_id=catalog.id  where catalog_relation.catalog_type=1 and catalog_relation.sku_id is not null and  catalog.parent_id not in (" + baseCategoryIds_trade.join(',') + ")) tmptrade on s.id=tmptrade.sku_id";

        cquery += " left join (select catalog_relation.sku_id from catalog_relation join catalog on catalog_relation.catalog_id=catalog.id  where catalog_relation.catalog_type=1 and catalog_relation.sku_id is not null and  catalog.parent_id not in (" + baseCategoryIds_trade.join(',') + ")) tmptrade on s.id=tmptrade.sku_id";

        qFilter.push('tmptrade.sku_id is null');
      }
    }


    if (baseCategoryIds_service.length > 0) {
      if (bigType == 'solution') {
        query += " left join (select sku_id from catalog_relation where catalog_type=2 and sku_id is not null and catalog_id not in (" + baseCategoryIds_service.join(',') + ")) tmpservice on s.id=tmpservice.sku_id";

        cquery += " left join (select sku_id from catalog_relation where catalog_type=2 and sku_id is not null and catalog_id not in (" + baseCategoryIds_service.join(',') + ")) tmpservice on s.id=tmpservice.sku_id";

        qFilter.push('tmpservice.sku_id is null');
      } else //服务
      {
        query += " left join (select catalog_relation.sku_id from catalog_relation join catalog on catalog_relation.catalog_id=catalog.id  where catalog_relation.catalog_type=2 and catalog_relation.sku_id is not null and  catalog.parent_id not in (" + baseCategoryIds_service.join(',') + ")) tmpservice on s.id=tmpservice.sku_id";

        cquery += " left join (select catalog_relation.sku_id from catalog_relation join catalog on catalog_relation.catalog_id=catalog.id  where catalog_relation.catalog_type=2 and catalog_relation.sku_id is not null and  catalog.parent_id not in (" + baseCategoryIds_service.join(',') + ")) tmpservice on s.id=tmpservice.sku_id";

        qFilter.push('tmpservice.sku_id is null');
      }
    }


    qFilter = qFilter.join(' AND ');
    if (qFilter != '') {
      qFilter = ' AND ' + qFilter;
    }

    let total = await this.app.mysql.query(cquery + ' where 1=1 ' + qFilter);
    this.ctx.logger.error('total:::::', cquery + ' where 1=1 ' + qFilter);
    total = total[0]["countval"];
    let list = await this.app.mysql.query(query + ' where 1=1 ' + qFilter + ' GROUP BY s.id ORDER BY s.is_use DESC,s.gmt_modify DESC LIMIT ' + (page - 1) * limit + ',' + limit);
    this.ctx.logger.error('list:::::',query + ' where 1=1 ' + qFilter + ' GROUP BY s.id ORDER BY s.is_use DESC,s.gmt_modify DESC LIMIT ' + (page - 1) * limit + ',' + limit);

    for (let item of list) {
      item.is_use == 0 ? item.is_use = false : item.is_use = true;
      item.is_buy == 0 ? item.is_buy = false : item.is_buy = true;

      item.time = moment(item.gmt_modify).format('YYYY-MM-DD HH:mm:ss');

      let tradeList = [],
        serviceList = [];
      let tradeCata = await this.app.mysql.select('catalog_relation', {
        where: {
          sku_id: item.id,
          catalog_type: 1
        }
      });
      let serviceCate = await this.app.mysql.select('catalog_relation', {
        where: {
          sku_id: item.id,
          catalog_type: 2
        }
      });
      let catalogList = await this.app.mysql.query(`select a.alias from catalog a,catalog_relation b where sku_id=${item.id} and b.catalog_id=a.id and a.alias!='subclass'`)
      item.catalog = catalogList
      if (bigType == 'sku') {
        if (tradeCata && tradeCata.length > 0) {
          for (let ti of tradeCata) {
            let tradeInfo = await this.app.mysql.get('catalog', {
              id: ti.catalog_id,
              is_delete: 0
            });
            if (tradeInfo) {
              let tradeParantInfo = await this.app.mysql.get('catalog', {
                id: tradeInfo.parent_id,
                is_delete: 0
              });
              tradeList.push(tradeParantInfo.name + '-' + tradeInfo.name);
            }
          }
        }

        if (serviceCate && serviceCate.length > 0) {
          for (let si of serviceCate) {
            let serviceInfo = await this.app.mysql.get('catalog', {
              id: si.catalog_id,
              is_delete: 0
            });
            if (serviceInfo) {
              let serviceParantInfo = await this.app.mysql.get('catalog', {
                id: serviceInfo.parent_id,
                is_delete: 0
              });
              if (serviceParantInfo) {
                serviceList.push(serviceParantInfo.name + '-' + serviceInfo.name);
              }
            }
          }
        }

        item.tradeList = tradeList;
        item.serviceList = serviceList;
      } else {
        if (tradeCata && tradeCata.length > 0) {
          for (let ti of tradeCata) {
            let tradeInfo = await this.app.mysql.get('catalog', {
              id: ti.catalog_id,
              is_delete: 0
            });
            if (tradeInfo && tradeInfo && tradeInfo.name) {
              tradeList.push(tradeInfo.name);
            }
          }
        }

        if (serviceCate && serviceCate.length > 0) {
          for (let si of serviceCate) {
            let serviceInfo = await this.app.mysql.get('catalog', {
              id: si.catalog_id,
              is_delete: 0
            });
            if (serviceList && serviceInfo && serviceInfo.name) {
              serviceList.push(serviceInfo.name);
            }
          }
        }

        item.tradeList = tradeList;
        item.serviceList = serviceList;
      }

      if (bigType == 'solution') {
        item.tran_result = await this.ctx.service.checkflow.getDetail('sku_solution', item.id);
      } else {
        item.tran_result = await this.ctx.service.checkflow.getDetail('sku_service', item.id);
      }
    }

    return {
      list,
      total
    }
  }

  async getInfo(id) {
    const detail = await this.app.mysql.get('sku', {
      id: id
    });

    if (detail) {
      const content = await this.app.mysql.select('sku_content', {
        columns: ['title', 'content', 'hide_title', 'template_id', 'anchor_title', 'anchor_show'],
        where: {
          'sku_id': detail.id
        },
        orders: [
          ['id', '']
        ]
      });

      const qa = await this.app.mysql.select('sku_qa', {
        columns: ['question', 'answer'],
        where: {
          'sku_id': detail.id
        },
        orders: [
          ['id', '']
        ]
      });

      for (let item of content) {
        item.hide_title == 0 ? item.hide_title = false : item.hide_title = true;
        if (item.content) {
          item.content = item.content.replace(/\n/g, '').replace(/\.\.\/\.\.\/static/g, '/static').replace(/\.\.\/static/g, '/static');
        }
      }

      for (let item of qa) {
        if (item.content) {
          item.content = item.content.replace(/\n/g, '').replace(/\.\.\/\.\.\/static/g, '/static').replace(/\.\.\/static/g, '/static');
        }
      }

      detail.content = content;
      detail.qa = qa;

      return {
        detail
      }
    } else {
      return {
        detail: ''
      };
    }
  }

  async getEditInfo(alias) {
    const detail = await this.app.mysql.get('sku', {
      id: alias
    });

    // const sContent = await this.app.mysql.query(`select sc.id, sc.title, sc.content, sc.hide_title, sc.template_id, sc.anchor_title, sc.anchor_show,t.id template_id,
    // t.name template_name,t.title template_title,t.content template_content
    // from sku_content sc,templates t where sc.sku_id=${detail.id} and sc.template_id=t.id`)
    // console.log(sContent, 'sContent')
    

    const sContent = await this.app.mysql.select('sku_content', {
      columns: ['id', 'title', 'content', 'hide_title', 'template_id', 'anchor_title', 'anchor_show'],
      where: {
        'sku_id': detail.id
      },
      orders: [
        ['id', '']
      ]
    });

    sContent.forEach(item => {
      if (item.template_id) {
        this.app.mysql.select('templates', {
          columns: ['id', 'name', 'title', 'content'],
          where: {
            'id': item.template_id
          }
        }).then(res => {
          if (res.length) {
            item.title = res[0].title
            item.content = res[0].content
          }
        })
      }
    })

    // 获取sku关联的资讯列表
    const skuCases = await this.app.mysql.query(`select 
            a.title, a.anchor_title, a.anchor_show, b.id as case_id, b.title as name
            from sku_case a
            JOIN cases b
            ON a.case_id = b.id
            WHERE a.sku_id = ${detail.id}`);
    skuCases.forEach(item => {
      item.edit = false;
      item.anchor_show = String(item.anchor_show)
      item.title = item.title != 0 ? item.title : ''
      item.anchor_title = item.anchor_title != 0 ? item.anchor_title : ''
    })
    detail.skuCases = skuCases

    // 获取sku关联的资料列表
    const skuResource = await this.app.mysql.select('sku_resource', {
      columns: ['id', 'title', 'name', 'resource_id', 'link', 'anchor_title', 'anchor_show'],
      where: {
        'sku_id': detail.id
      }
    });
    skuResource.forEach(item => {
      item.edit = false;
      item.anchor_show = String(item.anchor_show)
      item.title = item.title != 0 ? item.title : ''
      item.anchor_title = item.anchor_title != 0 ? item.anchor_title : ''
      item.link = item.link != 0 ? item.link : ''
      item.name = item.name != 0 ? item.name : ''
      item.resource_id = item.resource_id != 0 ? item.resource_id : ''
    })
    detail.skuResource = skuResource

    // 获取sku关联的楼层排序
    const skuFloors = await this.app.mysql.select('sku_floor_sort', {
      columns: ['floor_mark'],
      where: {
        'sku_id': detail.id
      },
      orders: [
        ['floor_sort', '']
      ]
    });
    let skuFloor = []
    skuFloors.forEach(item => {
      skuFloor.push(item.floor_mark)
    })
    detail.skuFloor = skuFloor

    const faq = await this.app.mysql.select('sku_qa', {
      columns: ['id', 'question', 'answer', 'title', 'anchor_title', 'anchor_show'],
      where: {
        'sku_id': detail.id
      },
      orders: [
        ['id', '']
      ]
    });

    const region = await this.app.mysql.select('sku_region', {
      columns: ['id', 'sku_id', 'region_id'],
      where: {
        'sku_id': detail.id
      },
    });

    const tag = await this.app.mysql.select('tag_relation', {
      columns: ['id', 'sku_id', 'tag_name'],
      where: {
        'sku_id': detail.id
      },
    });

    const recommends = await this.app.mysql.select('sku_recommend', {
      where: {
        'source_id': detail.id
      }
    })
    detail.recommends = recommends

    const tradeCata = await this.app.mysql.select('catalog_relation', {
      where: {
        sku_id: detail.id,
        catalog_type: 1
      }
    });
    const serviceCata = await this.app.mysql.select('catalog_relation', {
      where: {
        sku_id: detail.id,
        catalog_type: 2
      }
    });

    if (tradeCata && tradeCata.length > 0) {
      for (let [i, ti] of tradeCata.entries()) {
        let tradeInfo = await this.app.mysql.get('catalog', {
          id: ti.catalog_id,
          is_delete: 0
        });
        if (tradeInfo) {
          let tradeParantInfo = await this.app.mysql.get('catalog', {
            id: tradeInfo.parent_id,
            is_delete: 0
          });
          ti.parentName = tradeParantInfo && tradeParantInfo.name;
          ti.name = tradeInfo.name;
          ti.id = tradeInfo.id;
        } else {
          tradeCata.splice(i, 1);
        }

      }
    }

    if (serviceCata && serviceCata.length > 0) {
      for (let [i, si] of serviceCata.entries()) {
        let serviceInfo = await this.app.mysql.get('catalog', {
          id: si.catalog_id,
          is_delete: 0
        });
        if (serviceInfo) {
          let serviceParantInfo = await this.app.mysql.get('catalog', {
            id: serviceInfo.parent_id,
            is_delete: 0
          });
          si.parentName = serviceParantInfo && serviceParantInfo.name;
          si.name = serviceInfo.name;
          si.id = serviceInfo.id;
        } else {
          serviceCata.splice(i, 1);
        }

      }
    }

    for (let item of sContent) {
      item.hide_title == 1 ? item.hide_title = true : item.hide_title = false;
      item.lock = item.template_id ? true : false;
      item.template_id = item.template_id ? item.template_id : 0;
      item.anchor_show = String(item.anchor_show)
      item = JSON.stringify(item);
    }

    for (let item of faq) {
      item.title = item.title || '常见问题解答'
      item.anchor_show = String(item.anchor_show)
      item = JSON.stringify(item);
    }

    detail.sContent = sContent;
    detail.faq = faq;

    detail.description = detail.description.replace(/\r\n/g, "\\r\\n").replace(/\n/g, "\\n");

    if (detail.banner_content) {
      detail.banner_content = detail.banner_content.replace(/\'/g, '"').replace(/\n/g, '').replace(/\.\.\/\.\.\/static/g, '/static').replace(/\.\.\/static/g, '/static');
    }

    if (detail.m_banner_content) {
      detail.m_banner_content = detail.m_banner_content.replace(/\'/g, '"').replace(/\n/g, '').replace(/\.\.\/\.\.\/static/g, '/static').replace(/\.\.\/static/g, '/static');
    }

    if (detail.is_buy == 1) {
      detail.is_buy = true;
    } else {
      detail.is_buy = false;
    }

    if (detail.is_use == 1) {
      detail.is_use = true;
    } else {
      detail.is_use = false;
    }

    if (detail.is_el == 1) {
      detail.is_el = true;
    } else {
      detail.is_el = false;
    }

    detail.region = [];
    for (let item of region) {
      detail.region.push(item.region_id);
    }

    detail.tag = [];
    for (let item of tag) {
      detail.tag.push(item.tag_name);
    }

    detail.tradeCata = tradeCata;
    detail.serviceCata = serviceCata;

    return {
      detail
    }
  }

  async skuEdit(postData) {
    const {
      app
    } = this;
    const gmt_create = moment().format('YYYY-MM-DD HH:mm:ss');
    const Literal = this.app.mysql.literals.Literal;

    const repeatCheck = await this.app.mysql.count('sku', {
      name: postData.name,
      is_delete: 0
    });

    let isbuy = 0,
      isuse = 0,
      is_el = 0;
    if (postData.is_buy == true) {
      isbuy = 1;
    }

    if (postData.is_use == true) {
      isuse = 1;
    }

    if (postData.is_el == true) {
      is_el = 1;
    }

    const modifyUser = this.ctx.session.userInfo;
    const row = {
      id: postData.id,
      name: postData.name.replace(/'/g, '"'),
      alias: postData.alias,
      bu_id: postData.bu_id,
      sub_title: postData.sub_title,
      description: postData.description,
      is_message: postData.is_message,
      seo_text: postData.seo_text,
      cover_img: postData.cover_img,
      thumb_img: postData.thumb_img,
      banner_content: postData.banner_content,
      banner_type: postData.banner_type,
      m_cover_img: postData.m_cover_img,
      m_thumb_img: postData.m_thumb_img,
      m_banner_content: postData.m_banner_content,
      m_banner_type: postData.m_banner_type,
      is_buy: isbuy,
      is_use: isuse,
      is_el: is_el,
      buy_url: postData.buy_url,
      el_url: postData.el_url,
      type: postData.type,
      last_modify: modifyUser.user_name || modifyUser.name || '',
      //gmt_create: gmt_create,
      page_title: postData.page_title,
      page_keywords: postData.page_keywords,
      page_description: postData.page_description,
      mpage_title: postData.mpage_title,
      mpage_keywords: postData.mpage_keywords,
      mpage_description: postData.mpage_description,
      img_alt: postData.img_alt,
      // recommends: postData.recommends
    };

    // 更新推荐记录
    const recommendDatas = []
    await app.mysql.query(`DELETE FROM sku_recommend WHERE source_id=${postData.id}`)
    if (postData.recommends.length) {
      postData.recommends.forEach((item, index) => {
        if (item.sku_id) {
          recommendDatas.push(`(${postData.id}, ${item.sku_id}, '${item.sku_name}', '${item.sku_image}', '${item.sku_url}', ${index + 1}, '${moment().format('YYYY-MM-DD HH:mm:ss')}',  '${moment().format('YYYY-MM-DD HH:mm:ss')}')`)
        }
      })
      if (recommendDatas.length) await app.mysql.query(`INSERT INTO sku_recommend (source_id, sku_id, sku_name, sku_image, sku_url, sort_num, gmt_modify, gmt_create) VALUE ${recommendDatas.join(',')}`);
    }
    /*LOGS*/
    const originalData = await this.app.mysql.get('sku', {
      id: postData.id
    });
    let changValue = [];
    if (originalData.is_use != row.is_use) {
      changValue.push('修改上下架');
    }

    if (originalData.name != row.name) {
      changValue.push('修改基本属性-名称');
    }

    if (originalData.bu_id != row.bu_id) {
      changValue.push('修改基本属性-BU');
    }

    if (originalData.sub_title != row.sub_title) {
      changValue.push('修改基本属性-副标题');
    }

    if (originalData.description != row.description) {
      changValue.push('修改描述-描述');
    }

    if (originalData.is_message != row.is_message) {
      changValue.push('修改是否为留言表单');
    }

    if (originalData.seo_text != row.seo_text) {
      changValue.push('修改描述-SEO关键字');
    }

    if (originalData.is_buy != row.is_buy) {
      changValue.push('修改描述-是否可订购');
    }

    if (originalData.buy_url != row.buy_url) {
      changValue.push('修改描述-订购链接');
    }

    if (originalData.is_el != row.is_el) {
      changValue.push('修改描述-是否开启课程');
    }

    if (originalData.el_url != row.el_url) {
      changValue.push('修改描述-课程链接');
    }

    if (originalData.cover_img != row.cover_img) {
      changValue.push('修改描述-封面图');
    }

    if (originalData.thumb_img != row.thumb_img) {
      changValue.push('修改描述-缩略图');
    }

    if (originalData.page_title != row.page_title) {
      changValue.push('修改SEO设置-Title');
    }

    if (originalData.banner_type != row.banner_type) {
      changValue.push('修改描述-PC端banner展示形式');
    }

    if (originalData.banner_content != row.banner_content) {
      changValue.push('修改描述-PC端banner富文本内容');
    }

    if (originalData.m_banner_type != row.m_banner_type) {
      changValue.push('修改描述-移动端端banner展示形式');
    }

    if (originalData.m_banner_content != row.m_banner_content) {
      changValue.push('修改描述-移动端端banner富文本内容');
    }

    if (originalData.page_keywords != row.page_keywords) {
      changValue.push('修改SEO设置-Keywords');
    }

    if (originalData.page_description != row.page_description) {
      changValue.push('修改SEO设置-Description');
    }

    if (originalData.mpage_title != row.mpage_title) {
      changValue.push('修改移动端SEO设置-Title');
    }

    if (originalData.mpage_keywords != row.mpage_keywords) {
      changValue.push('修改移动端SEO设置-Keywords');
    }

    if (originalData.mpage_description != row.mpage_description) {
      changValue.push('修改移动端SEO设置-Description');
    }
    if (originalData.img_alt != row.img_alt) {
      changValue.push('修改img alt属性');
    }

    if (postData.delTrade && postData.delTrade.length > 0) {
      changValue.push('删除基本属性-所属行业(' + postData.delTrade + ')');
    }

    if (postData.addTrade && postData.addTrade.length > 0) {
      changValue.push('新增基本属性-所属行业(' + postData.addTrade + ')');
    }

    if (postData.delService && postData.delService.length > 0) {
      changValue.push('删除基本属性-所属服务(' + postData.delService + ')');
    }

    if (postData.addService && postData.addService.length > 0) {
      changValue.push('新增基本属性-所属服务(' + postData.addService + ')');
    }

    /* */
    let orgContent = await this.app.mysql.select('sku_content', {
      where: {
        sku_id: postData.id,
      },
      columns: ['id', 'title', 'content', 'hide_title', 'template_id', 'anchor_title', 'anchor_show']
    });
    let orgFaq = await this.app.mysql.select('sku_qa', {
      where: {
        sku_id: postData.id,
      },
      columns: ['question', 'answer', 'title', 'anchor_title', 'anchor_show']
    });
    let orgTag = await this.app.mysql.select('tag_relation', {
      where: {
        sku_id: postData.id,
      },
      columns: ['tag_name']
    });
    let orgRegion = await this.app.mysql.select('sku_region', {
      where: {
        sku_id: postData.id,
      },
      columns: ['region_id']
    });


    let orgRegions = [];
    for (let item of orgRegion) {
      orgRegions.push(item.region_id);
    }

    /*END*/

    if (postData.type == 2) {
      row.class_info = JSON.stringify(postData.course);
    }

    const tradeList = postData.tradeList,
      serviceList = postData.serviceList,
      regionList = postData.region,
      tagList = postData.tag;

    const contents = postData.sContent,
      faqs = postData.faq,
      skuCases = postData.skuCases,
      skuResource = postData.skuResource,
      skuFloor = postData.skuFloor;

    let conChange = false,
      faqChange = false,
      tagChange = false,
      regChange = false;

    let sscontent = postData.sContent;
    let ssfaq = postData.faq;
    let ctit = [],
      ftit = [];
    for (let [i, item] of sscontent.entries()) {
      item.hide_title ? item.hide_title = 1 : item.hide_title = 0;

      if (i < orgContent.length) {
        if (!item.show) {
          changValue.push('删除楼层信息[' + orgContent[i].title + ']');
        } else {
          let inum = i + 1;
          if (item.hide_title != orgContent[i].hide_title) {
            changValue.push('修改楼层' + inum + '的隐藏标题');
          }

          if (item.title != orgContent[i].title) {
            changValue.push('修改楼层' + inum + '的标题');
          }

          if (item.content != orgContent[i].content) {
            changValue.push('修改楼层' + inum + '的内容');
          }
        }
      } else {
        if (item.title && item.content) {
          ctit.push(item.title);
        }
      }
    }

    if (ctit.length > 0) {
      changValue.push('新增楼层[' + ctit + ']');
    }

    for (let [i, item] of ssfaq.entries()) {
      if (i < orgFaq.length) {
        if (!item.show) {
          changValue.push('删除常见问题[' + orgFaq[i].question + ']');
        } else {
          let imum = i + 1;
          if (item.question != orgFaq[i].question) {
            changValue.push('修改常见问题' + imum + '的问题');
          }

          if (item.answer != orgFaq[i].answer) {
            changValue.push('修改常见问题' + imum + '的内容');
          }

          if (item.title != orgFaq[i].title) {
            changValue.push('修改常见问题' + imum + '的标题');
          }
        }
      } else {
        if (item.question && item.answer) {
          ftit.push(item.question);
        }
      }
    }

    if (ftit.length > 0) {
      changValue.push('新增常见问题[' + ftit + ']');
    }


    let cctag = [];
    for (let item of orgTag) {
      cctag.push(item.tag_name)
    }

    if (cctag.sort().toString() != postData.tag.sort().toString()) {
      tagChange = true;
    }

    if (orgRegions.sort().toString() != regionList.sort().toString()) {
      regChange = true;
    }


    if (tagChange) {
      changValue.push('修改描述-标签');
    }

    if (regChange) {
      changValue.push('修改基本属性-目的国');
    }

    if (postData.type == 2 && (JSON.stringify(postData.course) != originalData.class_info)) {
      changValue.push('修改基本属性-课程信息');
    }

    let sid;
    let tradeNames = [],
      serviceNames = [];

    let checkflowResult = await this.ctx.service.checkflow.getDetail('sku_service', postData.id);
    const result = await app.mysql.beginTransactionScope(async conn => {
      row.gmt_modify = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      await conn.update('sku', row);

      if (row.is_use && checkflowResult != null) {
        await conn.update('checkflow', {
          id: checkflowResult.id,
          tran_note: '',
          tran_status: 10,
          tran_admin_time: gmt_create,
          tran_admin_id: this.ctx.session.userInfo.id
        });
      } else if (checkflowResult != null) {
        await conn.update('checkflow', {
          id: checkflowResult.id,
          is_overdue: 1
        });
      }

      const delTrade = postData.delTrade,
        addTrade = postData.addTrade,
        delService = postData.delService,
        addService = postData.addService;

      /* */

      sid = postData.id;
      const dropRegion = await conn.delete('sku_region', {
        sku_id: sid
      });
      const dropContent = await conn.delete('sku_content', {
        sku_id: sid
      });
      const dropFaq = await conn.delete('sku_qa', {
        sku_id: sid
      });
      const dropTag = await conn.delete('tag_relation', {
        sku_id: sid
      });

      for (let i of regionList) {
        await conn.insert('sku_region', {
          sku_id: sid,
          region_id: i,
          gmt_create: gmt_create
        });
      }

      if (delTrade) {
        for (let item of delTrade) {
          await conn.delete('catalog_relation', {
            sku_id: sid,
            catalog_id: item
          });
          let pObj = await this.app.mysql.get('catalog', {
            id: item
          });
          let pId = pObj.parent_id;
          await conn.delete('catalog_relation', {
            sku_id: sid,
            catalog_id: pId
          });
        }
      }

      if (delService) {
        for (let item of delService) {
          await conn.delete('catalog_relation', {
            sku_id: sid,
            catalog_id: item
          });
          let pObj = await this.app.mysql.get('catalog', {
            id: item
          });
          let pId = pObj.parent_id;
          await conn.delete('catalog_relation', {
            sku_id: sid,
            catalog_id: pId
          });
        }
      }

      for (let item of tradeList) {
        let d = await conn.count('catalog_relation', {
          sku_id: sid,
          catalog_id: item.id
        });

        if (d == 0) {
          await conn.insert('catalog_relation', {
            sku_id: sid,
            catalog_id: item.id,
            catalog_type: 1,
            gmt_create: gmt_create,
            sort_num: 0
          });
        }

        let pObj = await conn.get('catalog', {
          id: item.id
        });
        let pId = pObj.parent_id;
        let c = await conn.count('catalog_relation', {
          sku_id: sid,
          catalog_id: pId
        });

        if (c == 0) {
          let update = await conn.insert('catalog_relation', {
            sku_id: sid,
            catalog_id: pId,
            gmt_create: gmt_create,
            sort_num: 0
          });
        }

        if (pId == 1 || pId == 2) {
          if (pObj && pObj.name) {
            tradeNames.push(pObj.name);
          }
        } else {
          let cataInfo = await conn.get('catalog', {
            id: pId
          });
          if (cataInfo && cataInfo.name) {
            tradeNames.push(cataInfo.name);
          }
        }


      }

      for (let item of serviceList) {
        let d = await conn.count('catalog_relation', {
          sku_id: sid,
          catalog_id: item.id
        });

        if (d == 0) {
          await conn.insert('catalog_relation', {
            sku_id: sid,
            catalog_id: item.id,
            catalog_type: 2,
            gmt_create: gmt_create,
            sort_num: 0
          });
        }

        let pObj = await conn.get('catalog', {
          id: item.id
        });
        let pId = pObj.parent_id;
        let c = await conn.count('catalog_relation', {
          sku_id: sid,
          catalog_id: pId
        });

        if (c == 0) {
          let update = await conn.insert('catalog_relation', {
            sku_id: sid,
            catalog_id: pId,
            gmt_create: gmt_create,
            sort_num: 0
          });
        }

        if (pId == 1 || pId == 2) {
          if (pObj && pObj.name) {
            serviceNames.push(pObj.name);
          }
        } else {
          let cataInfo = await conn.get('catalog', {
            id: pId
          });
          if (cataInfo && cataInfo.name) {
            serviceNames.push(cataInfo.name);
          }
        }
      }

      if (changValue.length > 0) {
        changValue = changValue.join(',');
        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          log: changValue,
          sku_id: postData.id,
          model: 'SKU配置-' + (row.type < 3 ? '服务' : '解决方案'),
          name: postData.name
        })
      }

      let solrContent = [postData.description];
      for (let item of contents) {
        // if (item.title || item.content) {
        item.hide_title == true ? item.hide_title = 1 : item.hide_title = 0;
        if (item.show) {
          await conn.insert('sku_content', {
            sku_id: sid,
            title: item.title,
            content: item.content,
            hide_title: item.hide_title,
            template_id: item.template_id,
            anchor_title: item.anchor_title,
            anchor_show: item.anchor_show,
            gmt_create: gmt_create
          });
        }
        solrContent.push(item.title + ' ' + item.content);
        // }
      }

      // 更新资讯数据
      let caseDatas = []
      await conn.query(`DELETE FROM sku_case WHERE sku_id=${postData.id}`)
      if (skuCases.length) {
        skuCases.forEach((item, index) => {
          const title = skuCases[0].title || 0;
          const anchor_title = skuCases[0].anchor_title || 0;
          const case_id = item.case_id || 0;
          caseDatas.push(`('${title}', ${case_id}, ${postData.id}, '${anchor_title}', ${skuCases[0].anchor_show}, '${moment().format('YYYY-MM-DD HH:mm:ss')}',  '${moment().format('YYYY-MM-DD HH:mm:ss')}')`)
        })
        if (caseDatas.length) await conn.query(`INSERT INTO sku_case (title, case_id, sku_id, anchor_title, anchor_show, create_time, modify_time) VALUE ${caseDatas.join(',')}`);
      }
      // 更新下载资源数据
      let resourceDatas = []
      await conn.query(`DELETE FROM sku_resource WHERE sku_id=${postData.id}`)
      if (skuResource.length) {
        skuResource.forEach((item, index) => {
          const title = skuResource[0].title || 0;
          const anchor_title = skuResource[0].anchor_title || 0;
          const name = item.name || 0;
          const link = item.link || 0;
          const resource_id = item.resource_id || 0;
          resourceDatas.push(`('${title}', '${name}', '${link}', ${postData.id}, '${anchor_title}', ${skuResource[0].anchor_show}, '${moment().format('YYYY-MM-DD HH:mm:ss')}',  '${moment().format('YYYY-MM-DD HH:mm:ss')}', '${resource_id}')`)
        })
        if (resourceDatas.length) await conn.query(`INSERT INTO sku_resource (title, name, link, sku_id, anchor_title, anchor_show, create_time, modify_time, resource_id) VALUE ${resourceDatas.join(',')}`);
      }

      // 更新排序
      let floorSortDatas = []
      await conn.query(`DELETE FROM sku_floor_sort WHERE sku_id=${postData.id}`)
      if (skuFloor.length) {
        skuFloor.forEach((item, index) => {
          floorSortDatas.push(`('${item}', ${index + 1}, ${postData.id}, '${moment().format('YYYY-MM-DD HH:mm:ss')}')`)
        })
        if (floorSortDatas.length) await conn.query(`INSERT INTO sku_floor_sort (floor_mark, floor_sort, sku_id,create_time) VALUE ${floorSortDatas.join(',')}`);
      }

      for (let item of faqs) {
        // if (item.question && item.answer) {
        if (item.show) {
          await conn.insert('sku_qa', {
            sku_id: sid,
            question: item.question || '',
            answer: item.answer || '',
            title: item.title,
            anchor_title: item.anchor_title,
            anchor_show: item.anchor_show,
            gmt_create: gmt_create
          });
        }

        solrContent.push(item.question + ' ' + item.answer);
        // }
      }

      for (let item of tagList) {
        if (item) {
          await conn.insert('tag_relation', {
            tag_name: item,
            sku_id: sid
          });
        }
      }

      if (solrContent.length > 0) {
        solrContent = solrContent.join('  ');
      }

      if (postData.is_use) {
        let options = {
          url: env[this.app.config.env].solrUrl + '/add',
          method: 'POST',
          headers: {
            pid: solrPid,
            timestamp: new Date().getTime(),
          },
          json: true,
          body: {
            id: 'S_' + sid,
            title: postData.name,
            alias: row.alias,
            sub_title: postData.sub_title,
            summary: postData.description.replace(/<.*?>/g, "").replace(/\n/g, ''),
            content: solrContent.replace(/<.*?>/g, "").replace(/\n/g, ''),
            type: '1',
            buy: isbuy,
            industry: tradeNames.join(','),
            service: serviceNames.join(','),
            imgUrl: postData.thumb_img,
            mImgUrl: postData.m_thumb_img || postData.thumb_img,
            catalog: postData.catalogTrade.concat(postData.catalogService).join(','),
            createDate: moment(new Date()).format('YYYY/MM/DD')
          }
        }

        let parr = JSON.stringify(options.body);
        parr += solrPstr;
        parr = crypto.createHash('md5').update(parr).digest("hex");
        options.headers.sign = parr;

        request(options, function (error, response, body) {
          if (error || response.statusCode > 400) {
            return {
              success: false
            };
          }
        });
      } else {
        let options = {
          url: env[this.app.config.env].solrUrl + '/del',
          method: 'POST',
          headers: {
            pid: solrPid,
            timestamp: new Date().getTime(),
          },
          json: true,
          body: {
            id: 'S_' + sid,
          }
        }

        let parr = JSON.stringify(options.body);
        parr += solrPstr;
        parr = crypto.createHash('md5').update(parr).digest("hex");
        options.headers.sign = parr;

        request(options, function (error, response, body) {
          if (error || response.statusCode > 400) {
            return {
              success: false
            };
          }
        });
      }

      return {
        success: true
      };
    }, this.ctx);

    /**/
    // const success = result.affectedRows === 1;
    return {
      success: result.success
    };
  }

  async skuAdd(postData) {
    const {
      app
    } = this;
    const gmt_create = moment().format('YYYY-MM-DD HH:mm:ss');
    const Literal = this.app.mysql.literals.Literal;

    /*
    const repeatCheck = await this.app.mysql.count('sku', {
        name: postData.name,
        is_delete: 0
    });
    if(repeatCheck > 0){
        return { fail:true,msg:'已存在该名称的SKU' };
        return;
    }
    */

    let isbuy = 0,
      is_delete = 0,
      is_el = 0,
      isuse = 0;
    if (postData.is_buy == true) {
      isbuy = 1;
    }
    if (postData.is_use == true) {
      isuse = 1;
    }
    if (postData.is_delete) is_delete = 1;
    if (postData.is_el) is_el = 1;

    const modifyUser = this.ctx.session.userInfo;
    const row = {
      name: postData.name.replace(/'/g, '"'),
      bu_id: postData.bu_id,
      alias: postData.alias,
      sub_title: postData.sub_title,
      description: postData.description,
      is_message: postData.is_message,
      seo_text: postData.seo_text,
      cover_img: postData.cover_img,
      thumb_img: postData.thumb_img,
      banner_content: postData.banner_content,
      banner_type: postData.banner_type,
      m_cover_img: postData.m_cover_img,
      m_thumb_img: postData.m_thumb_img,
      m_banner_content: postData.m_banner_content,
      m_banner_type: postData.m_banner_type,
      is_copy: postData.is_copy || 0,
      is_buy: isbuy,
      is_use: isuse,
      is_delete,
      is_el,
      gmt_create: gmt_create,
      buy_url: postData.buy_url,
      type: postData.type,
      last_modify: modifyUser.user_name || modifyUser.name || '',
      page_title: postData.page_title,
      page_keywords: postData.page_keywords,
      page_description: postData.page_description,
      mpage_title: postData.mpage_title,
      mpage_keywords: postData.mpage_keywords,
      mpage_description: postData.mpage_description,
      img_alt: postData.img_alt
    };



    if (postData.type == 2) {
      row.class_info = JSON.stringify(postData.course);
    }

    const tradeList = postData.tradeList,
      serviceList = postData.serviceList,
      regionList = postData.region,
      tagList = postData.tag;


    const contents = postData.sContent,
      faqs = postData.faq,
      skuCases = postData.skuCases,
      skuResource = postData.skuResource,
      skuFloor = postData.skuFloor;

    let sid;

    let solrContent = [postData.description];
    let tradeNames = [],
      serviceNames = [];
    const result = await app.mysql.beginTransactionScope(async conn => {
      const insertResult = await conn.insert('sku', row);

      sid = insertResult.insertId;
      for (let i of regionList) {
        await conn.insert('sku_region', {
          sku_id: sid,
          region_id: i,
          gmt_create: gmt_create
        });
      }


      for (let item of tradeList) {

        await conn.insert('catalog_relation', {
          sku_id: sid,
          catalog_id: item.id,
          catalog_type: 1,
          gmt_create: gmt_create,
          sort_num: 0
        });

        let pObj = await conn.get('catalog', {
          id: item.id
        });
        let pId = pObj.parent_id;
        let c = await conn.count('catalog_relation', {
          sku_id: sid,
          catalog_id: pId
        });

        if (c == 0) {
          let update = await conn.insert('catalog_relation', {
            sku_id: sid,
            catalog_id: pId,
            gmt_create: gmt_create,
            sort_num: 0
          });
        }

        let cataInfo = await conn.get('catalog', {
          id: pId
        });
        if (cataInfo && cataInfo.name) {
          tradeNames.push(cataInfo.name);
        }
      }

      for (let item of serviceList) {
        await conn.insert('catalog_relation', {
          sku_id: sid,
          catalog_id: item.id,
          catalog_type: 2,
          gmt_create: gmt_create,
          sort_num: 0
        });

        let pObj = await conn.get('catalog', {
          id: item.id
        });
        let pId = pObj.parent_id;
        let c = await conn.count('catalog_relation', {
          sku_id: sid,
          catalog_id: pId
        });

        if (c == 0) {
          let update = await conn.insert('catalog_relation', {
            sku_id: sid,
            catalog_id: pId,
            gmt_create: gmt_create,
            sort_num: 0
          });
        }

        let cataInfo = await conn.get('catalog', {
          id: pId
        });
        if (cataInfo && cataInfo.name) {
          serviceNames.push(cataInfo.name);
        }
      }

      for (let item of contents) {
        // if (item.title && item.content) {
        item.hide_title == true ? item.hide_title = 1 : item.hide_title = 0;
        if (item.show) {
          await conn.insert('sku_content', {
            sku_id: sid,
            title: item.title,
            content: item.content,
            hide_title: item.hide_title,
            template_id: item.template_id,
            anchor_title: item.anchor_title,
            anchor_show: item.anchor_show,
            gmt_create: gmt_create
          });
        }

        solrContent.push(item.title + ' ' + item.content);
        // }
      }

      for (let item of faqs) {
        // if (item.question && item.answer) {
        if (item.show) {
          await conn.insert('sku_qa', {
            sku_id: sid,
            question: item.question || '',
            answer: item.answer || '',
            title: item.title,
            anchor_title: item.anchor_title,
            anchor_show: item.anchor_show,
            gmt_create: gmt_create
          });
        }

        solrContent.push(item.question + ' ' + item.answer);
        // }
      }

      for (let item of tagList) {
        if (item) {
          await conn.insert('tag_relation', {
            tag_name: item,
            sku_id: sid
          });
        }
      }

      await conn.insert('logs', {
        user_name: this.ctx.session.userInfo.name,
        sku_id: sid,
        log: '创建' + postData.name,
        model: 'SKU配置-' + (row.type < 3 ? '服务' : '解决方案'),
        name: postData.name
      });

      return {
        success: true
      };
    }, this.ctx);

    if (solrContent.length > 0) {
      solrContent = solrContent.join('  ');
    }

    if (postData.is_use) {
      let options = {
        url: env[this.app.config.env].solrUrl + '/add',
        method: 'POST',
        headers: {
          pid: solrPid,
          timestamp: new Date().getTime(),
        },
        json: true,
        body: {
          id: 'S_' + sid,
          title: postData.name,
          alias: row.alias,
          sub_title: postData.sub_title,
          summary: postData.description.replace(/<.*?>/g, "").replace(/\n/g, ''),
          content: solrContent.replace(/<.*?>/g, "").replace(/\n/g, ''),
          type: '1',
          buy: isbuy,
          industry: tradeNames.join(','),
          service: serviceNames.join(','),
          imgUrl: postData.thumb_img,
          mImgUrl: postData.m_thumb_img || postData.thumb_img,
          catalog: postData.catalogTrade.concat(postData.catalogService).join(','),
          createDate: moment(new Date()).format('YYYY/MM/DD')
        }
      }

      let parr = JSON.stringify(options.body);
      parr += solrPstr;
      parr = crypto.createHash('md5').update(parr).digest("hex");
      options.headers.sign = parr;

      request(options, function (error, response, body) {
        if (error || response.statusCode > 400) {
          return;
        }
      });
    }

    //const success = result.affectedRows === 1;
    await this.ctx.service.baidu.Push(`/sku/${sid}`);
    const id = {
      id: sid
    };

    // 插入推荐记录
    const recommendDatas = []
    if (postData.recommends.length) {
      postData.recommends.forEach((item, index) => {
        if (item.sku_id) {
          recommendDatas.push(`(${sid}, ${item.sku_id}, '${item.sku_name}', '${item.sku_image}', '${item.sku_url}', ${index + 1}, '${moment().format('YYYY-MM-DD HH:mm:ss')}',  '${moment().format('YYYY-MM-DD HH:mm:ss')}')`)
        }
      })
      if (recommendDatas.length) await app.mysql.query(`INSERT INTO sku_recommend (source_id, sku_id, sku_name, sku_image, sku_url, sort_num, gmt_modify, gmt_create) VALUE ${recommendDatas.join(',')}`);
    }

    // 插入资讯数据
    let caseDatas = []
    if (skuCases.length) {
      skuCases.forEach((item, index) => {
        const title = skuCases[0].title || 0;
        const anchor_title = skuCases[0].anchor_title || 0;
        const case_id = item.case_id || 0;
        caseDatas.push(`('${title}', ${case_id}, ${sid}, '${anchor_title}', ${skuCases[0].anchor_show}, '${moment().format('YYYY-MM-DD HH:mm:ss')}',  '${moment().format('YYYY-MM-DD HH:mm:ss')}')`)
      })
      if (caseDatas.length) await app.mysql.query(`INSERT INTO sku_case (title, case_id, sku_id, anchor_title, anchor_show, create_time, modify_time) VALUE ${caseDatas.join(',')}`);
    }
    // 插入下载资源数据
    let resourceDatas = []
    if (skuResource.length) {
      skuResource.forEach((item, index) => {
        const title = skuResource[0].title || 0;
        const anchor_title = skuResource[0].anchor_title || 0;
        const name = item.name || 0;
        const link = item.link || 0;
        const resource_id = item.resource_id || 0;
        resourceDatas.push(`('${title}', '${name}', '${link}', ${sid}, '${anchor_title}', ${skuResource[0].anchor_show}, '${moment().format('YYYY-MM-DD HH:mm:ss')}',  '${moment().format('YYYY-MM-DD HH:mm:ss')}', '${resource_id}')`)
      })
      if (resourceDatas.length) await app.mysql.query(`INSERT INTO sku_resource (title, name, link, sku_id, anchor_title, anchor_show, create_time, modify_time, resource_id) VALUE ${resourceDatas.join(',')}`);
    }

    // 插入排序
    let floorSortDatas = []
    if (skuFloor.length) {
      skuFloor.forEach((item, index) => {
        floorSortDatas.push(`('${item}', ${index + 1}, ${sid}, '${moment().format('YYYY-MM-DD HH:mm:ss')}')`)
      })
      if (floorSortDatas.length) await app.mysql.query(`INSERT INTO sku_floor_sort (floor_mark, floor_sort, sku_id,create_time) VALUE ${floorSortDatas.join(',')}`);
    }

    return {
      id
    };
  }

  async skuDelete(postData) {
    const {
      app
    } = this;
    const id = postData.id;

    const result = await app.mysql.beginTransactionScope(async conn => {
      await conn.update('sku', {
        id: id,
        is_delete: 1,
        gmt_modify :moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      });

      const info = await conn.get('sku', {
        id: id
      });
      if (!id.length) {
        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          sku_id: id,
          log: '删除' + info.name,
          model: 'SKU配置-' + (info.type < 3 ? '服务' : '解决方案'),
          name: info.name
        });
      }

      return {
        success: true
      };

    }, this.ctx);

    let options = {
      url: env[this.app.config.env].solrUrl + '/del',
      method: 'POST',
      headers: {
        pid: solrPid,
        timestamp: new Date().getTime(),
      },
      json: true,
      body: {
        id: 'S_' + id,
      }
    }

    let parr = JSON.stringify(options.body);
    parr += solrPstr;
    parr = crypto.createHash('md5').update(parr).digest("hex");
    options.headers.sign = parr;

    request(options, function (error, response, body) {
      if (error || response.body.resultCode !== '0') {
        return;
      }
    });

    // const success = result.affectedRows > 0;
    return {
      success: result.success
    };
  }

  async skuUpdateValue(id, value, type) {
    const row = {
      id: id
    };

    value == true ? value = 1 : value = 0;
    row[type] = value;
    row.gmt_modify = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    const update = await this.app.mysql.update('sku', row);

    const success = update.affectedRows === 1;
    return {
      success
    };
  }

  async tagAdpt(id) {
    const tag_name = await this.app.mysql.select('tag_relation', {
      where: {
        sku_id: id
      }
    });

    if (tag_name.length == 0) {
      let list = [];
      return {
        list
      }
    } else {
      let arr = [];
      for (let item of tag_name) {
        arr.push(item.tag_name);
      }

      //this.app.mysql.escape(arr)
      let list = await this.app.mysql.query('SELECT s.id,s.name,s.thumb_img FROM tag_relation t LEFT JOIN sku s ON t.sku_id=s.id WHERE t.tag_name IN (' + this.app.mysql.escape(arr) + ') AND t.sku_id IS NOT NULL AND t.sku_id!=' + id + ' AND s.is_use=1 GROUP BY s.id');

      if (list.length > 3) {
        let outList = [];

        for (var i = 0; i < list.length; i++) {
          outList[i] = i;
        }

        outList.sort(function () {
          return 0.5 - Math.random();
        });

        let newList = [];
        for (var i = 0; i < list.length; i++) {
          newList.push(list[outList[i]]);
        }

        list = newList;
      }

      return {
        list
      };

    }
  }

  async getCatalogSkus(params, type) {
    const {
      app
    } = this;
    let queryType = type ? type : '1,2';

    const id = params.id;
    let page = params.page || 1,
      limit = params.limit || 10,
      is_buy = params.is_buy,
      is_use = params.is_use,
      name = params.name || '';

    if (!page || page == 'NULL' || page == '') {
      page = 1;
    }

    if (!limit || limit == 'NULL' || limit == '') {
      limit = 10;
    }

    let qFilter = [];

    if (typeof is_buy == 'boolean') {
      if (is_buy == false) {
        qFilter.push('s.is_buy=0');
      }

      if (is_buy == true) {
        qFilter.push('s.is_buy=1');
      }
    }

    if (typeof is_use == 'boolean') {
      if (is_use == false) {
        qFilter.push('s.is_use=0');
      }

      if (is_use == true) {
        qFilter.push('s.is_use=1');
      }
    }

    if (name && name != '') {
      qFilter.push('s.name LIKE "%' + name + '%"');
    }

    qFilter.push(`s.type IN (${queryType})`);
    qFilter = qFilter.join(' AND ');
    if (qFilter != '') {
      qFilter = ' AND ' + qFilter;
    }

    let query = 'SELECT s.id,s.name,s.alias,s.type,s.is_use,s.thumb_img,s.is_buy,s.gmt_modify,r.sort_num, r.gmt_modify as last_modify  FROM sku s LEFT JOIN catalog_relation r ON s.id=r.sku_id LEFT JOIN catalog c ON r.catalog_id=c.id WHERE s.is_delete=0 AND c.id=' + id;
    let cquery = 'SELECT s.id FROM sku s LEFT JOIN catalog_relation r ON s.id=r.sku_id LEFT JOIN catalog c ON r.catalog_id=c.id WHERE s.is_delete=0 AND c.id=' + id;
    let total = await this.app.mysql.query(cquery + qFilter);
    let list = await this.app.mysql.query(query + qFilter + ' ORDER BY r.sort_num DESC,s.is_use DESC,s.gmt_modify DESC LIMIT ' + (page - 1) * limit + ',' + limit);
    console.log(query + qFilter + ' ORDER BY r.sort_num DESC,s.is_use DESC,s.gmt_modify DESC LIMIT ' + (page - 1) * limit + ',' + limit);
    total = total.length;

    for (let item of list) {
      item.is_use == 0 ? item.is_use = false : item.is_use = true;
      item.is_buy == 0 ? item.is_buy = false : item.is_buy = true;
      item.time = moment(item.gmt_modify).format('YYYY-MM-DD HH:mm:ss');
      item.last_modify = moment(item.last_modify).format('YYYY-MM-DD HH:mm:ss');

      let tradeList = [],
        serviceList = [];
      let tradeCata = await this.app.mysql.select('catalog_relation', {
        where: {
          sku_id: item.id,
          catalog_type: 1
        }
      });
      let serviceCate = await this.app.mysql.select('catalog_relation', {
        where: {
          sku_id: item.id,
          catalog_type: 2
        }
      });


      if (tradeCata && tradeCata.length > 0) {
        for (let ti of tradeCata) {
          let tradeInfo = await this.app.mysql.get('catalog', {
            id: ti.catalog_id,
            is_delete: 0
          });
          if (tradeInfo) {
            let tradeParantInfo = await this.app.mysql.get('catalog', {
              id: tradeInfo.parent_id,
              is_delete: 0
            });
            if (tradeParantInfo) {
              tradeList.push(tradeParantInfo.name + '-' + tradeInfo.name);
            }

          }

        }
      }

      if (serviceCate && serviceCate.length > 0) {
        for (let si of serviceCate) {
          let serviceInfo = await this.app.mysql.get('catalog', {
            id: si.catalog_id,
            is_delete: 0
          });
          if (serviceInfo) {
            let serviceParantInfo = await this.app.mysql.get('catalog', {
              id: serviceInfo.parent_id,
              is_delete: 0
            });
            if (serviceParantInfo) {
              serviceList.push(serviceParantInfo.name + '-' + serviceInfo.name);
            }

          }

        }
      }

      item.tradeList = tradeList;
      item.serviceList = serviceList;
    }

    let cataList = [];
    const thisObj = await app.mysql.get('catalog', {
      id: id
    });
    const thisName = {
      name: thisObj.name,
      id: thisObj.id
    };

    if (thisObj.parent_id != 1 && thisObj.parent_id != 2) {
      const parantObj = await app.mysql.get('catalog', {
        id: thisObj.parent_id
      });
      const parentName = {
        name: parantObj.name,
        id: parantObj.id
      };
      cataList.push(parentName);
    }

    cataList.push(thisName);

    return {
      list,
      total,
      cataList
    };
  }

  async setSort(data) {
    const {
      app
    } = this;

    // let result = [];
    let names = [];
    let catalog_id = '',
      bigType = '';
    const result = await app.mysql.beginTransactionScope(async conn => {
      for (let item of data) {
        const o = await conn.get('catalog_relation', {
          catalog_id: item.catalog_id,
          sku_id: item.sku_id
        });
        catalog_id = item.catalog_id;
        bigType = item.bigType;
        if (o.sort_num != item.sort_num) {
          const r = await conn.update('catalog_relation', {
            sort_num: item.sort_num
          }, {
            where: {
              catalog_id: item.catalog_id,
              sku_id: item.sku_id
            }
          });
          const info = await conn.get('sku', {
            id: item.sku_id
          });
          names.push(info.name);
        }

      }

      if (catalog_id == 6 && names.length > 0) {
        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          log: '修改' + names.join(',') + '排序',
          model: '运维内容维护',
          name: names.join(',')
        });
      } else if (names.length > 0) {
        const cinfo = await conn.get('catalog', {
          id: catalog_id
        })
        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          catalog_id: catalog_id,
          log: '修改' + names.join(',') + '排序',
          model: bigType == 'trade' ? '行业分类' : '服务分类',
          name: cinfo.name
        });
      }

      return {
        success: true
      };

    }, this.ctx);


    return {
      result
    };
  }

  async getSkuByCata(data) {
    const {
      app
    } = this;
    const catalog_id = data.id,
      name = data.name.trim() || '',
      page = data.page || 1,
      limit = data.limit || 5;

    let query = [];
    query.push('cr.catalog_id=' + catalog_id);
    query.push('s.is_delete=0');
    query.push('s.is_use=1');
    query.push('s.type IN (1,2)');
    if (name && name != '') {
      query.push('s.name LIKE "%' + name + '%"');
    }

    query = query.join(' AND ');

    const list = await app.mysql.query('SELECT s.id,s.name,s.sub_title,s.description,s.is_message,s.thumb_img,s.is_buy FROM sku s LEFT JOIN catalog_relation cr ON cr.sku_id=s.id WHERE ' + query + ' ORDER BY cr.sort_num,s.is_use DESC,s.gmt_modify DESC LIMIT ' + (page - 1) * limit + ',' + limit);
    let total = await app.mysql.query('SELECT s.id FROM sku s LEFT JOIN catalog_relation cr ON cr.sku_id=s.id WHERE ' + query);
    total = total.length;

    if (list.length > 0) {
      for (let item of list) {
        item.tags = await app.mysql.select('tag_relation', {
          columns: ['id', 'tag_name'],
          where: {
            sku_id: item.id
          },
        })
      }
    }

    return {
      list,
      total,
      page
    };
  }

  async getSkuByTrainCata(data) {
    const {
      app
    } = this;
    const catalog_id = data.id,
      name = data.name.trim() || '',
      page = data.page || 1,
      limit = data.limit || 5,
      area = data.area,
      time = data.time,
      days = data.days,
      is_buy = data.is_buy;

    let query = [];
    query.push('cr.catalog_id=' + catalog_id);
    query.push('s.is_delete=0');
    query.push('s.is_use=1');
    if (is_buy == 1) {
      query.push('s.is_buy=1');
    }
    query.push('s.type IN (1,2)');
    if (name && name != '') {
      query.push('s.name LIKE "%' + name + '%"');
    }

    query = query.join(' AND ');

    let list = await app.mysql.query('SELECT s.id,s.name,s.sub_title,s.description,s.is_message,s.thumb_img,s.class_info,s.is_buy FROM sku s LEFT JOIN catalog_relation cr ON cr.sku_id=s.id WHERE ' + query + ' ORDER BY cr.sort_num,s.is_use DESC,s.gmt_modify DESC');

    let newList = [];
    for (let item of list) {
      let class_info = item.class_info;
      if (class_info) {
        item.class_info = JSON.parse(class_info);
        newList.push(item);
      }
    }


    for (let [i, item] of newList.entries()) {
      let class_info = item.class_info;
      if (time && class_info.time.indexOf(time) == -1) {
        newList.splice(i, 1);
      }

      if (area && class_info.area.indexOf(area) == -1) {
        newList.splice(i, 1);
      }

      if (days && class_info.days != days) {
        newList.splice(i, 1);
      }

    }

    if (!time && !area && !days) {
      newList = list;
    }

    let total = newList.length;

    for (let [i, item] of newList.entries()) {
      if (i < (page - 1) * limit && i > page * limit) {
        newList.splice(i, 1);
      }
    }

    if (newList.length > 0) {
      for (let item of newList) {
        item.tags = await app.mysql.select('tag_relation', {
          columns: ['id', 'tag_name'],
          where: {
            sku_id: item.id
          },
        })
      }
    }

    list = newList;
    return {
      list,
      total,
      page
    };
  }

  //update 一级分类
  async updateSkuCata() {
    const {
      app
    } = this;

    const skus = await app.mysql.query('SELECT s.id,c.parent_id FROM sku s LEFT JOIN catalog_relation cr ON cr.sku_id=s.id LEFT JOIN catalog c ON c.id=cr.catalog_id WHERE c.parent_id IS NOT NULL');

    let gmt_create = moment().format('YYYY-MM-DD HH:mm:ss');
    for (let item of skus) {
      let c = await app.mysql.count('catalog_relation', {
        sku_id: item.id,
        catalog_id: item.parent_id
      });

      if (c == 0) {
        let update = await app.mysql.insert('catalog_relation', {
          sku_id: item.id,
          catalog_id: item.parent_id,
          gmt_create: gmt_create,
          sort_num: 0
        });
      }
    }

    //return newData;
  }

  //解除关联
  async dropAlias(params) {
    const {
      app
    } = this;

    const sku_id = params.sku_id,
      catalog_id = params.id;

    const firstDrop = await app.mysql.delete('catalog_relation', {
      sku_id: sku_id,
      catalog_id: catalog_id
    });

    const parentObj = await app.mysql.get('catalog', {
      id: catalog_id
    });
    const secondDrop = await app.mysql.delete('catalog_relation', {
      sku_id: sku_id,
      catalog_id: parentObj.parent_id
    });

    const childrenObj = await app.mysql.select('catalog', {
      columns: ['id'],
      where: {
        parent_id: catalog_id
      },
    });

    if (childrenObj) {
      for (let item of childrenObj) {
        let thirdDrop = await app.mysql.delete('catalog_relation', {
          sku_id: sku_id,
          catalog_id: item.id
        });
      }
    }

    const info = await app.mysql.get('sku', {
      id: sku_id
    });
    const cinfo = await app.mysql.get('catalog', {
      id: catalog_id
    });
    let pid = cinfo.parent_id;
    if (pid > 2) {
      const cinfop = await app.mysql.get('catalog', {
        id: cinfo.parent_id
      });
      pid = cinfop.parent_id;
    }

    await this.ctx.service.logs.log({
      type: 'catalog',
      id: catalog_id,
      log: '解除' + info.name + '的关联',
      model: pid == 1 ? '行业分类' : '服务分类',
      name: cinfo.name
    });

    const success = firstDrop.affectedRows === 1;
    return {
      success
    };
  }
}

module.exports = SkuService;
