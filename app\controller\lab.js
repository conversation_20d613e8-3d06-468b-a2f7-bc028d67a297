'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo; 
const { env } = require('../../config/info').siteInfo;
class LabController extends Controller {
  async labList() {
    const {
      ctx
    } = this;
    const labListQry = await ctx.service.lab.labListQry();

    await ctx.render('lab_list', {
      labList: JSON.stringify(labListQry.list),
      site_title: _info.site_title,
      page_title: '实验室列表',
      active: '17-1',
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      ticMallHost: env[this.app.config.env].ticMallHost,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: 1,
      hasPubviewPurview: 1,
      hasEditPurview: 1
    });
  }

  async labDetail() {
    const {
      ctx
    } = this;
    const {
      id
    } = ctx.params
    const labListDetail = await ctx.service.lab.labListDetail(id);
    // 为楼层割接数据，默认为显示
    // https://cnjira.sgs.net/browse/TIC-11230
    const content = JSON.parse(labListDetail.detail.content);
    content.floors.forEach(v => {
      if (v.showFloor === undefined) v.showFloor = 1
    })
    labListDetail.detail.content = JSON.stringify(content)

    await ctx.render('lab_detail', {
      detail: labListDetail.detail,
      site_title: _info.site_title,
      page_title: '实验室详情',
      active: '17-1',
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      ticMallHost: env[this.app.config.env].ticMallHost,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: 1,
      hasPubviewPurview: 1,
      hasEditPurview: 1
    });
  }

  async labMobileDetail() {
    const {
      ctx
    } = this;
    const {
      id
    } = ctx.params
    const labListDetail = await ctx.service.lab.labListDetail(id);

    await ctx.render('lab_mobile_detail', {
      detail: labListDetail.detail,
      site_title: _info.site_title,
      page_title: '移动端实验室详情',
      active: '17-1',
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      ticMallHost: env[this.app.config.env].ticMallHost,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: 1,
      hasPubviewPurview: 1,
      hasEditPurview: 1
    });
  }

  async labListQry() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.lab.labListQry(params);

    ctx.body = result;
  }

  async labSave() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.lab.labSave(params);

    ctx.body = result;
  }

  async labAdd() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.lab.labAdd(params);

    ctx.body = result;
  }

  async labDel() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.lab.labDel(params);

    ctx.body = result;
  }
}
module.exports = LabController;