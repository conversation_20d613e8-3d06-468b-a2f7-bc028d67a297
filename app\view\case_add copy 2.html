<% include header.html %>
<el-container id="Main" v-loading="loading">
  <% include eheader.html %>
  <el-container>
    <% include eside.html %>
    <iframe
      :src="console_url + '/case/add'+ '?userName=' + userName + '&userType=' + userType + '&userId=' + userId"
      style="width: 100%; border: none"
    ></iframe>
  </el-container>
</el-container>

<script>
  var main = new Vue({
    el: "#Main",
    data: {
      loading: false,
      portal_url: "<%- portal_url %>",
      console_url: "<%- console_url %>",
      userName: "<%- userName %>", // 用户名
      userType: "<%- userType %>", // 1:管理员 2:普通用户
      userId: "<%- userId %>",
    },
    methods: {},
    mounted() {
      this.$nextTick(() => {
        document.getElementById("preLoading").style.display = "none";
      });
      window.addEventListener(
        "message",
        function (e) {
          // 跳转到添加页面
          if (e.data.handleAdd) window.location.href = e.data.handleAdd;
          // 跳转到编辑页面
          if (e.data.handleEdit) window.location.href = e.data.handleEdit;
          // 跳转到列表页面
          if (e.data.handleList) window.location.href = e.data.handleList;
        },
        false
      );
    },
  });
</script>

<style></style>
<% include footer.html %>
