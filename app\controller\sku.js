'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const { env } = require('../../config/info').siteInfo;
class SkuController extends Controller {
  async skuList() {
    const {
      ctx
    } = this;
    const params = ctx.request.query;
    const name = params.name;

    let serviceList;
    let tradeList;

    let searchCondition = {
      bigType: 'sku',
      name: name
    };


    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      searchCondition.allowServiceList = dataids.ids;

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      tradeList = await ctx.service.catalog.getTradeList(paramsData);

      searchCondition.allowTradeList = dataids.ids;
    } else {
      serviceList = await ctx.service.catalog.getSercataList();
      tradeList = await ctx.service.catalog.getTradeList();
    }


    const list = await ctx.service.sku.getList(searchCondition);

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;
    let hasEditPurview = 1;
    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }

      if (dataids.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (dataids.publish != 1) {
        hasPubviewPurview = 0;
      }
      if (dataids.edit != 1) {
        hasEditPurview = 0;
      }
    }


    let onlyEditPurview = 0
    if (this.ctx.session.userInfo.type == 1) {
      onlyEditPurview = 0;
    } else {
      const purviewList = await ctx.service.admin.getMenuPurview2(this.ctx.session.userInfo.name, "查看+导出");

      for (var menuId of purviewList) {
        if (menuId == 6) //3是菜单表里面的服务分类
        {
          onlyEditPurview = 1;
          break;
        }
      }
    }
    await ctx.render('sku_service', {
      serviceIds: this.ctx.session.apiData.data.serviceData.ids,
      onlyEditPurview,
      site_title: _info.site_title,
      page_title: 'SKU配置',
      active: '3-1',
      csrf: ctx.csrf,
      list: JSON.stringify(list.list),
      length: list.list.length - 1,
      total: list.total,
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      bigType: 'sku',
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      name: name,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
      hasEditPurview: hasEditPurview,
      buIds: this.ctx.session.buIds ? JSON.stringify(this.ctx.session.buIds) : JSON.stringify([])
    });

  }

  async listFilter() {
    const {
      ctx
    } = this;

    const value = ctx.request.body.value,
      types = ctx.request.body.type;
    const result = await ctx.service.sku.getSkuList(value, types);

    ctx.body = {
      success: true,
      list: result.list,
      total: result.total
    }
  }

  async skuAdd() {

    const {
      ctx
    } = this;

    const alias = ctx.params.alias;
    const regionList = await ctx.service.setting.getRegionList();

    let serviceList;
    let tradeList;
    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;

      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;

      tradeList = await ctx.service.catalog.getTradeList(paramsData);
    } else {
      serviceList = await ctx.service.catalog.getSercataList();
      tradeList = await ctx.service.catalog.getTradeList();
    }

    const citys = await ctx.service.setting.getCitysList();

    if (alias == 'add') {
      let hasCheckedPurview = 1;
      let hasPubviewPurview = 1;

      if (this.ctx.session.userInfo.type != 1) {
        let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
        if (dataids == null) {
          dataids = {};
          dataids.ids = [0];
        }

        if (dataids.checked != 1) {
          hasCheckedPurview = 0;
        }
        if (dataids.publish != 1) {
          hasPubviewPurview = 0;
        }
      }

      await ctx.render('sku_service_add', {
        site_title: _info.site_title,
        ticMallHost: env[this.app.config.env].ticMallHost,
        page_title: '添加SKU',
        active: '3-1',
        csrf: ctx.csrf,
        region: JSON.stringify(regionList.list),
        citys: JSON.stringify(citys.list),
        serviceList: JSON.stringify(serviceList.list),
        tradeList: JSON.stringify(tradeList.list),
        bigType: 'sku',
        view_url: env[this.app.config.env].view_url,
        mview_url: env[this.app.config.env].mview_url,
        userInfo: ctx.session.userInfo,
        hasCheckedPurview: hasCheckedPurview,
        hasPubviewPurview: hasPubviewPurview
      });




    } else if (alias != '') {

      var checkflag = await ctx.service.admin.checkDataCanOperator('sku', parseInt(alias));

      if (!checkflag) {
        await ctx.render('error', {});
        return;
      }


      const detail = await ctx.service.sku.getEditInfo(alias);
      const logs = await ctx.service.logs.listAll({
        type: 'sku',
        id: alias,
        page: 1,
        limit: 10
      });

      detail.detail.sContent.forEach(item => {
        item.show = true;
      });

      detail.detail.faq.forEach(item => {
        item.show = true;
      });

      let hasCheckedPurview = 1;
      let hasPubviewPurview = 1;

      if (this.ctx.session.userInfo.type != 1) {
        let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
        if (dataids == null) {
          dataids = {};
          dataids.ids = [0];
        }

        if (dataids.checked != 1) {
          hasCheckedPurview = 0;
        }
        if (dataids.publish != 1) {
          hasPubviewPurview = 0;
        }
      }

      let checkflowResult = await ctx.service.checkflow.getDetail('sku_service', alias);
      if (checkflowResult == null) {
        checkflowResult = {};
      }

      await ctx.render('sku_service_edit', {
        site_title: _info.site_title,
        ticMallHost: env[this.app.config.env].ticMallHost,
        page_title: '编辑SKU',
        active: '3-1',
        csrf: ctx.csrf,
        region: JSON.stringify(regionList.list),
        citys: JSON.stringify(citys.list),
        serviceList: JSON.stringify(serviceList.list),
        tradeList: JSON.stringify(tradeList.list),
        alias: alias,
        form: detail.detail,
        sContent: JSON.stringify(detail.detail.sContent),
        faq: JSON.stringify(detail.detail.faq),
        regions: JSON.stringify(detail.detail.region),
        tradeCata: JSON.stringify(detail.detail.tradeCata),
        serviceCata: JSON.stringify(detail.detail.serviceCata),
        tag: detail.detail.tag,
        bigType: 'sku',
        view_url: env[this.app.config.env].view_url,
        mview_url: env[this.app.config.env].mview_url,
        userInfo: ctx.session.userInfo,
        logs: JSON.stringify(logs),
        hasCheckedPurview: hasCheckedPurview,
        hasPubviewPurview: hasPubviewPurview,
        checkflowResult: JSON.stringify(checkflowResult)
      });

    } else {
      ctx.body = 'unkonw type';
    }
  }

  async skuEdit() {
    const {
      ctx
    } = this;

    const postData = ctx.request.body;
    const params = ctx.request.body;

    var tradearr = [];
    var servicearr = [];
    for (let item of params.tradeList) {
      var tmpRow = await ctx.service.catalog.getById(item.id);
      if (tmpRow) {
        if (tradearr.indexOf(tmpRow.parent_id) == -1) {
          tradearr.push(tmpRow.parent_id);
        }
      }
    }

    for (let item of params.serviceList) {
      var tmpRow = await ctx.service.catalog.getById(item.id);
      if (tmpRow) {
        if (servicearr.indexOf(tmpRow.parent_id) == -1) {
          servicearr.push(tmpRow.parent_id);
        }
      }
    }

    var trade_flag = await this.ctx.service.admin.checkUserTradeIsAllow(tradearr);
    var service_flag = await this.ctx.service.admin.checkUserServiceIsAllow(servicearr);

    if (!trade_flag || !service_flag) //检查提交过来的数据是否有权限
    {
      ctx.body = {
        fail: true,
        needReLogin: 1,
        loginUrl: '/login',
        msg: '请重新登录'
      };
      return;
    }


    const result = await ctx.service.sku.skuAdd(postData);

    if (result.id) {
      ctx.body = {
        success: true,
        data: result.id
      };
    } else {
      ctx.body = result;
    }
  }

  async skuEdit2() {
    //服务修改
    const {
      ctx
    } = this;

    const postData = ctx.request.body;

    const params = ctx.request.body;
    if (postData.id && parseInt(postData.id) > 0) {
      //检查老数据是否有权限
      var checkflag = await ctx.service.admin.checkDataCanOperator('sku', postData.id);
      if (!checkflag) {
        ctx.body = {
          fail: true,
          needReLogin: 1,
          loginUrl: '/login',
          msg: '请重新登录'
        };
        return;
      }
    }

    var tradearr = [];
    var servicearr = [];
    for (let item of params.tradeList) {
      var tmpRow = await ctx.service.catalog.getById(item.id);
      if (tmpRow) {
        if (tradearr.indexOf(tmpRow.parent_id) == -1) {
          tradearr.push(tmpRow.parent_id);
        }
      }
    }

    for (let item of params.serviceList) {
      var tmpRow = await ctx.service.catalog.getById(item.id);
      if (tmpRow) {
        if (servicearr.indexOf(tmpRow.parent_id) == -1) {
          servicearr.push(tmpRow.parent_id);
        }
      }
    }

    var trade_flag = await this.ctx.service.admin.checkUserTradeIsAllow(tradearr);
    var service_flag = await this.ctx.service.admin.checkUserServiceIsAllow(servicearr);

    if (!trade_flag || !service_flag) //检查提交过来的数据是否有权限
    {
      ctx.body = {
        fail: true,
        needReLogin: 1,
        loginUrl: '/login',
        msg: '请重新登录'
      };
      return;
    }

    const result = await ctx.service.sku.skuEdit(postData);

    const cndParams = {
      fileUrls: [postData.pageLink]
    }
    if (result.success) {
      // 保存成功推送CDN刷新
      // await ctx.service.external.CDNRefresh(cndParams);
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async skuDelete() {
    const {
      ctx
    } = this;

    const postData = ctx.request.body;

    var checkflag = await ctx.service.admin.checkDataCanOperator('sku', postData.id);
    if (!checkflag) {
      ctx.body = {
        fail: true,
        data: null
      };
      return;
    }

    const result = await ctx.service.sku.skuDelete(postData);

    const cndParams = {
      fileUrls: [postData.pageLink]
    }
    if (result.success) {
      // 保存成功推送CDN刷新
      // await ctx.service.external.CDNRefresh(cndParams);
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async skuChangeValue() {
    const {
      ctx
    } = this;

    const postData = ctx.request.body;

    var checkflag = await ctx.service.admin.checkDataCanOperator('sku', postData.id);
    if (!checkflag) {
      ctx.body = {
        fail: true,
        data: null
      };
      return;
    }

    const result = await ctx.service.sku.skuUpdateValue(postData.id, postData.value, postData.type);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async getList() {
    const {
      ctx
    } = this;

    //const value = ctx.request.body.value, types = ctx.request.body.type, page = ctx.request.body.page;
    const params = ctx.request.body;
    params.bigType = 'sku';

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      params.allowServiceList = dataids.ids;

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      params.allowTradeList = dataids.ids;
    }

    const list = await ctx.service.sku.getList(params);

    ctx.body = {
      success: true,
      data: list
    }
  }

  //solution
  async solutionList() {
    const {
      ctx
    } = this;

    let params = {
      bigType: 'solution'
    };


    let paramsData = [];
    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData.ids = dataids.ids;
      params.allowServiceList = paramsData.ids;
    }

    const serviceList = await ctx.service.catalog.getSercataList(paramsData);

    paramsData = [];
    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData.ids = dataids.ids;
      params.allowTradeList = paramsData.ids;
    }

    const tradeList = await ctx.service.catalog.getTradeList(paramsData);

    const list = await ctx.service.sku.getList(params);

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;
    let hasEditPurview = 1;

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }

      if (dataids.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (dataids.publish != 1) {
        hasPubviewPurview = 0;
      }
      if (dataids.edit != 1) {
        hasEditPurview = 0;
      }
    }

    let onlyEditPurview = 0
    if (this.ctx.session.userInfo.type == 1) {
      onlyEditPurview = 0;
    } else {
      const purviewList = await ctx.service.admin.getMenuPurview2(this.ctx.session.userInfo.name, "查看+导出");

      for (var menuId of purviewList) {
        if (menuId == 7) //3是菜单表里面的服务分类
        {
          onlyEditPurview = 1;
          break;
        }
      }
    }

    await ctx.render('sku_solution', {
      tradeIds: this.ctx.session.apiData.data.tradeData.ids,
      onlyEditPurview,
      site_title: _info.site_title,
      page_title: '解决方案配置',
      active: '3-2',
      csrf: ctx.csrf,
      list: JSON.stringify(list.list),
      length: list.list.length - 1,
      total: list.total,
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      bigType: 'solution',
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
      hasEditPurview: hasEditPurview,
      buIds: this.ctx.session.buIds ? JSON.stringify(this.ctx.session.buIds) : JSON.stringify([])
    });
  }

  async solutionAdd() {
    const {
      ctx
    } = this;

    const alias = ctx.params.alias;
    const regionList = await ctx.service.setting.getRegionList();

    let serviceList;
    let tradeList;
    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      tradeList = await ctx.service.catalog.getTradeList(paramsData);
    } else {

      serviceList = await ctx.service.catalog.getSercataList();

      tradeList = await ctx.service.catalog.getTradeList();

    }

    const citys = await ctx.service.setting.getCitysList();

    if (alias == 'add') {

      let hasCheckedPurview = 1;
      let hasPubviewPurview = 1;

      if (this.ctx.session.userInfo.type != 1) {
        let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
        if (dataids == null) {
          dataids = {};
          dataids.ids = [0];
        }

        if (dataids.checked != 1) {
          hasCheckedPurview = 0;
        }
        if (dataids.publish != 1) {
          hasPubviewPurview = 0;
        }
      }

      await ctx.render('sku_solution_add', {
        site_title: _info.site_title,
        ticMallHost: env[this.app.config.env].ticMallHost,
        page_title: '添加解决方案',
        active: '3-2',
        csrf: ctx.csrf,
        region: JSON.stringify(regionList.list),
        citys: JSON.stringify(citys.list),
        serviceList: JSON.stringify(serviceList.list),
        tradeList: JSON.stringify(tradeList.list),
        bigType: 'solution',
        view_url: env[this.app.config.env].view_url,
        mview_url: env[this.app.config.env].mview_url,
        userInfo: ctx.session.userInfo,
        hasCheckedPurview: hasCheckedPurview,
        hasPubviewPurview: hasPubviewPurview
      });
    } else if (alias != '') {

      var checkflag = await ctx.service.admin.checkDataCanOperator('solution', parseInt(alias));

      if (!checkflag) {
        await ctx.render('error', {});
        return;
      }

      const detail = await ctx.service.sku.getEditInfo(alias);
      // const logs = await ctx.service.logs.list({sku_id: alias});
      const logs = await ctx.service.logs.listAll({
        type: 'sku',
        id: alias,
        page: 1,
        limit: 10
      });

      detail.detail.tradeCata.forEach(item => {
        tradeList.list.forEach(item2 => {
          if (item2.id == item.id) {
            item2.checked = true;
          }
        })
      });

      detail.detail.serviceCata.forEach(item => {
        serviceList.list.forEach(item2 => {
          if (item2.id == item.id) {
            item2.checked = true;
          }
        })
      });

      detail.detail.sContent.forEach(item => {
        item.show = true;
      });

      detail.detail.faq.forEach(item => {
        item.show = true;
      });

      let hasCheckedPurview = 1;
      let hasPubviewPurview = 1;

      if (this.ctx.session.userInfo.type != 1) {
        let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
        if (dataids == null) {
          dataids = {};
          dataids.ids = [0];
        }

        if (dataids.checked != 1) {
          hasCheckedPurview = 0;
        }
        if (dataids.publish != 1) {
          hasPubviewPurview = 0;
        }
      }

      let checkflowResult = await ctx.service.checkflow.getDetail('sku_solution', alias);
      if (checkflowResult == null) {
        checkflowResult = {};
      }

      await ctx.render('sku_solution_edit', {
        site_title: _info.site_title,
        ticMallHost: env[this.app.config.env].ticMallHost,
        page_title: '编辑解决方案',
        active: '3-2',
        csrf: ctx.csrf,
        region: JSON.stringify(regionList.list),
        citys: JSON.stringify(citys.list),
        serviceList: JSON.stringify(serviceList.list),
        tradeList: JSON.stringify(tradeList.list),
        alias: alias,
        form: detail.detail,
        sContent: JSON.stringify(detail.detail.sContent),
        faq: JSON.stringify(detail.detail.faq),
        regions: JSON.stringify(detail.detail.region),
        tradeCata: JSON.stringify(detail.detail.tradeCata),
        serviceCata: JSON.stringify(detail.detail.serviceCata),
        tag: detail.detail.tag,
        bigType: 'solution',
        view_url: env[this.app.config.env].view_url,
        mview_url: env[this.app.config.env].mview_url,
        userInfo: ctx.session.userInfo,
        logs: JSON.stringify(logs),
        hasCheckedPurview: hasCheckedPurview,
        hasPubviewPurview: hasPubviewPurview,
        checkflowResult: JSON.stringify(checkflowResult)
      });

    } else {
      ctx.body = 'unkonw type';
    }
  }

  // 获取详情
  async getSkuDetail() {
    const {
      ctx
    } = this;
    const { id: alias } = ctx.request.body;

    const regionList = await ctx.service.setting.getRegionList();

    let serviceList;
    let tradeList;
    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      tradeList = await ctx.service.catalog.getTradeList(paramsData);
    } else {

      serviceList = await ctx.service.catalog.getSercataList();

      tradeList = await ctx.service.catalog.getTradeList();

    }


    var checkflag = await ctx.service.admin.checkDataCanOperator('solution', parseInt(alias));
    if (!checkflag) {
      await ctx.render('error', {});
      return;
    }
    const detail = await ctx.service.sku.getEditInfo(alias);

    detail.detail.tradeCata.forEach(item => {
      tradeList.list.forEach(item2 => {
        if (item2.id == item.id) {
          item2.checked = true;
        }
      })
    });

    detail.detail.serviceCata.forEach(item => {
      serviceList.list.forEach(item2 => {
        if (item2.id == item.id) {
          item2.checked = true;
        }
      })
    });

    detail.detail.sContent.forEach(item => {
      item.show = true;
    });

    detail.detail.faq.forEach(item => {
      item.show = true;
    });

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }

      if (dataids.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (dataids.publish != 1) {
        hasPubviewPurview = 0;
      }
    }

    let checkflowResult = await ctx.service.checkflow.getDetail('sku_solution', alias);
    if (checkflowResult == null) {
      checkflowResult = {};
    }

    ctx.body = {
      success: true,
      data: {
        site_title: _info.site_title,
        ticMallHost: env[this.app.config.env].ticMallHost,
        page_title: '编辑解决方案',
        active: '3-2',
        csrf: ctx.csrf,
        region: regionList.list,
        serviceList: serviceList.list,
        tradeList: tradeList.list,
        alias: alias,
        form: detail.detail,
        sContent: detail.detail.sContent,
        faq: detail.detail.faq,
        regions: detail.detail.region,
        tradeCata: detail.detail.tradeCata,
        serviceCata: detail.detail.serviceCata,
        tag: detail.detail.tag,
        bigType: 'solution',
        view_url: env[this.app.config.env].view_url,
        mview_url: env[this.app.config.env].mview_url,
        userInfo: ctx.session.userInfo,
        hasCheckedPurview: hasCheckedPurview,
        hasPubviewPurview: hasPubviewPurview,
        checkflowResult: checkflowResult
      }
    }
  }

  async solutionEdit() {
    const {
      ctx
    } = this;

    const postData = ctx.request.body;
    postData.bigType = 'solution';

    var params = ctx.request.body;

    var tradearr = [];
    var servicearr = [];
    for (let item of params.tradeList) {
      tradearr.push(item.id);
    }

    for (let item of params.serviceList) {
      servicearr.push(item.id);
    }

    var trade_flag = await this.ctx.service.admin.checkUserTradeIsAllow(tradearr);
    var service_flag = await this.ctx.service.admin.checkUserServiceIsAllow(servicearr);
    if (!trade_flag || !service_flag) //检查提交过来的数据是否有权限
    {
      ctx.body = {
        fail: true,
        needReLogin: 1,
        loginUrl: '/login',
        msg: '请重新登录'
      };
      return;
    }


    const result = await ctx.service.sku.skuAdd(postData);

    if (result.id) {
      ctx.body = {
        success: true,
        data: result.id
      };
    } else {
      ctx.body = result;
    }
  }

  async solutionEdit2() {
    const {
      ctx
    } = this;

    const postData = ctx.request.body;

    var params = ctx.request.body;

    if (postData.id && parseInt(postData.id) > 0) {
      //检查老数据是否有权限
      var checkflag = await ctx.service.admin.checkDataCanOperator('news', postData.id);
      if (!checkflag) {
        ctx.body = {
          fail: true,
          needReLogin: 1,
          loginUrl: '/login',
          msg: '请重新登录'
        };
        return;
      }
    }

    var tradearr = [];
    var servicearr = [];
    for (let item of params.tradeList) {
      tradearr.push(item.id);
    }

    for (let item of params.serviceList) {
      servicearr.push(item.id);
    }

    var trade_flag = await this.ctx.service.admin.checkUserTradeIsAllow(tradearr);
    var service_flag = await this.ctx.service.admin.checkUserServiceIsAllow(servicearr);

    if (!trade_flag || !service_flag) //检查提交过来的数据是否有权限
    {
      ctx.body = {
        fail: true,
        needReLogin: 1,
        loginUrl: '/login',
        msg: '请重新登录'
      };
      return;
    }

    const result = await ctx.service.sku.skuEdit(postData);
    const cndParams = {
      fileUrls: [postData.pageLink]
    }
    if (result.success) {
      // 保存成功推送CDN刷新
      // await ctx.service.external.CDNRefresh(cndParams);
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async solutionDelete() {
    const {
      ctx
    } = this;

    const postData = ctx.request.body;

    var checkflag = await ctx.service.admin.checkDataCanOperator('solution', postData.id);
    if (!checkflag) {
      ctx.body = {
        fail: true,
        data: null
      };
      return;
    }
    const result = await ctx.service.sku.skuDelete(postData);
    const cndParams = {
      fileUrls: [postData.pageLink]
    }
    if (result.success) {
      // 保存成功推送CDN刷新
      // await ctx.service.external.CDNRefresh(cndParams);
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async solutionChangeValue() {
    const {
      ctx
    } = this;

    const postData = ctx.request.body;

    var checkflag = await ctx.service.admin.checkDataCanOperator('solution', postData.id);
    if (!checkflag) {
      ctx.body = {
        fail: true,
        data: null
      };
      return;
    }

    const result = await ctx.service.sku.skuUpdateValue(postData.id, postData.value, postData.type);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async getList2() {
    const {
      ctx
    } = this;

    //const value = ctx.request.body.value, types = ctx.request.body.type, page = ctx.request.body.page;
    const params = ctx.request.body;
    params.bigType = 'solution';

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      params.allowServiceList = dataids.ids;

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      params.allowTradeList = dataids.ids;
    }

    const list = await ctx.service.sku.getList(params);

    ctx.body = {
      success: true,
      data: list
    }
  }

  async saveSort() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;


    const result = await ctx.service.sku.setSort(params.data);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async dropAlias() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;

    const result = await ctx.service.sku.dropAlias(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

};

module.exports = SkuController;