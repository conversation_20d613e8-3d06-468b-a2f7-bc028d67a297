'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const { env } = require('../../config/info').siteInfo;
const nodeExcel = require('excel-export');
const fs = require('fs'),
  path = require('path'),
  moment = require('moment');

class AnalysisController extends Controller {
  async keywordList() {
    const {
      ctx
    } = this;

    const result = await ctx.service.analysis.keywordList({
      page: 1
    });

    await ctx.render('analysis_keyword', {
      site_title: _info.site_title,
      page_title: '关键词统计',
      active: '10-2',
      list: JSON.stringify(result.list),
      total: result.total,
      csrf: ctx.csrf,
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
    });
  }

  async getKeywordList() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const result = await ctx.service.analysis.keywordList(params);

    ctx.body = {
      success: true,
      data: {
        list: result.list,
        total: result.total
      }
    };
  }

  async entryList() {
    const {
      ctx
    } = this;

    const result = await ctx.service.analysis.entryList({
      page: 1
    });

    await ctx.render('analysis_entry', {
      site_title: _info.site_title,
      page_title: '页面来源分析',
      active: '10-1',
      list: JSON.stringify(result.list),
      total: result.total,
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
    });
  }

  async getEntryList() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const result = await ctx.service.analysis.entryList(params);

    ctx.body = {
      success: true,
      data: {
        list: result.list,
        total: result.total
      }
    };
  }

  async entryListFrom() {
    const {
      ctx
    } = this;
    const params = ctx.request.query;
    const url = params.url;
    const result = await ctx.service.analysis.entryListFrom({
      page: 1,
      url: url
    });

    await ctx.render('analysis_entry_detail', {
      site_title: _info.site_title,
      page_title: '页面来源分析',
      active: '10-1',
      list: JSON.stringify(result.list),
      total: result.total,
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      url: url,
      userInfo: ctx.session.userInfo,
    });
  }

  async getEntryListFrom() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const result = await ctx.service.analysis.entryListFrom(params);

    ctx.body = {
      success: true,
      data: {
        list: result.list,
        total: result.total
      }
    };
  }

  async entryExport() {
    const {
      ctx,
      app
    } = this;
    const params = ctx.request.body;
    params.getype = 'export';
    const list = await ctx.service.analysis.entryListExport(params);

    const conf = {};
    conf.cols = [{
      caption: '访问页面',
      type: 'string',
      width: 500
    },
    {
      caption: '站内站外',
      type: 'string',
      width: 100

    },
    // {
    //     caption: '来源',
    //     type: 'string',
    //     width: 500
    // }, 
    {
      caption: 'IP',
      type: 'string',
      width: 500
    },
    {
      caption: '错误码',
      type: 'string',
      width: 100
    }, {
      caption: '访问时间',
      type: 'string',
      width: 200
    }
    ];

    let temp = [];
    for (let item of list.list) {
      var buffer = [item.url, item.source, item.ip || '', item.code || '', moment(item.gmt_create).format('YYYY/MM/DD HH:mm:ss')];
      temp.push(buffer);
    }
    conf.rows = temp;

    let result = nodeExcel.execute(conf);
    let uploadDir = path.join(app.baseDir, 'app/public/upload/excel');
    // let dtime = (new Date()).getTime();
    // let filename = '400点击分析';
    let filename = '';
    if (params.date) {
      let fdate = params.date;
      fdate[0] = moment(fdate[0]).format('YYMMDDHHmmss');
      fdate[1] = moment(fdate[1]).format('YYMMDDHHmmss');
      filename = fdate.join('-');
    } else {
      filename = moment(new Date()).format('YYMMDDHHmmss');
    }
    try {
      fs.mkdirSync(uploadDir);
    } catch (e) { }
    filename = '页面来源分析' + filename;
    let filePath = path.join(uploadDir, filename + ".xlsx"); //文件名
    const exp = fs.writeFileSync(filePath, result, 'binary');

    ctx.body = {
      success: true,
      data: '/static/upload/excel/' + filename + '.xlsx'
    };
  }

  async keywordExport() {
    const {
      ctx,
      app
    } = this;
    const params = ctx.request.body;
    params.getype = 'export';
    const list = await ctx.service.analysis.keywordListExport(params);

    const conf = {};
    conf.cols = [{
      caption: '关键词',
      type: 'string',
      width: 500
    },
    {
      caption: '次数',
      type: 'number',
      width: 100
    },
    {
      caption: '最后搜索时间',
      type: 'string',
      width: 500
    },
    {
      caption: '开始时间',
      type: 'string',
      width: 500
    },
    {
      caption: '结束时间',
      type: 'string',
      width: 500
    },

    ];

    let temp = [];
    let startTime = ''
    let endTime = ''
    if (list.time.length) {
      startTime = moment(list.time[0]).format('YYYY/MM/DD HH:mm:ss')
      endTime = moment(list.time[1]).format('YYYY/MM/DD HH:mm:ss')
    } else {
      startTime = moment(list.minDate).format('YYYY/MM/DD HH:mm:ss') || '2018/01/01/'
      endTime = moment(Date.now()).format('YYYY/MM/DD HH:mm:ss')
    }
    for (let item of list.list) {
      var buffer = [item.keyword, item.num, moment(item.gmt_create).format('YYYY/MM/DD HH:mm:ss'), startTime, endTime];
      temp.push(buffer);
    }
    conf.rows = temp;
    let result = nodeExcel.execute(conf);
    let uploadDir = path.join(app.baseDir, 'app/public/upload/excel');
    let filename = '';
    if (params.date) {
      let fdate = params.date;
      fdate[0] = moment(fdate[0]).format('YYMMDDHHmmss');
      fdate[1] = moment(fdate[1]).format('YYMMDDHHmmss');
      filename = fdate.join('-');
    } else {
      filename = moment(new Date()).format('YYMMDD_HHmmss');
    }
    try {
      fs.mkdirSync(uploadDir);
    } catch (e) { }
    filename = 'TIC CMS关键词统计_' + filename;
    let filePath = path.join(uploadDir, filename + ".xlsx"); //文件名
    const exp = fs.writeFileSync(filePath, result, 'binary');

    ctx.body = {
      success: true,
      data: '/static/upload/excel/' + filename + '.xlsx'
    };
  }

  // 资源下载统计页面
  async downloadList() {
    const {
      ctx
    } = this;

    const result = await ctx.service.analysis.downloadList({
      page: 1
    });

    await ctx.render('analysis_download', {
      site_title: _info.site_title,
      page_title: '资源下载统计',
      active: '10-4',
      list: JSON.stringify(result.list),
      total: result.total,
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
    });
  }
  // 资源下载统计列表
  async getDownloadList() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const result = await ctx.service.analysis.downloadList(params);

    ctx.body = {
      success: true,
      data: {
        list: result.list,
        total: result.total
      }
    };
  }
  // 导出
  async exportDownloadList() {
    const {
      ctx,
      app
    } = this;
    const params = ctx.request.body;
    params.limit = 10000000
    const list = await ctx.service.analysis.downloadList(params);
    const conf = {
      rows: []
    };
    list.list.forEach((v, i) => {
      conf.rows.push([v.id, moment(v.time).format('YYYY/MM/DD HH:mm:ss'), v.file_name, v.file_type, v.group, v.user_name, v.user_phone, v.user_email])
    })
    conf.cols = [{
      caption: '序号',
      type: 'number',
      width: 20
    },
    {
      caption: '下载时间',
      type: 'string',
      width: 100
    },
    {
      caption: '资料名称',
      type: 'string',
      width: 500
    },
    {
      caption: '资料类型',
      type: 'string',
      width: 500
    },
    {
      caption: '编辑分组',
      type: 'string',
      width: 500
    },
    {
      caption: '用户姓名',
      type: 'string',
      width: 500
    },
    {
      caption: '用户电话',
      type: 'string',
      width: 500
    },
    {
      caption: '用户E-mail',
      type: 'string',
      width: 500
    }
    ];
    let result = nodeExcel.execute(conf);
    let uploadDir = path.join(app.baseDir, 'app/public/upload/excel');
    let filename = moment(new Date()).format('YYMMDD_HHmmss');
    try {
      fs.mkdirSync(uploadDir);
    } catch (e) { }
    filename = 'TIC CMS下载统计_' + filename;
    let filePath = path.join(uploadDir, filename + ".xlsx"); //文件名
    const exp = fs.writeFileSync(filePath, result, 'binary');

    ctx.body = {
      success: true,
      data: '/static/upload/excel/' + filename + '.xlsx'
    };
  }
}

module.exports = AnalysisController;