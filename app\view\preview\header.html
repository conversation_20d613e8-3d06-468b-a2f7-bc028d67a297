<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="description" content="<% if(detail.seo_text){ %><%- detail.seo_text %><% } %>">
    <title> <%- detail.name || detail.title %> - SGS</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon"/>
    <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon"/>
    <link rel="stylesheet" href="/static/preview/css/swiper.min.css">
    <link rel="stylesheet" href="/static/preview/css/select2.min.css">
    <link rel="stylesheet" href="/static/preview/css/style.css">
    <script src="/static/preview/js/jquery.min.js"></script>
    <script src="/static/preview/js/js.cookie.min.js"></script>
    <script src="/static/preview/js/common.js"></script>
    <script src="/static/preview/js/indexcom.js"></script>
</head>
<body>
<!--
<div class="box">
    <div class="message">
        <div class="messageIcon"></div>
    </div>

    <div class="note">
        在线客服
    </div>
</div>
-->
<div class="headBox">
    <div class="head">
        <div class="sgs">SGS Online</div>
        <ul class="headUl">
            <li class="headLi first">
                <span class="headLi-first-icon"></span>
                <span class="headLi-first-word">购物车( 0 )</span>
            </li>
            <li class="headLi">我的订单</li>
            <li class="headLi">个人中心</li>
            <li class="headLi"></li>
            <li class="headLi">您好，欢迎来到 SGS 商城</li>
        </ul>
    </div>
</div>


<div class="nav detailNav">
    <div class="navBox">
        <div class="navImg">
            <img src="/static/preview/images/logo.png" alt="">
        </div>
        <ul class="navUl">
                <li class="navLi"><a href="/preview">首页</a></li>
                <li class="navLi s">
                    您的行业
                    <ul class="drag">
                        <div class="navBor">

                        </div>

                        <% if(tradeNavi && tradeNavi.length > 0){ %>
                        <% for (var item of tradeNavi){ %>
                        <li class="dragLi">

                            <a class="dragLi-word" href="/preview/industry/<%- item.alias %>/"><%- item.name %></a>
                        </li>
                        <% } %>
                        <% } %>
                        
                        <div class="dragAngel">

                        </div>
                    </ul>
                </li>
                <li class="navLi t">我们的服务
                    <ul class="drag">
                        <div class="navBor">

                        </div>
                        <% if(serviceNavi && serviceNavi.length > 0){ %>
                            <% for (var item of serviceNavi){ %>
                            <li class="dragLi">
    
                                <a class="dragLi-word" href="/preview/service/<%- item.alias %>/"><%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        <div class="dragAngel">

                        </div>
                    </ul>
                </li>
            </ul>

        <div class="navSearchBox">
            <form action="/preview/search" target="_blank">
                <a href="javascript:;" class="navSearchIcon">
                    <div  class="navSearchIconIcon"></div>
                </a>
                <input type="text" class="navSearch" id="keyWord" name="q" onkeyup="_search(event)">
            </form>

        </div>
    </div>
</div>

<script>
    $(function(){
        $('.navSearchIcon').on('click',function(){
            var v = $('#keyWord').val().trim();
            v = encodeURIComponent(v);

            if(!v || v == ''){
                return;
            }

            window.open('/preview/search?q='+v);
        })
    });

    function _search(e){
        e.preventDefault;
        var keycode = e.keyCode;
        if(keycode ==  13){
            $('.navSearchIcon').trigger('click');
        }
    }
</script>