var _hmt = _hmt || [];
var pathname = window.location.pathname
if (pathname !== '/industry/Industrial/') {
    (function () {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?f7e679727f4c1e2bcf3ee26c2940e8f3";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
    })();
}

window._pt_lt = new Date().getTime();
window._pt_sp_2 = [];
var acid = '1185934b';
_pt_sp_2.push('setAccount,' + acid);
var _protocol = (("https:" == document.location.protocol) ? " https://" : " http://");
(function () {
    var atag = document.createElement('script'); atag.type = 'text/javascript'; atag.async = true;
    atag.src = _protocol + 'js.ptengine.cn/' + acid + '.js';
    var s = document.getElementsByTagName('script')[0];
    s.parentNode.insertBefore(atag, s);
})();