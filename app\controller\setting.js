'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const { env } = require('../../config/info').siteInfo;
class SettingController extends Controller {
  async region() {
    const {
      ctx
    } = this;

    const regionList = await ctx.service.setting.getRegionList();
    const logs = await ctx.service.logs.listAll({
      model: '设置-目的国',
      page: 1,
      limit: 10
    });


    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;
    let hasEditPurview = 1;

    if (this.ctx.session.userInfo.type != 1) {

    }

    await ctx.render('region', {
      site_title: _info.site_title,
      page_title: '目的国配置',
      active: '12-1',
      list: JSON.stringify(regionList.list),
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
      hasCheckedPurview: 1,
      hasPubviewPurview: 1,
      hasEditPurview: 1
    });
  }

  async regionAdd() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const name = params.name,
      alias = params.alias;
    if (!name || !alias || name == '' || alias == '') {
      ctx.body = {
        fail: true,
        data: null
      };
    } else {
      const result = await ctx.service.setting.addRegion(name, alias);

      if (result) {
        ctx.body = {
          success: true,
          data: null
        };
      } else {
        ctx.body = {
          fail: true,
          data: null
        };
      }
    }


  }

  async regionInfo() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const id = params.id;

    const result = await ctx.service.setting.regionInfo(id);
    let info = result.info;

    if (info) {
      ctx.body = {
        success: true,
        data: info
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }

  }

  async regionEdit() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const id = params.id,
      name = params.name,
      alias = params.alias;

    const result = await ctx.service.setting.regionUpdate(id, name, alias);
    let success = result.success;

    if (success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async regionDelete() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const id = params.id;

    const result = await ctx.service.setting.regionDelete(id);
    let success = result.success;

    if (success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async citys() {
    const {
      ctx
    } = this;

    const citysList = await ctx.service.setting.getCitysList();
    const logs = await ctx.service.logs.listAll({
      model: '设置-城市',
      page: 1,
      limit: 10
    });

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;
    let hasEditPurview = 1;

    if (this.ctx.session.userInfo.type != 1) {

    }
    await ctx.render('citys', {
      site_title: _info.site_title,
      page_title: '城市配置',
      active: '12-2',
      list: JSON.stringify(citysList.list),
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
      hasCheckedPurview: 1,
      hasPubviewPurview: 1,
      hasEditPurview: 1
    });
  }

  async cityAdd() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const name = params.name
    if (!name || name == '') {
      ctx.body = {
        fail: true,
        data: null
      };
    } else {
      const result = await ctx.service.setting.addCity(name);

      if (result) {
        ctx.body = {
          success: true,
          data: null
        };
      } else {
        ctx.body = {
          fail: true,
          data: null
        };
      }
    }


  }

  async cityInfo() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const id = params.id;

    const result = await ctx.service.setting.cityInfo(id);
    let info = result.info;

    if (info) {
      ctx.body = {
        success: true,
        data: info
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }

  }

  async cityEdit() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const id = params.id,
      name = params.name;

    const result = await ctx.service.setting.cityUpdate(id, name);
    let success = result.success;

    if (success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async cityDelete() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const id = params.id;

    const result = await ctx.service.setting.cityDelete(id);
    let success = result.success;

    if (success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async tardeRelevance() {
    const {
      ctx
    } = this;

    const regionList = await ctx.service.setting.getRegionList();
    const logs = await ctx.service.logs.listAll({
      model: '设置-产品行业映射',
      page: 1,
      limit: 10
    });
    const serviceList = await ctx.service.external.getUserCatalogList({
      alias: 'service',
      account: ctx.session.userInfo.user_name || this.ctx.session.userInfo.name
    }, ctx.headers);
    const tradeList = await ctx.service.external.getUserCatalogList({
      alias: 'industry',
      account: ctx.session.userInfo.user_name || this.ctx.session.userInfo.name
    }, ctx.headers);
    const task = await ctx.service.external.tardeRelevanceQryTask({
      alias: 'service'
    }, ctx.headers);

    await ctx.render('tradeRelevance', {
      tasks: JSON.stringify(task.data.items),
      serviceList: JSON.stringify(serviceList),
      tradeList: JSON.stringify(tradeList),
      site_title: _info.site_title,
      page_title: '产品行业映射配置',
      active: '12-4',
      list: JSON.stringify(regionList.list),
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
      hasCheckedPurview: 1,
      hasPubviewPurview: 1,
      hasEditPurview: 1
    });
  }

  async tardeRelevanceQry() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.tardeRelevanceQry(params, ctx.headers);

    ctx.body = result;
  }

  async tardeRelevanceAdd() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.tardeRelevanceAdd(params, ctx.headers);

    ctx.body = result;
  }

  async tardeRelevanceMod() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.tardeRelevanceMod(params, ctx.headers);

    ctx.body = result;
  }

  async tardeRelevanceDel() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.tardeRelevanceDel(params, ctx.headers);

    ctx.body = result;
  }

  async tardeRelevanceSort() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.tardeRelevanceSort(params, ctx.headers);

    ctx.body = result;
  }

  async tardeRelevanceQryTask() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.tardeRelevanceQryTask(params, ctx.headers);

    ctx.body = result;
  }

  async tardeRelevanceSaveTask() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.tardeRelevanceSaveTask(params, ctx.headers);

    ctx.body = result;
  }

  async getQuoteAlias() {
    const {
      ctx
    } = this;
    const result = await ctx.service.external.getUserCatalogList({
      alias: 'service',
      account: ctx.session.userInfo.user_name || this.ctx.session.userInfo.name
    }, ctx.headers);

    ctx.body = result;
  }

  // bu manage page
  async buManage() {
    const {
      ctx
    } = this;
    // bu list qurry
    const result = await ctx.service.external.buManageQry({}, ctx.headers);
    const logs = await ctx.service.logs.listAll({
      model: '设置-bu',
      page: 1,
      limit: 10
    });

    await ctx.render('bu_manage', {
      list: JSON.stringify(result.data.items),
      site_title: _info.site_title,
      page_title: 'bu配置',
      active: '12-5',
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
      hasCheckedPurview: 1,
      hasPubviewPurview: 1,
      hasEditPurview: 1
    });
  }

  // bu manage query
  async buManageQry() {
    const {
      ctx
    } = this;
    const result = await ctx.service.external.buManageQry({}, ctx.headers);
    ctx.body = result
  }

  // bu manage addtion
  async buManageAdd() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    delete params.buId;
    const result = await ctx.service.external.buManageAdd(params, ctx.headers);
    ctx.body = result
  }

  // bu manage modify
  async buManageModify() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.buManageModify(params, ctx.headers);
    ctx.body = result
  }

  // bu manage delete
  async buManageDel() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.buManageDel(params, ctx.headers);
    ctx.body = result
  }

  /* IM工具 */
  async IMtool() {
    const {
      ctx
    } = this;
    const IMtool = await ctx.service.external.getIMtool({
      paraName: 'CHAT_SET'
    }, ctx.headers);
    const logs = await ctx.service.logs.listAll({
      model: '设置-IM',
      page: 1,
      limit: 10
    });

    await ctx.render('im_tool', {
      IMlist: JSON.stringify(IMtool),
      site_title: _info.site_title,
      page_title: 'IM工具',
      active: '12-6',
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
      hasCheckedPurview: 1,
      hasPubviewPurview: 1,
      hasEditPurview: 1
    });
  }
  async IMtoolQry() {
    const {
      ctx
    } = this;
    // const IMtool = await ctx.service.external.getIMtool({
    //   paraName: 'CHAT_SET'
    // }, ctx.headers);
    const params = ctx.request.body;
    const result = await ctx.service.external.getIMtool(params, ctx.headers);
    ctx.body = result
  }
  

  async IMtoolModify() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.IMtoolModify(params, ctx.headers);
    ctx.body = result
  }
};

module.exports = SettingController;