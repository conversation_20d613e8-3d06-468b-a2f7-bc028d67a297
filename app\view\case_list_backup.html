<% include header.html %>
    <el-container id="Main" class="caseList">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        资讯中心
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        <% if(type=='draft' ){ %>草稿箱<% }else{ %>资讯列表<% }%>
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row v-loading="loading">
                            <el-col :span="24">
                                <el-form :inline="true" :model="form">
                                    <el-form-item label="服务">
                                        <el-select v-model="form.service" placeholder="请选择"
                                        clearable>
                                            <el-option v-for="item in serviceList" :key="item.id" :label="item.name"
                                                :value="item.id">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="行业">
                                        <el-select v-model="form.trade" placeholder="请选择" clearable>
                                            <el-option v-for="item in tradeList" :key="item.id" :label="item.name"
                                                :value="item.id">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="类型">
                                        <el-select v-model="form.type" placeholder="请选择" clearable>
                                            <el-option v-for="item in types" :key="item.id" :label="item.name"
                                                :value="item.id">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="BU">
                                        <el-select placeholder="请选择" v-model="form.bu_id" clearable>
                                            <el-option v-for="item of buList" :key="item.buId" :label="item.buName"
                                                :value="item.buId">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="创建时间">
                                        <el-date-picker style="width: 240px;"
                                            v-model="form.create_time" value-format="yyyy-MM-dd" type="daterange"
                                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
                                        </el-date-picker>
                                    </el-form-item>
                                    <el-form-item label="统计时间">
                                        <el-date-picker style="width: 240px;" v-model="form.qryDate"
                                            value-format="yyyy-MM-dd" type="daterange" range-separator="至"
                                            start-placeholder="开始日期" end-placeholder="结束日期">
                                        </el-date-picker>
                                        
                                    </el-form-item>
                                    <el-form-item label="标题">
                                        <el-input v-model="form.title" placeholder="请输入标题"
                                            ></el-input>
                                    </el-form-item>
                                </el-form>
                            </el-col>
                            <el-col :span="12" style="text-align:left; padding-bottom: 10px;">
                                <el-button v-if="hasEditPurview==1" @click='_topMulti' :disabled="check">
                                    批量置顶
                                </el-button>
                                <el-button v-if="hasEditPurview==1" @click='_hotMulti' :disabled="check">
                                    批量推荐
                                </el-button>
                                <a href="/case/add/backup" v-if="hasEditPurview==1" style="color:#FFF;"
                                    class="el-button el-button--primary">添加</a>
                            </el-col>
                            <el-col :span="12" style="text-align:right; padding-bottom: 10px;">
                                <el-button type="primary" @click="_getList">搜索</el-button>
                                <el-button @click='handleExport' type="primary">导出</el-button>
                            </el-col>
                            <el-table :data="tableList.items" stripe style="width: 100%" @selection-change="change">
                                <el-table-column type="selection" width="50"
                                    :selectable='checkboxInit'></el-table-column>
                                <el-table-column prop="title" label="标题" width="200" :show-overflow-tooltip="true">
                                    <template slot-scope="scope">
                                        <div class="name">
                                            <i class="el-icon-edit" @click='handlerTitle(scope.row)'
                                                style="cursor: pointer;"></i>
                                            <a :href="'<%- view_url %>/case/' + scope.row.alias + '/detail-'+scope.row.id+'.html'"
                                                target="_blank">
                                                {{scope.row.title}}
                                            </a>
                                            <span v-if='scope.row.is_copy'>复</span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="buName" label="BU" width="80">
                                </el-table-column>
                                <el-table-column prop="name" label="类型">
                                </el-table-column>
                                <el-table-column prop="service" label="关联服务" width="120">
                                    <!-- <template slot-scope="scope">
                                        <el-tooltip placement="top" effect="light" popper-class="spopper">
                                            <div slot="content">
                                                <h3 style="margin:0;padding-bottom:10px">所属服务</h3>
                                                <el-col :span="24" v-for="(item, index) in scope.row.serviceList"
                                                    :key='index' style="padding:5px 0">
                                                    {{item}}</el-col>
                                            </div>
                                            <span
                                                style="overflow:hidden;white-space: nowrap;text-overflow: ellipsis;">{{scope.row.serviceList[0]}}</span>
                                        </el-tooltip>
                                    </template> -->
                                </el-table-column>
                                <el-table-column prop="industry" label="关联行业" width="120">
                                    <!-- <template slot-scope="scope">
                                        <el-tooltip placement="top" effect="light" popper-class="spopper">
                                            <div slot="content">
                                                <h3 style="margin:0;padding-bottom:10px">关联行业</h3>
                                                <el-col :span="24" v-for="(item, index) in scope.row.tradeList"
                                                    :key='index' style="padding:5px 0">
                                                    {{item}}</el-col>
                                            </div>
                                            <span>{{scope.row.tradeList[0]}}</span>
                                        </el-tooltip>
                                    </template> -->
                                </el-table-column>
                                <el-table-column label="审批人" prop="auditCode">
                                   
                                </el-table-column>
                                <el-table-column label="审批状态">
                                  <template slot-scope="scope">
                                    <span v-if="scope.row.auditStatus == ''"></span>
                                    <span v-else-if="scope.row.auditStatus==0">审批中</span>
                                    <span v-else-if="scope.row.auditStatus==10">已发布</span>
                                    <span v-else="scope.row.auditStatus==20">已退回</span>
                                  </template>
                              </el-table-column>
                              <el-table-column label="审批时间" prop="auditDate" >
                       
                            </el-table-column>
                                <el-table-column prop="gmtCreate" label="创建时间" sortable></el-table-column>
                                <el-table-column prop="gmtPublishTime" label="发布时间" sortable></el-table-column>
                                <el-table-column label="UV" prop="uv"></el-table-column>
                                <el-table-column label="PV" prop="pv"></el-table-column>
                                <el-table-column label="会员权限">
                                    <template slot-scope="scope">
                                        <span>{{scope.row.isPublic ? '是' : '否'}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="用户注册数量" width="120" prop="regNum"></el-table-column>
                                <el-table-column label="分类置顶" width="80" prop="isTop">
                                    <template slot-scope="scope">
                                        <el-switch v-model="scope.row.isTop" :active-value=1 :inactive-value=0
                                            @change='handlerChangeTop(scope.row)'>
                                        </el-switch>
                                    </template>
                                </el-table-column>
                                <el-table-column label="首页推荐" width="80" prop="isHot">
                                    <template slot-scope="scope">
                                        <el-switch v-model="scope.row.isHot" :active-value=1 :inactive-value=0
                                            @change='handlerChangeHot(scope.row)'>
                                        </el-switch>
                                    </template>
                                </el-table-column>
                                <el-table-column fixed="right" label="操作" width="160">
                                    <template slot-scope="scope">
                                        <div v-if="scope.row.is_publish">
                                            <el-button-group>
                                                <a v-if="hasEditPurview==1" :href="'/case/edit/backup/'+scope.row.id"
                                                    style="color:#FFF;"
                                                    class="el-button el-button--primary el-button--mini">编辑</a>
                                                <el-button v-if="hasEditPurview==1" type="danger" size="mini"
                                                    @click='_del(scope.row.id,scope.row.title)'>删除</el-button>
                                            </el-button-group>
                                        </div>
                                        <div v-else>
                                            <div v-if="scope.row.tran_result==null">
                                                <el-button-group style="margin-bottom:10px;">
                                                    <el-button v-if="hasEditPurview==1 && hasPubviewPurview==0"
                                                        type="danger" size="mini"
                                                        @click='_sendcheckDialog(scope.row)'>送审</el-button>
                                                    </span>

                                                    <el-button-group>
                                                        <a v-if="hasEditPurview==1" :href="'/case/edit/backup/'+scope.row.id"
                                                            style="color:#FFF;"
                                                            class="el-button el-button--primary el-button--mini">编辑</a>
                                                        <el-button v-if="hasEditPurview==1" type="danger" size="mini"
                                                            @click='_del(scope.row.id,scope.row.title)'>删除</el-button>
                                                    </el-button-group>
                                            </div>
                                            <div v-else-if="scope.row.tran_result.tran_status==0">
                                                <el-button-group>
                                                    <a v-if="hasCheckedPurview==1" :href="'/case/edit/backup/'+scope.row.id"
                                                        style="color:#FFF;"
                                                        class="el-button el-button--primary el-button--mini">编辑</a>
                                                    <el-button type="danger" size="mini">送审中</el-button>
                                                </el-button-group>
                                            </div>

                                            <div v-else-if="scope.row.tran_result.tran_status==10">
                                                <el-button-group>
                                                    <a v-if="hasEditPurview==1" :href="'/case/edit/backup/'+scope.row.id"
                                                        style="color:#FFF;"
                                                        class="el-button el-button--primary el-button--mini">编辑</a>
                                                    <el-button v-if="hasEditPurview==1" type="danger" size="mini"
                                                        @click='_del(scope.row.id,scope.row.title)'>删除</el-button>
                                                </el-button-group>
                                            </div>

                                            <div v-else>

                                                <el-button-group style="margin-bottom:10px;">
                                                    <el-button v-if="hasEditPurview==1 && hasPubviewPurview==0"
                                                        type="danger" size="mini"
                                                        @click='_sendcheckDialog(scope.row)'>送审</el-button>
                                                    </span>

                                                    <el-button-group>
                                                        <a v-if="hasEditPurview==1" :href="'/case/edit/backup/'+scope.row.id"
                                                            style="color:#FFF;"
                                                            class="el-button el-button--primary el-button--mini">编辑</a>
                                                        <el-button v-if="hasEditPurview==1" type="danger" size="mini"
                                                            @click='_del(scope.row.id,scope.row.title)'>删除</el-button>
                                                    </el-button-group>
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <el-pagination @current-change="handleCurrentChange" background
                                :current-page.sync="form.page" :page-size="10"
                                layout="total, prev, pager, next" :total="tableList.totalNum"
                                style="padding:10px 0;text-align: right;">
                            </el-pagination>
                        </el-row>
                        <el-dialog title="送审提示" :visible.sync="dialogVisible" width="30%">
                            <div>您正在送审</div>
                            <h3 style="text-indent: 20px;">{{currentNewsTitle}}</h3>
                            <div>请选择审批用户分组：</div>
                            <el-select v-model="selectGroup" placeholder="请选择">
                                <el-option v-for="item in checkGroup" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>

                            <span slot="footer" class="dialog-footer">
                                <el-button @click="dialogVisible = false">取 消</el-button>
                                <el-button type="primary" @click="_sendcheck()">确 定</el-button>
                            </span>
                        </el-dialog>
                        <el-dialog title="编辑标题" :visible.sync="editTitle.dialog" width="30%">
                            <el-input type="textarea" :rows="2" placeholder="请输入标题" v-model="editTitle.data.title">
                            </el-input>
                            <span slot="footer" class="dialog-footer">
                                <el-button @click="editTitle.dialog = false">取 消</el-button>
                                <el-button type="primary" @click="saveTitle">确 定</el-button>
                            </span>
                        </el-dialog>
                    </el-main>
            </el-container>
    </el-container>

    <script>
        var main = new Vue({
            el: '#Main',
            data: {
                userInfo: '',
                type: '<%- type %>',
                tableList: {
                  items: [],
                  totalNum: 0,
                },
                form: {
                    type: '',
                    service: '',
                    trade: '',
                    title: '',
                    page: 1,
                    create_time: '',
                    qryDate: [],
                    is_publish: <% if(type == 'draft'){ %> 0 <% }else { %> 1 <% }%>,
                        bu_id: ''
            },
        types: <%- caseType %>,
            tradeList: <%- tradeList %>,
                serviceList: <%- serviceList %>,
                    loading: false,
                        check: true,
                            checkIds: [],
                                checkGroup: [],
                                    selectGroup: null,
                                        currentNewsTitle: '',
                                            currentId: 0,
                                                loading: false,
                                                    check: true,
                                                        checkIds: [],
                                                            dialogVisible: false,
                                                                hasCheckedPurview: <%- hasCheckedPurview %>,
                                                                    hasPubviewPurview: <%- hasPubviewPurview %>,
                                                                        hasEditPurview: <%- hasEditPurview %>,
                                                                            editTitle: {
            dialog: false,
                data: { }
        },
        buList: [],
        buIds: <%- buIds || '' %>
        },
        methods: {
            handlerChangeHot(row) {
                this.editCase(row)
            },
            handlerChangeTop(row) {
                this.editCase(row)
            },
            editCase(params) {
                var param = {
                    id: params.id,
                    is_top: params.isTop,
                    is_hot: params.isHot,
                    title: params.title
                }
                axios.post('/case/modify', param)
                    .then(result => {
                        if (result.data.success) {
                            this.$message.success('修改成功');
                            this.editTitle.dialog = false;
                        } else {
                            this.$message.error(result.data.msg);
                        }
                    });
            },
            handlerChangeCreateTime(val) {
                this.getList();
            },
            handlerTitle(row) {
                this.editTitle.dialog = true
                this.editTitle.data = row
            },
            saveTitle() {
                this.editCase(this.editTitle.data)
            },
            checkboxInit(row, index) {
                //如果正在审批中不能删除
                return (row.tran_result && row.tran_result.tran_status == 0) ? 0 : 1;
            },
            _sendcheck(row) {
                var that = this;
                that.loading = true;
                var objData = { data_type: 'case', data_id: that.currentId, data_name: that.currentNewsTitle, receive_groupid: that.selectGroup };

                if (!this.selectGroup) {
                    that.loading = false;
                    this.$message.error('请选择审批用户分组');
                    return;
                }
                axios.post('/checkflow/apply', objData)
                    .then(function (result) {
                        if (result.data.success) {
                            that.loading = false;
                            that.dialogVisible = false;
                            if (result.data.success) {
                                that.$message.success('送审成功');
                                that.getList();
                            } else {
                                if (result.data.needReLogin == 1) {
                                    that.$alert(result.data.msg, '提示', {
                                        confirmButtonText: '确定', callback: action => {
                                            window.location.href = result.data.loginUrl;
                                        }
                                    });
                                }
                                else {
                                    that.$alert(result.data.msg, '提示', {
                                        confirmButtonText: '确定', callback: action => {
                                            window.location.reload();
                                        }
                                    });
                                }
                            }
                        }
                    });
            },
            _sendcheckDialog(row) {
                var that = this;
                that.loading = true;
                that.currentId = 0;
                that.selectGroup = null;

                axios.post('/role/getRoleList', { type: 'case', id: row.id })
                    .then(function (result) {
                        if (result.data.success) {
                            that.loading = false;
                            that.dialogVisible = true;
                            that.currentId = row.id;
                            that.currentNewsTitle = row.title;
                            that.checkGroup = result.data.data;
                        }
                        else {
                            that.$message.error('加载错误');
                        }
                    });
            },
            handleCurrentChange(r) {
                this.form.page = r;
                this.getList();
            },
            getList(){
              console.log(this.form)
                const params = {
                  title: this.form.title,
                  catalogIds: [],
                  gmtCreate: '',
                  catalogId: this.form.type,
                  buId: this.form.bu_id,
                  qryDate:  '',
                  buIds: this.buIds,
                  pageNum: this.form.page,
                  pageRow: 10,
                  isPublish: <% if (type == 'draft') { %> 0 <% } else { %> 1 <% }%>
              }
              if (this.form.qryDate) params.qryDate = this.form.qryDate.length ? `${this.form.qryDate[0]} 00:00:00/${this.form.qryDate[1]} 23:59:59` : ''
              if (this.form.create_time) params.gmtCreate = this.form.create_time.length ? `${this.form.create_time[0]} 00:00:00/${this.form.create_time[1]} 23:59:59` : ''
              if (this.form.service)  params.catalogIds.push(this.form.service)
                if (this.form.trade) params.catalogIds.push(this.form.trade)
              const that = this
              axios.post('/case/list/qry', params)
                  .then(function (result) {
                      
                      that.loading = false;
                      that.tableList = result.data.data;
                  });
            },
            _del(id, name, alias) {
                var that = this;
                this.$confirm('是否删除' + name + '？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    that.loading = true;
                    const pageLink = '<%- view_url %>' + '/case/'+ alias +'/detail-'+ id +'.html'
                    axios.post('/case/delete', { id: id, pageLink,  _csrf: '<%- csrf %>' })
                        .then(function (result) {
                            if (result.data.success) {
                                that._default();
                                window.location.reload();
                            }
                            else {
                                if (result.data.needReLogin == 1) {
                                    that.$alert(result.data.msg, '提示', {
                                        confirmButtonText: '确定', callback: action => {
                                            window.location.href = result.data.loginUrl;
                                        }
                                    });
                                }
                                else {
                                    that.$alert(result.data.msg, '提示', {
                                        confirmButtonText: '确定', callback: action => {
                                            window.location.reload();
                                        }
                                    });
                                }
                            }
                        });
                }).catch(e => {
                    return e;
                });
            },
            change(val){
                var that = this;
                that.checkIds = [];
                val.forEach(function (item, i) {
                    that.checkIds.push(item.id);
                });

                if (that.checkIds.length == 0) {
                    that.check = true;
                } else {
                    that.check = false;
                }
            },
            _delMulti(){
                var that = this;
                this.$confirm('是否删除已选择的咨询？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    that.loading = true;
                    axios.post('/case/delete', { id: that.checkIds, _csrf: '<%- csrf %>' })
                        .then(function (result) {
                            if (result.data.success) {
                                that._default();
                                window.location.reload();
                            }
                            else {
                                if (result.data.needReLogin == 1) {
                                    that.$alert(result.data.msg, '提示', {
                                        confirmButtonText: '确定', callback: action => {
                                            window.location.href = result.data.loginUrl;
                                        }
                                    });
                                }
                                else {
                                    that.$alert(result.data.msg, '提示', {
                                        confirmButtonText: '确定', callback: action => {
                                            window.location.reload();
                                        }
                                    });
                                }
                            }
                        });
                }).catch(e => {
                    return e;
                });
            },
            _hotMulti(){
                var that = this;
                this.$confirm('是否首页展示已选择的咨询？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    that.loading = true;
                    axios.post('/case/modify/multiple', {
                        id: that.checkIds,
                        column: 'is_hot',
                        _csrf: '<%- csrf %>'
                    })
                        .then(function (result) {
                            if (result.data.success) {
                                that.$message.success('修改成功')
                                window.location.reload();
                            } else {
                                that.$message.error(result.data.msg)
                            }
                        });
                }).catch(e => {
                    return e;
                });
            },
            _topMulti(){
                var that = this;
                this.$confirm('是否置顶已选择的咨询？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    that.loading = true;
                    axios.post('/case/modify/multiple', {
                        id: that.checkIds,
                        column: 'is_top',
                        _csrf: '<%- csrf %>'
                    })
                        .then(function (result) {
                            if (result.data.success) {
                                that.$message.success('修改成功')
                                window.location.reload();
                            } else {
                                that.$message.error(result.data.msg)
                            }
                        });
                }).catch(e => {
                    return e;
                });
            },
            _default() {
                this.form.id = 0;
                this.form.name = '';
                this.form.alias = '';
            },
            setPublish(id){
                var that = this;
                that.loading = true;
                axios.post('/case/updatePublish', {
                    id: id, is_publish: <% if(type == 'draft'){ %> 1 <% }else { %> 0 <% }%>, _csrf: '<%- csrf %>'
            }).then(function (result) {
                if (result.data.success) {
                    that.getList();
                }
            });
        },
        _getList(type){
            this.getList();
        },
        // 获取bu列表
        qryBuList() {
            var that = this;
            axios.post('/setting/buManage/qry', {}).
                then(function (result) {
                    if (result.data.resultCode === '0') {
                        that.buList = result.data.data.items
                    } else {
                        that.$message.error(result.data.resultMsg);
                    }
                });
        },
        // 导出数据excle
        handleExport() {
            console.log(this.form)
            const params = {
                title: this.form.title,
                catalogIds: [],
                // gmtPublishTime: this.form.publish_time,
                gmtCreate: '',
                catalogId: this.form.type,
                buId: this.form.bu_id,
                qryDate:  '',
                buIds: this.buIds,
                isPublish: <% if (type == 'draft') { %> 0 <% } else { %> 1 <% }%>
            }
            if (this.form.qryDate) params.qryDate = this.form.qryDate.length ? `${this.form.qryDate[0]} 00:00:00/${this.form.qryDate[1]} 23:59:59` : ''
            if (this.form.create_time) params.gmtCreate = this.form.create_time.length ? `${this.form.create_time[0]} 00:00:00/${this.form.create_time[1]} 23:59:59` : ''
            if (this.form.service)  params.catalogIds.push(this.form.service)
                if (this.form.trade) params.catalogIds.push(this.form.trade)
            axios.post('/export/excle', params).then(res1 => {
                axios({
                    method: 'post',
                    url: res1.data.host + '/ticMall/business/api.v1.mgmt/cases/exp',
                    data: params,
                    headers: {
                        'Content-Type': 'application/json',
                        pid: res1.data.pid,
                        pcode: res1.data.pcode,
                        timestamp: res1.data.timestamp,
                        sign: res1.data.sign,
                        frontUrl: window.location.origin,
                    },
                    responseType: "blob",
                }).then(res => {
                    let data = res.data;
                    let filename = ''
                    //从response的headers中获取filename, 后端response.setHeader("Content-disposition", "attachment; filename=xxxx.docx") 设置的文件名;
                    var contentDisposition = res.headers["content-disposition"];
                    if (contentDisposition) {
                        var patt = new RegExp("filename=([^;]+\\.[^\\.;]+);*");
                        var result = patt.exec(contentDisposition);
                        filename = result ? decodeURIComponent(result[1]) : filename;
                    }
                    var blob = new Blob([res.data], {
                        type: "application/actet-stream;charset=utf-8",
                    });
                    var downloadElement = document.createElement("a");
                    var href = window.URL.createObjectURL(blob); //创建下载的链接
                    downloadElement.style.display = "none";
                    downloadElement.href = href;
                    downloadElement.download = decodeURI(filename).replace(
                        /\+/g,
                        " "
                    );
                    document.body.appendChild(downloadElement);
                    downloadElement.click();
                    setTimeout(() => {
                        document.body.removeChild(downloadElement);
                        window.URL.revokeObjectURL(href);
                    }, 1000);
                })
            })

        }
        },
        mounted(){
            this.$nextTick(function () {
                //this.types.unshift({id:0,name:'全部'});
                document.getElementById('preLoading').style.display = 'none';
                this.qryBuList()
                this.getList()
            });
        }
    });
    </script>

    <style>
        .name {
            position: relative;
        }

        .name span {
            position: absolute;
            right: -10px;
            top: 0;
            color: #fff;
            font-size: 12px;
            background: #F56C6C;
            display: block;
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            border-radius: 50%;
            font-style: normal;
        }
    </style>
    <% include footer.html %>