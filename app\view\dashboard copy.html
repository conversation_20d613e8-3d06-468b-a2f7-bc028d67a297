<% include header.html %>

<el-container id="Main">
    <% include eheader.html %>
    <el-container>
        <% include eside.html %>

        <el-main>
            <el-breadcrumb separator-class="el-icon-arrow-right">
                <el-breadcrumb-item>
                    <a href='/'>首页</a>
                </el-breadcrumb-item>
                <el-breadcrumb-item>
                    工作台
                </el-breadcrumb-item>
            </el-breadcrumb>
            
            <hr>
            <el-row style="margin-top:20px">
               
              
				
				<% if(userInfo.type==2){ %>
				<el-col :span="24">
				<el-tabs v-model="activeName" >
					<% if(hasCheckedPurview==1){ %>
					<el-tab-pane label="审批列表Tab页" name="first">
						 <el-form :inline="true" :model="form">
							<el-row>
						  
						    <el-form-item label="状态">
						    <el-select v-model="checkedList.tran_status" placeholder="请选择"  clearable>
						        <el-option
						            v-for="item in checkedList.tranStatusList"
						            :key="item.id"
						            :label="item.name"
						            :value="item.id">
						        </el-option>
						    </el-select>
						  	</el-form-item>
														
							  <el-form-item label="送审分组">
							    <el-select v-model="checkedList.send_admin_groupid" placeholder="请选择"  clearable>
							        <el-option
							            v-for="item in checkedList.groupList"
							            :key="item.id"
							            :label="item.name"
							            :value="item.id">
							        </el-option>
							    </el-select>
								</el-form-item>
							
							 <el-form-item label="类型">
							    <el-select v-model="checkedList.data_type" placeholder="请选择"  clearable>
							        <el-option
							            v-for="item in checkedList.typeList"
							            :key="item.id"
							            :label="item.name"
							            :value="item.id">
							        </el-option>
							    </el-select>
							</el-form-item>
							
							 <el-form-item label="服务">
							    <el-select v-model="checkedList.service" placeholder="请选择"  clearable>
							        <el-option
							            v-for="item in checkedList.serviceList"
							            :key="item.id"
							            :label="item.name"
							            :value="item.id">
							        </el-option>
							    </el-select>
							</el-form-item>
							
							 <el-form-item label="行业">
							    <el-select v-model="checkedList.trade" placeholder="请选择"   clearable>
							        <el-option
							            v-for="item in checkedList.tradeList"
							            :key="item.id"
							            :label="item.name"
							            :value="item.id">
							        </el-option>
							    </el-select>
							</el-form-item>
							</el-row>
							<el-row>
							 <el-col :span="24">
								  <el-form-item label="名称">
									<el-input v-model="checkedList.data_name" placeholder="请输入名称" style="width:14em"></el-input>
								  
								  </el-form-item>
								  
								   <el-form-item label="送审时间">
										 <el-date-picker value-format="yyyy-MM-dd" format="yyyy-MM-dd" v-model="checkedList.send_admin_time" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
										</el-date-picker>
										<el-button type="primary" @click="_searchCheckedList">搜索</el-button>
										 <el-button type="primary" @click="_defaultChecked">重置</el-button>
								  </el-form-item>
								 </el-col> 
							</el-row>
							
							<el-row>
							<el-col :span="24">
								待审批数量：<span style="color:blue">{{checkedList.waitChecked}}</span>
								</el-col> 
								</el-row>
						</el-form>
						
						<el-table :data="checkedList.list" stripe style="width: 100%" @selection-change="checkedChange">
						    <el-table-column type="selection" width="50"></el-table-column>
						    <el-table-column prop="data_name" label="名称">
								
						    </el-table-column>
							
							<el-table-column prop="data_type" label="类型">
								
							</el-table-column>
						
							<el-table-column prop="" label="链接地址">
								   <template slot-scope="scope">
								    <a :href="scope.row.data_link" target="_blank">链接</a>
								</template>
							</el-table-column>
							
							<el-table-column label="所属服务" width="120">
							    <template slot-scope="scope">
							        <el-tooltip placement="top" effect="light" popper-class="spopper">
							            <div slot="content">
							                <h3 style="margin:0;padding-bottom:10px">所属服务</h3>
							                <el-col :span="24" v-for="(item, index) in scope.row.serviceList" style="padding:5px 0" :key='index'>{{item}}</el-col>
							            </div>
							            <span v-if="scope.row.serviceList && scope.row.serviceList.length>0" style="overflow:hidden;white-space: nowrap;text-overflow: ellipsis;">{{scope.row.serviceList[0]}}</span>
							        </el-tooltip>
							    </template>
							</el-table-column>
							<el-table-column label="关联行业" width="120">
							    <template slot-scope="scope">
							        <el-tooltip placement="top" effect="light" popper-class="spopper">
							            <div slot="content">
							                <h3 style="margin:0;padding-bottom:10px">关联行业</h3>
							                <el-col :span="24" v-for="(item, index) in scope.row.tradeList" :key='index' style="padding:5px 0">{{item}}</el-col>
							            </div>
							            <span v-if="scope.row.tradeList && scope.row.tradeList.length>0" >{{scope.row.tradeList[0]}}</span>
							        </el-tooltip>
							    </template>
							</el-table-column>
							
							<el-table-column prop="tran_status" label="状态">
								
							</el-table-column>
				
							
							 <el-table-column prop="send_admin_name" label="送审用户">
							</el-table-column>
							
							 <el-table-column label="送审分组">
								 <template slot-scope="scope">
									    <div v-for="(value, key, index) in scope.row.roleList" :key='key'>
												{{value.name}}
										</div>
								 </template>
							</el-table-column>
							
							 <el-table-column prop="send_admin_time" label="送审时间">
						    </el-table-column>
							
							<el-table-column prop="tran_admin_time" label="审批时间">
							</el-table-column>
							
							 <el-table-column fixed="right" label="操作" width="160" fixed="right">
						        <template slot-scope="scope">
						            <el-button-group>
										
										 <el-button v-if="scope.row.tran_status=='审批中'" type="primary" size="mini" @click='_checkOK(scope.row.id,scope.row.data_name,scope.row.data_type,scope.row.data_id)'>发布</el-button>
						                <el-button v-if="scope.row.tran_status=='审批中'"  type="danger" size="mini" @click='_checkFailedUI(scope.row.id,scope.row.data_name, scope.row)'>退回</el-button>
						            </el-button-group>
						        </template>
						    </el-table-column>
							
						</el-table>
					
						<el-pagination @current-change="checkedHandleCurrentChange" background :current-page.sync="checkedList.page" :page-size="checkedList.limit"
						    layout="total, prev, pager, next" :total="checkedList.total" style="padding:10px 0;text-align: right;">
						</el-pagination>
						
					</el-tab-pane>
					<% } %>
					<% if(hasEditChecked==1){ %>
					<el-tab-pane label="送审列表Tab页" name="second">
							 <el-form :inline="true" :model="form">
							<el-row>
						  
						     <el-form-item label="状态">
						  <el-select v-model="sendCheckedList.tran_status" placeholder="请选择"  clearable>
						      <el-option
						          v-for="item in sendCheckedList.tranStatusList"
						          :key="item.id"
						          :label="item.name"
						          :value="item.id">
						      </el-option>
						  </el-select>
						  </el-form-item>
						  
							  <el-form-item label="审批分组">
							    <el-select v-model="sendCheckedList.receive_groupid" placeholder="请选择"  clearable>
							        <el-option
							            v-for="item in sendCheckedList.groupList"
							            :key="item.id"
							            :label="item.name"
							            :value="item.id">
							        </el-option>
							    </el-select>
								</el-form-item>
							
							 <el-form-item label="类型">
							    <el-select v-model="sendCheckedList.data_type" placeholder="请选择"  clearable>
							        <el-option
							            v-for="item in sendCheckedList.typeList"
							            :key="item.id"
							            :label="item.name"
							            :value="item.id">
							        </el-option>
							    </el-select>
							</el-form-item>
							
							 <el-form-item label="服务">
							    <el-select v-model="sendCheckedList.service" placeholder="请选择"  clearable>
							        <el-option
							            v-for="item in sendCheckedList.serviceList"
							            :key="item.id"
							            :label="item.name"
							            :value="item.id">
							        </el-option>
							    </el-select>
							</el-form-item>
							
							 <el-form-item label="行业">
							    <el-select v-model="sendCheckedList.trade" placeholder="请选择"   clearable>
							        <el-option
							            v-for="item in sendCheckedList.tradeList"
							            :key="item.id"
							            :label="item.name"
							            :value="item.id">
							        </el-option>
							    </el-select>
							</el-form-item>
							</el-row>
							<el-row>
							 <el-col :span="24">
								  <el-form-item label="名称">
									<el-input v-model="sendCheckedList.data_name" placeholder="请输入名称" style="width:14em"></el-input>
								  
								  </el-form-item>
								  
								   <el-form-item label="送审时间">
										 <el-date-picker value-format="yyyy-MM-dd" format="yyyy-MM-dd" v-model="sendCheckedList.send_admin_time" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
										</el-date-picker>
										<el-button type="primary" @click="_searchSendCheckedList()">搜索</el-button>
										 <el-button type="primary" @click="_defaultSendChecked">重置</el-button>
								  </el-form-item>
								 </el-col> 
							</el-row>
							
							<el-row>
							<el-col :span="24">
								待审批数量：<span style="color:blue">{{sendCheckedList.waitChecked}}</span> 已退回数量：
								<span style="color:red">{{sendCheckedList.failedChecked}}</span>
								</el-col> 
								</el-row>
						</el-form>
						
						<el-table :data="sendCheckedList.list" stripe style="width: 100%" >
						    <el-table-column type="selection" width="50"></el-table-column>
						    <el-table-column prop="data_name" label="名称">
								
						    </el-table-column>
							
							<el-table-column prop="data_type" label="类型">
								
							</el-table-column>
						
							<el-table-column prop="" label="链接地址">
								   <template slot-scope="scope">
								    <a :href="scope.row.data_link" target="_blank">链接</a>
								</template>
							</el-table-column>
							
							<el-table-column label="所属服务" width="120">
							    <template slot-scope="scope">
							        <el-tooltip placement="top" effect="light" popper-class="spopper">
							            <div slot="content">
							                <h3 style="margin:0;padding-bottom:10px">所属服务</h3>
							                <el-col :span="24" v-for="(item, index) in scope.row.serviceList" :key='index' style="padding:5px 0">{{item}}</el-col>
							            </div>
							            <span  v-if="scope.row.serviceList && scope.row.serviceList.length>0" style="overflow:hidden;white-space: nowrap;text-overflow: ellipsis;">{{scope.row.serviceList[0]}}</span>
							        </el-tooltip>
							    </template>
							</el-table-column>
							<el-table-column label="关联行业" width="120">
							    <template slot-scope="scope">
							        <el-tooltip placement="top" effect="light" popper-class="spopper">
							            <div slot="content">
							                <h3 style="margin:0;padding-bottom:10px">关联行业</h3>
							                <el-col :span="24" v-for="(item, index) in scope.row.tradeList" :key='index' style="padding:5px 0">{{item}}</el-col>
							            </div>
							            <span  v-if="scope.row.tradeList && scope.row.tradeList.length>0"> {{scope.row.tradeList[0]}}</span>
							        </el-tooltip>
							    </template>
							</el-table-column>
							
							<el-table-column prop="tran_status" label="状态">
								
							</el-table-column>
									
							 <el-table-column prop="send_admin_time" label="送审时间">
							</el-table-column>	
							
							 <el-table-column prop="tran_admin_name" label="审批用户">
							</el-table-column>
							
							 <el-table-column prop="receive_groupid_name" label="审批分组">
							</el-table-column>
							
							<el-table-column prop="tran_admin_time" label="审批时间">
							</el-table-column>
							
							<el-table-column prop="tran_note" label="退回原因">
							</el-table-column>
							
						</el-table>
											
						<el-pagination @current-change="sendCheckedHandleCurrentChange" background :current-page.sync="sendCheckedList.page" :page-size="sendCheckedList.limit"
						    layout="total, prev, pager, next" :total="sendCheckedList.total" style="padding:10px 0;text-align: right;">
						</el-pagination>
						
					</el-tab-pane>
					<% } %>
				</el-tabs>
				</el-col>
				
					<el-dialog
						  title="审批提示"
						  :visible.sync="dialogVisible"
						  width="30%">
						  <div>确定以下内容审批退回？</div>
						  <h3 style="text-indent: 20px;">{{currentNewsTitle}}</h3>
						  <div>退回原因</div>
							 
							 <el-input
  type="textarea"
  :rows="2"
   maxlength="100"
  placeholder="请输入内容"
  v-model="sendCheckedList.tran_note">
</el-input>
											
						  <span slot="footer" class="dialog-footer">
							 <el-button @click="dialogVisible = false">取 消</el-button>
							  <el-button type="primary" @click="_checkFailed()">确 定</el-button>
						  </span>
					</el-dialog>
				<% } %>


               
            </el-row>
        </el-main>
    </el-container>
</el-container>


<script>
    var main = new Vue({
        el: '#Main',
        data:{
            form:{
                name: ''
            },
			checkedList:{
				send_admin_groupid:'',
				groupList:<%- sendCheckGroupList %>,
				data_type:'',
				typeList:[{'id':'news','name':'新闻'},{'id':'sku_service','name':'SKU服务'},{'id':'sku_solution','name':'SKU解决方案'},{'id':'case','name':'案例'},{'id':'res','name':'资源'}],
				service:'',
				serviceList:<%- serviceList %>,
				trade:'',
				tradeList:<%- tradeList %>,
				data_name:'',
				list:<%- checkDataList %>,
				total:<%- checkDataTotal %>,
				page:1,
				limit:10,
				send_admin_time:'',
				tran_status:'',
				tranStatusList:[{'id':'0','name':'审批中'},{'id':'10','name':'已发布'},{'id':'20','name':'已退回'}],
				waitChecked:<%- waitCheckedNumTotal %>
			},
			sendCheckedList:{
				receive_groupid:'',
				tran_status:'',
				tranStatusList:[{'id':'0','name':'审批中'},{'id':'10','name':'已发布'},{'id':'20','name':'已退回'}],
				groupList:<%- checkGroupList %>,
				typeList:[{'id':'news','name':'新闻'},{'id':'sku_service','name':'SKU服务'},{'id':'sku_solution','name':'SKU解决方案'},{'id':'case','name':'案例'},{'id':'res','name':'资源'}],				service:'',
				serviceList:<%- serviceList %>,
				trade:'',
				tradeList:<%- tradeList %>,
				data_name:'',
				list:<%- sendCheckDataList %>,
				total:<%- sendCheckDataTotal %>,
				page:1,
				limit:10,
				send_admin_time:'',
				tranStatusList:[{'id':'0','name':'审批中'},{'id':'10','name':'已发布'},{'id':'20','name':'已退回'}],
				waitChecked:<%- sendCheckedListWaitCheckedNumTotal %>,
				failedChecked:<%- sendCheckedListWFailedNumTotal %>,
				tran_note:''
			},
            tickets: <%- list %>,
			activeName:'<%- activeName %>',
			dialogVisible:false,
			currentNewsTitle:'',
			currentNewsId:'',
			currentType:'',

			hasEditChecked: <%- hasEditChecked %>,
			hasCheckedPurview: <%- hasCheckedPurview %>,
			currRow: ''
			
        },
        methods:{
            onSubmit:function(){
                if(this.form.name != ''){
                    window.location.href = '/sku/service?name=' + this.form.name;
                }
                
            },
			_getCheckedList:function()
			{
				var that = this;

				that.loading = true;
				var params = this.checkedList;
			
				params._csrf = '<%- csrf %>';
				axios.post('/dashboard/checkedlist/get', params)
				    .then(function(result) {
				        that.loading = false;
				        if (result.data.success) {
				            that.checkedList.list = result.data.data.list;
							that.checkedList.total = result.data.data.total;
							that.checkedList.page = result.data.data.page;
							that.checkedList.limit = result.data.data.limit;
							
							that.checkedList.waitChecked = result.data.data.waitCheckedNumTotal;
				        }else{
				            that.$message.error('获取列表出错');
				        }
				    });
			},
			_getSendCheckedList:function()
			{
				var that = this;
				that.loading = true;
				var params = this.sendCheckedList;
				params._csrf = '<%- csrf %>';
				axios.post('/dashboard/sendcheckedlist/get', params)
				    .then(function(result) {
				        that.loading = false;
				        if (result.data.success) {
							
				            that.sendCheckedList.list = result.data.data.list;
							that.sendCheckedList.total = result.data.data.total;
							that.sendCheckedList.page = result.data.data.page;
							that.sendCheckedList.limit = result.data.data.limit;
							

							that.sendCheckedList.waitChecked = result.data.data.waitCheckedNumTotal;
							that.sendCheckedList.failedChecked = result.data.data.failedCheckedNumTotal;
				        }else{
				            that.$message.error('获取列表出错');
				        }
				    });
			},
			_searchSendCheckedList()
			{
				this.sendCheckedList.page =1;
				this._getSendCheckedList();
			},
			sendCheckedHandleCurrentChange()
			{
				this._getSendCheckedList();
			},
			
			checkedChange(val){
			 
			},
			_searchCheckedList()
			{
				this.checkedList.page =1;
				this._getCheckedList();
			},
			checkedHandleCurrentChange(r) {
                this._getCheckedList();
            },
			getList(){
			    var that = this;
			    
			    that.loading = true;
			    var params = this.form;
			    params._csrf = '<%- csrf %>';
			    axios.post('/news/list/get', params)
			        .then(function(result) {
			            that.loading = false;
			            if (result.data.success) {
			                that.tableList = result.data.data;
			            }else{
			                that.$message.error('获取列表出错');
			            }
			        });
			},
			 _defaultChecked() {

				this.checkedList.tran_status='';
                this.checkedList.receive_groupid = '';
                this.checkedList.data_type = '';
                this.checkedList.service = '';
                this.checkedList.trade = '';
                this.checkedList.data_name = '';
                this.checkedList.send_admin_time = '';
            },
			
			_defaultSendChecked()
			{
				this.sendCheckedList.tran_status='';
				this.sendCheckedList.receive_groupid = '';
				this.sendCheckedList.data_type = '';
				this.sendCheckedList.service = '';
				this.sendCheckedList.trade = '';
				this.sendCheckedList.data_name = '';
				this.sendCheckedList.send_admin_time = '';
			},
			_checkOK(id,title,data_type,data_id)
			{
		
			  var that = this;
	
                this.$confirm('确定以下内容审批通过？<br/>'+title, '审批提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning',
				  dangerouslyUseHTMLString:true
                }).then(() => {
                    that.loading = true;
                    axios.post('/dashboard/checkoperator', { id: id,data_id:data_id,currentType:data_type,tran_note:'',name:title,tran_status:10, _csrf: '' })
                    .then(function(result) {
                        if (result.data.success) {
                            window.location.reload();
                        }
                    });
                }).catch(e =>{
									return e;
                });

			},
			_checkFailedUI(id,title, row)
			{
				var that = this;
			
				that.currentNewsTitle=title;
				that.currentNewsId=id;
				that.dialogVisible = true;
				this.currRow = row
			},
			_checkFailed()
			{
				var that = this;
				
				 this.$confirm('确定以下内容审批退回吗？<br/>'+that.currentNewsTitle, '审批提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning',
				  dangerouslyUseHTMLString:true
				}).then(() => {
						that.loading = true;
						let params = { 
							id: that.currentNewsId, 
							tran_note: that.sendCheckedList.tran_note, // 退回原因
							name: that.currentNewsTitle, 
							tran_status: 20, 
							_csrf: '', 
							type_name: that.currRow.data_type, // 类型
							service_name: that.currRow.serviceList[0] || '', // 所属服务
							trade_name: that.currRow.tradeList[0] || '',// 关联行业
							tran_status_name: that.currRow.tran_status, // 状态
							tran_name: '', // 送审用户
							group_name: that.currRow.receive_groupid_name || '', // 分组名称
							send_admin_time: that.currRow.send_admin_time, // 送审时间
							tran_admin_time: '' // 审批时间
						}
				    axios.post('/dashboard/checkoperator', params)
				    .then(function(result) {
						that.sendCheckedList.tran_note='';
				        if (result.data.success) {
				            window.location.reload();
				        }
				    });
				}).catch(e =>{
					return e;
				});
			}
        },
        mounted:function(){
            this.$nextTick(function(){
                document.getElementById('preLoading').style.display = 'none';
            });
        }
    })
</script>
<% include footer.html %>