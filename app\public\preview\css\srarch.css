.SearchBox{
    width: 1226px;height: auto;
    margin: 0 auto;
}
.searchleftBox{
    width: 849px;
    float: left;
    /*margin-top: 21px;*/
    position: relative;
}
.searchAlls{
    width: 812px;height: 63px;
    margin-bottom: 30px;
    padding-left: 37px;
    background: #f8f8f8;
}
.angle{
    width:0;
    height:0;
    position: absolute;
    overflow:hidden;
    font-size: 0;     /*是因为, 虽然宽高度为0, 但在IE6下会具有默认的 */
    line-height: 0;  /* 字体大小和行高, 导致盒子呈现被撑开的长矩形 */
    border-width:10px;
    border-style:solid dashed dashed dashed;/*IE6下, 设置余下三条边的border-style为dashed,即可达到透明的效果*/
    border-color: transparent transparent #f8f8f8 transparent;
    top: 51px;
    left: 50%;
    transform: translateX(-50%);
    display: none;
}
.angleShow{
    display: block;
}
.angleHide{
    display: none;
}
.searchAll{
    width: auto;
    height: 100%;
    margin-right: 36px;
    font-size: 16px;
    letter-spacing: normal;
    text-align: justify;
    color: #000000;
    line-height: 63px;
    cursor: pointer;
    position: relative;
}
.searchAll:hover{
    color: rgb(254,102,3);
}
.searchRelative{
    width: 812px;
    height: 63px;
    padding-left: 37px;
    background: #f8f8f8;
}
.searchRelativeT{
    width: auto;
    font-size: 14px;
    line-height: 63px;
    color: #666666;
    margin-right: 42px;
}
.searchRelativeC{
    font-size: 14px;
    line-height: 63px;
    color: #000000;
    margin-right: 36px;
    cursor: pointer;
}
.searchRelativeC:hover{
    color: rgb(254,102,3);
}
.searcrServer{
    width: 812px;
    height: 63px;
    padding-left: 37px;
    background: #f8f8f8;
    margin-bottom: 30px;
}
.searchConAll{
    width: 100%;
    height: auto;
}
.searchCon{
    width: 100%;
    height: 110px;
    border-bottom: 1px solid #eeeeee;
}
.searchCon1{
    width: auto;
    height: 22px;
    font-size: 16px;
    color: #000000;
    margin-top: 34px;
    margin-bottom: 20px;
    display: block;
    cursor: pointer;
}
.searchCon1:hover{
    color: rgb(254,102,2);
    text-decoration: underline;
}
.searchCon-time{
    width: auto;
    padding-right: 12px;
    margin-right: 5px;
    border-right: 1px solid #cccccc;
    font-size: 14px;
    color: #999999;
    display: inline-block;
    height: 14px;
    line-height: 14px;
}
.searchCon-Mb{
    width: auto;
    font-size: 14px;
    color: #999999;
}
.file_but{
    width: auto;
    /*height: auto;*/
    height: 20px;
    float: right;
    font-size: 14px;
    color: #333333;
    margin-right: 15px;
    vertical-align: center;
    background: url(../images/down.png) no-repeat;
    background-position: 0 0;
    padding-left: 24px;
    line-height: 20px ;
    cursor: pointer;
}
.file_but a:hover{
    color: rgb(254,102,2);
}
.file_but a{
    width: auto;
    line-height: 19px;
    margin-right: 15px;
}

.buttonIcon{
    display: inline-block;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: url(../images/down.png);

    /*background-size: contain;*/
}
.pageNums{
    width: 400px;
    height: 48px;
    background-color: #f8f8f8;
    margin: 28px auto 50px;
    padding: 0 176px;
    position: relative;
}
.pageNum{
    width: 44px;
    padding: 0 68px;
    height: 20px;
    display: inline-block;
    line-height: 20px;
    text-align: center;
    border-right: 1px solid #dddddd;
    border-left: 1px solid #dddddd;
    left: 300px;
}
.pageNums span{
    font-size: 14px;
    color: #666666;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}
.pageNums span:hover{
    color: rgb(254,102,2);
}
.lastpage{
    width: 44px;
    height: 20px;
    left: 176px;
}
.nextpage{
    width: 44px;
    height: 20px;
    right: 140px;
}
.searchrightBox{
    width: 326px;
    float: right;
    background-color: #f8f8f8;
    margin-top: 21px;
}
.searchrightBox1{
    width: 280px;
    margin: 0 auto;
    padding-bottom: 51px;
}
.yours{
    width: 100%;
    height: 74px;
    border-bottom: 1px solid #cccccc;
    cursor: pointer;
}
.yours:hover .your-word{
    color: rgb(254,102,3);
}
.your-word{
    height: 100%;
    line-height: 74px;
    color: #000000;
    /*font-weight: bold;*/
}
.dragbut{
    width: 22px;
    height: 22px;
    float: right;
    margin-top: 28px;
}
.dragbut img{
    width: 100%;height: 100%;
}
.yourcon ul{
    height: auto;
    margin-top: 18px;
    padding: 0;
}
.yourconLi{
    margin-bottom: 20px;

    font-size: 14px;
    cursor: pointer;
    list-style: none;
    /*margin-left: 20px;*/
}
.yourconLi a{
    color: #999999;
}
.yourconLi:hover a{
    color: rgb(254,102,2);
    /*text-decoration: underline;*/
}
.clickActive{
    color: rgb(254,102,2);
    font-weight: bold;
}
.your-cons{
    height: 0;
    overflow: hidden;
}
.your-cons.first{
    height: auto;
}
.relaActive{
    color: rgb(254,102,2);
}