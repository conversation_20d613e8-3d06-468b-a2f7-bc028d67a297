<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    
                                    <el-breadcrumb-item>
                                        运维维护
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24" style="text-align:right">
                                <el-button  v-if="hasEditPurview==1"  size="small" type="primary" @click='_editSort' v-show="!editType">修改序号</el-button>
                                <el-button   v-if="hasEditPurview==1"  size="small" type="success" @click="_saveSort" v-show="editType">保存</el-button>
                                <el-button  v-if="hasEditPurview==1"  size="small" @click='_delMulti' :disabled="check">批量删除</el-button>
                                <el-button  v-if="hasEditPurview==1"   size="small" type="primary" @click='_add'>添加</el-button>
                            </el-col>
                        </el-row>
                        <el-row v-loading="loading">
                                <el-col :span="24">
                                    <el-table :data="tableList.list" style="width: 100%"  @selection-change="change">
                                        <el-table-column type="selection" width="50">
                                        </el-table-column>
                                        <el-table-column width="50">
                                            <template slot-scope="scope">
                                                <img :src="scope.row.thumb_img" style="width:40px" />
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="name" label="名称" width="240">
                                        </el-table-column>
                                        <el-table-column prop="sort_num" label="排序" width="80">
                                           <template slot-scope="scope">
                                                <template v-if="!editType">{{scope.row.sort_num}}</template>
                                                <el-input v-else v-model="scope.row.sort_num"></el-input>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="is_use" label="上架" width="60">
                                            <template slot-scope="scope">
                                                <span v-if="scope.row.is_use">是</span>
                                                <span v-else style="color:#900">否</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="alias" label="链接地址" style="white-space: nowrap;">
                                                <template slot-scope="scope" v-if="scope.row.id">
                                                    <a :href="'<%- view_url %>/sku/'+scope.row.id" target="_blank"><%- view_url %>/sku/{{scope.row.id}}</a>
                                                </template>
                                        </el-table-column>
                                        <el-table-column label="所属服务" width="180">
                                            <template slot-scope="scope">
                                                <el-tooltip placement="top" effect="light" popper-class="spopper">
                                                    <div slot="content">
                                                        <h3 style="margin:0;padding-bottom:10px">所属服务</h3>
                                                        <el-col :span="24" v-for="(item, index) in scope.row.serviceList" :key='index' style="padding:5px 0">{{item}}</el-col>
                                                    </div>
                                                    <span style="overflow:hidden;white-space: nowrap;text-overflow: ellipsis;">{{scope.row.serviceList[0]}}</span>
                                                </el-tooltip>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="关联行业" width="180">
                                            <template slot-scope="scope">
                                                <el-tooltip placement="top" effect="light" popper-class="spopper">
                                                    <div slot="content">
                                                        <h3 style="margin:0;padding-bottom:10px">关联行业</h3>
                                                        <el-col :span="24" v-for="(item, index) in scope.row.tradeList" :key='index' style="padding:5px 0">{{item}}</el-col>
                                                    </div>
                                                    <span>{{scope.row.tradeList[0]}}</span>
                                                </el-tooltip>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="操作" width="160">
                                            <template slot-scope="scope">
                                                <a  v-if="hasEditPurview==1"   class="el-button el-button--primary el-button--mini" style="color:#fff" :href="'/sku/service/' + scope.row.id">编辑</a>
                                                <el-button  v-if="hasEditPurview==1"   type="danger" size="mini" @click='_del(scope.row.id,scope.row.name)'>移除</el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                    <el-pagination @current-change="handleCurrentChange" background :current-page.sync="form.page" :page-size="form.limit"
                                        layout="total, prev, pager, next" :total="form.total" style="padding:10px 0;text-align: right;">
                                    </el-pagination>
                                </el-col>
                            </el-row>
                            <div class="logshow-wrapper" v-if="logs.list.length > 0">
                            <el-row class="logshow" :class="{on: logshow}">
                                <span class="icon"><i class="el-icon-arrow-up" @click="showLog"></i></span>
                                <ul>
                                    <li v-for="(item, index) of logs.list" :key='index'>{{item.time}}, {{item.user_name}}, {{item.log}}</li>
                                </ul>
                                <el-pagination
                                  @current-change="logPageChange"
                                  :current-page.sync="logs.page"
                                  :page-size="logs.limit"
                                  layout="total, prev, pager, next"
                                  :total="logs.total">
                                </el-pagination>
                            </el-row>
                            </div>
                    </el-main>
            </el-container>
            <el-dialog title="添加SKU" :visible.sync="dialogFormVisible" width="480px">
                <el-form :model="form" label-width="90px">
                    <el-form-item label="URL" prop="name" >
                        <el-input v-model="skuUrl" auto-complete="off"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogFormVisible = false">取 消</el-button>
                    <el-button type="primary" @click="_addSku()">确 定</el-button>
                </div>
            </el-dialog>
    </el-container>
    <script>
    var main = new Vue({
        el: '#Main',
        data: {
            tableList: {
                list: <%- list %>,
            },
            form: {
                page: 1,
                limit: 10,
                total: <%- total %>,
            },
            skuUrl: '',
            loading: false,
            dialogFormVisible: false,
            check: true,
            checkIds: [],
            editType: false,
            logs: <%- logs %>,
            logshow: false,
			hasCheckedPurview: <%- hasCheckedPurview %>,
			hasPubviewPurview: <%- hasPubviewPurview %>,
			hasEditPurview: <%- hasEditPurview %>
        },
        methods: {
            handleCurrentChange(val){
                this.form.page = val;
                this.getData();
            },
            handleSizeChange(val){
                this.form.limit = val;
                this.getData();
            },
            getData(){
                var that = this;
                that.loading = true;
                var params = that.form;
                params._csrf = '<%- csrf %>';
                url = '/operation/list';
                axios.post(url, params)
                .then(function(result) {
                    if (result.data) {
                        that.loading = false;
                        that.tableList.list = result.data.data.list;
                        that.form.total = result.data.data.total;
                        /*
                        that.tableList.curr = that.form.page;
                        */
                    }
                });
            },
            _del(id, name) {
                var that = this;
                this.$confirm('是否解除与' + name + '的关联？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    that.loading = true;
                    url = '/operation/skuDrop';
                    axios.post(url, { sku_id: id,id: 6, _csrf: '<%- csrf %>' })
                        .then(function(result) {
                            if (result.data.success) {
                                //that._default();
                                window.location.reload();
                            }
                        });
                }).catch(e => {
                    return e;
                });
            },
            _delMulti(){
                var that = this;
                this.$confirm('是否解除已选择SKU得关联？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    that.loading = true;
                    url = '/operation/skuDrop';
                    axios.post(url, { sku_id: that.checkIds,id: 6, _csrf: '<%- csrf %>' })
                        .then(function(result) {
                            if (result.data.success) {
                                //that._default();
                                window.location.reload();
                            }
                        });
                }).catch(e => {
                    return e;
                });
            },
            _add(){
                this.skuUrl = '';
                this.dialogFormVisible = true;
            },
            _addSku(){
                if(this.skuUrl == ''){
                    return;
                }
                
                var that = this;
                axios.post('/operation/add', { skuUrl: this.skuUrl, _csrf: '<%- csrf %>' })
                    .then(function(result) {
                        if (result.data.success) {
                            window.location.reload();
                        }else{
                            that.$message.error(result.data.msg);
                        }
                    }).catch(e => {
                        return e;
                    });
            },
            change(val){
                var that = this;
                that.checkIds = [];
                val.forEach(function(item,i){
                    that.checkIds.push(item.id);
                });

                if(that.checkIds.length ==0 ){
                    that.check = true;
                }else{
                    that.check = false;
                }
            },
            _editSort(){
                this.editType = true;
            },
            _saveSort(){
                var postData = [];
                this.tableList.list.forEach(item => {
                    postData.push({catalog_id: <%- id %>,sku_id:item.id,sort_num:item.sort_num});
                });
                this.editType = false;

                url = '/sku/service/saveSort';
                axios.post(url, { data: postData, _csrf: '<%- csrf %>' })
                    .then(function(result) {
                        if (result.data.success) {
                            that._default();
                            
                        }
                        window.location.reload();
                    });
            },
            showLog(){
                if(this.logs.list.length > 1){
                    this.logshow = !this.logshow;
                }
            },
            logPageChange(page){
                axios.post('/logslist',{page:page,model:'运维内容维护',limit:10,_csrf:'<%- csrf %>'}).then(res => {
                    let data = res.data;
                    if(data.success){
                        this.logs = data.data;
                    }
                    
                })
            }
        },
         mounted(){
            this.$nextTick(function(){
                document.getElementById('preLoading').style.display = 'none';
            });
        }
    });
    </script>
    <% include footer.html %>