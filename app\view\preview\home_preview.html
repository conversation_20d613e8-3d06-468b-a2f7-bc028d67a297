<% include header.html %>


<div class="banner">
    <div class="swiper-container">
        <div class="swiper-wrapper">
            
            <% for(item of banner){ %>
            <div class="swiper-slide bannerLi1" style="background-image:url(<%- item.img_path %>)">
                <% if(item.btn_url && !item.btn_value){ %><a href="<%- item.btn_url %>" target="blank" style="display:block;width:100%;height:100%;"><% } %>
                <div class="baa">
                    <div class="bannerTitle"><%- item.btn_text %></div>
                    <div class="bannerDetail"><%- item.btn_description %></div>
                    <% if(item.btn_value){ %>
                    <a class="bannerMore" href="<%- item.btn_url %>" target="blank">
                        <%- item.btn_value %>
                        <div class="bannermoreBox">
            
                        </div>
                        <div class="bannermoreword"><%- item.btn_value %></div>
                    </a>
                    <% } %>
                </div>
                <% if(item.btn_url && !item.btn_value){ %></a><% } %>
            </div>
            <% } %>
        </div>
        <!-- Add Arrows -->
        <div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>
    </div>


    
</div>

    <div class="location">
        <ul>
            <li class="locationLi">
                <a href="">首页</a>
            </li>
            <li class="locationLi icon"></li>
            <li class="locationLi">
                <a href="javascript:;"><% if(detail.parantId == 1){ %>您的行业<% }else{ %>我们的服务<% } %></a>
            </li>
            <li class="locationLi icon"></li>
            <li class="locationLi">
                <a href="javascript:;"><%- detail.name %></a>
            </li>
        </ul>
    </div>

    <div class="introduce">
        <div class="introduceTitle"><%- detail.name %></div>
        <div class="introduceLeft">
            <div class="introduceLeft-word">
                    <%- detail.description.replace(/\n/g,'<br>') %>
            </div>
            <a class="introduceLeft-question" href="/preview/ticket" style="display: inline-block">

                <span class="introduceLeft-questionbox1"></span>
                <span class="introduceLeft-question1">咨询留言</span>
            </a>
        </div>

        <div class="introduceRight" style="background:none">
            <img src="<%- detail.cover_img %>" style="max-width:100%">
        </div>
    </div>

    <div class="server">
        <div class="serverBox sebox2">
            <div class="serverHead">

                <div class="bor">

                </div>
                <ul>
                    <% for(item of children){ %>
                        <!--serverLiActive-->
                    <li class="serverLi">
                        <%- item.name %>
                    </li>
                    <% } %>
                    
                </ul>
                <div class="allServer"><a href="/preview/catalog/<%- detail.alias %>/service">全部服务</a></div>
                <div class="serverIcon"></div>
            </div>
            <div class="serverCon">
                <% for(item of children){ %>
                    <ul>
                        <% for(sku of item.skus){ %>
                        <li class="serverConLi">
                            <a href="/preview/sku/<%- sku.id %>">
                                <img src="<%- sku.thumb_img %>" alt="">
                                <span class="serverConLiWorBac">
                                    <span class="serverConLiWord1"><%- sku.name %></span>
                                    <span class="serverConLiWord2"><%- sku.sub_title %></span>
                                </span>
                            </a>
    
                        </li>
                        <% } %>
                    </ul>
                <% } %>
            </div>

            <!--
            <div class="pages">
                <ul>
                    <li class="pagesLi pagesLiActive">1</li>
                    <li class="pagesLi">2</li>
                    <li class="pagesLi">3</li>
                </ul>
            </div>
            -->
        </div>
    </div>

    <% if(detail.page_type == 1){ %>
    <% if(solution.length > 0){ %>
    <div class="solution">
        <div class="serverBox sebox2" style="background-color: #fff">
            <div class="serverHead">
                <ul>
                    <li class="serverLi solu">解决方案</li>
                </ul>
            </div>

            <div class="solutionCon">
                <ul>
                    <% for(item of solution){ %>
                    <li class="solutionConLi">
                        <a href="/preview/sku/<%- item.id %>">
                            <img src="<%- item.thumb_img %>" alt="">
                            <div class="cc">
                                <span class="solutionConLi-word1"><%- item.name %></span>
                                <p><%- item.description %></p>
                            </div>
                            
                        </a>
                    </li>
                    <% } %>
                </ul>
            </div>
            <!--
            <div class="pages">
                <ul>
                    <li class="pagesLi pagesLiActive">1</li>
                    <li class="pagesLi">2</li>
                    <li class="pagesLi">3</li>
                </ul>
            </div>
            -->
        </div>
    </div>
    <% } %>
    
    <% if(cases.length > 0){ %>
    <div class="cooperation">
        <div class="cooperationBox">
            <div class="serverHead">
                <ul>
                    <li class="serverLi demo"><%- detail.case_title %></li>
                </ul>
            </div>

            <div class="cooperationDemo">
                <ul class="cooperationDemo1">
                    <% cases.forEach(function(item,i){ %>
                    <% if(i == 0 || i%2 == 0){ %>
                        <li><a href="/preview/case/detail-<%- item.id %>.html" target="_blank"><%- item.title %></a></li>
                    <% } %>    
                    <% }) %>
                </ul>

                <ul class="cooperationDemo2">
                    <% cases.forEach(function(item,i){ %>
                    <% if(i%2 == 1){ %>
                        <li><a href="/preview/case/detail-<%- item.id %>.html" target="_blank"><%- item.title %></a></li>
                    <% } %>    
                    <% }) %>
                </ul>
            </div>
        </div>
        <!--
        <div class="pages">
            <ul>
                <li class="pagesLi pagesLiActive">1</li>
                <li class="pagesLi">2</li>
                <li class="pagesLi">3</li>
            </ul>
        </div>
    -->
    </div>
    <% } %>
    
    <% if(resource.length > 0){ %>
    <div class="resources">
        <div class="resourceBox">
            <div class="serverHead">
                <ul>
                    <li class="serverLi reletive">相关资源 </li>
                </ul>
            </div>

            <div class="resourceCon">
                <ul>
                    <% for(var item of resource){ %>
                    <li class="resourceConLi">
                        <img src="/static/preview/images/liebiao.png" alt="">
                        <span class="resourceConLi-word"><a href="<%- item.path %>"><%- item.title %></a></span>
                    </li>
                    <% } %>
                </ul>
            </div>
        </div>
    </div>
    <% } %>
    <% } %>

<script src="/static/preview/js/swiper.min.js"></script>
<script>
    $(function(){
        $('.sebox2 .serverCon ul:eq(0)').addClass('show');
    })
    var swiper = new Swiper('.swiper-container', {
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        loop: true
    });

</script>
<% include footer.html %>


