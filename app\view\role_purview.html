<% include header.html %>
  <style>
    .specil .el-form-item__label {
      font-weight: bold !important;
      color: #000;
      font-size: 16px;
    }

    .speciluser .el-form-item__label {
      font-weight: bold !important;
      color: #000;
      font-size: 1.17em;
      margin-left: 0px;
      text-align: left;
    }

    .el-transfer-panel {
      width: 180px;
    }
  </style>
  <el-container id="Main">
    <% include eheader.html %>
      <el-container>
        <% include eside.html %>
          <el-main>
            <el-row>
              <el-col :span="24">
                <el-breadcrumb separator-class="el-icon-arrow-right">
                  <el-breadcrumb-item>
                    <a href='/'>首页</a>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    <a href='/role/list'>权限配置</a>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    分组配置
                  </el-breadcrumb-item>
                </el-breadcrumb>
              </el-col>
            </el-row>
            <el-row>
              <h3 class="stit">分组名称：{{form.name}} &nbsp;&nbsp; &nbsp;&nbsp;状态：{{effectText}}
                <div>
                  <div style="float:left;">分组描述：</div>
                  <div v-html="summarybr" style=" display:inline;float:left;"></div>
                  <div style="clear:both;">创建时间：{{form.create_time}}</div>
              </h3>
              <el-form :model="form" label-width="120px" ref="form">
                <el-form-item label="用户列表" class="speciluser">
                  <el-transfer v-model="form.user" :data="userList" :button-texts="['删除', '增加']"
                    :titles="['全部用户', '选中用户']">
                  </el-transfer>

                </el-form-item>
                <h3 class="stit">数据权限</h3>
                <el-form-item label="分组权限" class="specil">
                  <el-checkbox-group v-model="form.dataPurview">
                    <el-checkbox label="查看" @change="handleCheckedChange"></el-checkbox>
                    <el-checkbox label="编辑" @change="handleCheckedChange"></el-checkbox>
                    <el-checkbox label="发布" @change="handleCheckedChange"></el-checkbox>
                    <el-checkbox label="审核" @change="handleCheckedChange"></el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-row>
                  <el-form-item label="分类权限" class="specil">
                    <el-col :span="12">
                      <el-transfer v-model="form.traceDataPurview.ids" :data="tradeList" :button-texts="['删除', '增加']"
                        :titles="['全部行业', '选中行业']"></el-transfer>
                    </el-col>
                    <el-col :span="12">
                      <el-transfer v-model="form.serviceDataPurview.ids" :data="serviceList"
                        :button-texts="['删除', '增加']" :titles="['全部服务', '选中服务']"></el-transfer>
                    </el-col>
                  </el-form-item>
                </el-row>
              </el-form>
              <hr>
              <h3 class="stit">BU配置</h3>
              <el-checkbox-group v-model="form.bu_ids">
                <el-checkbox v-for="(bu, index) of buList" :key="index" :label="bu.id">{{ bu.name }}</el-checkbox>
              </el-checkbox-group>
              <hr>
              <h3 class="stit">功能权限模块</h3>
              <el-form :model="form" ref="form">
                <el-row :gutter="20" v-for="firstMenu in form.menuList" :key="firstMenu.id">
                  <template v-if="firstMenu.level_id == 1">
                    <el-col :span="2" style="text-align: right;">
                      <b>{{firstMenu.name}}
                      </b>
                    </el-col>

                    <el-col :span="17" style="text-align:left;">

                      <el-form-item :label="firstMenu.name" label-width="120px" v-if="firstMenu.nodeCount == 0">
                        <el-checkbox-group v-model="firstMenu.role_purview_json">

                          <el-checkbox @change="handleChecked " v-for="function_item in firstMenu.function_json"
                            :label="function_item" :key="firstMenu.id+function_item">{{function_item}}</el-checkbox>
                        </el-checkbox-group>
                      </el-form-item>

                      <el-form-item v-else>
                        <el-form-item :label="secondMenu.name" label-width="120px" :key="secondMenu.id"
                          v-for="(secondMenu, sIndex) in form.menuList" :key='sIndex'
                          v-if="secondMenu.parent_id == firstMenu.id">

                          <el-checkbox-group v-if="secondMenu.name == '服务分类'" v-model="secondMenu.role_purview_json">
                            <el-checkbox @change='handleServiceChecked'
                              v-for="function_item in secondMenu.function_json" :label="function_item"
                              :key="secondMenu.id+function_item">{{function_item}}</el-checkbox>

                          </el-checkbox-group>

                          <el-checkbox-group v-else-if="secondMenu.name == '行业分类'"
                            v-model="secondMenu.role_purview_json">
                            <el-checkbox @change='handleTradeChecked' v-for="function_item in secondMenu.function_json"
                              :label="function_item" :key="secondMenu.id+function_item">{{function_item}}</el-checkbox>

                          </el-checkbox-group>
                          <!-- 按钮权限只能单选 -->
                          <el-checkbox-group v-else-if="secondMenu.name == '服务'" v-model="secondMenu.role_purview_json">
                            <el-checkbox @change='handleServiceChecked1'
                              v-for="function_item in secondMenu.function_json" :label="function_item"
                              :key="secondMenu.id+function_item">{{function_item}}</el-checkbox>
                          </el-checkbox-group>
                          <el-checkbox-group v-else-if="secondMenu.name == '解决方案'"
                            v-model="secondMenu.role_purview_json">
                            <el-checkbox @change='handleTradeChecked1' v-for="function_item in secondMenu.function_json"
                              :label="function_item" :key="secondMenu.id+function_item">{{function_item}}</el-checkbox>
                          </el-checkbox-group>

                          <el-checkbox-group v-else v-model="secondMenu.role_purview_json">
                            <el-checkbox v-for="function_item in secondMenu.function_json" :label="function_item"
                              :key="secondMenu.id+function_item">{{function_item}}</el-checkbox>

                          </el-checkbox-group>

                        </el-form-item>
                      </el-form-item>


                    </el-col>
                  </template>
                </el-row>
              </el-form>
              <el-col :span="24" style="background: #39F;padding:12px 0;color:#FFF">
                <el-col :span="12" style="padding-left:12px;">
                  <el-button @click="_cancel">返回列表</el-button>
                </el-col>
                <el-col :span="12" style="text-align:right;padding-right:12px;">
                  <el-button type="success" @click='onSubmit'> 保 存 </el-button>
                </el-col>
              </el-col>
            </el-row>
          </el-main>
      </el-container>
  </el-container>
  <script>
    var ai = 0, bi = 0;
    var olddataPurview;
    var main = new Vue({
      el: '#Main',
      computed:
      {
        summarybr: function () {

          return this.form.summary.replace(/\n/g, '<br/>');
        }
      },
      data: {
        form: {
          id: <%- id %>,
          name: '<%- form.name %>',
          create_time: '<%- form.create_time %>',
          menuList:<%- menuList %>,
          dataPurview: [],
          user: [],
          traceDataPurview:<%- traceDataPurview %>,
          serviceDataPurview:<%- serviceDataPurview %>,
          summary: `<%- form.summary %>`,
          bu_ids: <%- bu_ids || [] %>
        },
        buList: <%- buList %>,
        effectText: '<%- form.effectText %>',
        userList:<%- userList %>,
        tradeList:<%- tradeList %>,
        serviceList:<%- serviceList %>,
        loading: false,
      },
      created: function () {


        for (var i = 0; i < this.form.menuList.length; i++) {

          var item = this.form.menuList[i];
          if (item.name == '工作台') {
            if (!this.form.menuList[i].role_purview_json || this.form.menuList[i].role_purview_json.length == 0) {
              this.form.menuList[i].role_purview_json = ['开启'];
            }
          }
        }
        //初始化数据
        var that = this;
        olddataPurview = this.form.dataPurview;
        that.form.user =<%- groupUserList %>;
        var traceDataPurview = that.form.traceDataPurview;
        var serviceDataPurview = that.form.serviceDataPurview;

        if (traceDataPurview.look == 1 || serviceDataPurview.look == 1) {
          that.form.dataPurview.push('查看');
        }

        if (traceDataPurview.edit == 1 || serviceDataPurview.edit == 1) {
          that.form.dataPurview.push('编辑');
        }

        if (traceDataPurview.publish == 1 || serviceDataPurview.publish == 1) {
          that.form.dataPurview.push('发布');
        }

        if (traceDataPurview.checked == 1 || serviceDataPurview.checked == 1) {
          that.form.dataPurview.push('审核');
        }
      },
      methods: {
        handleServiceChecked(flag, obj) {
          if (flag) {
            var that = this;
            for (var iIndex in that.form.menuList) {
              var item = that.form.menuList[iIndex];
              if (item.name == '服务分类') {
                item.role_purview_json = [obj.currentTarget.defaultValue];
                return;
              }
            }
          }
        },
        handleServiceChecked1(flag, obj) {
          if (flag) {
            var that = this;
            for (var iIndex in that.form.menuList) {
              var item = that.form.menuList[iIndex];
              if (item.name == '服务') {
                item.role_purview_json = [obj.currentTarget.defaultValue];
                return;
              }
            }
          }
        },
        handleTradeChecked(flag, obj) {
          if (flag) {
            var that = this;
            for (var iIndex in that.form.menuList) {
              var item = that.form.menuList[iIndex];
              if (item.name == '行业分类') {
                item.role_purview_json = [obj.currentTarget.defaultValue];
                return;
              }
            }
          }
        },
        handleTradeChecked1(flag, obj) {
          
          if (flag) {
            var that = this;
            for (var iIndex in that.form.menuList) {
              var item = that.form.menuList[iIndex];
              if (item.name == '解决方案') {
                item.role_purview_json = [obj.currentTarget.defaultValue];
                return;
              }
            }
          }
        },
        handleChecked(flag, obj) {
          for (var i = 0; i < this.form.menuList.length; i++) {

            var item = this.form.menuList[i];
            if (item.name == '工作台') {
              if (!this.form.menuList[i].role_purview_json || this.form.menuList[i].role_purview_json.length == 0) {
                this.form.menuList[i].role_purview_json = ['开启'];
              }
            }
          }
        },
        handleCheckedChange(flag, obj) {
          var that = this;

          var btnCmd = '';
          if (obj.currentTarget.defaultValue == '查看') {
            if (flag)//查看选中
            {
              //故意保留, 为了逻辑清晰
            }
            else//查看取消
            {
              //查看取消,什么权限都没有了
              that.form.dataPurview = [];
            }
          }
          else if (obj.currentTarget.defaultValue == '编辑') {
            if (flag)//编辑选中
            {
              //如果查看没有勾选,需要增加查看

              if (that.form.dataPurview.indexOf('查看') == -1) {
                that.form.dataPurview.push('查看');
              }

            }
            else//编辑取消,发布和审核去掉
            {
              var data = [];
              for (var iIndex in that.form.dataPurview) {
                var item = that.form.dataPurview[iIndex];
                if (item == '查看') {
                  data.push(item);
                }
              }
              that.form.dataPurview = data;
            }

          }
          else if (obj.currentTarget.defaultValue == '发布') {
            if (flag)//发布选中
            {
              //如果查看和编辑没有勾选,需要增加查看和编辑

              if (that.form.dataPurview.indexOf('查看') == -1) {
                that.form.dataPurview.push('查看');
              }

              if (that.form.dataPurview.indexOf('编辑') == -1) {
                that.form.dataPurview.push('编辑');
              }
            }
            else//发布取消
            {
              var data = [];
              for (var iIndex in that.form.dataPurview) {
                var item = that.form.dataPurview[iIndex];
                if (item != '审核') {
                  data.push(item);
                }
              }
              that.form.dataPurview = data;
            }
          }
          else if (obj.currentTarget.defaultValue == '审核') {
            if (flag)//审核选中
            {
              //如果查看和编辑没有勾选,需要增加查看和编辑

              if (that.form.dataPurview.indexOf('查看') == -1) {
                that.form.dataPurview.push('查看');
              }

              if (that.form.dataPurview.indexOf('编辑') == -1) {
                that.form.dataPurview.push('编辑');
              }

              if (that.form.dataPurview.indexOf('发布') == -1) {
                that.form.dataPurview.push('发布');
              }
            }
            else//发布取消
            {
              //什么都不发生
            }
          }
        },
        onSubmit() {
          var that = this;

          if (!this.form.name) {
            this.$message.error('请输入用户名称');
            return;
          }
          if (!this.form.bu_ids.length) {
            this.$message.error('请选择BU配置');
            return;
          }

          if ((that.form.traceDataPurview.ids && that.form.traceDataPurview.ids.length > 0) || (that.form.serviceDataPurview.ids && that.form.serviceDataPurview.ids.length > 0))//如果勾选了服务或者行业,上面的数据权限必须选择1个
          {
            if (that.form.dataPurview.length == 0) {
              this.$message.error('请至少勾选1个分组权限');
              return;
            }
          }

          if (that.form.dataPurview.length == 0)//如果选择了某些模块,必须要勾选分组
          {
            for (var iIndex in that.form.menuList) {
              var item = that.form.menuList[iIndex];
              if (item.name == '新闻类型' || item.name == '资料类型' || item.name == '资讯类型' || item.name == '服务' || item.name == '解决方案' || item.name == '新闻列表' || item.name == '资料列表' || item.name == '草稿箱' || item.name == '资讯列表') {
                if (item.role_purview_json.length > 0) {
                  this.$message.error('请至少勾选1个分组权限');
                  return;
                }
              }
            }
          }

          if ((!that.form.traceDataPurview.ids || that.form.traceDataPurview.ids.length == 0) && (!that.form.serviceDataPurview.ids || that.form.serviceDataPurview.ids.length == 0))//如果行业和服务都为空,如果选择了某些模块,必须要勾选分类
          {
            for (var iIndex in that.form.menuList) {
              var item = that.form.menuList[iIndex];
              if (item.name == '新闻类型' || item.name == '资料类型' || item.name == '资讯类型' || item.name == '服务' || item.name == '解决方案' || item.name == '新闻列表' || item.name == '资料列表' || item.name == '草稿箱' || item.name == '资讯列表') {
                if (item.role_purview_json.length > 0) {
                  this.$message.error('请至少勾选1个服务或者1个行业');
                  return;
                }
              }
            }
          }

          if (that.form.dataPurview.length > 0)//如果勾选了数据权限,那么服务和行业至少选择1个
          {
            if ((!that.form.traceDataPurview.ids || that.form.traceDataPurview.ids.length == 0) && (!that.form.serviceDataPurview.ids || that.form.serviceDataPurview.ids.length == 0)) {
              this.$message.error('请至少勾选1个服务或者1个行业');
              return;
            }
          }

          if ((that.form.traceDataPurview.ids && that.form.traceDataPurview.ids.length > 0) || (that.form.serviceDataPurview.ids && that.form.serviceDataPurview.ids.length > 0))//如果勾选了服务或者行业,至少勾选一个模块
          {
            var flag = true;
            for (var iIndex in that.form.menuList) {
              var item = that.form.menuList[iIndex];
              if (item.name == '新闻类型' || item.name == '资料类型' || item.name == '资讯类型' || item.name == '服务' || item.name == '解决方案' || item.name == '新闻列表' || item.name == '资料列表' || item.name == '草稿箱' || item.name == '资讯列表') {
                if (item.role_purview_json && item.role_purview_json.length > 0) {
                  flag = false;
                  break;
                }
              }
            }

            if (flag) {
              this.$message.error('请至少勾选一个相关的功能权限模块');
              return;
            }
          }

          this.$confirm('是否确认修改数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {

            this.$refs['form'].validate((valid) => {
              if (valid) {
                /* var loading = this.$loading({
                  lock: true,
                  text: 'Loading',
                  spinner: 'el-icon-loading',
                  background: 'rgba(255, 255, 255, 0.7)'
                }); */

                var params = that.form;
                params._csrf = '<%- csrf %>';
                
                axios.post('/role/rolePurviewSave', params)
                  .then(function (result) {
                    // loading.close();

                    if (result.data.success) {
                      that.$message({
                        message: '保存成功。',
                        type: 'success'
                      });

                      window.location = '/role/list';
                    } else {
                      that.$message.error(result.data.msg);
                    }
                  });
              } else {
                return false;
              }
            });
          });
        },

        _cancel() {
          window.location = '/role/list/';
        }
      },
      mounted() {
        this.$nextTick(function () {
          document.getElementById('preLoading').style.display = 'none';
          this.form.bu_ids = this.form.bu_ids.map(v => v = Number(v))
        });
      }
    });
  </script>
  <% include footer.html %>