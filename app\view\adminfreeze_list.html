<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        权限配置
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                       冻结用户
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row v-loading="loading">
                            <el-col :span="16">
                                <el-form :inline="true" :model="form">

									<el-form-item label="用户">
									    <el-input v-model="form.name" placeholder="请输入用户" style="width:14em"></el-input>
									</el-form-item>

                                    <el-form-item>
                                        <el-button type="primary" @click="search_list()">查询</el-button>
										 <el-button type="primary" @click="_default">重置</el-button>
                                    </el-form-item>
                                </el-form>
                            </el-col>


                            <el-table :data="tableList.list" stripe style="width: 100%" @selection-change="change">
                          
                                <el-table-column prop="name" label="用户名称">
                                </el-table-column>
								 <el-table-column prop="gmt_create" label="用户添加时间">
								</el-table-column>
								
								<el-table-column prop="delete_time" label="用户删除时间">
								</el-table-column>
								<el-table-column fixed="right" label="操作" width="160" fixed="right">
								    <template slot-scope="scope">
								         <el-button size="small"  type="primary" @click='_activeUser(scope.row.id)' >激活</el-button>
								    </template>
								</el-table-column>
								
                            </el-table>
                            <el-pagination @current-change="handleCurrentChange" background :current-page.sync="form.page" :page-size="tableList.limit"
                                layout="total, prev, pager, next" :total="tableList.total" style="padding:10px 0;text-align: right;">
                            </el-pagination>
                        </el-row>
                    </el-main>
            </el-container>
    </el-container>
    <script>
    var main = new Vue({
        el: '#Main',
        data: {
            tableList: {
                list: <%- list %>,
                total: <%- total %>,
                page: 1,
                limit: 10
            },
            form: {
                name: '',
                role_id: '',
                page: 1
            },
            roleList: <%- roleList %>,
            loading: false,
            check: true,
            checkIds:[],
        },
        methods: {
            handleCurrentChange(r) {
                this.form.page = r;
                this._getList();
            },
			 change(val){
			    var that = this;
			    that.checkIds = [];
			    val.forEach(function(item,i){
			        that.checkIds.push(item.id);
			    });
			
			    if(that.checkIds.length ==0 ){
			        that.check = true;
			    }else{
			        that.check = false;
			    }
			},
			search_list()
			{
				this.form.page =1;
				this._getList();
			},
            _getList(){
                var that = this;
                
                that.loading = true;
                var params = this.form;
                params._csrf = '<%- csrf %>';
                axios.post('/adminfreeze/list/get', params)
                    .then(function(result) {
                        that.loading = false;
                        if (result.data.success) {
                            that.tableList = result.data.data;
                        }else{
                            that.$message.error('获取列表出错');
                        }
                    });
            },
            _activeUser(admin_id){
                var that = this;
				
                this.$confirm('是否确定激活该用户？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    that.loading = true;
                    axios.post('/adminfreeze/active', { id: admin_id, _csrf: '<%- csrf %>' })
                        .then(function(result) {
                            that.loading = false;
                            if (result.data.success) {
                                that._default();
                                window.location.reload();
                            }
                            else
                            {
                            	
                            	that.$message.error(result.data.msg);
                            }
                        });
                }).catch(e => {
                    return e;
                });
            },
            
            _default() {

                this.form.name = '';
                this.form.role_id = '';
            }
		},  
        mounted(){
            this.$nextTick(function(){
                //this.types.unshift({id:0,name:'全部'});

                document.getElementById('preLoading').style.display = 'none';
            });
        }
    });
    </script>
    <% include footer.html %>