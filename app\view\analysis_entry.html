<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        页面来源分析
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row v-loading="loading">
                            <el-col :span="24">
                                <el-form :inline="true" :model="form">
                                    <el-form-item>
                                        <el-date-picker v-model="form.time" type="datetimerange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间"  :picker-options="pickerOptions2">
                                        </el-date-picker>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="getList">查询</el-button>
                                        <el-button type="success" @click="_export">导出</el-button>
                                    </el-form-item>
                                </el-form>
                            </el-col>
                            <el-table :data="tableList.list" stripe style="width: 100%">
                                <el-table-column prop="url" label="访问页面">
                                </el-table-column>
                                <el-table-column prop="source" label="站内站外">
                                </el-table-column>
                                <el-table-column prop="from" label="来源">
                                </el-table-column>
                                <el-table-column prop="ip" label="IP">
                                </el-table-column>
                                <el-table-column prop="code" label="错误码">
                                </el-table-column>
                                <el-table-column prop="gmt_create" label="访问时间">
                                    <template slot-scope="scope">
                                        {{moment(scope.row.gmt_create)}}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="" label="操作" width="80">
                                    <template slot-scope="scope">
                                        <a :href="'/analysis/url/detail?url='+scope.row.url">
                                            <el-button type="primary" size="mini">详情</el-button>
                                        </a>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <el-pagination @current-change="handleCurrentChange" background :current-page.sync="form.page" :page-size="form.limit" layout="total, prev, pager, next" :total="tableList.total" style="padding:10px 0;text-align: right;">
                            </el-pagination>
                        </el-row>
                    </el-main>
            </el-container>
    </el-container>
    <script>
    var main = new Vue({
        el: '#Main',
        data: {
            tableList: {
                list: <%- list %>,
                total: <%- total %>,
            },
            form: {
                page: 1,
                limit: 10,
                time: '',
            },
            loading: false,
            pickerOptions2: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
        },
        methods: {
            handleCurrentChange(r) {
                this.getList();
            },
            getList() {
                var that = this;

                that.loading = true;
                var params = this.form;
                params._csrf = '<%- csrf %>';
                axios.post('/analysis/entryList', params)
                    .then(function(result) {
                        that.loading = false;
                        if (result.data.success) {
                            result.data.data.list.forEach(function(item) {
                                item.url = decodeURI(item.url);
                            })
                            that.tableList = result.data.data;
                        } else {
                            that.$message.error('获取列表出错');
                        }
                    });
            },
            moment(date){
                return moment(date).format('YYYY-MM-DD HH:mm:ss');
            },
            _export(){

                var params = this.form;
                var that = this;
                that.loading = true;
                params._csrf = '<%- csrf %>';
                axios.post('/analysis/entry_export', params)
                    .then(function(result) {
                        that.loading = false;
                        if(result.data.success){
                            window.location = result.data.data;
                        }
                    });
            }
        },
        mounted() {
            this.$nextTick(function() {
                document.getElementById('preLoading').style.display = 'none';
            });
        }
    });
    </script>
    <% include footer.html %>