"use strict";

const Controller = require("egg").Controller;
const _info = require("../../config/info").siteInfo;
const { env } = require("../../config/info").siteInfo;

class CaseController extends Controller {
  async typeList() {
    const { ctx } = this;

    const typeList = await ctx.service.case.typeList();
    const logs = await ctx.service.logs.listAll({
      model: "资讯中心-案例类别",
      page: 1,
      limit: 10,
    });

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;
    let hasEditPurview = 1;

    if (this.ctx.session.userInfo.type != 1) {
      let objService = await ctx.service.admin.getDataPurviewByService(
        this.ctx.session.userInfo.name
      );
      let objTrade = await ctx.service.admin.getDataPurviewByTrade(
        this.ctx.session.userInfo.name
      );

      if (objService.checked != 1 && objTrade.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (objService.publish != 1 && objTrade.publish != 1) {
        hasPubviewPurview = 0;
      }
      if (objService.edit != 1 && objTrade.edit != 1) {
        hasEditPurview = 0;
      }
    }

    await ctx.render("case_type", {
      site_title: _info.site_title,
      page_title: "案例类别",
      active: "7-2",
      list: JSON.stringify(typeList.list),
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
      hasEditPurview: hasEditPurview,
    });
  }

  async addType() {
    const { ctx } = this;

    const params = ctx.request.body;

    const result = await ctx.service.case.addType(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null,
      };
    } else {
      ctx.body = {
        fail: true,
        data: null,
      };
    }
  }

  async getType() {
    const { ctx } = this;

    const params = ctx.request.body;
    const info = await ctx.service.case.typeGet(params);

    ctx.body = {
      success: true,
      data: info.info,
    };
  }

  async typeDelete() {
    const { ctx } = this;

    const params = ctx.request.body;
    const result = await ctx.service.case.typeDelete(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null,
      };
    } else {
      ctx.body = {
        fail: true,
        data: null,
      };
    }
  }

  async typeEdit() {
    const { ctx } = this;

    const params = ctx.request.body;
    const result = await ctx.service.case.typeEdit(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null,
      };
    } else {
      ctx.body = {
        fail: true,
        data: null,
      };
    }
  }

  async caseList() {
    const { ctx } = this;

    // const params = {
    //   type: 0,
    //   page: 1,
    //   is_publish: 1
    // }

    // const typeList = await ctx.service.case.typeList();

    // let serviceList;
    // let tradeList;
    // if (this.ctx.session.userInfo.type != 1) {
    //   let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
    //   if (dataids == null) {
    //     dataids = {};
    //     dataids.ids = [0];
    //   }
    //   let paramsData = [];
    //   paramsData.ids = dataids.ids;
    //   params.allowServiceList = paramsData.ids;
    //   serviceList = await ctx.service.catalog.getSercataList(paramsData);

    //   dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
    //   if (dataids == null) {
    //     dataids = {};
    //     dataids.ids = [0];
    //   }
    //   paramsData = [];
    //   paramsData.ids = dataids.ids;
    //   params.allowTradeList = paramsData.ids;
    //   tradeList = await ctx.service.catalog.getTradeList(paramsData);
    // } else {
    //   serviceList = await ctx.service.catalog.getSercataList();
    //   tradeList = await ctx.service.catalog.getTradeList();
    // }

    // const result = await ctx.service.case.getList(params);
    // result.list.some(item => {
    //   item.is_publish == 1 ? item.is_publish = true : item.is_publish = false;
    // });

    // let hasCheckedPurview = 1;
    // let hasPubviewPurview = 1;
    // let hasEditPurview = 1;

    // if (this.ctx.session.userInfo.type != 1) {
    //   let objService = await ctx.service.admin.getDataPurviewByService(this.ctx.session.userInfo.name);
    //   let objTrade = await ctx.service.admin.getDataPurviewByTrade(this.ctx.session.userInfo.name);

    //   if (objService.checked != 1 && objTrade.checked != 1) {
    //     hasCheckedPurview = 0;
    //   }
    //   if (objService.publish != 1 && objTrade.publish != 1) {
    //     hasPubviewPurview = 0;
    //   }
    //   if (objService.edit != 1 && objTrade.edit != 1) {
    //     hasEditPurview = 0;
    //   }
    // }

    // await ctx.render('case_list', {
    //   site_title: _info.site_title,
    //   page_title: '资讯列表',
    //   active: '7-1',
    //   list: JSON.stringify(result.list),
    //   caseType: JSON.stringify(typeList.list),
    //   serviceList: JSON.stringify(serviceList.list),
    //   tradeList: JSON.stringify(tradeList.list),
    //   total: result.total,
    //   csrf: ctx.csrf,
    //   type: 'list',
    //   view_url: env[this.app.config.env].view_url,
    //   mview_url: env[this.app.config.env].mview_url,
    //   userInfo: ctx.session.userInfo,
    //   hasCheckedPurview: hasCheckedPurview,
    //   hasPubviewPurview: hasPubviewPurview,
    //   hasEditPurview: hasEditPurview,
    //   buIds: this.ctx.session.buIds ? JSON.stringify(this.ctx.session.buIds) : JSON.stringify([])
    // });
    await ctx.render("case_list", {
      site_title: _info.site_title,
      page_title: "资讯列表",
      active: "7-1",
      csrf: ctx.csrf,
      type: "",
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      userInfo: ctx.session.userInfo,
      userName: ctx.session.userInfo.name || ctx.session.userInfo.user_name,
      userType: this.ctx.session.userInfo.type,
      userId: this.ctx.session.userInfo.id,
      buIds: this.ctx.session.buIds ? JSON.stringify(this.ctx.session.buIds) : JSON.stringify([])
    });
  }

   async caseListWithCatalogId() {
    const { ctx } = this;

    await ctx.render("case_list", {
      site_title: _info.site_title,
      page_title: "资讯列表",
      active: "7-1",
      csrf: ctx.csrf,
      type: "",
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      userInfo: ctx.session.userInfo,
      userName: ctx.session.userInfo.name || ctx.session.userInfo.user_name,
      userType: this.ctx.session.userInfo.type,
      userId: this.ctx.session.userInfo.id,
      buIds: this.ctx.session.buIds ? JSON.stringify(this.ctx.session.buIds) : JSON.stringify([]),
      catalogId: this.ctx.params.catalogId
    });
  }

  async caseListBackup() {
    const { ctx } = this;

    const params = {
      type: 0,
      page: 1,
      is_publish: 1
    }

    const typeList = await ctx.service.case.typeList();

    let serviceList;
    let tradeList;
    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      params.allowServiceList = paramsData.ids;
      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      params.allowTradeList = paramsData.ids;
      tradeList = await ctx.service.catalog.getTradeList(paramsData);
    } else {
      serviceList = await ctx.service.catalog.getSercataList();
      tradeList = await ctx.service.catalog.getTradeList();
    }

    const result = await ctx.service.case.getList(params);
    result.list.some(item => {
      item.is_publish == 1 ? item.is_publish = true : item.is_publish = false;
    });

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;
    let hasEditPurview = 1;

    if (this.ctx.session.userInfo.type != 1) {
      let objService = await ctx.service.admin.getDataPurviewByService(this.ctx.session.userInfo.name);
      let objTrade = await ctx.service.admin.getDataPurviewByTrade(this.ctx.session.userInfo.name);

      if (objService.checked != 1 && objTrade.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (objService.publish != 1 && objTrade.publish != 1) {
        hasPubviewPurview = 0;
      }
      if (objService.edit != 1 && objTrade.edit != 1) {
        hasEditPurview = 0;
      }
    }

    await ctx.render('case_list_backup', {
      site_title: _info.site_title,
      page_title: '资讯列表',
      active: '7-1',
      list: JSON.stringify(result.list),
      caseType: JSON.stringify(typeList.list),
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      total: result.total,
      csrf: ctx.csrf,
      type: 'list',
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
      hasEditPurview: hasEditPurview,
      buIds: this.ctx.session.buIds ? JSON.stringify(this.ctx.session.buIds) : JSON.stringify([])
    });
  }

  async caseDraftList() {
    const { ctx } = this;

    // const params = {
    //   type: 0,
    //   page: 1,
    //   is_publish: 0
    // }

    // const typeList = await ctx.service.case.typeList();

    // let serviceList;
    // let tradeList;
    // if (this.ctx.session.userInfo.type != 1) {
    //   let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
    //   if (dataids == null) {
    //     dataids = {};
    //     dataids.ids = [0];
    //   }
    //   let paramsData = [];
    //   paramsData.ids = dataids.ids;
    //   params.allowServiceList = paramsData.ids;
    //   serviceList = await ctx.service.catalog.getSercataList(paramsData);

    //   dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
    //   if (dataids == null) {
    //     dataids = {};
    //     dataids.ids = [0];
    //   }
    //   paramsData = [];
    //   paramsData.ids = dataids.ids;
    //   params.allowTradeList = paramsData.ids;
    //   tradeList = await ctx.service.catalog.getTradeList(paramsData);
    // } else {
    //   serviceList = await ctx.service.catalog.getSercataList();
    //   tradeList = await ctx.service.catalog.getTradeList();
    // }
    // const result = await ctx.service.case.getList(params);

    // result.list.some(item => {
    //   item.is_publish == 1 ? item.is_publish = true : item.is_publish = false;
    // });

    // let hasCheckedPurview = 1;
    // let hasPubviewPurview = 1;
    // let hasEditPurview = 1;
    // if (this.ctx.session.userInfo.type != 1) {
    //   let objService = await ctx.service.admin.getDataPurviewByService(this.ctx.session.userInfo.name);
    //   let objTrade = await ctx.service.admin.getDataPurviewByTrade(this.ctx.session.userInfo.name);

    //   if (objService.checked != 1 && objTrade.checked != 1) {
    //     hasCheckedPurview = 0;
    //   }
    //   if (objService.publish != 1 && objTrade.publish != 1) {
    //     hasPubviewPurview = 0;
    //   }
    //   if (objService.edit != 1 && objTrade.edit != 1) {
    //     hasEditPurview = 0;
    //   }
    // }

    // await ctx.render('case_list', {
    //   site_title: _info.site_title,
    //   page_title: '草稿箱',
    //   active: '7-3',
    //   list: JSON.stringify(result.list),
    //   caseType: JSON.stringify(typeList.list),
    //   serviceList: JSON.stringify(serviceList.list),
    //   tradeList: JSON.stringify(tradeList.list),
    //   total: result.total,
    //   csrf: ctx.csrf,
    //   type: 'draft',
    //   view_url: env[this.app.config.env].view_url,
    //   mview_url: env[this.app.config.env].mview_url,
    //   userInfo: ctx.session.userInfo,
    //   hasCheckedPurview: hasCheckedPurview,
    //   hasPubviewPurview: hasPubviewPurview,
    //   hasEditPurview: hasEditPurview,
    //   buIds: this.ctx.session.buIds ? JSON.stringify(this.ctx.session.buIds) : JSON.stringify([])
    // });
    await ctx.render("case_list", {
      site_title: _info.site_title,
      page_title: "资讯列表",
      active: "7-3",
      csrf: ctx.csrf,
      type: "draft",
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      userInfo: ctx.session.userInfo,
      userName: ctx.session.userInfo.name || ctx.session.userInfo.user_name,
      userType: this.ctx.session.userInfo.type,
      userId: this.ctx.session.userInfo.id,
      buIds: this.ctx.session.buIds ? JSON.stringify(this.ctx.session.buIds) : JSON.stringify([])
    });
  }
  async caseDraftListBackup() {
    const { ctx } = this;

    const params = {
      type: 0,
      page: 1,
      is_publish: 0
    }

    const typeList = await ctx.service.case.typeList();

    let serviceList;
    let tradeList;
    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      params.allowServiceList = paramsData.ids;
      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      params.allowTradeList = paramsData.ids;
      tradeList = await ctx.service.catalog.getTradeList(paramsData);
    } else {
      serviceList = await ctx.service.catalog.getSercataList();
      tradeList = await ctx.service.catalog.getTradeList();
    }
    const result = await ctx.service.case.getList(params);

    result.list.some(item => {
      item.is_publish == 1 ? item.is_publish = true : item.is_publish = false;
    });

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;
    let hasEditPurview = 1;
    if (this.ctx.session.userInfo.type != 1) {
      let objService = await ctx.service.admin.getDataPurviewByService(this.ctx.session.userInfo.name);
      let objTrade = await ctx.service.admin.getDataPurviewByTrade(this.ctx.session.userInfo.name);

      if (objService.checked != 1 && objTrade.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (objService.publish != 1 && objTrade.publish != 1) {
        hasPubviewPurview = 0;
      }
      if (objService.edit != 1 && objTrade.edit != 1) {
        hasEditPurview = 0;
      }
    }

    await ctx.render('case_list_backup', {
      site_title: _info.site_title,
      page_title: '草稿箱',
      active: '7-3',
      list: JSON.stringify(result.list),
      caseType: JSON.stringify(typeList.list),
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      total: result.total,
      csrf: ctx.csrf,
      type: 'draft',
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
      hasEditPurview: hasEditPurview,
      buIds: this.ctx.session.buIds ? JSON.stringify(this.ctx.session.buIds) : JSON.stringify([])
    });
  }

  async getList() {
    const { ctx } = this;

    const params = ctx.request.body;

    // if (this.ctx.session.userInfo.type != 1) {
    //   let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');
    //   if (dataids == null) {
    //     dataids = {};
    //     dataids.ids = [0];
    //   }
    //   let paramsData = [];
    //   paramsData.ids = dataids.ids;
    //   params.allowServiceList = paramsData.ids;

    //   dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
    //   if (dataids == null) {
    //     dataids = {};
    //     dataids.ids = [0];
    //   }
    //   paramsData = [];
    //   paramsData.ids = dataids.ids;
    //   params.allowTradeList = paramsData.ids;
    // }

    // const result = await ctx.service.case.getList(params);

    // result.list.some(item => {
    //   item.is_publish == 1 ? item.is_publish = true : item.is_publish = false;
    // });

    const result = await ctx.service.external.qryCaseList(params);

    ctx.body = {
      success: true,
      data: result,
    };
  }
  

  async caseAdd() {
    const { ctx } = this;

    // let serviceList = null;
    // let tradeList = null;

    // if (this.ctx.session.userInfo.type != 1) {
    //   let dataids = await ctx.service.admin.getDataPurview(
    //     this.ctx.session.userInfo.name,
    //     "服务分类权限"
    //   );
    //   if (dataids == null || dataids.edit == 0) {
    //     dataids = {};
    //     dataids.ids = [0];
    //   }
    //   let paramsData = [];
    //   paramsData.ids = dataids.ids;
    //   serviceList = await ctx.service.catalog.getSercataList(paramsData);

    //   dataids = await ctx.service.admin.getDataPurview(
    //     this.ctx.session.userInfo.name,
    //     "行业分类权限"
    //   );
    //   if (dataids == null || dataids.edit == 0) {
    //     dataids = {};
    //     dataids.ids = [0];
    //   }
    //   paramsData = [];
    //   paramsData.ids = dataids.ids;
    //   tradeList = await ctx.service.catalog.getTradeList(paramsData);
    // } else {
    //   serviceList = await ctx.service.catalog.getSercataList();
    //   tradeList = await ctx.service.catalog.getTradeList();
    // }
    // const typeList = await ctx.service.case.typeList();

    // let hasCheckedPurview = 1;
    // let hasPubviewPurview = 1;

    // if (this.ctx.session.userInfo.type != 1) {
    //   let objService = await ctx.service.admin.getDataPurview(
    //     this.ctx.session.userInfo.name,
    //     "服务分类权限"
    //   );
    //   let objTrade = await ctx.service.admin.getDataPurview(
    //     this.ctx.session.userInfo.name,
    //     "行业分类权限"
    //   );

    //   if (objService.checked != 1 && objTrade.checked != 1) {
    //     hasCheckedPurview = 0;
    //   }
    //   if (objService.publish != 1 && objTrade.publish != 1) {
    //     hasPubviewPurview = 0;
    //   }
    // }

    // await ctx.render("case_add", {
    //   site_title: _info.site_title,
    //   ticMallHost: env[this.app.config.env].ticMallHost,
    //   page_title: "添加案例",
    //   active: "7-1",
    //   serviceList: JSON.stringify(serviceList.list),
    //   tradeList: JSON.stringify(tradeList.list),
    //   typeList: JSON.stringify(typeList.list),
    //   csrf: ctx.csrf,
    //   view_url: env[this.app.config.env].view_url,
    //   mview_url: env[this.app.config.env].mview_url,
    //   userInfo: ctx.session.userInfo,
    //   hasCheckedPurview: hasCheckedPurview,
    //   hasPubviewPurview: hasPubviewPurview,
    // });

    await ctx.render("case_add", {
      site_title: _info.site_title,
      page_title: "添加案例",
      active: "7-1",
      csrf: ctx.csrf,
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      userInfo: ctx.session.userInfo,
      userName: ctx.session.userInfo.name || ctx.session.userInfo.user_name,
      userType: this.ctx.session.userInfo.type,
      userId: this.ctx.session.userInfo.id,
    });
  }
  async caseAddBackup() {
    const { ctx } = this;

    let serviceList = null;
    let tradeList = null;

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(
        this.ctx.session.userInfo.name,
        "服务分类权限"
      );
      if (dataids == null || dataids.edit == 0) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      dataids = await ctx.service.admin.getDataPurview(
        this.ctx.session.userInfo.name,
        "行业分类权限"
      );
      if (dataids == null || dataids.edit == 0) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      tradeList = await ctx.service.catalog.getTradeList(paramsData);
    } else {
      serviceList = await ctx.service.catalog.getSercataList();
      tradeList = await ctx.service.catalog.getTradeList();
    }
    const typeList = await ctx.service.case.typeList();

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;

    if (this.ctx.session.userInfo.type != 1) {
      let objService = await ctx.service.admin.getDataPurview(
        this.ctx.session.userInfo.name,
        "服务分类权限"
      );
      let objTrade = await ctx.service.admin.getDataPurview(
        this.ctx.session.userInfo.name,
        "行业分类权限"
      );

      if (objService.checked != 1 && objTrade.checked != 1) {
        hasCheckedPurview = 0;
      }
      if (objService.publish != 1 && objTrade.publish != 1) {
        hasPubviewPurview = 0;
      }
    }

    await ctx.render("case_add_backup", {
      site_title: _info.site_title,
      ticMallHost: env[this.app.config.env].ticMallHost,
      page_title: "添加案例",
      active: "7-1",
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      typeList: JSON.stringify(typeList.list),
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
    });

  }
  

  async caseEdit() {
    const { ctx } = this;

    const id = ctx.params.id;

    // let serviceList = null;
    // let tradeList = null;

    // var checkflag = await ctx.service.admin.checkDataCanOperator("case", id);

    // if (!checkflag) {
    //   await ctx.render("error", {});
    //   return;
    // }

    // if (this.ctx.session.userInfo.type != 1) {
    //   let dataids = await ctx.service.admin.getDataPurview(
    //     this.ctx.session.userInfo.name,
    //     "服务分类权限"
    //   );
    //   if (dataids == null) {
    //     dataids = {};
    //     dataids.ids = [0];
    //   }
    //   let paramsData = [];
    //   paramsData.ids = dataids.ids;
    //   serviceList = await ctx.service.catalog.getSercataList(paramsData);

    //   dataids = await ctx.service.admin.getDataPurview(
    //     this.ctx.session.userInfo.name,
    //     "行业分类权限"
    //   );
    //   if (dataids == null) {
    //     dataids = {};
    //     dataids.ids = [0];
    //   }
    //   paramsData = [];
    //   paramsData.ids = dataids.ids;
    //   tradeList = await ctx.service.catalog.getTradeList(paramsData);
    // } else {
    //   serviceList = await ctx.service.catalog.getSercataList();
    //   tradeList = await ctx.service.catalog.getTradeList();
    // }

    // const typeList = await ctx.service.case.typeList();

    const detail = await ctx.service.case.getDetail(id);
    // const logs = await ctx.service.logs.listAll({
    //   type: "case",
    //   id: id,
    //   page: 1,
    //   limit: 10,
    // });

    // let tradeCataArr = [],
    //   serviceCataArr = [];
    // detail.detail.tradeCata.forEach((item) => {
    //   tradeCataArr.push(item.id);
    // });

    // detail.detail.serviceCata.forEach((item) => {
    //   serviceCataArr.push(item.id);
    // });

    // let hasCheckedPurview = 1;
    // let hasPubviewPurview = 1;

    // if (this.ctx.session.userInfo.type != 1) {
    //   if (this.ctx.session.userInfo.type != 1) {
    //     let objService = await ctx.service.admin.getDataPurview(
    //       this.ctx.session.userInfo.name,
    //       "服务分类权限"
    //     );
    //     let objTrade = await ctx.service.admin.getDataPurview(
    //       this.ctx.session.userInfo.name,
    //       "行业分类权限"
    //     );

    //     if (objService.checked != 1 && objTrade.checked != 1) {
    //       hasCheckedPurview = 0;
    //     }
    //     if (objService.publish != 1 && objTrade.publish != 1) {
    //       hasPubviewPurview = 0;
    //     }
    //   }
    // }

    // let checkflowResult = await ctx.service.checkflow.getDetail("case", id);
    // if (checkflowResult == null) {
    //   checkflowResult = {};
    // }

    // /* 获取推荐的sku列表 */
    // const skuList = await ctx.service.case.getSku(id);
    // const recommends = skuList.detail;

    // await ctx.render("case_edit", {
    //   site_title: _info.site_title,
    //   ticMallHost: env[this.app.config.env].ticMallHost,
    //   page_title: "编辑案例",
    //   active: "7-1",
    //   serviceList: JSON.stringify(serviceList.list),
    //   tradeList: JSON.stringify(tradeList.list),
    //   typeList: JSON.stringify(typeList.list),
    //   tradeCata: JSON.stringify(detail.detail.tradeCata),
    //   serviceCata: JSON.stringify(detail.detail.serviceCata),
    //   tradeCataArr: JSON.stringify(tradeCataArr),
    //   serviceCataArr: JSON.stringify(serviceCataArr),
    //   form: detail.detail,
    //   id: id,
    //   csrf: ctx.csrf,
    //   view_url: env[this.app.config.env].view_url,
    //   mview_url: env[this.app.config.env].mview_url,
    //   userInfo: ctx.session.userInfo,
    //   logs: JSON.stringify(logs),
    //   hasCheckedPurview: hasCheckedPurview,
    //   hasPubviewPurview: hasPubviewPurview,
    //   checkflowResult: JSON.stringify(checkflowResult),
    //   recommends,
    // });

    let active = '7-1';
    if (!detail.detail.is_publish) {
      active = '7-3';
    }

    await ctx.render("case_edit", {
      site_title: _info.site_title,
      page_title: "编辑案例",
      active,
      csrf: ctx.csrf,
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      userInfo: ctx.session.userInfo,
      userName: ctx.session.userInfo.name || ctx.session.userInfo.user_name,
      userType: this.ctx.session.userInfo.type,
      userId: this.ctx.session.userInfo.id,
      id
    });
  }
  async caseEditBackup() {
    const { ctx } = this;

    const id = ctx.params.id;

    let serviceList = null;
    let tradeList = null;

    var checkflag = await ctx.service.admin.checkDataCanOperator("case", id);

    if (!checkflag) {
      await ctx.render("error", {});
      return;
    }

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(
        this.ctx.session.userInfo.name,
        "服务分类权限"
      );
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      let paramsData = [];
      paramsData.ids = dataids.ids;
      serviceList = await ctx.service.catalog.getSercataList(paramsData);

      dataids = await ctx.service.admin.getDataPurview(
        this.ctx.session.userInfo.name,
        "行业分类权限"
      );
      if (dataids == null) {
        dataids = {};
        dataids.ids = [0];
      }
      paramsData = [];
      paramsData.ids = dataids.ids;
      tradeList = await ctx.service.catalog.getTradeList(paramsData);
    } else {
      serviceList = await ctx.service.catalog.getSercataList();
      tradeList = await ctx.service.catalog.getTradeList();
    }

    const typeList = await ctx.service.case.typeList();

    const detail = await ctx.service.case.getDetail(id);
    const logs = await ctx.service.logs.listAll({
      type: "case",
      id: id,
      page: 1,
      limit: 10,
    });

    let tradeCataArr = [],
      serviceCataArr = [];
    detail.detail.tradeCata.forEach((item) => {
      tradeCataArr.push(item.id);
    });

    detail.detail.serviceCata.forEach((item) => {
      serviceCataArr.push(item.id);
    });

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;

    if (this.ctx.session.userInfo.type != 1) {
      if (this.ctx.session.userInfo.type != 1) {
        let objService = await ctx.service.admin.getDataPurview(
          this.ctx.session.userInfo.name,
          "服务分类权限"
        );
        let objTrade = await ctx.service.admin.getDataPurview(
          this.ctx.session.userInfo.name,
          "行业分类权限"
        );

        if (objService.checked != 1 && objTrade.checked != 1) {
          hasCheckedPurview = 0;
        }
        if (objService.publish != 1 && objTrade.publish != 1) {
          hasPubviewPurview = 0;
        }
      }
    }

    let checkflowResult = await ctx.service.checkflow.getDetail("case", id);
    if (checkflowResult == null) {
      checkflowResult = {};
    }

    /* 获取推荐的sku列表 */
    const skuList = await ctx.service.case.getSku(id);
    const recommends = skuList.detail;

    await ctx.render("case_edit_backup", {
      site_title: _info.site_title,
      ticMallHost: env[this.app.config.env].ticMallHost,
      page_title: "编辑案例",
      active: "7-1",
      serviceList: JSON.stringify(serviceList.list),
      tradeList: JSON.stringify(tradeList.list),
      typeList: JSON.stringify(typeList.list),
      tradeCata: JSON.stringify(detail.detail.tradeCata),
      serviceCata: JSON.stringify(detail.detail.serviceCata),
      tradeCataArr: JSON.stringify(tradeCataArr),
      serviceCataArr: JSON.stringify(serviceCataArr),
      form: detail.detail,
      id: id,
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
      checkflowResult: JSON.stringify(checkflowResult),
      recommends,
    });
  }

  

  async caseSave() {
    const { ctx } = this;

    const params = ctx.request.body;

    if (params.id && parseInt(params.id) > 0) {
      //检查老数据是否有权限
      var checkflag = await ctx.service.admin.checkDataCanOperator(
        "case",
        params.id
      );
      if (!checkflag) {
        ctx.body = {
          fail: true,
          needReLogin: 1,
          loginUrl: "/login",
          msg: "请重新登录",
        };
        return;
      }
    }

    var trade_flag = await this.ctx.service.admin.checkUserTradeIsAllow(
      params.tradeList
    );
    var service_flag = await this.ctx.service.admin.checkUserServiceIsAllow(
      params.serviceList
    );

    if (!trade_flag || !service_flag) {
      //检查提交过来的数据是否有权限
      ctx.body = {
        fail: true,
        needReLogin: 1,
        loginUrl: "/login",
        msg: "请重新登录",
      };
      return;
    }

    if (this.ctx.session.userInfo.type != 1) {
      let objService = await this.ctx.service.admin.getDataPurview(
        this.ctx.session.userInfo.name,
        "服务分类权限"
      );
      let objTrade = await this.ctx.service.admin.getDataPurview(
        this.ctx.session.userInfo.name,
        "行业分类权限"
      );

      if (objService.edit != 1 && objTrade.edit != 1) {
        //没有编辑权限
        ctx.body = {
          fail: true,
          needReLogin: 1,
          loginUrl: "/login",
          msg: "请重新登录",
        };
        return;
      }
    }

    const result = await ctx.service.case.caseAdd(params);
    /* 保存推荐的sku */
    params.id = params.id ? params.id : result.id;
    const saveSku = await ctx.service.case.saveSku(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null,
        id: result.id,
      };
    } else {
      ctx.body = {
        fail: true,
        data: null,
      };
    }
  }

  async caseDelete() {
    const { ctx } = this;

    const id = ctx.request.body.id;
    const pageLink = ctx.request.body.pageLink;
    const result = await ctx.service.case.caseDelete(id, pageLink);

    var checkflag = await ctx.service.admin.checkDataCanOperator("case", id);
    if (!checkflag) {
      ctx.body = {
        fail: true,
        data: null,
      };
      return;
    }

    if (result.success) {
      ctx.body = {
        success: true,
        data: null,
      };
    } else {
      ctx.body = {
        fail: true,
        data: null,
      };
    }
  }

  async caseUpdatePublish() {
    const { ctx } = this;
    const id = ctx.request.body.id,
      is_publish = ctx.request.body.is_publish;

    var checkflag = await ctx.service.admin.checkDataCanOperator("case", id);
    if (!checkflag) {
      ctx.body = {
        fail: true,
        data: null,
      };
      return;
    }
    const result = await ctx.service.case.updatePublish({
      id: id,
      is_publish: is_publish,
    });

    if (result.success) {
      ctx.body = {
        success: true,
        data: null,
      };
    } else {
      ctx.body = {
        fail: true,
        data: null,
      };
    }
  }

  async caseModify() {
    const { ctx } = this;

    const params = ctx.request.body;
    const result = await ctx.service.case.caseModify(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null,
        id: result.id,
      };
    } else {
      ctx.body = {
        fail: true,
        data: null,
      };
    }
  }

  // 批量置顶、热门
  async caseModiyMultiple() {
    const { ctx } = this;

    const params = ctx.request.body;
    const result = await ctx.service.case.caseModiyMultiple(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null,
        id: result.id,
      };
    } else {
      ctx.body = {
        fail: true,
        data: null,
      };
    }
  }

  // 获取case详情
  async caseDtl() {
    const { ctx } = this;
    const params = ctx.request.body;
    const result = await ctx.service.case.caseDtl(params);

    ctx.body = result;
  }

  // 根据地址查找case详情
  // async findCaseForUrl() {
  //   const {
  //     ctx
  //   } = this;
  //   const params = ctx.request.body;
  //   function getDynamicParam(url) {
  //     console.log(url)
  //     let parts = url.split('/');
  //     let lastPart = parts.pop();
  //     let id =  0
  //     if (lastPart && lastPart.split('-').length && lastPart.split('-')[1]) {
  //       id = lastPart.split('-')[1].split('.')[0];
  //     }
  //     return id;
  //   }
  //   let id = getDynamicParam(params.skuUrl);

  //   if (!id) {
  //     ctx.body = {
  //       fail: true,
  //       msg: 'URL错误'
  //     }
  //     return;
  //   } else {
  //     const result = await ctx.service.case.getDetail(id);
  //     if (result) {
  //       ctx.body = {
  //         success: true,
  //         data: result.detail
  //       };
  //     } else {
  //       ctx.body = {
  //         fail: true,
  //         msg: result.msg
  //       };
  //     }
  //   }
  // }
}

module.exports = CaseController;
