* {
    margin: 0;
    padding: 0;
    outline: none;
}

*:not(input, textarea) {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
}

body {
    background: #4b4b4b;
    color: #000;
    font-size: 14px;
    line-height: 1.6;
    min-width: 320px;
}

.wrapper {
	position: relative;
    width: 100%;
    padding-top: 50px;
    padding-bottom: 50px;
    max-width: 768px;
    margin: 0 auto;
}
header,footer,div,section,ul,li{
	box-sizing: border-box;
}
header{
	position: fixed;
	width: 100%;
	height: 50px;
	padding: 15px 8px;
	background-color: #fff;
	z-index: 8;
	transition: all 0.4s;
}
.logo{
	float: left;
}
.logo img{
	height: 25px;
	margin-top: -3px;
}
.rnav{
	float: right;
	margin-right: 5px;
}
.hnav{
	position: absolute;
	width: 130px;
	height: auto;
	padding-top: 20px;
	margin-left: -105px;
	opacity: 0;
	z-index: 0;
	transition: all 0.4s;
	visibility: hidden;
}
.hnav.on{
	opacity: 1;
	z-index: 7;
	visibility: visible;
}
.hnav:before{
	content: '';
	display: block;
	width: 0;
	height: 0;
	overflow: hidden;
	position: absolute;
	top: 10px;
	right: 10px;
	border: 5px solid transparent;
	border-bottom-color: #fff;
}
.hnav ul{
	width: 100%;
	height: auto;
	overflow: hidden;
	background-color: #fff;
	border-radius: 8px;
	box-shadow: rgba(0,0,0,0.4) 0 0 6px;
}
.hnav ul li{
	float: left;
	width: 100%;
	height: 45px;
	line-height: 45px;
}
.hnav ul li a{
	display: block;
	color: #000;
	text-decoration: none;
}
.hnav ul li a i{
	float: left;
	width: 20px;
	height: 20px;
	background-position: 50% 50%;
	background-repeat: no-repeat;
	margin: 12px 15px;
}
.hnav ul li a i.icon1{
	background-image: url(../images/<EMAIL>);
	-webkit-background-size: 16px auto;
	background-size: 16px auto;
}
.hnav ul li a i.icon2{
	background-image: url(../images/<EMAIL>);
	-webkit-background-size: 14px auto;
	background-size: 14px auto;
}
.hnav ul li a i.icon3{
	background-image: url(../images/<EMAIL>);
	-webkit-background-size: 16px auto;
	background-size: 16px auto;
}
a.htel{
	float: right;
	width: 20px;
	height: 20px;
	background: url(../images/<EMAIL>) no-repeat 50% 50%;
	-webkit-background-size: auto 15px;
	background-size: auto 15px;
	margin-right: 25px;
}
.rnav a.btn{
	display: block;
	width: 20px;
	height: 20px;
	background: url(../images/<EMAIL>) no-repeat 50% 50%;
	-webkit-background-size: 16px auto;
	background-size: 16px auto;
}
.banner img{
	display: block;
	width: 100%;
}
.imain{
	padding: 12px 24px;
	overflow: hidden;
	z-index: 2;
}
.imain .item{
	float: left;
	width: 100%;
	margin: 12px 0;
	background-color: #fff;
	overflow: hidden;
	transition: all 0.4s;
}
.imain .item img{
	display: block;
	width: 100%;
}
.imain .item .c{
	padding: 20px 24px;
	font-size: 12px;
}
.imain .item .c h3{
	font-size: 20px;
	font-weight: normal;
	padding-bottom: 16px;
	line-height: 1.2;
}
.imain .item .c p{
	height: 76px;
}
.fnav{
	position: fixed;
	left: 0;
	bottom: 0;
	height: 50px;
	width: 100%;
	background-color: #292929;
	box-shadow: rgba(0,0,0,0.2) 0 -3px 6px;
	z-index: 9;
}
.fnav ul li{
	list-style: none;
	float: left;
	width: 25%;
	text-align: center;
	font-size: 12px;
}
.fnav ul li a{
	display: block;
	width: 100%;
	height: 50px;
	color: #a0a0a0;
	text-decoration: none;
	overflow: hidden;
}
.fnav ul li i{
	display: block;
	width: 24px;
	height: 24px;
	background-position: 50% 50%;
	background-repeat: no-repeat;
	margin: 6px auto 0;
}
.fnav ul li i.icon1{
	background-image: url(../images/<EMAIL>);
	-webkit-background-size: 21px auto;
	background-size: 21px auto;
}
.fnav ul li i.icon2{
	background-image: url(../images/<EMAIL>);
	-webkit-background-size: 21px auto;
	background-size: 21px auto;
}
.fnav ul li i.icon3{
	background-image: url(../images/<EMAIL>);
	-webkit-background-size: 21px auto;
	background-size: 21px auto;
}
.fnav ul li i.icon4{
	background-image: url(../images/<EMAIL>);
	-webkit-background-size: 21px auto;
	background-size: 21px auto;
}
footer{
	width: 100%;
	text-align: center;
	background-color: #181818;
	color: #494949;
	padding: 24px 0;
}
footer a{
	color: #494949;
	text-decoration: none;
}
footer a.gopc{
	display: block;
	width: 60px;
	height: 60px;
	margin: 0 auto;
	color: #848384;
	font-size: 14px;
}
footer .gopc i{
	display: block;
	width: 40px;
	height: 40px;
	margin: 0 auto;
	background: url(../images/<EMAIL>) no-repeat 50% 50%;
	-webkit-background-size: 40px auto;
	background-size: 40px auto;
	margin-bottom: 6px;
}
footer .finfo{
	font-size: 12px;
	padding-top: 16px;
	line-height: 2;
}
.swiper-pagination-bullet-active{
	background-color: #FFF;
}
.form{
	width: 100%;
	background-color: #fff;
	padding: 24px 0;
	box-sizing: border-box;
	overflow: hidden;
}
.form-tit{
	text-align: center;
	overflow: hidden;
	padding-bottom: 20px;
	width: calc(100% + 48px);
	margin-left: -24px;
}
.form-tit h3{
	font-size: 21px;
	font-weight: normal;
	color: #292929;
}
.form-tit p{
	font-size: 12px;
	color: #747474;
	width: 26em;
	margin: 0 auto;
	max-width: 100%;
}
.form span.imp{
	color:#FE660B;
}
.form-item,.form-button{
	width: 100%;
	padding: 5px 0 15px;
	overflow: hidden;
	position: relative;
}
.form-item .label{
	display: block;
	font-size: 14px;
	padding-bottom: 5px;
}
.form-item select,.form-item input[type=text],.form-item input[type=tel],.form-item input[type=email]{
	display: block;
	width: 100%;
	height: 40px;
	line-height: 40px;
	border: 1px solid #EDEDEE;
	border-radius: 3px;
	background-color: #FFF;
	font-size: 14px;
	padding: 0 12px;
	box-sizing: border-box;
	appearance:none;
	-moz-appearance:none;
	-webkit-appearance:none;
}
.form-item span.arrfix{
	content: '';
	position: absolute;
	display: block;
	width: 0;
	height: 0;
	border: 5px solid transparent;
	border-top-color:#878787;
	right: 10px;
	top: 50px;
	transition: transform 0.4s;
}
.form-item select:focus ~ span.arrfix{
	transform: rotate(180deg);
	margin-top: -5px;
}
.form-item textarea{
	display: block;
	width: 100%;
	height: 160px;
	padding: 5px;
	border: 1px solid #EDEDEE;
	border-radius: 3px;
	background-color: #FFF;
	resize: none;
	box-sizing: border-box;
	font-size: 14px;
	line-height: 1.6;
}
.form-item select.err,.form-item input.err,.form-item textarea.err{
	border-color: #FE660B;
}
.form-button button{
	display: block;
	width: 100%;
	height: 45px;
	font-size: 15px;
	background: #FE660B;
	color: #FFF;
	border: 0;
	margin-top: 40px;
}
.form-item input[type=checkbox]{
	appearance:none;
	-moz-appearance:none;
	-webkit-appearance:none;
	display: inline-block;
	width: 15px;
	height: 15px;
	border: 0;
	background-color: #DCDCDD;
	vertical-align: middle;
	margin-right: 6px;
	position: relative;
	top:-2px;
}
.form-item input[type=checkbox]:checked{
	background-color: #FE660B;
	border-radius: 3px;
	background: url(../../images/accpeted.png);
}
.steptip{
	width: 100%;
	height: auto;
	overflow: hidden;
	text-align: center;
	padding-top: 10px;
}
.steptip i{
	display: inline-block;
	width: 6px;
	height: 6px;
	border: 1px solid #FE660B;
	border-radius: 50%;
	margin: 0 6px;
}
.steptip i.on{
	background-color: #FE660B;
}
.swrapper{
	width: 200%;
}
.sws{
	float: left;
	width: 50%;
	padding: 0 24px;
	box-sizing: border-box;
	transition: all 0.4s;
}
p.ttip{
	display: none;
	font-size: 12px;
	color: #FE660B;
	position: absolute;
}

@media all and (min-width: 640px){
	.imain .item{
		width: calc(50% - 12px);
	}
	.imain .item:nth-child(2n){
		margin-left: 24px;
	}
}
.success {
	background: #fff;
	padding: 25% 5% 0 5%;
	text-align: center;
	min-height: 350px;
}
.success p {
	height: 75px;
	line-height: 33px;
	font-size: 16px;
	padding-bottom: 50px;
	text-indent: 2.4em;
	background: url(../images/icon_suc.png) no-repeat left top;
}
.success a {
	color: #fff;
	background: #FE660B;
	font-size: 12px;
	padding: 10px 20px;
	text-decoration: none;
	border-radius: 3px;
}