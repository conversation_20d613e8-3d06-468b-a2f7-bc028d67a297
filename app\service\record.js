'use strict';

const Service = require('egg').Service;
const moment = require('moment');

class RecordService extends Service {
    async list(params) {
        const { app } = this;
        const page = params.page || 1,
            limit = params.limit || 10;

        const url = params.url,
            ip = params.ip,
            date = params.date,
            getype = params.getype;

        let qarr = [];
        if(url){
            qarr.push('url LIKE "%'+url+'%"');
        }

        if(ip){
            qarr.push('ip LIKE "%'+ip+'%"');
        }

        if(date){
            if(date[0]){
                qarr.push('create_time>"'+moment(date[0]).format('YYYY-MM-DD HH:mm:ss')+'"');
            }

            if(date[1]){
                qarr.push('create_time<"'+moment(date[1]).format('YYYY-MM-DD HH:mm:ss')+'"');
            }
        }

        if(qarr.length > 0){
            qarr = qarr.join(' AND ');
            qarr = ' WHERE ' + qarr;
        }else{
            qarr = '';
        }

        let total = await app.mysql.query('SELECT id FROM click_record'+ qarr);
        let list = [];
        if(getype == 'export'){
            list = await app.mysql.query('SELECT * FROM click_record'+ qarr +' ORDER BY create_time DESC');
        }else{
            list = await app.mysql.query('SELECT * FROM click_record'+ qarr +' ORDER BY create_time DESC LIMIT ' + (page-1)*limit+','+limit);
        }
        
        total = total.length;
        return { list,total };
    }

}

module.exports = RecordService;