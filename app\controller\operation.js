'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo; 
const { env } = require('../../config/info').siteInfo;
class OperationController extends Controller {
  async hotList() {
    const {
      ctx
    } = this;
    const list = await ctx.service.operation.hotList({
      page: 1,
      limit: 10
    });

    const logs = await ctx.service.logs.listAll({
      model: '运维内容维护',
      page: 1,
      limit: 10
    });

    let hasCheckedPurview = 1;
    let hasPubviewPurview = 1;
    let hasEditPurview = 1;

    await ctx.render('hot', {
      site_title: _info.site_title,
      page_title: '运维维护',
      active: 9,
      csrf: ctx.csrf,
      list: JSON.stringify(list.list),
      length: list.list.length - 1,
      total: list.total,
      id: 6,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
      hasCheckedPurview: hasCheckedPurview,
      hasPubviewPurview: hasPubviewPurview,
      hasEditPurview: hasEditPurview
    });
  }

  async skuAdd() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    let url = params.skuUrl;
    url = url.split('/');
    const id = url[url.length - 1];

    if (typeof Number(id) != 'number') {
      ctx.body = {
        fail: true,
        msg: 'URL错误'
      }
      return;
    } else {
      const result = await ctx.service.operation.skuAdd(id);

      if (result.success) {
        ctx.body = {
          success: true,
          data: result.success
        };
      } else {
        ctx.body = {
          fail: true,
          msg: result.msg
        };
      }
    }
  }

  async skuDrop() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const id = params.sku_id;

    const result = await ctx.service.operation.skuDrop(id);

    if (result.success) {
      ctx.body = {
        success: true,
        data: result.success
      };
    } else {
      ctx.body = {
        fail: true,
        msg: result.msg
      };
    }
  }

  async getList() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;

    const result = await ctx.service.operation.hotList(params);

    ctx.body = {
      sucess: true,
      data: result
    };

  }
}

module.exports = OperationController;