'use strict';
const fs = require("fs")

module.exports = appInfo => {
  const config = exports = {};

  exports.bodyParser = {
    jsonLimit: '5mb',
    formLimit: '6mb',
  };

  // use for cookie sign key, should change to your own and keep security
  config.keys = appInfo.name + '_1523100884345_7455';


  // add your config here
  config.middleware = [];

  // 静态文件重定向
  config.siteFile = {
    "/favicon.ico": "/static/favicon.ico",
    "/baidu_verify_code-TiYvwQQQyz.html": fs.readFileSync(appInfo.baseDir + "/app/public/baidu_verify_code-TiYvwQQQyz.html"),
  };

  // 日志写入目录
  exports.logger = {
    dir: '/mnt/datadisk1/applogs/ticCMS',
  };

  config.view = {
    defaultExt: '.html',
    mapping: {
      '.ejs': 'ejs',
      '.html': 'ejs'
    }
  }

  /*
    mysql
  */
  // 环境判断
  const isTestEnv = ['test'].includes(appInfo.env);

  config.mysql = {
    client: isTestEnv ? {
      /* test */
      host: 'sgsdbcne2mysqlcnappdevmaster.mysql.database.chinacloudapi.cn',
      port: '3306',
      user: 'ticcms',
      password: 'jkl;4vyTO1cr',
      database: 'ticcms',
      useSSL: false,
      charset: 'utf8mb4' // 强制使用utf8mb4字符集
    } : {
      /* uat */
      host: 'sgsdbcne2flexiblemysqlticsnewuat.mysql.database.chinacloudapi.cn',
      port: '3306',
      user: 'ticcms@sgsdbcne2mysqlticsnewuat',
      password: '?s%^g>4B}p7-%Xv8F_h.a',
      database: 'ticcms',
      useSSL: false,
      charset: 'utf8mb4' // 强制使用utf8mb4字符集
    },
    app: true,
    agent: false
  };

  // 生产DB环境单独控制，不在这里控制

  /* */
  config.static = {
    maxAge: 0,
    prefix: '/static'
  }
  config.security = {
    csrf: {
      enable: false,
    }
  }

  /* files whitlist*/
  config.multipart = {
    whitelist: [
      '.jpg', '.jpeg', // image/jpeg
      '.png', // image/png, image/x-png
      '.gif', // image/gif
      '.bmp', // image/bmp
      '.wbmp', // image/vnd.wap.wbmp
      '.webp',
      '.tif',
      '.psd',
      '.pdf',
      // text
      '.svg',
      '.js', '.jsx',
      '.json',
      '.css', '.less',
      '.html', '.htm',
      '.xml',
      '.txt',
      // tar
      '.zip',
      '.7z',
      '.gz', '.tgz', '.gzip',
      // video
      '.mp3',
      '.mp4',
      '.avi',
      // document
      '.doc', '.docx',
      '.xls', '.xlsx',
      '.ppt', '.pptx',
    ],
    mode: 'file',
  };

  exports.sentry = {
    dsn: 'https://<EMAIL>/20',
  };

  return config;
};
