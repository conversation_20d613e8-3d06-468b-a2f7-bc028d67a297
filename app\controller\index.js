"use strict";

const Controller = require("egg").Controller;
const _info = require("../../config/info").siteInfo;
const { env } = require("../../config/info").siteInfo;
const moment = require("moment");

function subString(str, n) {
  var r = /[^\x00-\xff]/g;
  var m;

  if (str.replace(r, "**").length > n) {
    m = Math.floor(n / 2);

    for (var i = m, l = str.length; i < l; i++) {
      if (str.substr(0, i).replace(r, "**").length >= n) {
        var newStr = str.substr(0, i) + "...";
        return newStr;
      }
    }
  }

  return str;
}

class IndexController extends Controller {
  async index() {
    const { ctx } = this;
    // const fs = require('fs');
    // const detail = fs.readFileSync('./config/homepage.json', 'utf8');
    const detail = await ctx.service.index.getDetail();

    let outDetail = "";
    try {
      outDetail = JSON.parse(detail.detail.content);
    } catch (e) {
      outDetail = detail.detail.content;
    }
    const logs = await ctx.service.logs.listAll({
      model: "pc端首页配置",
      page: 1,
      limit: 10,
    });

    await ctx.render("index", {
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      detail: outDetail,
      site_title: _info.site_title,
      ticMallHost: env[this.app.config.env].ticMallHost,
      page_title: "pc端首页配置",
      active: "4-1",
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
    });
  }

  async indexMobile() {
    const { ctx } = this;
    const detail = await ctx.service.index.getDetailMobile();
    let serviceList = [];
    let outDetail = "";
    try {
      outDetail = JSON.parse(detail.detail.content);
      serviceList = detail.detail.serviceList;
      //serviceList = JSON.parse(detail.detail.serviceList)
    } catch (e) {
      outDetail = detail.detail.content;
      serviceList = detail.detail.serviceList;
    }

    const logs = await ctx.service.logs.listAll({
      model: "移动端首页配置",
      page: 1,
      limit: 10,
    });

    await ctx.render("index_mobile", {
      detail: outDetail,
      serviceList: serviceList,
      site_title: _info.site_title,
      ticMallHost: env[this.app.config.env].ticMallHost,
      page_title: "移动端首页设置",
      active: "4-2",
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
    });
  }

  async getSku() {
    const { ctx } = this;
    const params = ctx.request.body;
    let url = params.skuUrl;
    url = url.split("/");
    if (url.length && url.length >= 3) {
      const id = url[url.length - 1];
      const alias = url[url.length - 2];
      const sku = url[url.length - 3];

      if (!id || typeof Number(id) != "number" || sku !== "sku") {
        ctx.body = {
          fail: true,
          msg: "URL错误",
        };
        return;
      } else {
        const result = await ctx.service.index.getSku(id, alias);

        if (result.success) {
          ctx.body = {
            success: true,
            data: result.data,
          };
        } else {
          ctx.body = {
            fail: true,
            msg: result.msg,
          };
        }
      }
    } else {
      ctx.body = {
        fail: true,
        msg: "URL错误",
      };
    }
  }

  async getJf() {
    const { ctx } = this;
    const params = ctx.request.body;
    let url = params.url;
    url = url.split("/");
    const id = url[url.length - 1];

    if (typeof Number(id) != "number") {
      ctx.body = {
        fail: true,
        msg: "URL错误",
      };
      return;
    } else {
      const result = await ctx.service.index.getJf(id);

      if (result.success) {
        ctx.body = {
          success: true,
          data: result.data,
        };
      } else {
        ctx.body = {
          fail: true,
          msg: result.msg,
        };
      }
    }
  }

  async getNews() {
    const { ctx } = this;
    const params = ctx.request.body;
    let url = params.url;
    url = url.split("/");
    const nid = url[url.length - 1];
    const id = nid.replace("detail-", "").replace(".html", "");

    if (typeof Number(id) != "number") {
      ctx.body = {
        fail: true,
        msg: "URL错误",
      };
      return;
    } else {
      const result = await ctx.service.index.getNews(id);
      if (result.success) {
        const newsType = await ctx.service.index.getNewsType(
          result.data.catalog_id
        );
        // 获取html文本中的第一张图片
        let contentImg = "";
        result.data.content.replace(
          /<img [^>]*src=['"]([^'"]+)[^>]*>/,
          function (match, capture) {
            contentImg = capture;
          }
        );

        result.data.content = result.data.content.replace(/&lt;/g, "<");
        result.data.content = result.data.content.replace(/&gt;/g, ">");
        result.data.content = result.data.content.replace(/&nbsp;/g, " ");
        result.data.content = result.data.content.replace(/&mdash;/g, "—");
        result.data.content = subString(
          result.data.content.replace(/<.*?>/g, ""),
          90
        );
        result.data.time = moment(result.data.gmt_publish_time).format(
          "YYYY / MM / DD"
        );
        result.data.type = newsType.result.name;
        result.data.contentImg = contentImg;
        ctx.body = {
          success: true,
          data: result.data,
        };
      } else {
        ctx.body = {
          fail: true,
          msg: result.msg,
        };
      }
    }
  }

  async save() {
    const { ctx } = this;

    const params = ctx.request.body;
    // const fs = require('fs');

    // const path = './config/homepage.json';
    // fs.writeFileSync(path, JSON.stringify(params), 'utf8');

    const cndParams = {
      fileUrls: [env[this.app.config.env].view_url + '/'],
    };
    if (params.type == "updatesku") {
      const data = await ctx.service.index.getDetail();
      let content = JSON.parse(data.detail.content);
      let list = content.items;
      for (let item of list) {
        for (let floor of item.node) {
          for (let sku of floor.skus) {
            let sid = sku.id;
            if (sid) {
              const sr = await ctx.service.sku.getInfo(sku.id);
              sku.is_buy = sr.detail.is_buy;
            }
          }
        }
      }
      const result = await ctx.service.index.save(JSON.stringify(content));
      // 保存成功推送CDN刷新
      if (result.success) {
        await ctx.service.external.CDNRefresh(cndParams);
      }
      ctx.body = {
        success: result.success,
        data: null,
      };
    } else {
      const result = await ctx.service.index.save(JSON.stringify(params));
      // 保存成功推送CDN刷新
      if (result.success) {
        await ctx.service.external.CDNRefresh(cndParams);
      }
      ctx.body = {
        success: result.success,
        data: null,
      };
    }
  }

  async saveMobile() {
    const { ctx } = this;

    const params = ctx.request.body;
    // const fs = require('fs');

    // const path = './config/homepage.json';
    // fs.writeFileSync(path, JSON.stringify(params), 'utf8');
    if (params.type == "updatesku") {
      const data = await ctx.service.index.getDetail();
      let content = JSON.parse(data.detail.content);
      let list = content.items;
      for (let item of list) {
        for (let floor of item.node) {
          for (let sku of floor.skus) {
            let sid = sku.id;
            if (sid) {
              const sr = await ctx.service.sku.getInfo(sku.id);
              sku.is_buy = sr.detail.is_buy;
            }
          }
        }
      }
      const result = await ctx.service.index.save(JSON.stringify(content));
      ctx.body = {
        success: true,
        data: null,
      };
    } else {
      const result = await ctx.service.index.saveMobile(JSON.stringify(params));

      ctx.body = {
        success: true,
        data: null,
      };
    }
  }

  async coreSupplier() {
    const { ctx } = this;
    const detail = await ctx.service.index.getCoreSupplier();
    let outDetail = "";
    try {
      outDetail = JSON.parse(detail.detail.content);
    } catch (e) {
      outDetail = detail.detail.content;
    }

    const logs = await ctx.service.logs.listAll({
      model: "核心供应商",
      page: 1,
      limit: 10,
    });

    await ctx.render("coreSupplier", {
      detail: outDetail,
      site_title: _info.site_title,
      ticMallHost: env[this.app.config.env].ticMallHost,
      page_title: "核心供应商",
      active: "18",
      csrf: ctx.csrf,
      portal_url: env[this.app.config.env].portal_url,
      console_url: env[this.app.config.env].console_url,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
    });
  }

  async coreSupplierSave() {
    const { ctx } = this;

    const params = ctx.request.body;
    const result = await ctx.service.index.coreSupplierSave(
      JSON.stringify(params)
    );
    ctx.body = {
      success: true,
      data: null,
    };
  }
}

module.exports = IndexController;
