<% include header.html %>

<div class="myLocation">
    <div class="myLocationBox">
        <div class="myLocation-word1"><a href="">首页</a></div>
        <div class="myLocation-icon1"></div>
        <div class="myLocation-word2">“<%- keyword %>”的搜索结果</div>
    </div>
</div>

<div class="SearchBox">
    <div class="searchleftBox">
        <% if(total == 0){ %>
        <div class="sempty">
            <h3>很抱歉，没有为您找到对应的结果，建议您：</h3>
            <p>- 请检查输入的关键字是否有误</p>
            <p>- 请尝试其他关键字，或较常用的字词</p>
            <p>- 请点击页面右侧导航栏“您的行业”、”我们的服务“、“热点服务”进一步查找</p>
            <p>- 如果以上均未能给您提供帮助，请点击右侧“在线客服”，联系我们</p>
        </div>
        <% }else{ %>
        
        <div class="searchAlls">
            <span class="searchAll<% if(typeof type == 'undefined'){ %> clickActive<% } %>"><div class="angle angleShow"></div><a href="/preview/search?q=<%- keyword %>&page=1">全部</a></span>
            <% if(types[0]){ %>
            <span class="searchAll<% if(type == 0){ %> clickActive<% } %>"><div class="angle"></div><a href="/preview/search?q=<%- keyword %>&page=1&type=0">服务与解决方案</a></span>
            <% } %>

            <% if(types[1]){ %>
            <span class="searchAll<% if(type == 1){ %> clickActive<% } %>"><div class="angle"></div><a href="/preview/search?q=<%- keyword %>&page=1&type=1">合作案例</a></span>
            <% } %>

            <% if(types[2]){ %>
            <span class="searchAll<% if(type == 2){ %> clickActive<% } %>"><div class="angle"></div><a href="/preview/search?q=<%- keyword %>&page=1&type=2">新闻</a></span>
            <% } %>

            <% if(types[3]){ %>
            <span class="searchAl<% if(type == 3){ %> clickActive<% } %>"><div class="angle"></div><a href="/preview/search?q=<%- keyword %>&page=1&type=3">相关资源</a></span>
            <% } %>
        </div>

        <% if(type == 0){ %>
        <% if(cataTypes[0]){ %>
        <div class="searchRelative">
            <span class="searchRelativeT">相关行业：</span>
            <span class="searchRelativeC<% if(typeof pid=='undefined'){ %> clickActive<% } %>"><a href="/preview/search?q=<%- keyword %>&page=1&type=0">全部</a></span>
            <% for(let item of catas){ %>
            <% if(item.parent_id == 1){ %>  
            <span class="searchRelativeC<% if(pid==item.id){ %> clickActive<% } %>"><a href="/preview/search?q=<%- keyword %>&page=1&type=0&pid=<%- item.id %>"><%- item.name %></a></span>
            <% } %>
            <% } %>
        </div>
        <% } %>

        <% if(cataTypes[1]){ %>
        <div class="searcrServer">
            <span class="searchRelativeT">服务类型：</span>
            <span class="searchRelativeC<% if(typeof pid=='undefined'){ %> clickActive<% } %>"><a href="/preview/search?q=<%- keyword %>&page=1&type=0">全部</a></span>
            <% for(let item of catas){ %>
            <% if(item.parent_id == 2){ %>  
            <span class="searchRelativeC<% if(pid==item.id){ %> clickActive<% } %>"><a href="/preview/search?q=<%- keyword %>&page=1&type=0&pid=<%- item.id %>"><%- item.name %></a></span>
            <% } %>
            <% } %>
        </div>
        <% } %>
        <% } %>

        <div class="searchConAll">
            <% for(let item of list){ %>
                <% if(item.service_Id){ %>
                <div class="searchCon">
                    <div class="searchConB1">
                        <a class="searchCon1T" href="/preview/sku/<%- item.service_Id %>" target="_blank">
                            <%- item.title %>
                        </a>
                        <span class="searchCon1C">服务</span>
                        <span class="searchCon1T1">
                            <%- item.time %>
                        </span>
                    </div>
                    <div class="searchCon2">
                        <%- item.description %>
                    </div>
                </div>
                <% }else if(item.solutionId){ %>
                <div class="searchCon">
                    <div class="searchConB1">
                        <a class="searchCon1T" href="/preview/sku/<%- item.solutionId %>" target="_blank">
                            <%- item.title %>
                        </a>
                        <span class="searchCon1C">解决方案</span>
                        <span class="searchCon1T1">
                            <%- item.time %>
                        </span>
                    </div>
                    <div class="searchCon2">
                        <%- item.description %>
                    </div>
                </div>
                <% }else if(item.case_id){ %>
                <div class="searchCon">
                    <div class="searchConB1">
                        <a class="searchCon1T" href="/preview/case/detail-<%- item.case_id %>.html" target="_blank">
                            <%- item.title %>
                        </a>
                        <span class="searchCon1C">案例</span>
                        <span class="searchCon1T1">
                            <%- item.time %>
                        </span>
                    </div>
                    <div class="searchCon2">
                        <%- item.description %>
                    </div>
                </div>
                <% }else if(item.news_id){ %>
                <div class="searchCon">
                    <div class="searchConB1">
                        <a class="searchCon1T" href="/preview/news/detail-<%- item.news_id %>.html" target="_blank">
                            <%- item.title %>
                        </a>
                        <span class="searchCon1C">新闻</span>
                        <span class="searchCon1T1">
                            <%- item.time %>
                        </span>
                    </div>
                    <div class="searchCon2">
                        <%- item.description %>
                    </div>
                </div>
                <% }else if(item.resource_id){ %>
                <div class="searchCon">
                    <div class="searchConB1">
                        <a class="searchCon1T">
                            <%- item.title %>
                        </a>
                        <span class="searchCon1C">资源</span>
                        <span class="searchCon1T1">
                            <%- item.time %>
                        </span>
                    </div>
                    <div class="file_but1">
                        <a content="down" href="<%- item.path %>" download target="_blank">下载</a>
                    </div>
                </div>
                <% } %>
            <% } %>
            
            <% if(total > 10){ %>
            <div class="pageNums">
                
                <span class="lastpage">
                    <% if(page > 1){ %>
                    <a href="/preview/search?q=<%- keyword %>&page=<%- Number(page)-1 %><% if(typeof type == 'number'){ %>&type=<%- type %><% } %><% if(typeof pid == 'number'){ %>&pid=<%- pid %><% } %>">上一页</a>
                    <% }else{ %>
                        &nbsp;
                    <% } %>
                </span>
                
                <span class="pageNum">
                    第<%- page %>页
                </span>
                
                <span class="nextpage">
                    <% if(page < Math.ceil(total/10)){ %>
                    <a href="/preview/search?q=<%- keyword %>&page=<%- Number(page)+1 %><% if(typeof type == 'number'){ %>&type=<%- type %><% } %><% if(typeof pid == 'number'){ %>&pid=<%- pid %><% } %>">下一页</a>
                    <% }else{ %>
                        &nbsp;
                    <% } %>
                </span>
            </div>
            <% } %>
        </div>
        <% } %>
    </div>
    <div class="searchrightBox">
        <div class="searchrightBox1">
            <div class="your">
                <div class="yours">
                    <span class="your-word">您的行业</span>
                    <span class="dragbut">
                        <img src="/static/preview/images/jianhao.png" alt="" class="dragbut-icon">
                    </span>
                </div>

                <div class="your-cons first">
                    <div class="yourcon">
                        <ul>
                            <% if(tradeNavi && tradeNavi.length > 0){ %>
                            <% for (var item of tradeNavi){ %>
                            <li class="yourconLi">
                                <a href="/preview/industry/<%- item.alias %>/"><%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="your">
                <div class="yours">
                    <span class="your-word">我们的服务</span>
                    <span class="dragbut">
                        <img src="/static/preview/images/jiahao.png" alt="" class="dragbut-icon">
                    </span>
                </div>

                <div class="your-cons">
                    <div class="yourcon">
                        <ul>
                            <% if(serviceNavi && serviceNavi.length > 0){ %>
                            <% for (var item of serviceNavi){ %>
                            <li class="yourconLi">
                                <a href="/preview/service/<%- item.alias %>/"><%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="your">
                <div class="yours">
                    <span class="your-word">热点服务</span>
                    <span class="dragbut">
                        <img src="/static/preview/images/jiahao.png" alt="" class="dragbut-icon">
                    </span>
                </div>

                <div class="your-cons">
                    <div class="yourcon">
                        <ul>
                            <% if(hot && hot.length > 0){ %>
                            <% for (var item of hot){ %>
                            <li class="yourconLi">
                                <a href="/preview/sku/<%- item.id %>/"><%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<style>
    .sempty{
        width:560px;
        margin: 60px auto;
        font-size: 14px;
    }
    .sempty h3{
        font-size: 16px;
        border-bottom: 1px solid #CCC;
        text-align: center;
        padding: 10px 0;
    }
</style>
<script>
$(function () {

    var $_searchAll = $('.searchAll');
    $_searchAll.click(function () {
        $(this).addClass("clickActive").siblings(".searchAll").removeClass("clickActive");
        $(this).children(".angle").addClass("angleShow").end().siblings(".searchAll").children(".angle").removeClass("angleShow");
    })

    var $_searchRelativeC = $(".searchRelativeC");
    $_searchRelativeC.click(function () {
        $(this).addClass("clickActive").siblings(".searchRelativeC").removeClass("clickActive");
    })

    var $_drag = $('.dragbut-icon');
    var $_yours = $(".yours");

    $_yours.click(function () {

        var $_con = $(this).siblings(".your-cons");

        // for(var i=0;i<$_drag.length;i++){
        //     if($_drag.eq(i).attr("src") == "./images/"){
        //
        //     }
        // }

        if($_con.css("height")=="0px"){
            $_con.animate({"height":486}).end().parents(".your").siblings(".your").children(".your-cons").animate({"height":0});
            $(this).find(".dragbut-icon").attr("src",'/static/preview/images/jianhao.png');
            $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(0).attr("src","/static/preview/images/jiahao.png");
            $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(1).attr("src","/static/preview/images/jiahao.png")
        }else{
            $(this).find(".dragbut-icon").attr("src",'/static/preview/images/jiahao.png');
            $_con.animate({"height":0});
        }
    })
})
</script>

<% include footer.html %>