.location{
    margin-top: 32px;
    margin-bottom: 20px;
}
.locationLi{
    float: left;
}
.listBigBox{
    width: 1226px;margin: 20px auto 0;
}
.categorybox{
    width: 100%;height: 63px;background: #f8f8f8;
    /*padding-left: 37px;*/
    position: relative;
    /*position: absolute;*/
}
.categoryUl{
    width: 1226px;height: 100%;
    margin: 0 auto 20px;
    position: relative;
    padding: 0;
}
.categoryLi{
    width: auto;height: 100%;
    line-height: 63px;
    margin-right: 36px;
    float: left;
    font-size: 14px;
    color: #000000;
    list-style: none;
}
.clickActive{
    color: rgb(254,102,3);
    font-weight: bold;
}
.categoryLi:hover{
    color: rgb(254,102,2);
    cursor: pointer;
}
.categoryLi:first-child{
    color: #666666;
    cursor: initial;
}
.categoryLi:first-child:hover{
    color: #666666;
}
.keySearch{
    width: auto;height: 30px;
    /*border-bottom: 1px solid #eeeeee;*/
    /*margin-bottom: 20px;*/

    position: absolute;
    top: 16px;right: 0px;

}
.keySearch input{
    font-size: 12px;
    line-height: 14px;
}
.keyWord{
    float: left;
    line-height: 30px;
    font-size: 14px;
    color: #666666;
}
.listSearch{
    width: 38px;height: 26px;
    float: left;
}
.keyInput{

    height: 20px;
    outline: none;
    float: left;
    padding: 2px 0px 2px 10px;
    border: 1px solid rgb(254,102,2);
}
.listBox{
    width: 100%;
    position: relative;
    margin-bottom: 80px;
    margin-top: 20px;
}
.listUl{
    width: 100%;
    padding: 0;
    margin: 0;padding: 0;
}
.listLi{
    width: 100%;height: 142px;
    margin-bottom: 20px;
    list-style: none;
}
.list-img{
    width: 284px;height: 142px;
    float: left;
    background: url(../images/listing1.png) no-repeat;
    /*background-size: contain;*/
}
.listwordbox{
    width: 906px;height: 100%;
    padding-left: 20px;
    float: left;
}
.navSearchIconIcon1{
    width: 14px;height: 14px;
    background: url(../images/search14.png);
}
.listwordT{
    width: auto;
    height: auto;
    margin-top: 0px;
    font-size: 14px;
    display: block;
    cursor: pointer;
}
.listwordT:hover{
    text-decoration: underline;
    color: rgb(254,102,2);
}
.listwordS{
    margin-top: 10px;
    font-size: 12px;
    padding-bottom: 10px;
    color: #999999;
}
.listwordI{
    padding-top: 18px;
    font-size: 12px;
    border-top: 1px dashed #eeeeee;
    margin-bottom: 15px;
    color: #999999;
}
.listwordC1{
    width: 16px;height: 16px;background: #cccccc;
    display: inline-block;
    background: url(../images/icon1.png);
    position: absolute;
    margin-right: 10px;
    top: 5px;
    /*transform: translateY(-50%);*/
}
.listwordC2{
    width: auto;
    height: 21px;
    line-height: 21px;
    padding: 0 5px;
    background: #eeeeee;
    font-size: 12px;
    display: inline-block;
    margin-left: 16px;
    color: #999999;
}
.serverpages{
    left: 0;
    bottom: -48px;
    padding: 0;
    margin: 0;
}
.listwordC{
    height: 21px;
    position: relative;
}
.listwordTS{
    height: 55px;
    margin-bottom: 10px;
}
.listwordC2:nth-child(2){
    margin-left: 28px;
}