$(function () {
    $(".homeConCon4Li").hover(function () {
        var $this = $(this),
            $c = $this.children(".learnMore");
        $c.animate({
            opacity: 1,
            bottom: 120
        })
    }, function () {
        var $this = $(this),
            $c = $this.children(".learnMore");
        $c.animate({
            opacity: 0,
            bottom: 40
        })
    })

    $(".title").each(function () {
        var $that = $(this);
        $that.click(function () {
            var $this = $(this);
            var index = $this.index();
            var width = $this.width() + 50;
            var cw = $(document).width();

            var left = $this.position().left;
            $that.parent().children(".titleBor").css("width", width).animate({
                left: left
            });
            $this.addClass("active").siblings(".title").removeClass("active");
        });
    })

    $(".homeConCon5Li").on('mouseover', function () {
        $(this).children('img:first').stop().fadeOut().siblings('img').stop().fadeIn();
        $(this).siblings('li').each(function () {
            $(this).children('img:first').stop().fadeIn().siblings('img').stop().fadeOut();
        });
    });
})