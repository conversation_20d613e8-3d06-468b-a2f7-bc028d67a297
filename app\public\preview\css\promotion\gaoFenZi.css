.clearfix:after{
  content:".";
  display:block;
  height:0;
  clear:both;
  visibility:hidden;
}
.clearfix{
  *+height:1%;
}
ul, li {
  list-style: none;
}
img {
  border: 0;
}
.wrap {
  width: 1226px;
  margin: 0 auto;
}
.bg-white {
  background: #fff;
}
.bg-gray {
  background: #f9f9f9;
}
.gaoFenZi * {
  margin: 0 auto;
  padding: 0;
  font-style: normal;
}
.gaoFenZi .banner {
  max-width: 1920px;
  margin: auto;
  height: 425px;
  background: url("../../images/promotion/gaoFenZi/banner.jpg") no-repeat center center;
  position: relative;
}
.gaoFenZi .banner .phone {
  margin: 250px 0 0 750px;
}
.gaoFenZi .banner .serviceBtn {
  /*position: absolute;*/
  margin: 250px 0 0 757px;
  width: 200px;
  height: 35px;
  line-height: 35px;
  color: #fff;
  font-size: 18px;
  text-align: center;
  background: #fe7103;
  border: 0;
  display: inline-block;
  cursor: pointer;
}
.gaoFenZi .banner .serviceBtn:hover {
  background: #cc5500;
  transform: .5s;
}
.gaoFenZi .banner-bottom {
  max-width: 1920px;
  margin: auto;
  height: 56px;
  background: url("../../images/promotion/gaoFenZi/banner-bottom.jpg") no-repeat center center;
  position: relative;
}
.gaoFenZi .banner-bottom .link-content {
  width: 1100px;
  margin: auto;
  display: flex;
  justify-content: space-around;
  line-height: 56px;
}
.gaoFenZi .banner-bottom .link-text {
  color: #fff;
  cursor: pointer;
  display: inline-block;
  width: 175px;
}
.gaoFenZi .banner-bottom .link-text:hover {
  color: #cc5500;
  font-weight: bold;
}
.gaoFenZi .banner-bottom .link-text:nth-child(5) {
  margin-right: 5px;
}
.gaoFenZi .banner-bottom .link-text:nth-child(1) {
  text-align: center;
}
.gaoFenZi .title {
  text-align: center;
  margin-bottom: 50px;
  padding-top: 60px;
}
.gaoFenZi .title h2 {
  font-size: 30px;
  font-weight: normal;
  margin-bottom: 20px;
}
.gaoFenZi .title p {
  font-size: 14px;
  margin-bottom: 30px;
  margin: 0 0 10px 0;
  color: #999;
}
.gaoFenZi .title span {
  width: 120px;
  height: 3px;
  display: inline-block;
  background: #e5e5e5;
}
.gaoFenZi .service .tab li {
  float: left;
  width: 110px;
  height: 110px;
  border-top: 1px solid #ddd;
  border-right: 1px solid #ddd;
  text-align: center;
  cursor: pointer;
}
.gaoFenZi .service .tab li:first-child {
  border-left: 1px solid #ddd;
}
.gaoFenZi .service .tab li.active {
  border-top: 1px solid #fe7103;
  border-right: 1px solid #fe7103;
  background: #fe7103;
}
.gaoFenZi .service .tab li span {
  color: #000;
  display: block;
  margin-top: 0px;
}
.gaoFenZi .service .tab li.active span {
  color: #fff;
}
.gaoFenZi .service .tab li i {
  display: inline-block;
  width: 55px;
  height: 55px;
  margin-top: 15px;
}
.gaoFenZi .service .tab li i.icon1 {
  background: url('../../images/promotion/gaoFenZi/service/icon1-a.png') no-repeat center center;
}
.gaoFenZi .service .tab li.active i.icon1 {
  background: url('../../images/promotion/gaoFenZi/service/icon1.png') no-repeat center center;
}
.gaoFenZi .service .tab li i.icon2 {
  background: url('../../images/promotion/gaoFenZi/service/icon2-a.png') no-repeat center center;
}
.gaoFenZi .service .tab li.active i.icon2 {
  background: url('../../images/promotion/gaoFenZi/service/icon2.png') no-repeat center center;
}
.gaoFenZi .service .tab li i.icon3 {
  background: url('../../images/promotion/gaoFenZi/service/icon3-a.png') no-repeat center center;
}
.gaoFenZi .service .tab li.active i.icon3 {
  background: url('../../images/promotion/gaoFenZi/service/icon3.png') no-repeat center center;
}
.gaoFenZi .service .tab li i.icon4 {
  background: url('../../images/promotion/gaoFenZi/service/icon4-a.png') no-repeat center center;
}
.gaoFenZi .service .tab li.active i.icon4 {
  background: url('../../images/promotion/gaoFenZi/service/icon4.png') no-repeat center center;
}
.gaoFenZi .service .tab li i.icon5 {
  background: url('../../images/promotion/gaoFenZi/service/icon5-a.png') no-repeat center center;
}
.gaoFenZi .service .tab li.active i.icon5 {
  background: url('../../images/promotion/gaoFenZi/service/icon5.png') no-repeat center center;
}
.gaoFenZi .service .tab li i.icon6 {
  background: url('../../images/promotion/gaoFenZi/service/icon6-a.png') no-repeat center center;
}
.gaoFenZi .service .tab li.active i.icon6 {
  background: url('../../images/promotion/gaoFenZi/service/icon6.png') no-repeat center center;
}
.gaoFenZi .service .tab li i.icon7 {
  background: url('../../images/promotion/gaoFenZi/service/icon7-a.png') no-repeat center center;
}
.gaoFenZi .service .tab li.active i.icon7 {
  background: url('../../images/promotion/gaoFenZi/service/icon7.png') no-repeat center center;
}
.gaoFenZi .service .tab li i.icon8 {
  background: url('../../images/promotion/gaoFenZi/service/icon8-a.png') no-repeat center center;
}
.gaoFenZi .service .tab li.active i.icon8 {
  background: url('../../images/promotion/gaoFenZi/service/icon8.png') no-repeat center center;
}
.gaoFenZi .service .tab li i.icon9 {
  background: url('../../images/promotion/gaoFenZi/service/icon9-a.png') no-repeat center center;
}
.gaoFenZi .service .tab li.active i.icon9 {
  background: url('../../images/promotion/gaoFenZi/service/icon9.png') no-repeat center center;
}
.gaoFenZi .service .tab li i.icon10 {
  background: url('../../images/promotion/gaoFenZi/service/icon10-a.png') no-repeat center center;
}
.gaoFenZi .service .tab li.active i.icon10 {
  background: url('../../images/promotion/gaoFenZi/service/icon10.png') no-repeat center center;
}
.gaoFenZi .service .tab li i.icon11 {
  background: url('../../images/promotion/gaoFenZi/service/icon11-a.png') no-repeat center center;
}
.gaoFenZi .service .tab li.active i.icon11 {
  background: url('../../images/promotion/gaoFenZi/service/icon11.png') no-repeat center center;
}
.gaoFenZi .service-card {
  border-top: 1px solid #fe7103;
  height: 400px;
}
.gaoFenZi .service-card ul {
  margin-top: 30px;
}
.gaoFenZi .service-card li {
  display: none;
}
.gaoFenZi .service-card p {
  text-align: center;
  font-size: 20px;
  color: #000;
  height: 60px;
  line-height: 60px;
}
.gaoFenZi .service-card p:first-child {
  text-align: left;
}
.gaoFenZi .service-card .left {
  float: left;
  width: 400px;
}
.gaoFenZi .service-card .left img {
  width: 400px;
}
.gaoFenZi .service-card .middle {
  float: left;
  width: 325px;
  margin-left: 85px;
  position: relative;
  height: 335px
}
.gaoFenZi .service-card .middle .serviceBtn {
  width: 200px;
  height: 35px;
  line-height: 35px;
  color: #fff;
  font-size: 18px;
  text-align: center;
  background: #fe7103;
  border: 0;
  margin-top: 20px;
  display: inline-block;
  position: absolute;
  bottom: 0px;
  left: 0;
  cursor: pointer;
}
.gaoFenZi .service-card .middle .serviceBtn:hover {
  background: #cc5500;
  transform: .5s;
}
.gaoFenZi .service-card .right {
  float: right;
  width: 325px;
}
.gaoFenZi .service-card .middle p, .gaoFenZi .service-card .right p {
  border-bottom: 1px solid #ddd;
}
.gaoFenZi .service-card dl {
  margin-top: 35px;
}
.gaoFenZi .service-card dd {
  /* height: 26px; */
  line-height: 26px;
  font-size: 16px;
}
.gaoFenZi .service-card dd span {
  color: #fe7103;
}
.gaoFenZi .service-card dd em {
  color: #333;
}
.gaoFenZi .service-card .right {
  position: relative;
  height: 335px;
}
.gaoFenZi .service-card .right .items {
  margin-top: 35px;
}
.gaoFenZi .service-card .right .items i {
  color: #fe7103;
  font-size: 20px;
}
.gaoFenZi .service-card .right .items span {
  line-height: 26px;
  font-size: 16px;
  color: #333;
}
.gaoFenZi .service-card .right .serviceBtn {
  width: 200px;
  height: 35px;
  line-height: 35px;
  color: #fff;
  font-size: 18px;
  text-align: center;
  background: #fe7103;
  border: 0;
  margin-top: 20px;
  display: inline-block;
  position: absolute;
  bottom: 0px;
  left: 0;
  cursor: pointer;
}
.gaoFenZi .service-card .right .serviceBtn:hover {
  background: #cc5500;
  transform: .5s;
}
/* 应用领域 */
.gaoFenZi .territory {}
.gaoFenZi .territory li {
  float: left;
  width: 310px;
  height: 380px;;
  padding: 0 40px;
  margin-right: 25px;
  margin-bottom: 25px;
  border: 1px solid #ddd;
  background: #fff;
}
.gaoFenZi .territory li:hover {
  border: 1px solid #fe7103;
  box-shadow: 4px 4px 4px #ccc;
  background: #fefefe;
}
.gaoFenZi .territory li:hover p {
  color: #333;
}
.gaoFenZi .territory li:nth-child(3n) {
  margin-right: 0;
}
.gaoFenZi .territory li:last-child h2 {
  font-size: 24px;
  line-height: 30px;
}
.gaoFenZi .territory h2 {
  font-size: 36px;
  color: #fe7103;
  height: 60px;
  line-height: 60px;
  padding: 30px;
  border-bottom: 1px solid #ddd;
  text-align: center;
}
.gaoFenZi .territory p {
  padding-top: 35px;
  text-indent: 2em;
  line-height: 26px;
  color: #999;
  border-bottom: 1px solid #ddd;
  height: 135px;
}
.gaoFenZi .territory div {
  text-align: center;
  margin-top: 35px;
}
.gaoFenZi .territory .serviceBtn {
  display: inline-block;
  width: 150px;
  height: 27px;
  line-height: 27px;
  color: #fff;
  font-size: 16px;
  background: #4c4c4c;
  cursor: pointer;
}
.gaoFenZi .territory .serviceBtn:hover {
  background: #fe7103;
  transform: .8s;
}
/* 测试项目 */
.gaoFenZi .bg-test {
  background-image: url('../../images/promotion/gaoFenZi/test-item.jpg');
  background-size: cover;
}
.gaoFenZi .test table {
  border-top: 1px solid #b2b6b9;
  border-left: 1px solid #b2b6b9;
  width: 1150px;
  margin: 0 auto;
}
.gaoFenZi .test th, .gaoFenZi .test td {
  border-right: 1px solid #b2b6b9;
  border-bottom: 1px solid #b2b6b9;
  font-size: 14px;
}
.gaoFenZi .test tr:nth-child(even) {
  background: rgba(214, 217, 219, .3);
}
.gaoFenZi .test tr:nth-child(odd) {
  background: rgba(255, 255, 255, .3);
}
.gaoFenZi .test th {
  width: 115px;
  padding: 5px;
}
.gaoFenZi .test th a {
  color: #fe7103;
}
.gaoFenZi .test td a {
  color: #000;
}
.gaoFenZi .test td {
  padding: 5px 15px;
  text-align: left;
}
.gaoFenZi .test {
  text-align: center;
  position: relative;
}
.gaoFenZi .test .phone {
  position: absolute;
  bottom: 48px;
  right: 39px;
}
.gaoFenZi .test .serviceBtn {
  display: inline-block;
  margin: 45px 0;
  background: #fe7103;
  color: #fff;
  width: 275px;
  height: 33px;
  line-height: 33px;
  text-align: center;
  cursor: pointer;
}
.gaoFenZi .test .serviceBtn:hover {
  background: #cc5500;
}
/* 证书 */
/*.gaoFenZi .certificate {
  position: relative;
  height: 230px;
  width: 1226px;
  overflow: hidden;
  margin-bottom: 80px;
}
.gaoFenZi .certificate ul {
  height: 230px;
  position: absolute;
  left: 0;
  top: 0;
  width: 999em;
}
.gaoFenZi .certificate li {
  float: left;
  margin-right: 30px;
}
.gaoFenZi .certificate img {
  height: 230px;
  box-shadow: 3px 3px 3px #ddd;
  cursor: pointer;
}*/
/* 服务流程 */
.gaoFenZi .step {
  width: 1226px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 90px;
}
.gaoFenZi .step-item {
  width: 190px;
  height: 435px;
  box-sizing: border-box;
  padding-top: 50px;
  position: relative;
}
.gaoFenZi .step-item h3 {
  text-align: center;
  margin-bottom: 50px;
}
.gaoFenZi .step-item .step-content p {
  color: #999;
  text-align: center;
}
.gaoFenZi .step-item .next-icon {
  width: 100%;
  text-align: center;
  position: absolute;
  top: 230px;
}
.gaoFenZi .step-item .step-icon {
  width: 100%;
  text-align: center;
  position: absolute;
  top: 305px;
}
.gaoFenZi .step-item h3 span {
  color: #fe7103;
}
.gaoFenZi .step1 {
  width: 570px;
  padding: 50px 60px 0 60px;
  background: #f2f2f2;
}
.gaoFenZi .step1 h3 {
  margin-bottom: 25px;
}
.gaoFenZi .step-form {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  position: relative;
}
.gaoFenZi .step-form .phone {
  position: absolute;
  bottom: 0;
  right: 0;
}
.gaoFenZi .step-form input {
  width: 220px;
  margin: 0 0 10px 0;
  height: 35px;
  line-height: 35px;
  outline: 0;
  border: 1px solid #ddd;
  padding: 0 10px;
  font-size: 14px;
  box-sizing: border-box;
}
.gaoFenZi .step-form input:focus, #content:focus {
  border: 1px solid #fe7103;
  /*background: #ffe0cc;*/
}
#email, #content, .form-item {
  width: 450px;
}
#content {
  width: 450px;
  height: 115px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 3px;
  background-color: #FFF;
  resize: none;
  box-sizing: border-box;
  font-size: 14px;
  line-height: 1.6;
  outline: 0;
}
.gaoFenZi .step-form .form-item {
  padding: 5px 0;
}
.gaoFenZi .step-form .form-item .label{
  display: block;
  font-size: 14px;
}
.gaoFenZi .step-form .form-item input[type=checkbox]{
  appearance:none;
  -moz-appearance:none;
  -webkit-appearance:none;
  display: inline-block;
  width: 15px;
  height: 15px;
  border: 0;
  background-color: #DCDCDD;
  vertical-align: middle;
  margin: 0 6px 0 0;
  padding: 0;
  position: relative;
  top: -2px;
}
.gaoFenZi .step-form .form-item input[type=checkbox]:checked{
  background-color: #FE660B;
  border-radius: 3px;
  background: url(../../images/accpeted.png);
}
.gaoFenZi .step2 {
  background: #f5f5f5;
}
.gaoFenZi .step3 {
  background: #f8f8f8;
}
.gaoFenZi .step4 {
  background: #fafafa;
}
.gaoFenZi .step-item .serviceBtn {
  width: 180px;
  height: 35px;
  line-height: 35px;
  color: #fff;
  font-size: 18px;
  text-align: center;
  background: #fe7103;
  border: 0;
  display: inline-block;
  cursor: pointer;
  margin: 0;
}
.gaoFenZi .step-item .serviceBtn:hover {
  background: #cc5500;
  transform: .5s;
}
.gaoFenZi .step-item .more {
  border: 1px solid #fe7103;
  color: #fe7103;
  width: 180px;
  height: 35px;
  line-height: 35px;
  text-align: center;
  font-size: 18px;
  display: inline-block;
  box-sizing: border-box;
  margin: 0;
}
.gaoFenZi .step-item .more:hover {
  background: #fe7103;
  color: #fff;
}
/*.gaoFenZi .step {
  padding-bottom: 60px;
}
.gaoFenZi .step .item {
  width: 218px;
  height: 218px;
  border-radius: 50%;
  background: #f9f9f9;
  text-align: center;
  float: left;
}
.gaoFenZi .step .item:hover {
  background: #fe7103;
  color: #fff;
}
.gaoFenZi .step .item:hover p, .gaoFenZi .step .item:hover em {
  color: #fff;
}
.gaoFenZi .step .item i {
  display: block;
  width: 55px;
  height: 47px;
  margin-top: 15px;
}
.gaoFenZi .step .icon1 {
  background: url('../../images/promotion/gaoFenZi/step/icon1-a.png') no-repeat center center;
}
.gaoFenZi .step .acitve .icon1  {
  background: url('../../images/promotion/gaoFenZi/step/icon1.png') no-repeat center center;
}
.gaoFenZi .step .icon2 {
  background: url('../../images/promotion/gaoFenZi/step/icon2-a.png') no-repeat center center;
}
.gaoFenZi .step .acitve .icon2  {
  background: url('../../images/promotion/gaoFenZi/step/icon2.png') no-repeat center center;
}
.gaoFenZi .step .icon3 {
  background: url('../../images/promotion/gaoFenZi/step/icon3-a.png') no-repeat center center;
}
.gaoFenZi .step .acitve .icon3  {
  background: url('../../images/promotion/gaoFenZi/step/icon3.png') no-repeat center center;
}
.gaoFenZi .step .icon4 {
  background: url('../../images/promotion/gaoFenZi/step/icon4-a.png') no-repeat center center;
}
.gaoFenZi .step .acitve .icon4  {
  background: url('../../images/promotion/gaoFenZi/step/icon4.png') no-repeat center center;
}
.gaoFenZi .step .item em {
  font-style: normal;
  color: #000;
  font-size: 20px;
  margin-top: 20px;
  display: inline-block;
}
.gaoFenZi .step .item p {
 font-size: 16px;
 color: #666;
 line-break: 24px;
 margin-top: 20px;
}
.gaoFenZi .step .dot {
  height: 218px;
  line-height: 218px;
  margin: 0 20px;
  float: left;
}
.gaoFenZi .step .dot span {
  background: #fe7103;
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  margin: 0 5px;
}*/
/* 实验室设备仪器 */
.gaoFenZi .product {
  position: relative;
  height: 600px;
  overflow: hidden;
}
.gaoFenZi .product .productBox {
  width: 999em;
  height: 520px;
  position: absolute;
  left: 0;
  top: 0;
}
.gaoFenZi .product .productBox div {
  float: left;
  width: 1226px;
  height: 520px;
}
.gaoFenZi .product .productBox li {
  float: left;
  width: 288px;
  margin: 0 24px 14px 0;
}
.gaoFenZi .product .productBox li:nth-child(4n) {
  margin-right: 0;
}
.gaoFenZi .product .productBox img {
  width: 288px;
  height: 208px;
  /* border: 1px solid #ddd; */
}
.gaoFenZi .product .productBox img:hover {
  /* border: 1px solid #fe7103; */
}
.gaoFenZi .product .productBox span {
  width: 288px;
  height: 35px;
  line-height: 35px;
  text-align: center;
  display: inline-block;
  color: #666;
  font-size: 14px;
}
.gaoFenZi .product .productBtn {
  text-align: center;
  position: absolute;
  top: 530px;
  width: 1226px;
}
.gaoFenZi .product .productBtn span {
  width: 128px;
  height: 8px;
  display: inline-block;
  margin: 0 2px;
  background: #bfbfbf;
  cursor: pointer;
}
.gaoFenZi .product .productBtn span.active {
  background: #fe7103;
}
/* 更多服务推荐 */
.gaoFenZi .moreService {
  text-align: center;
  padding-bottom: 50px;
}
.gaoFenZi .moreService ul {
  padding-bottom: 50px;
}
.gaoFenZi .moreService li {
  float: left;
  background: #fff;
  width: 284px;
  text-align: center;
  margin-right: 30px;
  cursor: pointer;
  padding-bottom: 30px;
}
.gaoFenZi .moreService li:hover {
  box-shadow: 0 8px 16px rgba(0,0,0,0.2);
  transition: .4s;
  transform: translate3d(0, -3px, 0);
}
.gaoFenZi .moreService li:hover h2 {
  color: #fe7103;
}
.gaoFenZi .moreService li:hover p {
  color: #000;
}
.gaoFenZi .moreService li:last-child {
  margin-right: 0;
}
.gaoFenZi .moreService li img {
  width: 284px;
  height: 142px;
}
.gaoFenZi .moreService li h2 {
  height: auto;
  text-align: center;
  display: block;
  margin: 20px auto 0;
  font-size: 17px;
  line-height: 1.47;
  color: #000000;
  font-weight: normal;
}
.gaoFenZi .moreService li p {
  height: auto;
  text-align: center;
  display: block;
  margin-top: 4px;
  font-size: 13px;
  color: #999;
  letter-spacing: normal;
  padding: 0 15px;
}
.gaoFenZi .moreService .more {
  border: 1px solid #fe7103;
  color: #fe7103;
  width: 150px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 18px;
  display: inline-block;
}
.gaoFenZi .moreService .more:hover {
  background: #fe7103;
  color: #fff;
}
/* 案例集锦 */
.gaoFenZi .case {
  padding-bottom: 100px;
}
.gaoFenZi .case .tab {
  float: left;
  width: 220px;
}
.gaoFenZi .case .tab li {
  width: 220px;
  height: 75px;
  /*background: #aeaeae;*/
  background: linear-gradient(to right, #fafafa, #ebebeb);
  border-bottom: 3px solid #f9f9f9;
  color: #000;
  font-size: 16px;
  text-align: center;
  line-height: 75px;
  cursor: pointer;
}
.gaoFenZi .case .tab li span {
  border-bottom: 2px solid #ddd;
  padding-bottom: 5px;
}
.gaoFenZi .case .tab li.active {
  background: #fe7103;
  font-weight: normal;
  color: #fff;
}
.gaoFenZi .case .tab li.active span {
  border-bottom: 2px solid #fff;
  padding-bottom: 5px;
}
.gaoFenZi .case .card {
  float: left;
  border: 2px solid #fe7103;
  width: 942px;
  padding: 30px;
  height: 401px;
  overflow: hidden;
  position: relative;
}
.gaoFenZi .case .card .box {
  height: 1212px;
  width: 942px;
  position: absolute;
  top: 0;
  left: 0;
}
.gaoFenZi .case .card .item {
  height: 404px;
  padding-top: 30px;
  padding-bottom: 32px;
}
.gaoFenZi .case .card .left {
  float: left;
  width: 250px;
  height: 410px;
  padding-right: 30px;
  margin-right: 30px;
  border-right: 1px solid #ddd;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.gaoFenZi .case .card .left p {
  text-align: center;
  font-size: 18px;
  color: #000;
}
.gaoFenZi .case .card .left img {
  margin: 30px 0;
  display: inline-block;
  width: 250px;
  height: 280px;
}
/*.gaoFenZi .case .card .left .serviceBtn {
  display: inline-block;
  width: 125px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #fe7103;
  border: 1px solid #fe7103;
  font-size: 14px;
}
.gaoFenZi .case .card .left .serviceBtn:hover {
  background: #fe7103;
  color: #fff;
}*/
.gaoFenZi .case .card .right {
  float: left;
  width: 630px;
  margin-top:50px;
}
.gaoFenZi .case .card .right dl {
  padding-bottom: 45px;
}
.gaoFenZi .case .card .right dt {
  color: #000;
  font-size: 14px;
  padding-bottom: 5px;
}
.gaoFenZi .case .card .right dd {
  color: #7f7f7f;
  font-size: 14px;
  line-height: 20px;
}
.gaoFenZi .case .card .right .serviceBtn {
  position: absolute;
  right: 0;
  width: 200px;
  height: 35px;
  line-height: 35px;
  color: #fff;
  font-size: 18px;
  text-align: center;
  background: #fe7103;
  border: 0;
  margin-top: 20px;
  display: inline-block;
  cursor: pointer;
}
.gaoFenZi .case .card .right .serviceBtn:hover {
  background: #cc5500;
  transform: .5s;
}

/* 留言 */
.gaoFenZi .messageBox {
  height: 70px;
  line-height: 70px;
  color: #fff;
  font-size: 16px;
  background: #232323;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 10;
}
.gaoFenZi .messageBox label {
  margin-right: 15px;
}
.gaoFenZi .messageBox em {
  color: #f00;
}
.gaoFenZi .messageBox input {
  width: 135px;
  height: 35px;
  line-height: 35px;
  outline: 0;
}
.gaoFenZi .messageBox input.focus {
  border: 2px solid #cc5500;
  background: #ffe0cc;
}
.gaoFenZi .messageBox input.input-b {
  width: 370px;
}
.gaoFenZi .messageBox button {
  background: #fe7103;
  color: #fff;
  height: 35px;
  line-height: 35px;
  text-align: center;
  width: 120px;
  border: 0;
  cursor: pointer;
}
.gaoFenZi .messageBox .content {
  width: 180px;
  float: right;
  text-align: center;
  margin-top: 10px;
}
.gaoFenZi .messageBox .content em {
  display: block;
  height: 25px;
  line-height: 25px;
}
.gaoFenZi .messageBox .content .con1 {
  font-size: 16px;
  color: #c2c2c2;
}
.gaoFenZi .messageBox .content .con1 i {
  color: #fe7103;
}
.gaoFenZi .messageBox .content .con2 {
  font-size: 16px;
  color: #595959;
}
.gaoFenZi .consult {
  padding-bottom: 100px;
  height: 45px;
}
.gaoFenZi .consult label {
  float: left;
}
.gaoFenZi .consult label:first-child {
  margin-left: 60px;
}
.gaoFenZi .consult input {
  height: 45px;
  line-height: 45px;
  padding-left: 10px;
  border: 1px solid #ddd;
  background: #fff;
  margin-right: 20px;
  width: 180px;
}
.gaoFenZi .consult input:last-child {
  margin-right: 10px;
}
.gaoFenZi .consult input.input-big {
  width: 380px;
}
.gaoFenZi .consult input:focus {
  border: 1px solid #fe7103;
  outline: none;
}
.gaoFenZi .consult input.focus {
  border: 1px solid #fe7103;
}
.gaoFenZi .consult button {
  background: #fe7103;
  color: #fff;
  height: 45px;
  line-height: 45px;
  text-align: center;
  width: 130px;
  border: 0;
  cursor: pointer;
  margin-left: 0;
}
.gaoFenZi .consult p {
  text-align: center;
  color: #999;
  font-size: 16px;
  padding-top: 20px;
}
/* 弹窗 */
.pop {
  display: none;
}
.pop .layout {
  background: rgba(0, 0, 0, .7);
  width: 1000px;
  height: 500px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 11;
}
.pop .popBox {
  width: 300px;
  height: 160px;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 12;
  background: #fff;
  border-radius: 5px;
  margin: auto;
}
.pop .popBox .tit {
  text-align: left;
  background: #f5f5f5;
  padding-left: 20px;
  font-size: 14px;
  border-bottom: 1px solid #ddd;
  height: 35px;
  line-height: 35px;
  margin: 0;
  border-radius: 3px 3px 0 0;
}
.pop .popBox .cont {
  text-indent: 2em;
  font-size: 14px;
  padding: 20px;
}
.pop .popBox .btn {
  text-align: center;
  margin-top: 5px;
}
.pop .popBox .btn span {
  width: 50px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  border-radius: 3px;
  background: #fe7103;
  color: #fff;
  font-size: 14px;
  display: inline-block;
  cursor: pointer;
}
.pop .imgBox {
  width: 80%;
  height: 80%;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 13;
  margin: auto;
  text-align: center;
}
.pop .imgBox img {
  max-width: 100%;
  max-height: 100%;
}
.pop .close {
  position: fixed;
  right: 20px;
  top: 20px;
  color: #fff;
  z-index: 14;
  font-size: 15px;
  cursor: pointer;
  border: 1px solid #fff;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  text-align: center;
  line-height: 30px;
}
/*测试现场轮播图*/
/*.wrap{*/
/*  width:1200px;*/
/*  margin:10px auto;*/
/*}*/
.slide {
  height:500px;
  position: relative;
}
.slide li{
  position: absolute;
  left:200px;
  top:0;
}
.slide li img{
  width:100%;
}
.arrow{
  opacity: 0;
}
.prev,.next{
  width:76px;
  height:112px;
  position: absolute;
  top:50%;
  margin-top:-56px;
  background: url("../../images/promotion/gaoFenZi/testSite/prev.png") no-repeat;
  z-index: 99;
}
.next{
  right:0;
  background-image: url("../../images/promotion/gaoFenZi/testSite/next.png");
}
.advantage {
  padding-bottom: 50px;
  height: 270px;
}

.advantage li {
  float: left;
  border-left: 1px solid #ddd;
  padding: 0 30px;
  height: 270px;
  width: 245px;
}

.advantage li:last-child {
  border-right: 1px solid #ddd;
}

.advantage h3 {
  color: #fe7103;
  font-size: 40px;
  height: 55px;
  line-height: 55px;
}

.advantage h3 em {
  font-family: Arail, Roboto, "Helvetica Neue";
}

.advantage span {
  color: #333;
  font-size: 20px;
  display: block;
  padding: 25px 0;
  font-weight: bold;
}

.advantage p {
  color: #666;
  line-height: 24px;
  font-size: 16px;
}
/*证书*/
.gaoFenZi .certificate-content .title img {
  vertical-align: middle;
}

/*触发IM弹窗图*/
.pop-im {
  display: none;
  width: 473px;
  height: 320px;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 14;
  margin: auto;
  text-align: center;
  cursor: pointer;
  box-shadow: 0 0 5px #888888;
}
.pop-im .close {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  position: absolute;
  right: 5px;
  top: 6px;
}
