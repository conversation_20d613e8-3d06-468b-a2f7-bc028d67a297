'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const fs = require('fs');
const moment = require('moment');

function subString(str, n) {
  var r = /[^\x00-\xff]/g;
  var m;

  if (str.replace(r, '**').length > n) {
    m = Math.floor(n / 2);

    for (var i = m, l = str.length; i < l; i++) {
      if (str.substr(0, i).replace(r, '**').length >= n) {
        var newStr = str.substr(0, i) + '...';
        return newStr;
      }
    }
  }

  return str;
}

class ViewController extends Controller {
  async cataInfo() {
    const { ctx } = this;

    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();

    ctx.body = { tradeNavi: tradeNavi, serviceNavi: serviceNavi }
  }

  async index() {
    const { ctx } = this;

    const detail = await ctx.service.index.getDetail();

    ctx.body = { detail: detail.detail.content }
  }

  async catalog() {
    const { ctx } = this;
    const alias = ctx.params.alias;

    if (alias == 'training') {
      const detail = await ctx.service.catalog.getCatalogInfoByAliasTrain(alias);

      ctx.body = { detail: detail }
    } else {
      const detail = await ctx.service.catalog.getCatalogInfoByAlias(alias);

      ctx.body = { detail: detail }
    }
  }

  async catalogSkus() {
    const { ctx } = this;
    const alias = ctx.params.alias;
    const params = ctx.request.query;
    const page = params.page,
      limit = params.limit,
      name = params.name,
      id = params.id;

    const detail = await ctx.service.sku.getSkuByCata({ id: id, page: page, limit: limit, name: name });

    ctx.body = { detail: detail }
  }

  async catalogTrainSkus() {
    const { ctx } = this;
    const alias = ctx.params.alias;
    const params = ctx.request.query;
    const page = params.page,
      limit = params.limit,
      name = params.name,
      id = params.id,
      area = params.area,
      time = params.time,
      days = params.days,
      is_buy = params.is_buy;

    const detail = await ctx.service.sku.getSkuByTrainCata({ id: id, page: page, limit: limit, name: name, area: area, time: time, days: days, is_buy: is_buy });

    ctx.body = { detail: detail }
  }

  async skuInfo() {
    const { ctx } = this;
    const id = ctx.params.id, alias = ctx.params.alias || '';
    const detail = await ctx.service.sku.getInfo(id);

    if (alias) {
      const cataInfo = await ctx.service.catalog.getCatalogInfoByAlias(alias);

      detail.detail.catalog_name = cataInfo.info.name;
      detail.detail.catalogAlias = cataInfo.info.alias;
      detail.detail.catalogParent = cataInfo.info.parent_id;
    }
    ctx.body = { detail: detail }
  }

  async tags() {
    const { ctx } = this;
    const id = ctx.params.id;
    const detail = await ctx.service.sku.tagAdpt(id);
    ctx.body = { list: detail }
  }
}

module.exports = ViewController;