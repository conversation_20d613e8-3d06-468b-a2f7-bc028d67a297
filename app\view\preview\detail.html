<% include header.html %>
<div class="location detailLoca">
        <ul>
            <li class="locationLi">首页</li>
            <li class="locationLi icon"></li>
            <li class="locationLi"><%- detail.name %></li>
        </ul>
    </div>
<div class="detailBox">
    <div class="detailbannerBox">
        <div class="detailbanner">
            <img src="<%- detail.cover_img %>" />
        </div>
        <div class="bannerInfo">
            <div style="min-height:224px">
            <div class="word1"><%- detail.name %></div>
            <div class="word2"><%- detail.sub_title %></div>
            <div class="word3" style="min-height: 0"><%- detail.description.replace(/\n/g,'<br>') %></div>

            <% if(detail.type == 2){ %>
            <div class="word-day">
                <!--<div class="word-day-icon"></div>-->
                <% if(class_infoTime){ %>
                <div class="word-day-word">开课时间：<% for(var item of class_infoTime){ %> <span style="display:inline-block;white-space: nowrap"><%- item %></span> <% } %></div>
                <% } %>
                <% if(class_infoArea){ %>
                <div class="word-day-word">开课城市：<% for(var item of class_infoArea){ %> <span style="display:inline-block;white-space: nowrap"><%- item %></span> <% } %></div>
                <% } %>
                <% if(class_infoDay){ %>
                <div class="word-day-word">课程天数：<%- class_infoDay %>天</div>
                <% } %>
            </div>
            <% } %>
            </div>
            <% if(!detail.buy_url || detail.buy_url == 'https://' || detail.buy_url == 'http://' ){ %>
            <div class="word5">
                <a href="/preview/ticket" style="color:#fe6603" target="_blank">
                <div class="word5-1"></div>
                <div class="word5-2">咨询报价</div>
                </a>
            </div>
            <% }else if(detail.is_buy == 1){ %>
            <div class="zixun-dinggou">
                <a href="/preview/ticket" target="_blank">
                <div class="zixun">咨询报价</div>
                </a>
                <div class="dinggou">
                    <div class="deepen"></div>
                    <div class="dinggouword"><a href="<%- detail.buy_url %>" style="color:#FFF" target="_blank">在线下单</a></div>

                </div>
            </div>
            <% } %>
        </div>
    </div>


    <% for (var item of detail.content){ %>
    <div class="serverConBox">
        <% if(!item.hide_title){ %>
        <div class="serverTitleBox">
            <div class="serverCon" style="float:left;width:auto;"><%- item.title %></div>
        </div>
        <% } %>
        <div class="rtfc" style="clear:both;width:100%;height:auot;overflow:hidden;font-size:14px;line-height:2"><%- item.content %></div>
    </div>
    <% } %>
    
    <% if(detail.qa.length > 0){ %>
    <div class="serverConBox">
        <div class="serverTitleBox" style="margin-bottom:25px">
            <div class="serverCon">常见问题</div>
        </div>
        
        <% for (var item of detail.qa){ %>
        <div class="questionBox" style="clear:both;overflow:hidden;">
            <div class="question-word1"><%- item.question %></div>
            <div class="question-word2 rtfc" style="font-size: 14px;line-height: 2"><%- item.answer %></div>
            <div class="question-icon"></div>
        </div>
        <% } %>
    
    </div>
    <% } %>
    
    <% if(tagAdpt.length > 0){ %>
    <div class="recoment">
        <div class="square"></div>
        <div class="recoment-word1">为您推荐</div>
        <!--<div class="recoment-word2">换一批</div>
        <div class="recoment-icon"></div>-->
        <ul class="recomentUl">
            <% for(var item of tagAdpt){ %>
            <li class="recomentLi">
                <a href="/preview/sku/<%- item.id %>" target="_blank">
                <div class="recomentLi-img">
                    <img src="<%- item.thumb_img %>" alt="">
                </div>
                <div class="recomentLi-word"><%- item.name %></div>
                </a>
            </li>
            <% } %>
        </ul>
    </div>
    <% } %>

</div>
<% include footer.html %>