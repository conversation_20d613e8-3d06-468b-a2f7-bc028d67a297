'use strict';

const Service = require('egg').Service;
const moment = require('moment');
const request = require('request');
const crypto = require('crypto');
const _siteInfo = require('../../config/info').siteInfo;
const solrUrl = _siteInfo.solrUrl,
  solrPstr = _siteInfo.solrPstr,
  solrPid = _siteInfo.solrPid;

class RoleService extends Service {

  //获取列表
  async getRoleScopeByAdmin(roleids) {

    const {
      app
    } = this;
    let sql = 'SELECT role.id,role.name,role.is_effect,role.gmt_create,role.summary from role where (is_effect=1 and is_delete=0) ';

    if (roleids.length > 0) {
      sql = 'SELECT role.id,role.name,role.is_effect,role.gmt_create,role.summary from role where (is_effect=1 and is_delete=0) or (id in (' + roleids.join(',') + '))';
    }

    const list = await app.mysql.query(sql);

    return list;
  }

  //获取列表
  async getList(params) {
    if (!params) {
      params = {};
    }

    const {
      app
    } = this;
    let query = ['1=1'];

    if (params && params.name) {
      query.push('name LIKE "%' + params.name + '%"');
    }

    if (params && params.is_effect) {
      query.push('is_effect=' + params.is_effect);
    }

    if (params && params.is_delete != null) {
      query.push('is_delete=' + params.is_delete);
    }

    let page = params.page || 1,
      limit = params.limit || 10;
    let start = (page - 1) * limit;

    //id，用户名称，用户创建时间，用户分组名称，分组创建时间，分组状态
    const total = await app.mysql.query('SELECT id from role  WHERE ' + query.join(' AND '));

    const list = await app.mysql.query('SELECT role.id,role.name,role.is_effect,role.gmt_create,role.summary from role where ' + query.join(' AND ') + ' order by id asc LIMIT ' + start + ',' + limit);

    return {
      list: list,
      total: total.length,
      page: page,
      limit: limit
    }
  }

  async getDetail(id) {
    const {
      app
    } = this;
    const detail = await app.mysql.get('role', { id: id });
    detail.buList = await app.mysql.query(`select BU_NAME as name, BU_ID  as id  from  bu  where  STATE = 1`)
    const buIds = await app.mysql.query(`select distinct bu_ids from role_data_purview  where role_id = ${id}`)
    detail.bu_ids = ''
    if (buIds.length && buIds[0].bu_ids) detail.bu_ids = buIds[0].bu_ids
    return detail;
  }

  async getByName(name) {
    const {
      app
    } = this;

    const detail = await app.mysql.get('role', {
      name: name
    });
    return detail;
  }

  //返回审核组
  async getDataCheckedGroup() {
    const {
      app
    } = this;
    let sql = 'select role.id,role.name from role join checkflow on role.id=checkflow.receive_groupid and checkflow.is_overdue=0 group by role.id,role.name';
    const list = await app.mysql.query(sql);
    return list;
  }

  //返回送审分组
  async getDataSendCheckedGroup() {
    const {
      app
    } = this;
    let sql = "select distinct ifnull(admin.roleid_json,'') as val from checkflow  join admin on checkflow.send_admin_id=admin.id";
    const result = await app.mysql.query(sql);


    if (result.length > 0) {
      let ids = [];
      for (let resultItem of result) {

        if (resultItem.val == null || resultItem.val == '' || resultItem.val == '[]') {
          continue;
        }

        ids.push(resultItem.val.replace(/\[|\]/ig, ''));

      }
      if (ids.length == 0) {
        return null;
      }

      sql = 'select id,name from role where id in (' + ids.join(',') + ') and is_delete=0 and is_effect=1';

      var list = await app.mysql.query(sql);
      return list;
    }

    return null;
  }



  async getAdminCount(role_id) {
    const {
      app
    } = this;

    let query = ['is_delete=0'];
    //数据库存储格式如下：[1,2]
    var newRoleId = "," + role_id + ",";
    query.push("CONCAT(',',REPLACE(REPLACE(roleid_json,'[',''),']',''),',') like '%" + newRoleId + "%'");

    const total = await app.mysql.query('SELECT id from admin  WHERE ' + query.join(' AND '));

    const adminCount = total.length;

    return adminCount;
  }

  async roleAdd(params) {
    const {
      app
    } = this;

    const gmt_create = await moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
    const last_modify = this.ctx.session.userInfo.name;

    var admin_create_id = this.ctx.session.userInfo.id;


    const row = {
      name: params.name,
      is_effect: params.is_effect,
      summary: params.summary,
      admin_modify_id: admin_create_id
    }

    let result = '',
      sid = 0;
    let oldObjectData = [];
    let changeValue = [];
    var needUpdateApiData = false;
    if (params.id && params.id != 0) {
      sid = params.id;
      row.id = params.id;
      oldObjectData = await this.app.mysql.get('role', {
        id: sid
      }); //原始对象

      if (params.name != oldObjectData.name) {
        changeValue.push('修改组-名称');
      }

      if (params.summary != oldObjectData.summary) {
        changeValue.push('修改组-描述');
      }

      if (params.is_effect != oldObjectData.is_effect) {
        changeValue.push('修改组-状态');
        needUpdateApiData = true;
      }

      result = await app.mysql.beginTransactionScope(async conn => {
        await conn.update('role', row);

        if (changeValue.length > 0) {
          await conn.insert('logs', {
            user_name: this.ctx.session.userInfo.name,
            role_id: sid,
            log: changeValue.join(','),
            model: '组',
            name: row.title
          });
        }

        return {
          success: true
        };
      }, this.ctx);

    } else {
      row.gmt_create = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
      row.admin_create_id = admin_create_id;
      row.is_delete = 0;
      result = await app.mysql.beginTransactionScope(async conn => {
        const insertResult = await conn.insert('role', row);
        sid = insertResult.insertId;

        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          role_id: sid,
          log: '创建' + row.name,
          model: '组',
          name: row.name
        });

        return {
          success: true
        };
      }, this.ctx);
    }
    if (needUpdateApiData) {
      await this.service.role.updateAllUserApiData(params.id);
    }
    return {
      success: result.success,
      id: sid
    };
  }

  async updateAllUserApiDataBeforeAfterRole(list) {
    const {
      app
    } = this;

    for (let item of list) {
      this.service.admin.updateAdminCloudData(item);
    }

  }

  async updateAllUserApiData(id) {
    const {
      app
    } = this;


    const list = await app.mysql.query("SELECT id from admin  where is_delete=0 and type=2 and  FIND_IN_SET('" + id + "',replace(replace(roleid_json,'[',''),']',''))");

    for (let item of list) {
      this.service.admin.updateAdminCloudData(item.id);
    }

  }

  async checkNameIsExists(name, id) {
    const {
      app
    } = this;

    var query = ['is_delete=0'];
    query.push("name='" + name + "'");
    if (id != null && id > 0) {
      query.push("id!=" + id);
    }
    const total = await app.mysql.query('SELECT id from role  WHERE ' + query.join(' AND '));
    return total.length > 0;
  }

  async roleDelete(id) {
    const {
      app
    } = this;
    const result = await app.mysql.beginTransactionScope(async conn => {

      if (typeof id == 'object') {
        for (let item of id) {

          const info = await this.app.mysql.get('role', {
            id: item
          });
          await conn.insert('logs', {
            user_name: this.ctx.session.userInfo.name,
            role_id: item,
            log: '删除' + info.name,
            model: '组',
            name: info.name
          });

          await conn.update('role', {
            id: item,
            is_delete: 1,
            name: info.name + '(' + item + ')'
          });
        }
      } else {
        const info = await this.app.mysql.get('role', {
          id: id
        });
        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          role_id: id,
          log: '删除' + info.name,
          model: '组',
          name: info.name
        });

        await conn.update('role', {
          id: params.id,
          is_delete: 1,
          name: info.name + '(' + item + ')'
        });
      }

      return {
        success: true
      };
    }, this.ctx);

    // const success = result.affectedRows > 0;
    return {
      success: result.success
    };
  }

  async updateEffect(params) {
    const {
      app
    } = this;

    if (!params.id) {
      return {
        fail: true,
        msg: '编号不正确'
      };
    }
    const row = {
      id: params.id,
      is_effect: params.is_effect
    }

    const result = await app.mysql.beginTransactionScope(async conn => {
      const result = await app.mysql.update('role', row);
      let oldObjectData = await this.app.mysql.select('role', {
        where: {
          id: params.id
        }
      }); //原始对象
      await conn.insert('logs', {
        user_name: this.ctx.session.userInfo.name,
        role_id: params.id,
        log: '修改状态',
        model: '组',
        name: oldObjectData.name
      });
      return {
        success: true
      };
    }, this.ctx);


    await this.service.role.updateAllUserApiData(params.id);

    return {
      success: result.success
    };
  }

  async getPurview(role_id) {

    const {
      app
    } = this;
    let query = ['1=1'];
    query.push('role_id=' + role_id);
    let params = [];
    let page = params.page || 1,
      limit = params.limit || 10000;
    let start = (page - 1) * limit;

    const total = await app.mysql.query('SELECT id from role_menu_purview  WHERE ' + query.join(' AND '));

    const list = await app.mysql.query('SELECT * from role_menu_purview where ' + query.join(' AND ') + ' order by id asc LIMIT ' + start + ',' + limit);

    for (let item of list) {

      if (item.function_value == null) {
        item.function_value = [];
      } else {
        item.function_value = JSON.parse(item.function_value);
      }
    }

    return {
      list: list,
      total: total.length,
      page: page,
      limit: limit
    }
  }

  async getDataPurview(role_id, ruleName) {

    const {
      app
    } = this;

    const detail = await app.mysql.get('role_data_purview', {
      role_id: role_id,
      rule_name: ruleName
    });

    return detail;
  }

  async savePurview(params) {
    const {
      app
    } = this;

    if (!params.id) {
      return {
        fail: true,
        msg: '编号不正确'
      };
    }

    const gmt_create = await moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
    const bu_ids = params.bu_ids.join(',')
    const last_modify = this.ctx.session.userInfo.name;

    var admin_create_id = this.ctx.session.userInfo.id;

    var id = params.id;


    var look = params.dataPurview.indexOf('查看') != -1 ? 1 : 0;
    var edit = params.dataPurview.indexOf('编辑') != -1 ? 1 : 0;
    var publish = params.dataPurview.indexOf('发布') != -1 ? 1 : 0;
    var checked = params.dataPurview.indexOf('审核') != -1 ? 1 : 0;


    if (params.user.length == 0) {
      let tmpsql = 'select 1 from checkflow where receive_groupid=' + id + ' and tran_status=0 and is_overdue=0';
      const total = await app.mysql.query(tmpsql);
      if (total.length > 0) {
        return {
          success: false,
          msg: '该分组有审核数据待处理，选中用户至少勾选1个'
        };
      }
    } else if (checked == 0) {
      let tmpsql = 'select 1 from checkflow where receive_groupid=' + id + ' and tran_status=0 and is_overdue=0';
      const total = await app.mysql.query(tmpsql);
      if (total.length > 0) {
        return {
          success: false,
          msg: '该分组有审核数据待处理，分组权限审核权限必须保留。'
        };
      }
    } else {
      var origin_trade_json = [];
      var origin_service_json = [];
      let tmpsql = 'select  trade_json,service_json from checkflow where  receive_groupid=' + id + ' and tran_status=0 and is_overdue=0';
      const list = await app.mysql.query(tmpsql);
      for (var i = 0; i < list.length; i++) {
        var item = list[i];
        if (item.trade_json) {
          item.trade_json = JSON.parse(item.trade_json);

          for (var j = 0; j < item.trade_json.length; j++) {
            if (origin_trade_json.indexOf(item.trade_json[j]) == -1) {
              origin_trade_json.push(item.trade_json[j]);
            }
          }
        }
        if (item.service_json) {
          item.service_json = JSON.parse(item.service_json);
          for (var j = 0; j < item.service_json.length; j++) {
            if (origin_service_json.indexOf(item.service_json[j]) == -1) {
              origin_service_json.push(item.service_json[j]);
            }
          }
        }
      }

      for (var i = 0; i < origin_trade_json.length; i++) //收集原有的行业
      {
        if (!params.traceDataPurview.ids || params.traceDataPurview.ids.indexOf(origin_trade_json[i]) == -1) {
          return {
            success: false,
            msg: '该分组的行业有审核数据待处理'
          };
        }
      }


      for (var i = 0; i < origin_service_json.length; i++) //收集原有的服务
      {
        if (!params.serviceDataPurview.ids || params.serviceDataPurview.ids.indexOf(origin_service_json[i]) == -1) {
          return {
            success: false,
            msg: '该分组的服务有审核数据待处理'
          };
        }
      }
    }

    var oldUserList = [];

    const result = await app.mysql.beginTransactionScope(async conn => {

      //先处理用户角色，遍历所有有效的
      var oldUserTable = await app.mysql.query("SELECT id from admin  where is_delete=0 and type=2 and  FIND_IN_SET('" + params.id + "',replace(replace(roleid_json,'[',''),']',''))");

      for (var oldUserIndex in oldUserTable) {
        oldUserList.push(oldUserTable[oldUserIndex].id);
      }
      let sql = "update admin set roleid_json= concat('[',TRIM(BOTH ',' from replace(concat(',',replace(replace(ifnull(roleid_json,'[]'),'[',','),']',','),','),'," + params.id + ",','')),']') where is_delete=0"; //将所有用户该组的id都移除

      await app.mysql.query(sql);
      if (params.user && params.user.length > 0) {
        sql = "update admin set roleid_json=replace(replace(ifnull(roleid_json,'[]'),']','," + id + "]'),'[,','[') where  is_delete=0 and id in (" + params.user.join(',') + ")";
        await app.mysql.query(sql);
      }


      sql = 'delete from role_data_purview where role_id=' + id;
      await app.mysql.query(sql);

      if (params.serviceDataPurview.ids && params.serviceDataPurview.ids.length > 0) {
        await conn.insert('role_data_purview', {
          role_id: id,
          rule_name: '服务分类权限',
          table_name: 'category',
          ids: JSON.stringify(params.serviceDataPurview.ids),
          look: look,
          edit: edit,
          publish: publish,
          checked: checked,
          admin_create_id: admin_create_id,
          gmt_create: gmt_create,
          admin_modify_id: admin_create_id,
          bu_ids
        });
      }

      if (params.traceDataPurview.ids && params.traceDataPurview.ids.length > 0) {
        await conn.insert('role_data_purview', {
          role_id: id,
          rule_name: '行业分类权限',
          table_name: 'category',
          ids: JSON.stringify(params.traceDataPurview.ids),
          look: look,
          edit: edit,
          publish: publish,
          checked: checked,
          admin_create_id: admin_create_id,
          gmt_create: gmt_create,
          admin_modify_id: admin_create_id,
          bu_ids
        });
      }

      //菜单权限
      sql = 'delete from role_menu_purview where role_id=' + id;
      await app.mysql.query(sql);

      for (let menuIndex in params.menuList) {
        let menu = params.menuList[menuIndex];

        if (menu.role_purview_json && menu.role_purview_json.length > 0) {

          await conn.insert('role_menu_purview', {
            menu_id: menu.id,
            role_id: id,
            function_value: JSON.stringify(menu.role_purview_json),
            admin_create_id: admin_create_id,
            gmt_create: gmt_create,
            admin_modify_id: admin_create_id
          });
        }
      }


      await conn.insert('logs', {
        user_name: this.ctx.session.userInfo.name,
        role_id: params.id,
        log: '修改角色权限',
        model: '组',
        name: params.name
      });
      return {
        success: true
      };
    }, this.ctx);



    const newUserList = await app.mysql.query("SELECT id from admin  where is_delete=0 and type=2 and  FIND_IN_SET('" + params.id + "',replace(replace(roleid_json,'[',''),']',''))");


    for (var uIndex in newUserList) {
      var u = newUserList[uIndex];
      if (oldUserList.indexOf(u.id) == -1) {
        oldUserList.push(u.id);
      }
    }

    await this.service.role.updateAllUserApiDataBeforeAfterRole(oldUserList);

    return {
      success: result.success
    };
  }

  //根据行业id和服务id获取有审核权限的组
  async getCheckedRoleList(params) {

    const {
      app
    } = this;

    let sql = 'select * from catalog_relation ';
    if (params.type == 'news') //扩展
    {
      sql = sql + ' where news_id=' + params.id;
    } else if (params.type == 'sku_service') //扩展
    {
      sql = sql + ' where sku_id=' + params.id;
    } else if (params.type == 'sku_solution') //扩展
    {
      sql = sql + ' where sku_id=' + params.id;
    } else if (params.type == 'case') //扩展
    {
      sql = sql + ' where case_id=' + params.id;
    } else if (params.type == 'res') //扩展
    {
      sql = sql + ' where resource_id=' + params.id;
    }


    let query = ['1=1'];
    let trandeSql = [];
    let serviceSql = [];
    const relationList = await app.mysql.query(sql);

    for (let relationIndex in relationList) {
      let relationItem = relationList[relationIndex];

      if (params.type == 'sku_service') //sku存入的子id
      {

        const detail = await app.mysql.get('catalog', {
          id: relationItem.catalog_id
        });

        relationItem.catalog_id = detail.parent_id;


        if (relationItem.catalog_type == 1) {
          trandeSql.push(" find_in_set(" + relationItem.catalog_id + ",replace(replace(ids,'[',''),']','')) ");
        } else if (relationItem.catalog_type == 2) {
          serviceSql.push(" find_in_set(" + relationItem.catalog_id + ",replace(replace(ids,'[',''),']','')) ");
        }

      } else //新闻
      {
        if (relationItem.catalog_type == 1) {
          trandeSql.push(" find_in_set(" + relationItem.catalog_id + ",replace(replace(ids,'[',''),']','')) ");
        } else if (relationItem.catalog_type == 2) {
          serviceSql.push(" find_in_set(" + relationItem.catalog_id + ",replace(replace(ids,'[',''),']','')) ");
        }
      }
    }

    if (trandeSql.length == 0) {
      trandeSql.push('1=1');
    }

    if (serviceSql.length == 0) {
      serviceSql.push('1=1');
    }
    sql = "select role.id,role.name from ( select * from  ( select role_id from role_data_purview where rule_name='行业分类权限' and checked=1 and " + trandeSql.join(' AND ') + " GROUP BY role_id union all select role_id from role_data_purview where rule_name='服务分类权限'  and checked=1 and " + serviceSql.join(' AND ') + " GROUP BY role_id)t group by role_id having  count(*)>1 ) t join role on t.role_id=role.id where role.is_effect=1 and role.is_delete=0";

    const list = await app.mysql.query(sql);

    return list;
  }


  async getCheckedRoleListBySelectIDS(params) {

    const {
      app
    } = this;

    let trandeSql = [];
    let serviceSql = [];

    if (params.trade.length == 0) {
      trandeSql.push('1=1');
    } else {
      for (let valIndex in params.trade) {
        let valValue = params.trade[valIndex];
        trandeSql.push(" find_in_set(" + valValue + ",replace(replace(ids,'[',''),']','')) ");
      }
    }

    if (params.service.length == 0) {
      serviceSql.push('1=1');
    } else {
      for (let valIndex in params.service) {
        let valValue = params.service[valIndex];
        serviceSql.push(" find_in_set(" + valValue + ",replace(replace(ids,'[',''),']','')) ");
      }
    }

    let sql = "select role.id,role.name from ( select * from  ( select role_id from role_data_purview where rule_name='行业分类权限' and checked=1 and " + trandeSql.join(' AND ') + " GROUP BY role_id union all select role_id from role_data_purview where rule_name='服务分类权限'  and checked=1 and " + serviceSql.join(' AND ') + " GROUP BY role_id)t group by role_id having  count(*)>1 ) t join role on t.role_id=role.id where role.is_effect=1 and role.is_delete=0";


    const list = await app.mysql.query(sql);

    return list;
  }
}

module.exports = RoleService;