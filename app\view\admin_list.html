<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        权限配置
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                       用户配置
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row v-loading="loading">
                            <el-col :span="16">
                                <el-form :inline="true" :model="form">

									<el-form-item label="用户">
									    <el-input v-model="form.name" placeholder="请输入用户" style="width:14em"></el-input>
									</el-form-item>
									
                                    <el-form-item label="分组">
                                        <el-select v-model="form.role_id" placeholder="请选择" clearable>
                                            <el-option
                                                v-for="item in roleList"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.id">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>


                                    <el-form-item>
                                        <el-button type="primary" @click="search_list()">查询</el-button>
										 <el-button type="primary" @click="_default">重置</el-button>
                                    </el-form-item>
                                </el-form>
                            </el-col>

                            <el-col :span="8" style="text-align:right">
                                <el-button size="small" @click='_delMulti' >批量删除</el-button>
                                <a href="/admin/add" style="color:#FFF;" class="el-button el-button--primary el-button--mini">添加</a>
                            </el-col>

                            <el-table :data="tableList.list" stripe style="width: 100%" @selection-change="change">
                                <el-table-column type="selection" width="50"></el-table-column>
                                <el-table-column prop="name" label="用户名称">
                                </el-table-column>
								 <el-table-column prop="gmt_create" label="用户创建时间">
								</el-table-column>
								 <el-table-column label="分组名称">
									 <template slot-scope="scope">
										    <div v-for="(value, key, index) in scope.row.roleList" :key='key'>
													{{value.name}}
											</div>
									 </template>
								</el-table-column>
								<el-table-column prop="title" label="分组创建时间">
									 <template slot-scope="scope">
										<div v-for="(value, key, index) in scope.row.roleList" :key='key'>
												{{value.role_gmt_create}}
										</div>
									</template>
								</el-table-column>
								<el-table-column prop="title" label="分组状态">
									 <template slot-scope="scope">
											<div v-for="(value, key, index) in scope.row.roleList" :key='key'>
												<span v-if="value.is_effect == 1">开启</span>
												<span v-else>冻结</span>
											</div>
									</template>
								</el-table-column>
								
								<el-table-column fixed="right" label="操作" width="160" fixed="right">
								    <template slot-scope="scope">
										
								        <el-button-group>
										<a :href="'/admin/edit/'+scope.row.id" style="color:#FFF;" class="el-button el-button--primary el-button--mini">编辑信息</a>
								        </el-button-group>
								    </template>
								</el-table-column>
								
                            </el-table>
                            <el-pagination @current-change="handleCurrentChange" background :current-page.sync="form.page" :page-size="tableList.limit"
                                layout="total, prev, pager, next" :total="tableList.total" style="padding:10px 0;text-align: right;">
                            </el-pagination>
                        </el-row>
                    </el-main>
            </el-container>
    </el-container>
    <script>
    var main = new Vue({
        el: '#Main',
        data: {
            tableList: {
                list: <%- list %>,
                total: <%- total %>,
                page: 1,
                limit: 10
            },
            form: {
                name: '',
                role_id: '',
                page: 1
            },
            roleList: <%- roleList %>,
            loading: false,
            check: true,
            checkIds:[],
        },
		created: function () {
			var that = this;
		
			that.roleList.unshift({'id':-1,name:'无分组'});
	
		},
        methods: {
            handleCurrentChange(r) {
                this.form.page = r;
                this._getList();
            },
			 change(val){
			    var that = this;
			    that.checkIds = [];
			    val.forEach(function(item,i){
			        that.checkIds.push(item.id);
			    });
			
			    if(that.checkIds.length ==0 ){
			        that.check = true;
			    }else{
			        that.check = false;
			    }
			},
			search_list()
			{
				this.form.page =1;
				this._getList();
			},
            _getList(){
                var that = this;
                
                that.loading = true;
                var params = this.form;
                params._csrf = '<%- csrf %>';
                axios.post('/admin/list/get', params)
                    .then(function(result) {
                        that.loading = false;
                        if (result.data.success) {
                            that.tableList = result.data.data;
                        }else{
                            that.$message.error('获取列表出错');
                        }
                    });
            },
            _delMulti(){
                var that = this;
				
				if(that.checkIds.length==0)
				{
					that.$message.error('请先勾选数据');
					return;
				}
				
                this.$confirm('是否删除已选择的用户？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    that.loading = true;
                    axios.post('/admin/delete', { id: that.checkIds, _csrf: '<%- csrf %>' })
                        .then(function(result) {
                            that.loading = false;
                            if (result.data.success) {
                                that._default();
                                window.location.reload();
                            }
                            else
                            {
                            	
                            	that.$message.error(result.data.msg);
                            }
                        });
                }).catch(e => {
                    return e;
                });
            },
            
            _default() {

                this.form.name = '';
                this.form.role_id = '';
            }
		},  
        mounted(){
            this.$nextTick(function(){
                //this.types.unshift({id:0,name:'全部'});

                document.getElementById('preLoading').style.display = 'none';
            });
        }
    });
    </script>
    <% include footer.html %>