'use strict';

const Service = require('egg').Service;
const moment = require('moment');
const request = require('request');
const crypto = require('crypto');
const _siteInfo = require('../../config/info').siteInfo;
const solrUrl = _siteInfo.solrUrl,
	solrPstr = _siteInfo.solrPstr,
	solrPid = _siteInfo.solrPid;

class MenuService extends Service {


	//获取列表
	async getList(params) {

		const {
			app
		} = this;
		let query = ['1=1'];
		if (params && params.hidePurviewModule == 1) {
			query.push("menu2.id not in (32,33,34,35)");
		}

		let page = params.page || 1,
			limit = params.limit || 10000;
		let start = (page - 1) * limit;

		const total = await app.mysql.query('SELECT id from menu menu2  WHERE ' + query.join(' AND '));

		const list = await app.mysql.query('SELECT id,name,level_id,parent_id,url,function_json,ifnull((select count(*) from menu where parent_id=menu2.id),0) as nodeCount from  menu menu2 where ' + query.join(' AND ') + ' order by sort_id desc, id asc LIMIT ' + start + ',' + limit);

		for (let item of list) {

			if (item.function_json == null) {
				item.function_json = [];
			} else {
				item.function_json = item.function_json && JSON.parse(item.function_json);
			}
		}

		return {
			list: list,
			total: total.length,
			page: page,
			limit: limit
		}
	}

	//获取列表
	async getMenuUrl(menuIds) {
		const {
			app
		} = this;
		const result = await app.mysql.query('SELECT url from menu WHERE id in (' + menuIds.join(',') + ') and url is not null limit 1');


		for (var itemIndex in result) {
			return result[itemIndex].url;
		}
		return '';
	}

}

module.exports = MenuService;