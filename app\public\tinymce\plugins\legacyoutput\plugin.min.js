!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),o=tinymce.util.Tools.resolve("tinymce.util.Tools"),t=function(a){a.settings.inline_styles=!1,a.on("init",function(){var e,t,n,i;e=a,t="p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img",n=o.explode(e.settings.font_size_style_values),i=e.schema,e.formatter.register({alignleft:{selector:t,attributes:{align:"left"}},aligncenter:{selector:t,attributes:{align:"center"}},alignright:{selector:t,attributes:{align:"right"}},alignjustify:{selector:t,attributes:{align:"justify"}},bold:[{inline:"b",remove:"all"},{inline:"strong",remove:"all"},{inline:"span",styles:{fontWeight:"bold"}}],italic:[{inline:"i",remove:"all"},{inline:"em",remove:"all"},{inline:"span",styles:{fontStyle:"italic"}}],underline:[{inline:"u",remove:"all"},{inline:"span",styles:{textDecoration:"underline"},exact:!0}],strikethrough:[{inline:"strike",remove:"all"},{inline:"span",styles:{textDecoration:"line-through"},exact:!0}],fontname:{inline:"font",attributes:{face:"%value"}},fontsize:{inline:"font",attributes:{size:function(e){return o.inArray(n,e.value)+1}}},forecolor:{inline:"font",attributes:{color:"%value"}},hilitecolor:{inline:"font",styles:{backgroundColor:"%value"}}}),o.each("b,i,u,strike".split(","),function(e){i.addValidElements(e+"[*]")}),i.getElementRule("font")||i.addValidElements("font[face|size|color|style]"),o.each(t.split(","),function(e){var t=i.getElementRule(e);t&&(t.attributes.align||(t.attributes.align={},t.attributesOrder.push("align")))})})},n=function(i){i.addButton("fontsizeselect",function(){var o=[],e=i.settings.fontsizeFormats||"8pt=1 10pt=2 12pt=3 14pt=4 18pt=5 24pt=6 36pt=7";return i.$.each(e.split(" "),function(e,t){var n=t,i=t,a=t.split("=");1<a.length&&(n=a[0],i=a[1]),o.push({text:n,value:i})}),{type:"listbox",text:"Font Sizes",tooltip:"Font Sizes",values:o,fixedWidth:!0,onPostRender:function(){var t=this;i.on("NodeChange",function(){var e;(e=i.dom.getParent(i.selection.getNode(),"font"))?t.value(e.size):t.value("")})},onclick:function(e){e.control.settings.value&&i.execCommand("FontSize",!1,e.control.settings.value)}}}),i.addButton("fontselect",function(){var n=[],e=function(e){for(var t=(e=e.replace(/;$/,"").split(";")).length;t--;)e[t]=e[t].split("=");return e}(i.settings.font_formats||"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats");return i.$.each(e,function(e,t){n.push({text:{raw:t[0]},value:t[1],textStyle:-1===t[1].indexOf("dings")?"font-family:"+t[1]:""})}),{type:"listbox",text:"Font Family",tooltip:"Font Family",values:n,fixedWidth:!0,onPostRender:function(){var t=this;i.on("NodeChange",function(){var e;(e=i.dom.getParent(i.selection.getNode(),"font"))?t.value(e.face):t.value("")})},onselect:function(e){e.control.settings.value&&i.execCommand("FontName",!1,e.control.settings.value)}}})};e.add("legacyoutput",function(e){t(e),n(e)})}();