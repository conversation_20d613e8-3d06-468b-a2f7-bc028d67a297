'use strict';

const Controller = require('egg').Controller;
const fs = require('fs');
const path = require('path');
const sendToWormhole = require('stream-wormhole');
const awaitWriteStream = require('await-stream-ready').write;
const crypto = require('crypto'),
  readChunk = require('read-chunk'),
  fileType = require('file-type'),
  Jimp = require('jimp'),
  moment = require('moment');

async function makeThumb(path, name) {
  let buffer = readChunk.sync(path, 0, 4100),
    fname = name;

  let r = fileType(buffer);
  let thumbPath = '';
  if (r && r.ext && (r.ext == 'jpg' || r.ext == 'png')) {
    fname = fname.split('.');
    fname[0] += '_thumb';
    fname = fname.join('.');
    thumbPath = path.split('/');
    thumbPath.splice(thumbPath.length - 1, 1);
    thumbPath.push(fname);
    thumbPath = thumbPath.join('/');

    Jimp.read(path, function (err, obj) {
      if (err) throw err;
      obj.resize(284, 142).write(thumbPath); // save 
    });


  };

  return thumbPath;
}

const upDir = 'app/public/upload',
  staticDir = '/static/upload';

async function setDir() {
  const nowDate = moment().format('YYYY/MM/DD');

  const theDir = upDir + '/' + nowDate + '/';
  return theDir;
}

async function mkdirsSync(dirname) {
  if (fs.existsSync(dirname)) {
    return true;
  } else {
    if (mkdirsSync(path.dirname(dirname))) {
      fs.mkdirSync(dirname);
      return true;
    }
  }
}

async function setName() {
  let str = "",
    range = 8,
    arr = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

  for (var i = 0; i < range; i++) {
    let pos = Math.round(Math.random() * (arr.length - 1));
    str += arr[pos];
  }
  return str;
}

class UploadController extends Controller {
  async img() {
    const { ctx } = this;

    const parts = ctx.multipart({ autoFields: true });
    const files = [];
    const thisDir = await setDir();

    let stream;
    while ((stream = await parts()) != null) {
      const filename = stream.filename.toLowerCase();

      try {
        fs.readdirSync(thisDir);
      } catch (e) {
        mkdirsSync(thisDir);
      }
      const target = path.join(this.config.baseDir, thisDir, filename);
      const writeStream = fs.createWriteStream(target);
      try {
        await awaitWriteStream(stream.pipe(writeStream));
      } catch (err) {
        await sendToWormhole(stream);
        throw err;
      }

      let fileFormat = filename.split(".");
      fileFormat[0] = await setName();

      let newfilename = fileFormat.join('.');

      fs.renameSync(thisDir + filename, thisDir + newfilename);

      files.push(newfilename);
    }

    let thumb = await makeThumb(thisDir + files[0], files[0]);

    ctx.body = {
      success: true,
      fields: Object.keys(parts.field).map(key => ({ key, value: parts.field[key] })),
      //thumb: thumb.replace(upDir, staticDir),
      cover: thisDir.replace(upDir, staticDir) + files[0],
    }
  }

  async imgEditor() {
    const { ctx } = this;

    const parts = ctx.multipart({ autoFields: true });
    const files = [];
    const thisDir = await setDir();

    let stream;
    while ((stream = await parts()) != null) {
      const filename = stream.filename.toLowerCase();

      try {
        fs.readdirSync(thisDir);
      } catch (e) {
        mkdirsSync(thisDir);
      }
      const target = path.join(this.config.baseDir, thisDir, filename);
      const writeStream = fs.createWriteStream(target);
      try {
        await awaitWriteStream(stream.pipe(writeStream));
      } catch (err) {
        await sendToWormhole(stream);
        throw err;
      }

      let fileFormat = filename.split(".");
      fileFormat[0] = await setName();

      let newfilename = fileFormat.join('.');

      fs.renameSync(thisDir + filename, thisDir + newfilename);

      files.push(newfilename);
    }

    ctx.body = { error: false, location: thisDir.replace(upDir, staticDir) + files[0] }
  }

  async upFile() {
    const { ctx } = this;

    const parts = ctx.multipart({ autoFields: true });
    const files = [];
    const thisDir = await setDir();

    let stream;
    while ((stream = await parts()) != null) {
      const filename = stream.filename.toLowerCase();
      try {
        fs.readdirSync(thisDir);
      } catch (e) {
        mkdirsSync(thisDir);
      }

      const target = path.join(this.config.baseDir, thisDir, filename);
      const writeStream = fs.createWriteStream(target);
      try {
        await awaitWriteStream(stream.pipe(writeStream));
      } catch (err) {
        await sendToWormhole(stream);
        throw err;
      }

      /*
      let fileFormat = filename.split(".");
      fileFormat[0] = await setName();
      
      
      let newfilename = fileFormat.join('.');

      fs.renameSync(thisDir + filename, thisDir + newfilename);
      */

      files.push(filename);
    }

    ctx.body = { error: false, path: thisDir.replace(upDir, staticDir) + files[0] }
  }
}

module.exports = UploadController;