<% include header.html %>
  <el-container id="Main">
    <% include eheader.html %>
      <el-container>
        <% include eside.html %>
          <el-main>
            <el-row>
              <el-col :span="24">
                <el-breadcrumb separator-class="el-icon-arrow-right">
                  <el-breadcrumb-item>
                    <a href='/'>首页</a>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    设置
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    IM工具
                  </el-breadcrumb-item>
                </el-breadcrumb>
              </el-col>
            </el-row>
            <el-row v-loading="loading">
              <el-table :data="IMlist" border style="width: 100%">
                <el-table-column prop="paraDesc" label="工程名称" width="300"></el-table-column>
                <el-table-column prop="paraMemo" label="说明">
                    <template slot-scope="scope">
                        <div v-html="scope.row.paraMemo"></div>
                    </template>
                </el-table-column>
                <el-table-column prop="paraValue" label="状态" width="120">
                  <template slot-scope="scope">
                    <el-switch @change='modifyIMtool($event, scope.row.paraCode, scope.row.groupName)'
                      v-model="scope.row.paraValue" active-text="开" inactive-text="关" active-value="1"
                      inactive-value="0"></el-switch>
                  </template>
                </el-table-column>
              </el-table>
              <!-- <div class="logshow-wrapper" v-if="logs.list.length > 0">
                <el-row class="logshow" :class="{on: logshow}">
                  <span class="icon"><i class="el-icon-arrow-up" @click="showLog"></i></span>
                  <ul>
                    <li v-for="(item, index) of logs.list" :key='index'>{{item.createTime}}, {{item.userName}},
                      {{item.log}}</li>
                  </ul>
                  <el-pagination @current-change="qryLog" :current-page.sync="logParam.pageNum"
                    :page-size="logParam.pageRow" layout="total, prev, pager, next" :total="logs.total">
                  </el-pagination>
                </el-row>
              </div> -->
            </el-row>
          </el-main>
      </el-container>
  </el-container>
  <script>
    var main = new Vue({
      el: '#Main',
      data: {
        loading: false,
        logs: <%- logs %>,
        IMlist: <%- IMlist %>,
        logshow: false,
        logParam: {
          operatorType: 8,
          operatorId: '',
          pageRow: 10,
          pageNum: 1
        }
      },
      methods: {
        modifyIMtool(paraValue, paraCode, groupName) {
          this.loading = true
          if (paraValue == '1') {
            // 开启当前，关闭同组其他
            const imList = this.IMlist.filter(v => {
              return v.groupName === groupName
            })
            imList.forEach(v => {
              if (v.paraValue) {
                axios.post('/setting/IMtool/mod', {
                  paraCode: v.paraCode,
                  paraValue: v.paraCode === paraCode ? 1 : 0
                })
                  .then(result => {
                    // this.IMtoolQry()
                    setTimeout(() => {
                      window.location.reload()
                    }, 1000)
                  });
              }
            })
          } else {
            // 判断当前是不是最后一个
            const imList = this.IMlist.filter(v => {
              return v.groupName === groupName && v.paraValue === '1'
            })
            if (imList.length <= 1) {
              this.$confirm('您确认关闭最后一个开关，关闭后用户端将无任何在线聊天工具。', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then((res) => {
                // 关闭当前
                const params = {
                  paraCode, // key IS_LEADOO_CHAT IS_KF5_CHAT
                  paraValue // value
                }
                axios.post('/setting/IMtool/mod', params)
                  .then(result => {
                    if (result.data.resultCode === '0') {
                      // this.$message.success('修改成功');
                      setTimeout(() => {
                        window.location.reload()
                      }, 1000)
                    } else {
                      this.$message.error(result.data.resultMsg);
                    }
                  });
              }).catch(e => {
                this.loading = false
                window.location.reload()
                return e;
              });
            }
          }
        },
        showLog() {
          if (this.logs.list.length > 1) {
            this.logshow = !this.logshow;
          }
        },
        qryLog(page) {
          this.logParam.pageNum = page
          axios.post('/log/qry', this.logParam)
            .then(res => {
              if (res.data.resultCode === '0') {
                this.logs = {
                  list: res.data.data.items,
                  total: res.data.data.totalNum
                };
              }
            }).catch(e => {
              this.$message.error(e);
            });
        },
        IMtoolQry() {
          axios.post('/setting/IMtool/qry', { paraName: 'CHAT_SET' })
            .then(res => {
              this.imList = JSON.parse(JSON.stringify(res.data))
            }).catch(e => {
              this.$message.error(e);
            });
        }
      },
      mounted() {
        this.$nextTick(() => {
          document.getElementById('preLoading').style.display = 'none';
          this.qryLog()
        })
      }
    });
  </script>
  <% include footer.html %>