'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const { env } = require('../../config/info').siteInfo;
class NavigationController extends Controller {
  async navigationSerive() {
    const {
      ctx
    } = this;
    const navigations = await ctx.service.external.getNavication({
      catalogId: 2
    }, ctx.headers);
    const logs = await ctx.service.logs.listAll({
      model: '设置-服务导航',
      page: 1,
      limit: 10
    });

    await ctx.render('navigation_service', {
      navigations: JSON.stringify(navigations),
      site_title: _info.site_title,
      page_title: '服务导航',
      active: '16-1',
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      ticMallHost: env[this.app.config.env].ticMallHost,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
      hasCheckedPurview: 1,
      hasPubviewPurview: 1,
      hasEditPurview: 1
    });
  }

  async navigationIndustry() {
    const {
      ctx
    } = this;
    const navigations = await ctx.service.external.getNavication({
      catalogId: 1
    }, ctx.headers);
    const logs = await ctx.service.logs.listAll({
      model: '设置-行业导航',
      page: 1,
      limit: 10
    });

    await ctx.render('navigation_industry', {
      navigations: JSON.stringify(navigations),
      site_title: _info.site_title,
      page_title: '行业导航',
      active: '16-2',
      csrf: ctx.csrf,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      ticMallHost: env[this.app.config.env].ticMallHost,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
      hasCheckedPurview: 1,
      hasPubviewPurview: 1,
      hasEditPurview: 1
    });
  }

  async navigationSave() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.navigationSave(params, ctx.headers);

    ctx.body = result;
  }

  async navigationDelete() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.navigationDelete(params, ctx.headers);

    ctx.body = result;
  }

  async navigationQuery() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.getNavication(params, ctx.headers);

    ctx.body = result;
  }
}
module.exports = NavigationController;