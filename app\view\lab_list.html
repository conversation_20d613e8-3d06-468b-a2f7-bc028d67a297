<% include header.html %>
  <el-container id="Main" v-loading="loading">
    <% include eheader.html %>
      <el-container>
        <% include eside.html %>
          <el-main>
            <el-row>
              <el-col :span="24">
                <el-breadcrumb separator-class="el-icon-arrow-right">
                  <el-breadcrumb-item>
                    <a href='/'>首页</a>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    实验室
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    实验室列表
                  </el-breadcrumb-item>
                </el-breadcrumb>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-button @click="dialogVisible = true" type='primary' style="margin-bottom: 20px;">添加实验室</el-button>
                <el-button @click='handleExport' type='primary' style="margin-bottom: 20px; float: right;" >导出</el-button>
              </el-col>
                <el-col :span="24">
                <el-table :data="labList" style="width: 100%">
                  <el-table-column prop="id" label="ID." width='40'>
                  </el-table-column>
                  <el-table-column prop="name" label="实验室名称">
                    <template slot-scope="scope">
                      <div class="name">
                        <span>{{scope.row.name}}</span>
                        <i v-if='scope.row.is_copy'>复</i>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="type" label="链接">
                    <template slot-scope="scope">
                      <a target="_blank" :href='view_url + "/lab/" + scope.row.alias'
                        v-if='scope.row.type === 1'>{{view_url + "/lab/" + scope.row.alias}}</a>
                      <a target="_blank" :href=' mview_url + "/lab?alias=" + scope.row.alias' v-else>{{ mview_url +
                        "/lab?alias=" + scope.row.alias}}</a>
                    </template>
                  </el-table-column>
                  <el-table-column prop="is_publish" label="是否上下架" width='100' align='center'>
                    <template slot-scope="scope">
                      {{scope.row.is_publish ? '是' : '否'}}
                    </template>
                  </el-table-column>
                  <el-table-column prop="type" label="PC/移动端" width='100' align='center'>
                    <template slot-scope="scope">
                      {{scope.row.type === 1 ? 'PC' : '移动'}}
                    </template>
                  </el-table-column>
                  <el-table-column prop="create_time" label="创建时间" width='140' align='center'>
                  </el-table-column>
                  <el-table-column prop="modify_time" label="修改时间" width='140' align='center'>
                  </el-table-column>
                  <el-table-column prop="name" label="操作" width='220' align='center'>
                    <template slot-scope="scope">
                      <el-button type="danger" size="mini" @click='handleClickEdit(scope.row.id, scope.row.type)'>编辑
                      </el-button>
                      <el-button type="primary" size="mini" @click='handleClickDelete(scope.row.id, scope.row.alias, scope.row.type)'>删除
                      </el-button>
                      <el-button size="mini" @click='handleClickCopy(scope.row)'>复制
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
          </el-main>
          <el-dialog title="添加实验室" :visible.sync="dialogVisible" width="50%">
            <div style="margin-bottom: 20px;">实验室名称：<el-input placeholder="请输入实验室名称..." v-model='addParam.name'>
              </el-input>
            </div>
            <div style="margin-bottom: 20px;">实验室别名：<el-input placeholder="请输入实验室别名..." v-model='addParam.alias'>
              </el-input>
            </div>
            <div style="margin-bottom: 20px;">PC/移动端：
              <el-switch v-model="type" :active-value='1' :inactive-value='2' active-text='PC' inactive-text='Mobile'>
              </el-switch>
              </el-input>
            </div>
            <span slot="footer" class="dialog-footer">
              <el-button type="primary" @click='handleAddLab'>保 存</el-button>
            </span>
          </el-dialog>

          <el-dialog :title="title" :visible.sync="copyDialog.visibale" width="50%">
            <el-form :model="copyDialog" label-width="100px" :rules="rules" ref="form">
              <el-form-item label="实验室名称" prop='name'>
                <el-input v-model='copyDialog.name' placeholder="请输入实验室名称..."></el-input>
              </el-form-item>
              <el-form-item label="实验室别名" prop='alias'>
                <el-input v-model='copyDialog.alias' placeholder="请输入实验室别名..."></el-input>
              </el-form-item>
              <!-- <el-form-item label="">
                <el-checkbox v-model='copyDialog.is_delete'>复制上下架</el-checkbox>
              </el-form-item> -->
            </el-form>
            <span slot="footer" class="dialog-footer">
              <el-button @click="copyDialog.visibale = false">取 消</el-button>
              <el-button type="primary" @click='copy'>确 定</el-button>
            </span>
          </el-dialog>
      </el-container>
  </el-container>

  <script>
    var main = new Vue({
      el: '#Main',
      data: {
        loading: false,
        labList: <%- labList %>,
        view_url: '<%- view_url %>',
        mview_url: '<%- mview_url %>',
        dialogVisible: false,
        type: 1,
        addParam: {
          name: '',
          alias: '',
          seo: {
            title: '',
            keywords: '',
            description: ''
          },
          content: {
            // 资质icon
            icon1: '',
            pic1: '',
            icon2: '',
            pic2: '',
            // 直达楼层
            map: [{}, {}, {}, {}, {}, {}],
            navigations: [],
            // banner背景图片地址
            bannerBg: '',
            // 是否留言表单 1是 0否（答题）
            isForm: 1,
            // 楼层模块
            floors: [
              {
                // 楼层类型 1.实验室简介
                type: 1,
                hash: '',
                title: '',
                showFloor: 1,
                showTitle: 1,
                subTitle: '',
                showSubTitle: 1,
                videoSource: '',
                floorAboutPic: '',
                aboutEditContent: ''
              },
              {
                // 楼层类型 1.实验室简介 2.测试服务
                type: 2,
                hash: '',
                title: '',
                showFloor: 1,
                showTitle: 1,
                subTitle: '',
                showSubTitle: 1,
                moreLink: '',
                list: [
                  {
                    title: '',
                    bg: '',
                    items: [
                      {
                        name: '',
                        link: ''
                      }
                    ]
                  }
                ]
              },
              {
                // 楼层类型 1.实验室简介 2.测试服务 3.一站式检测方案
                type: 3,
                hash: '',
                title: '',
                showFloor: 1,
                showTitle: 1,
                subTitle: '',
                showSubTitle: 1,
                moreLink: '',
                list: [
                  {
                    title: '',
                    bg: '',
                    des: '',
                  }
                ]
              },
              {
                // 楼层类型 1.实验室简介 2.测试服务 3.一站式检测方案 4.实验室地区分布
                type: 4,
                hash: '',
                title: '',
                showFloor: 1,
                showTitle: 1,
                subTitle: '',
                showSubTitle: 1,
                content: ''
              },
              {
                // 楼层类型 1.实验室简介 2.测试服务 3.一站式检测方案 4.实验室地区分布 5.数据展示楼层
                type: 5,
                hash: '',
                title: '',
                showFloor: 1,
                showTitle: 1,
                subTitle: '',
                showSubTitle: 1,
                content: ''
              },
              {
                // 楼层类型 1.实验室简介 2.测试服务 3.一站式检测方案 4.实验室地区分布 5.数据展示楼层 6.资质证书
                type: 6,
                hash: '',
                title: '',
                showFloor: 1,
                showTitle: 1,
                subTitle: '',
                showSubTitle: 1,
                content: ''
              },
              {
                // 楼层类型 1.实验室简介 2.测试服务 3.一站式检测方案 4.实验室地区分布 5.数据展示楼层 6.资质证书 7.技术文章
                type: 7,
                hash: '',
                title: '',
                showFloor: 1,
                showTitle: 1,
                subTitle: '',
                showSubTitle: 1,
                moreLink: '',
                list: [
                  {
                    link: '',
                    name: ''
                  }
                ]
              },
              {
                // 楼层类型 1.实验室简介 2.测试服务 3.一站式检测方案 4.实验室地区分布 5.数据展示楼层 6.资质证书 7.技术文章 8. 合作案例
                type: 8,
                hash: '',
                title: '',
                showFloor: 1,
                showTitle: 1,
                subTitle: '',
                showSubTitle: 1,
                moreLink: '',
                list: [
                  {
                    img: '',
                    title: '',
                    des: '',
                    tag: '',
                  }
                ]
              }
            ],
            content: []
          },
        },
        addParamMobile: {
          name: '',
          alias: '',
          seo: {
            title: '',
            keywords: '',
            description: ''
          },
          content: {
            // 资质icon
            icon1: '',
            icon2: '',
            // banner背景图片地址
            bannerBg: '',
            // 楼层模块
            floors: [
              {
                // 楼层类型 1.实验室简介 2.测试服务 3.一站式检测方案 4.实验室地区分布 5.数据展示楼层 6.资质证书 7.技术文章 8. 合作案例 9.自定义类型 10.热门测试项目楼层
                type: 10,
                title: '',
                showFloor: 1,
                showTitle: 1,
                subTitle: '',
                showSubTitle: 1,
                list: [
                  {
                    title: '',
                  }
                ]
              },
              {
                // 楼层类型 1.实验室简介 2.测试服务 3.一站式检测方案 4.实验室地区分布 5.数据展示楼层 6.资质证书 7.技术文章 8. 合作案例 9.自定义类型 10.热门测试项目楼层 11.服务项目涵盖
                type: 11,
                title: '',
                showFloor: 1,
                showTitle: 1,
                subTitle: '',
                showSubTitle: 1,
                list: [
                  {
                    img: '',
                    link: ''
                  }
                ]
              },
              {
                // 楼层类型 1.实验室简介 2.测试服务 3.一站式检测方案 4.实验室地区分布 5.数据展示楼层 6.资质证书 7.技术文章 8. 合作案例 9.自定义类型 10.热门测试项目楼层 12.定制化一站式检测方案
                type: 12,
                title: '',
                showFloor: 1,
                showTitle: 1,
                subTitle: '',
                showSubTitle: 1,
                showFloor: 1,
                list: [
                  {
                    img: '',
                    link: ''
                  }
                ]
              },
              {
                // 楼层类型 1.实验室简介 2.测试服务 3.一站式检测方案 4.实验室地区分布 5.数据展示楼层 6.资质证书 7.技术文章 8. 合作案例 9.自定义类型 10.热门测试项目楼层 12.定制化一站式检测方案 13. 留言表单 
                type: 13,
                title: '',
                showFloor: 1,
                showTitle: 1,
                subTitle: '',
                showSubTitle: 1,
              },
              {
                // 楼层类型 1.实验室简介 2.测试服务 3.一站式检测方案 4.实验室地区分布 5.数据展示楼层 6.资质证书 7.技术文章
                type: 7,
                title: '',
                showFloor: 1,
                showTitle: 1,
                subTitle: '',
                showSubTitle: 1,
                moreLink: '',
                list: [
                  {
                    link: '',
                    name: ''
                  }
                ]
              },
              {
                // 楼层类型 1.实验室简介 2.测试服务 3.一站式检测方案 4.实验室地区分布 5.数据展示楼层 6.资质证书 7.技术文章 8. 合作案例 9.自定义类型 10.热门测试项目楼层 12.定制化一站式检测方案 13/18. 留言表单 
                type: 18,
                title: '',
                showFloor: 1,
                showTitle: 1,
                subTitle: '',
                showSubTitle: 1,
              },
            ],
          }
        },
        copyDialog: {
          visibale: false,
          name: '',
          alias: '',
          // is_delete: false
        }, // 复制弹窗
        rules: {
          name: [
            { required: true, message: '请输入实验室名称', trigger: 'blur' },
          ],
          alias: [
            { required: true, message: '请输入实验室别名', trigger: 'blur' },
          ],
        },
        title: '',
        row: {}
      },
      methods: {
        handleClickEdit(id, type) {
          if (type === 1) {
            window.location.href = `/lab/${id}`
          } else {
            window.location.href = `/labMobile/${id}`
          }
        },
        handleClickDelete(id, alias, type) {
          this.$confirm('是否确认删除？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let pageLink = ''
            if (type === 1) pageLink = this.view_url + '/lab/' + alias
            axios.post('/lab/delete', { id, pageLink })
              .then(result => {
                if (result.data.success) {
                  this.$message.success('删除成功');
                  this.dialogVisible = false
                  window.location.reload()
                } else {
                  this.$message.error(result.data.msg || '系统错误，请稍后重试');
                }
                this.loading = false
              });
          })
        },
        handleAddLab() {
          if (!this.addParam.alias) {
            this.$message.error('别名不能为空')
          } else {
            let params = null
            if (this.type === 2) {
              this.addParamMobile.name = this.addParam.name
              this.addParamMobile.alias = this.addParam.alias
              this.addParamMobile.type = this.type
              params = this.addParamMobile
            } else {
              this.addParam.type = this.type
              params = this.addParam
            }

            axios.post('/lab/addtion', params)
              .then(result => {
                if (result.data.success) {
                  this.$message.success('添加成功');
                  this.dialogVisible = false
                  window.location.reload()
                } else {
                  this.$message.error(result.data.msg || '系统错误，请稍后重试');
                }
                this.loading = false
              });
          }
        },
        handleClickCopy(row) {
          this.copyDialog.visibale = true
          this.copyDialog.name = `复制 - ${row.name}`
          this.copyDialog.alias = `copy - ${row.alias}`
          this.row = row
          this.title = row.type === 1 ? `复制实验室：/lab/${row.alias}` : `复制实验室：/labMobile/${row.alias}`
        },
        copy() {
          this.$refs['form'].validate((valid) => {
            if (valid) {
              var loading = this.$loading({
                lock: true,
                text: '复制中，请等待...',
                spinner: 'el-icon-loading',
                background: 'rgba(255, 255, 255, 0.7)'
              });
              const cloneForm = JSON.parse(JSON.stringify(this.row))
              cloneForm.name = this.copyDialog.name;
              cloneForm.alias = this.copyDialog.alias;
              // if (!this.copyDialog.is_delete) cloneForm.is_delete = 1
              cloneForm.is_copy = 1 // 打上复制标签
              Object.keys(cloneForm).forEach(v => {
                if (v === 'content') cloneForm[v] = JSON.parse(cloneForm[v])
              })
              // 新增sku
              axios.post('/lab/addtion', cloneForm)
                .then((result) => {
                  if (result.data.success) {
                    window.location.href = cloneForm.type === 1 ? `/lab/${result.data.id}` : `/labMobile/${result.data.id}`
                  } else {
                    loading.close();
                    this.$message.error(result.data.msg)
                  }
                });
            }
          })
        },
        // 导出数据excle
        handleExport() {
          axios.post('/export/excle', {}).then(res1 => {
            axios({
              method: 'post',
              url: res1.data.host + '/ticMall/business/api.v1.mgmt/lab/exp',
              data: {},
              headers: {
                'Content-Type': 'application/json',
                pid: res1.data.pid,
                pcode: res1.data.pcode,
                timestamp: res1.data.timestamp,
                sign: res1.data.sign,
                frontUrl: window.location.origin,
              },
              responseType: "blob",
            }).then(res => {
              let data = res.data;
              let filename = ''
              //从response的headers中获取filename, 后端response.setHeader("Content-disposition", "attachment; filename=xxxx.docx") 设置的文件名;
              var contentDisposition = res.headers["content-disposition"];
              if (contentDisposition) {
                var patt = new RegExp("filename=([^;]+\\.[^\\.;]+);*");
                var result = patt.exec(contentDisposition);
                filename = result ? decodeURIComponent(result[1]) : filename;
              }
              var blob = new Blob([res.data], {
                type: "application/actet-stream;charset=utf-8",
              });
              var downloadElement = document.createElement("a");
              var href = window.URL.createObjectURL(blob); //创建下载的链接
              downloadElement.style.display = "none";
              downloadElement.href = href;
              downloadElement.download = decodeURI(filename).replace(
                /\+/g,
                " "
              );
              document.body.appendChild(downloadElement);
              downloadElement.click();
              setTimeout(() => {
                document.body.removeChild(downloadElement);
                window.URL.revokeObjectURL(href);
              }, 1000);
            })
          })
        }
      },
      mounted() {
        this.$nextTick(() => {
          document.getElementById('preLoading').style.display = 'none';
        })
      }
    });
  </script>

  <style>
    .name {
      position: relative;
    }

    .name i {
      position: absolute;
      right: 0;
      top: 0;
      color: #fff;
      font-size: 12px;
      background: #F56C6C;
      display: block;
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      border-radius: 50%;
      font-style: normal;
    }
  </style>
  <% include footer.html %>