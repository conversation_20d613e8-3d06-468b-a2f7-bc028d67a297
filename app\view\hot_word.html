<% include header.html %>
<el-container id="HotWord">
    <% include eheader.html %>
    <el-container>
        <% include eside.html %>
        <el-main>
            <el-row>
                <el-col :span="24">
                    <el-breadcrumb separator-class="el-icon-arrow-right">
                        <el-breadcrumb-item>
                            <a href='/'>首页</a>
                        </el-breadcrumb-item>
                        <el-breadcrumb-item>
                            设置
                        </el-breadcrumb-item>
                        <el-breadcrumb-item>
                            关键词
                        </el-breadcrumb-item>
                    </el-breadcrumb>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-table :data="datas" style="width: 100%">
                        <el-table-column label="序号" type='index'>
                        </el-table-column>
                        <el-table-column prop="name" label="关键词">
                        </el-table-column>
                        <el-table-column prop="is_light" label="是否高亮">
                            <template slot-scope="scope">
                                <el-switch v-model="scope.row.is_light" @change='switchChange($event, scope.row)'>
                                </el-switch>
                            </template>
                        </el-table-column>
                        <el-table-column prop="address" label="操作">
                            <template slot-scope="scope">
                                <el-button type="text" @click='handleEdit(scope.row)'>编辑</el-button>
                                <el-button type="text" @click='handleRemove(scope.row.id)'>删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-col>
                <el-col :span="24" style="padding-top: 20px;">
                    <el-button type="primary" @click='handleCreate'>创建</el-button>
                </el-col>
            </el-row>
        </el-main>
        <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
            <el-form ref="form" :model="form" label-width="80px">
                <el-form-item label="关键词">
                    <el-input v-model="form.name" maxlength='20'></el-input>
                </el-form-item>
                <el-form-item label="是否高亮">
                    <el-switch v-model="form.is_light">
                    </el-switch>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="handleSave">确 定</el-button>
            </span>
        </el-dialog>
    </el-container>
</el-container>
<script>
    var HotWord = new Vue({
        el: '#HotWord',
        data: {
            dialogVisible: false,
            value2: false,
            datas: [],
            form: {
                id: '',
                name: '',
                is_light: ''
            },
            loading: false
        },
        methods: {
            // 获取数据
            getData() {
                axios.post('/hotWord/list', {
                    _csrf: '<%- csrf %>'
                })
                    .then(res => {
                        if (res.status === 200) {
                            let datas = res.data.data.list
                            datas.forEach((item, index) => {
                                if (item.is_light) {
                                    item.is_light = true
                                } else {
                                    item.is_light = false
                                }
                            })
                            this.datas = datas
                        } else {
                            this.$message.error('系统错误，请稍后重试');
                        }
                    }).catch(e => {
                        this.$message.error(e);
                    });
            },
            // 提交验证
            handleSave() {
                if (this.form.id) {
                    // 编辑
                    this.edit();
                } else {
                    // 添加
                    this.insert();
                }
            },
            // 插入            
            insert() {
                let params = {
                    name: this.form.name,
                    is_light: this.form.is_light ? 1 : 0,
                    create_time: moment(new Date().valueOf()).format('YYYY-MM-DD HH:mm:ss'),
                    _csrf: '<%- csrf %>'
                }
                axios.post('/hotWord/insert', params)
                    .then(res => {
                        if (res.status === 200) {
                            this.$message.success('保存成功');
                            this.getData()
                        } else {
                            this.$message.error('系统错误，请稍后重试');
                        }
                        this.dialogVisible = false
                    }).catch(e => {
                        this.$message.error(e);
                        this.dialogVisible = false
                    });
            },
            // 编辑
            edit() {
                this.form.is_light = this.form.is_light ? 1 : 0
                let params = Object.assign(this.form, {
                    _csrf: '<%- csrf %>'
                })
                axios.post('/hotWord/edit', params)
                    .then(res => {
                        if (res.status === 200) {
                            this.$message.success('修改成功');
                            this.getData()
                        } else {
                            this.$message.error('系统错误，请稍后重试');
                        }
                        this.dialogVisible = false
                    }).catch(e => {
                        this.$message.error(e);
                        this.dialogVisible = false
                    });
            },
            handleCreate() {
                this.dialogVisible = true
                const obj = {
                    name: '',
                    is_light: 0,
                }
                this.form = obj
            },
            handleEdit(data) {
                this.dialogVisible = true
                this.form = data
            },
            handleRemove(id) {
                this.$confirm("确定删除该关键词数据？", "提示")
                    .then(() => {
                        this.delRow(id);
                    })
                    .catch(e => {
                        return e;
                    });
            },
            delRow(id) {
                let params = {
                    id
                }
                axios.post('/hotWord/del', params)
                    .then(res => {
                        if (res.status === 200) {
                            this.$message.success('删除成功');
                            this.getData()
                        } else {
                            this.$message.error('系统错误，请稍后重试');
                        }
                        this.dialogVisible = false
                    }).catch(e => {
                        this.$message.error(e);
                        this.dialogVisible = false
                    });
            },
            switchChange(e, data) {
                this.form = data
                this.edit()
            }
        },
        mounted() {
            this.$nextTick(function () {
                this.getData();
                document.getElementById('preLoading').style.display = 'none';
            });
        }
    });
</script>
<% include footer.html %>