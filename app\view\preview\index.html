<% include header.html %>

<div class="videoBox" style="overflow:hidden;position:relative;">
    <video src="/static/preview/vv.mp4" autoplay loop muted width="100%"></video>
    <div style="position: absolute;top:0;left: 0;width:100%;height:100%;background-color:rgba(0,0,0,0.5)"></div>
    <div class="videoWord2" style="top:150px">SGS是全球领先的检验、鉴定、测试和认证机构</div>
    <div class="videoWord3" style="font-size: 22px;">传递信任、共赢未来</div>
    <a class="learnMore" href="/preview/overview/about SGS" target="_blank">
        <div class="deepen"></div>
        <div class="learnMore-word">了解更多</div>
        <div class="learnMore-icon"></div>
    </a>
</div>

<div class="homeConBox">
    <% for(let item of detail.items){ %>
    <% if(item.type == 1){ %>
    <div class="homeCon1">
        <div class="homeContitle">
            <% for(let [n,node] of item.node.entries()){ %>
            <span class="title<% if(n == 0){ %> active<% } %>"><%- node.title %></span>
            <% } %>
            <span class="spanL">
                <% for(let [n,node] of item.node.entries()){ %>
                <span class="spanL-word" <% if(n != 0){ %> style="display:none" <% } %>><a
                        href="<%- node.link %>">查看更多</a></span>
                <% } %>
                <div class="spanL-icon"></div>
            </span>
            <div class="titleBor">

            </div>
        </div>
        <div class="homeConCon1">
            <% for(let [n,node] of item.node.entries()){ %>
            <div <% if(n!=0){ %>style="display:none" <% } %>>
                <div class="homeConConL">
                    <% if(node.skus[0].thumb_img){ %>
                    <a href="<%- node.skus[0].url %>">
                        <img src="<%- node.skus[0].thumb_img %>" alt="" class="">
                    </a>
                    <% } %>
                </div>
                <div class="homeConConR">
                    <ul>
                        <% for(let [i,o] of node.skus.entries()){ %>
                        <% if(i > 0 && o.thumb_img){ %>
                        <li class="homeConConRLi over">
                            <a href="<%- o.url %>">
                                <img src="<%- o.thumb_img %>" alt="" class="homeConConRLi-img">
                                <div class="homeConConRLi-word1"><%- o.name %></div>
                                <div class="homeConConRLi-word2"><%- o.sub_title %></div>
                            </a>
                        </li>
                        <% } %>
                        <% } %>
                    </ul>
                </div>
            </div>
            <% } %>
        </div>
    </div>
    <% }else if(item.type == 2){ %>
    <div class="homeCon2">
        <div class="homeCon2Box">
            <div class="homeContitle">
                <% for(let [n,node] of item.node.entries()){ %>
                <span class="title<% if(n == 0){ %> active<% } %>"><%- node.title %></span>
                <% } %>

                <span class="spanL">
                    <% for(let [n,node] of item.node.entries()){ %>
                    <span class="spanL-word" <% if(n != 0){ %> style="display:none" <% } %>><a
                            href="<%- node.link %>">查看更多</a></span>
                    <% } %>
                    <div class="spanL-icon"></div>
                </span>
                <div class="titleBor">

                </div>
            </div>

            <div class="homeConCon2">
                <% for(let [n,node] of item.node.entries()){ %>
                <ul<% if(n!=0){ %> style="display:none" <% } %>>
                    <% for(let [i,o] of node.skus.entries()){ %>
                    <% if(i < 3 && o.thumb_img){ %>
                    <li class="homeConCon2Li over">
                        <a href="<%- o.url %>">
                            <img src="<%- o.thumb_img %>" alt="" class="homeConConRLi-img"
                                style="width:390px;height:192px;">
                            <div class="homeConCon2Li-word1"><%- o.name %></div>
                            <div class="homeConCon2Li-borderBox">
                                <div class="homeConCon2Li-border"></div>
                            </div>
                            <div class="homeConConRLi-word2"><%- o.sub_title %></div>
                        </a>
                    </li>
                    <% } %>
                    <% } %>
                    </ul>
                    <% } %>
            </div>
        </div>
    </div>
    <% }else if(item.type == 3){ %>
    <div class="homeCon4">
        <div class="homeCon2Box">
            <div class="homeContitle">
                <% for(let [n,node] of item.node.entries()){ %>
                <span class="title<% if(n == 0){ %> active<% } %>"><%- node.title %></span>
                <% } %>

                <span class="spanL">
                    <% for(let [n,node] of item.node.entries()){ %>
                    <span class="spanL-word" <% if(n != 0){ %> style="display:none" <% } %>><a
                            href="<%- node.link %>">查看更多</a></span>
                    <% } %>
                    <div class="spanL-icon"></div>
                </span>
                <div class="titleBor">

                </div>
            </div>

            <div class="homeConCon2">
                <% for(let [n,node] of item.node.entries()){ %>
                <ul<% if(n!=0){ %> style="display:none" <% } %>>
                    <% for(let [i,o] of node.skus.entries()){ %>
                    <% if(i < 3 && o.thumb_img){ %>
                    <li class="homeConCon2Li over homeConCon4Li">
                        <img src="<%- o.thumb_img %>" alt="" class="homeConCon2Li-img2">
                        <img src="/static/preview/images/opacity.png" alt="" class="homeConCon2Li-img1">
                        <div class="homeConCon4Li-word1"><%- o.name %></div>
                        <div class="homeConCon4Li-word2"><%- o.sub_title %></div>
                        <a class="learnMore" href="<%- o.url %>">
                            <div class="deepen"></div>
                            <div class="learnMore-word">了解更多</div>
                            <div class="learnMore-icon"></div>
                        </a>
                    </li>
                    <% } %>
                    <% } %>
                    </ul>
                    <% } %>
            </div>
        </div>
    </div>
    <% }else if(item.type == 4){ %>
    <!-- 新楼层7格子 -->
    <div class="homeCon8">
        <div class="wrap">
            <div class="homeCon-head">
                <span><%- item.title %></span>
                <% for(let [n,node] of item.node.entries()){ %>
                <a class="more <% if(n == 0){ %>active<% } %>" target="_blank"
                    href="<% if(locals.env == 'prod'){ %> <%- node.link.replace('https://www.sgsonline.com.cn', '') %> <% }else{ %> <%- node.link.replace('http://**************', '') %> <% } %>">
                    <em>更多</em>
                    <i></i>
                </a>
                <% } %>
                <% if(item.node.length > 1){ %>
                <ul>
                    <% for(let [n,node] of item.node.entries()){ %>
                    <li class="<% if(n == 0){ %>active<% } %>">
                        <%- node.title %>
                    </li>
                    <% } %>
                </ul>
                <% } %>
            </div>
            <div class="homeCon8-body clearfix">
                <% for(let [n,node] of item.node.entries()){ %>
                <div class="homeCon8-item <% if(n == 0){ %>active<% } %>">
                    <% if(node.skus[0].thumb_img){ %>
                    <div class="side over">
                        <img src="<%- node.skus[0].thumb_img %>" />
                        <a target="_blank"
                            href="<% if(locals.env == 'prod'){ %> <%- node.skus[0].url.replace('https://www.sgsonline.com.cn', '') %> <% }else{ %> <%- node.skus[0].url.replace('http://**************', '') %> <% } %>">
                            立即查看
                        </a>
                    </div>
                    <% } %>
                    <ul class="list">
                        <% for(let [i,o] of node.skus.entries()){ %>
                        <% if(i > 0 && o.thumb_img){ %>
                        <li class="over">
                            <a
                                href="<% if(locals.env == 'prod'){ %> <%- o.url.replace('https://www.sgsonline.com.cn', '') %> <% }else{ %> <%- o.url.replace('http://**************', '') %> <% } %>">
                                <h2>
                                    <span class="<% if(o.is_buy == 1){ %>active<% } %>"><%- o.name %></span>
                                    <% if(o.is_buy == 1){ %><i></i><% } %>
                                </h2>
                                <p><%- o.sub_title %></p>
                                <img src="<%- o.thumb_img %>" alt="<%- o.name %>" />
                            </a>
                        </li>
                        <% } %>
                        <% } %>
                    </ul>
                </div>
                <% } %>
            </div>
        </div>
    </div>
    <% } %>
    <% } %>
    
    <!-- 新楼层自定义格子 -->
    <% for(let item of detail.customItems){ %>
    <% if(item.type === 1 || item.type === 3){ %>
    <div class="homeCon6">
        <div class="wrap">
            <div class="homeCon-head">
                <span><%- item.titleName %></span>
                <a style="display: block;" class="more" target="_blank"
                    href="<% if(locals.env == 'prod'){ %> <%- item.titleUrl.replace('https://www.sgsonline.com.cn', '') %> <% }else{ %> <%- item.titleUrl.replace('http://**************', '') %> <% } %>">
                    <em>更多</em>
                    <i></i>
                </a>
            </div>
            <div class="homeCon6-body clearfix">
                <% for(let box of item.items){ %>
                <div class="homeCon6-item over">
                    <div class="homeCon6-item-bg"
                        style="background-image: url('<%- box.bg %>');">
                        <span><%- box.title %></span>
                        <i></i>
                    </div>
                    <div class="homeCon6-item-list <% if(item.type === 1){ %>homeCon6-li<% } %>">
                        <div class="homeCon6-item-line">
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                        <ul class="clearfix">
                            <% for(let list of box.list){ %>
                            <li>
                                <a target="_blank"
                                    href="<% if(locals.env == 'prod'){ %> <%- list.url.replace('https://www.sgsonline.com.cn', '') %> <% }else{ %> <%- list.url.replace('http://**************', '') %> <% } %>">
                                    <%- list.name %>
                                </a>
                            </li>
                            <% } %>
                        </ul>
                        <a class="more" target="_blank"
                            href="<% if(locals.env == 'prod'){ %> <%- box.more.replace('https://www.sgsonline.com.cn', '') %> <% }else{ %> <%- box.more.replace('http://**************', '') %> <% } %>">更多></a>
                    </div>
                </div>
                <% } %>
    
            </div>
        </div>
    </div>
    <% }else if(item.type === 2){ %>
    <div class="homeCon7">
        <div class="wrap">
            <div class="homeCon-head">
                <span><%- item.titleName %></span>
                <a style="display: block;" class="more" target="_blank"
                    href="<% if(locals.env == 'prod'){ %> <%- item.titleUrl.replace('https://www.sgsonline.com.cn', '') %> <% }else{ %> <%- item.titleUrl.replace('http://**************', '') %> <% } %>">
                    <em>更多</em>
                    <i></i>
                </a>
            </div>
            <div class="homeCon7-body clearfix">
                <ul>
                    <% for(let box of item.items){ %>
                    <li class="over">
                        <div class="homeCon7-bg"
                            style="background-image: url('<%- box.bg %>');"></div>
                        <div class="homeCon7-mark"></div>
                        <div class="homeCon7-con">
                            <h2>
                                <a target="_blank"
                                    href="<% if(locals.env == 'prod'){ %> <%- box.more.replace('https://www.sgsonline.com.cn', '') %> <% }else{ %> <%- box.more.replace('http://**************', '') %> <% } %>"><%- box.title %></a>
                            </h2>
                            <p><%- box.des %></p>
                            <dl>
                                <% for(let list of box.list){ %>
                                <dd>
                                    <a target="_blank"
                                        href="<% if(locals.env == 'prod'){ %> <%- list.url.replace('https://www.sgsonline.com.cn', '') %> <% }else{ %> <%- list.url.replace('http://**************', '') %> <% } %>"><%- list.name %></a>
                                </dd>
                                <% } %>
                            </dl>
                        </div>
                    </li>
                    <% } %>
                </ul>
            </div>
        </div>
    </div>
    <% } %>
    <% } %>

    <div class="descrip">
        <div class="descripBox">
            <ul id="desShow">
                <% for(let [i,o] of detail.newsItems.entries()){ %>
                <li class="descripLi<% if(i==0){ %> big<% } %>">
                    <div class="bigBox">
                        <div class="liTitle" title="<%- o.title %>"><%- o.title %></div>
                        <% if(i==0){ %><div class="liTime"><%- o.time %></div><% } %>
                        <div class="liInfo"><%- o.content %></div>
                        <% if(i!=0){ %><div class="liTime"><%- o.time %></div><% } %>
                        <a class="learnMore" href="<%- o.url %>" target="_blank">
                            <div class="deepen"></div>
                            <div class="learnMore-word">了解更多</div>
                            <div class="learnMore-icon"></div>
                        </a>
                    </div>
                </li>
                <% } %>
            </ul>
        </div>
    </div>
</div>

<script>
    $(function () {
      $('.homeCon-head li').on('click', function () {
            var index = $(this).index()
            $(this).addClass("active").siblings().removeClass('active');
            $(this).closest('.homeCon-head').siblings('.homeCon8-body').find('.homeCon8-item').eq(index).addClass('active').siblings().removeClass("active")
            $(this).closest('.homeCon-head').find('.more').eq(index).addClass('active').siblings().removeClass("active")
        })
    });
</script>

<% include footer.html %>