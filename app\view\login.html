<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <el-row style="width:100%;clear:both">
                    <el-form v-model="form" style="width:480px;margin:40px auto;">
                        <el-form-item>
                            <el-input v-model="form.u" placeholder="用户名"></el-input>
                        </el-form-item>

                        <el-form-item>
                            <el-input v-model="form.p" placeholder="密码" type="password" @keyup.enter.native="submit"></el-input>
                        </el-form-item>

                        <el-form-item style="text-align: center">
                            <el-button type="primary" @click="submit">登录</el-button>
                        </el-form-item>
                    </el-form>
                </el-row>
            </el-container>
            <div id="purehtml-container" style="text-align:center"></div>
    </el-container>
    <script>
    var main = new Vue({
        el: '#Main',
        data: {
            form: {
                u: '<%- u %>',
                p: '',
            }
        },
        methods: {
            submit:function(){
                var that = this;
                var params = this.form;
                params._csrf = '<%- csrf %>';
                axios.post('/login', params)
                    .then(function(result) {
                        if (result.data.code == 0) {
                            that.$message.error(result.data.msg);
                        }
						else if(result.data.localUser=="1")
						{
							var t='/?localUser=1&user_name=' + result.data.user_name+"&token="+result.data.token;
                            window.location = '/?localUser=1&user_name=' + result.data.user_name+"&token="+result.data.token
                            // window.sessionStorage.setItem('isLogin', true)
						}
						else{
                            window.location = '/?sgsToken=' + result.data.sgsToken + '&user_name=' + result.data.user_name
                            // window.sessionStorage.setItem('isLogin', true)
                        }
                    }).catch(function(e){
                        that.$message.error('登录失败，请刷新重试。');
                    });
            },
        },
        mounted:function(){
            this.$nextTick(function(){
                document.getElementById('preLoading').style.display = 'none';
            });
        }
    });
    </script>
    <% include footer.html %>