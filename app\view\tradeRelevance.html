<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        设置
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        产品行业映射
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-tabs v-model="serviceAlias" @tab-click="handleClick" type="card">
                                    <el-tab-pane :disabled='loading' :label="tab.name" :name="tab.alias" v-for='(tab, index) in serviceList' :key='index'></el-tab-pane>
                                </el-tabs>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                当前服务类型：{{ servieName }}
                            </el-col>
                            <el-col :span="24" style='margin-top: 10px;'>
                                服务类型别名：
                            </el-col>
                            <el-col :span="18">
                                <el-input v-model='quoteAlias' maxlength="50" placeholder="请输入服务类型别名..." />
                            </el-col>
                            <el-col :span="24" style='margin-top: 10px;'>
                                咨询内容框的提示文案：
                            </el-col>
                            <el-col :span="18">
                                <el-input type="textarea" v-model='productExplain' maxlength="200" placeholder="请输入咨询内容框的提示文案..." />
                            </el-col>
                            <el-col :span="6" style="text-align: right;">
                                <el-button @click='saveTask' type="primary">保存别名和提示文案</el-button>
                            </el-col>
                        </el-row>
                        <el-row v-loading="loading">
                            <el-col :span="16">
                                <el-table :data="tableList" stripe border style="width: 100%">
                                    <el-table-column prop="id" label="" width="60">
                                        <template slot-scope="scope">
                                            {{scope.$index+1}}
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="productName" label="产品类别">
                                    </el-table-column>
                                    <el-table-column prop="industryName" label="行业映射">
                                    </el-table-column>
                                    <el-table-column fixed="right" label="操作" width="280">
                                        <template slot-scope="scope">
                                            <el-button-group>
                                                <el-button v-if="hasEditPurview==1"  type="primary" size="mini" @click='_edit(scope.row)'>编辑</el-button>
                                                <el-button v-if="hasEditPurview==1"  type="danger" size="mini" @click='_del(scope.row.productId, scope.row.industryName)'>删除</el-button>
                                                <el-button @click='move("up", scope.row.productId, scope.$index)' :disabled="!scope.$index" v-if="hasEditPurview==1"  type="danger" size="mini" icon="el-icon-top"></el-button>
                                                <el-button @click='move("down", scope.row.productId, scope.$index)' :disabled="scope.$index + 1 == tableList.length" v-if="hasEditPurview==1" type="danger" size="mini" icon="el-icon-bottom"></el-button>
                                            </el-button-group>
                                            <el-checkbox v-model="scope.row.jumpType" v-if="scope.row.jumpType" :disabled="1 == 1" :true-label=1 :false-label=0>OIQ</el-checkbox>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <div class="logshow-wrapper" v-if="logs.list.length > 0">
                                    <el-row class="logshow" :class="{on: logshow}">
                                        <span class="icon"><i class="el-icon-arrow-up" @click="showLog"></i></span>
                                        <ul>
                                            <li v-for="(item, index) of logs.list" :key='index'>{{item.createTime}}, {{item.userName}}, {{item.log}}</li>
                                        </ul>
                                        <el-pagination
                                        @current-change="qryLog"
                                        :current-page.sync="logParam.pageNum"
                                        :page-size="logParam.pageRow"
                                        layout="total, prev, pager, next"
                                        :total="logs.total">
                                        </el-pagination>
                                    </el-row>
                                </div>
                            </el-col>
                            <el-col :span="8"  v-if="hasEditPurview==1" >
                                <el-form ref="form" :model="form" label-width="150px">
                                    <el-form-item label="产品类别*">
                                        <el-input v-model="form.productName" maxlength="50"></el-input>
                                    </el-form-item>
                                    <el-form-item label="行业映射*">
                                        <el-select v-model="form.industryId" placeholder="请选择">
                                            <el-option v-for="item in tradeList" :key="item.id" :label="item.name" :value="item.id">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="是否关联OIQ">
                                        <el-checkbox v-model="form.jumpType" :true-label=1 :false-label=0></el-checkbox>
                                    </el-form-item>
                                    <el-form-item label="关联文案">
                                    <el-input v-model="form.productNotice" type="textarea"></el-input>
                                  </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="onSubmit" v-if="form.productId == 0">新建</el-button>
                                        <el-button type="primary" @click="onSubmit" v-else>编辑</el-button>
                                        <el-button @click="_default">取消</el-button>
                                    </el-form-item>
                                </el-form>
                            </el-col>
                        </el-row>
                    </el-main>
            </el-container>
    </el-container>
    <script>
    var main = new Vue({
        el: '#Main',
        data: {
            productExplain: '请详细描述您的需求',
            servieName: '',
            serviceAlias: '',
            quoteAlias: '',
            tasks:<%- tasks %>,
            serviceList: <%- serviceList %>,
            tradeList: <%- tradeList %>,
            tableList: [],
            form: {
                productId: 0,
                serviceId: '',
                industryId: '',
                productName: '',
                jumpType: 0,
                productNotice: ''
                // sortShow: 1,
            },
            loading: false,
            logs: <%- logs %>,
            logshow: false,
            hasEditPurview: <%- hasEditPurview %>,
            logParam: {
                operatorType: 7,
                operatorId: '',
                pageRow: 10,
                pageNum: 1
            }
        },
        methods: {
            handleClick(tab, event) {
                this._default()
                this.servieName = this.serviceList[tab.index].name;
                this.serviceAlias = this.serviceList[tab.index].alias;
                this.quoteAlias = this.serviceList[tab.index].quoteAlias;
                this.productExplain = this.tasks[tab.index].productExplain || '请详细描述您的需求';
                this.form.serviceId = this.tasks[tab.index].id;
                this.qryItem(this.tasks[tab.index].id)
                this.getServieList()
            },
            onSubmit() {
                if (!this.form.industryId || !this.form.productName) {
                   this.$message.error('产品类别/行业映射不能为空');
                } else {
                    if (this.form.productId) {
                        this.modItem()
                    } else {
                        this.addItem()
                    }
                }
            },
            _edit(row) {
                this.form.productId = row.productId;
                this.form.serviceId = row.serviceId;
                this.form.industryId = row.industryId;
                this.form.jumpType = row.jumpType;
                this.form.productName = row.productName;
                this.form.productNotice = row.productNotice;
                // this.form.sortShow = row.sortShow;
            },
            _del(id, name) {
                var that = this;
                this.$confirm('是否删除《'+ name +'》？', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                    this.delItem(id)
                }).catch(e =>{
                    return e;
                });
            },
            _default() {
                this.form.productId = 0;
                this.form.industryId = '';
                this.form.productName = '';
                this.form.jumpType = 0;
                // this.form.sortShow = 1;
            },
            showLog(){
                if(this.logs.list.length > 1){
                    this.logshow = !this.logshow;
                }
            },
            // logPageChange(page){
            //     axios.post('/logslist',{page:page,model:'设置-目的国',limit:10,_csrf:'<%- csrf %>'}).then(res => {
            //         let data = res.data;
            //         if(data.success){
            //             this.logs = data.data;
            //         }
                    
            //     })
            // },
            qryItem(serviceId) {
                this.loading = true
                axios.post('/setting/tardeRelevance/qry', {
                    serviceId
                })
                    .then(res => {
                        if (res.data.resultCode === '0') {
                            this.tableList = res.data.data.items
                            this.loading = false
                        }
                    }).catch(e => {
                        this.$message.error(e);
                        this.loading = false
                    });
            },
            getServieList() {
                this.loading = true
                axios.post('/setting/tardeRelevance/getQuoteAlias', {serviceId: this.form.serviceId})
                    .then(res => {
                        this.serviceList = res.data
                        this.loading = false
                    }).catch(e => {
                        this.$message.error(e);
                        this.loading = false
                    });
            },
            addItem() {
                axios.post('/setting/tardeRelevance/add', this.form)
                    .then(res => {
                        if (res.data.resultCode === '0') {
                            this._default()
                            this.qryItem(this.form.serviceId)
                            this.$message.success('新建成功');
                        }
                    }).catch(e => {
                        this.$message.error(e);
                    });
            },
            modItem() {
                axios.post('/setting/tardeRelevance/mod', this.form)
                    .then(res => {
                        if (res.data.resultCode === '0') {
                            this._default()
                            this.qryItem(this.form.serviceId)
                            this.$message.success('编辑成功');
                        }
                    }).catch(e => {
                        this.$message.error(e);
                    });
            },
            delItem(productId) {
                axios.post('/setting/tardeRelevance/del', { productId })
                   .then(res => {
                        if (res.data.resultCode === '0') {
                            this.qryItem(this.form.serviceId)
                            this.$message.success('删除成功');
                        }
                    }).catch(e => {
                        this.$message.error(e);
                    });
            },
            move(direction, productId, index) {
                console.log(direction, productId, index)
                let toID = 0;
                if (direction === 'up') {
                    toId = this.tableList[index - 1].productId;
                    this.sortItem(productId, toId)
                } else if (direction === 'down') {
                    toId = this.tableList[index + 1].productId;
                    this.sortItem(productId, toId)
                }
            },
            sortItem(formId, toId) {
                axios.post('/setting/tardeRelevance/sort', {
                    productId: formId,
                    otherId : toId
                })
                    .then(res => {
                        if (res.data.resultCode === '0') {
                            this.qryItem(this.form.serviceId)
                        }
                    }).catch(e => {
                        this.$message.error(e);
                    });
            },
            qryTask() {
                axios.post('/setting/tardeRelevance/qryTask', { alias: 'service'})
                    .then(res => {
                        if (res.data.resultCode === '0') {
                            this.tasks = res.data.data.items
                        }
                    }).catch(e => {
                        this.$message.error(e);
                    });
            },
            saveTask() {
                axios.post('/setting/tardeRelevance/saveTask', {
                    id: this.form.serviceId,
                    productExplain: this.productExplain,
                    quoteAlias: this.quoteAlias
                })
                    .then(res => {
                        if (res.data.resultCode === '0') {
                            this.$message.success('保存成功');
                            this.qryTask()
                        }
                    }).catch(e => {
                        this.$message.error(e);
                    });
            },
            qryLog(page) {
                this.logParam.pageNum = page
                axios.post('/log/qry', this.logParam)
                    .then(res => {
                        if (res.data.resultCode === '0') {
                            this.logs = {
                                list: res.data.data.items,
                                total: res.data.data.totalNum
                            };
                            console.log(this.logs)
                        }
                    }).catch(e => {
                        this.$message.error(e);
                    });
            }
        },
        mounted(){
            this.$nextTick(() => {
                document.getElementById('preLoading').style.display = 'none';
            })
            this.form.serviceId =  this.serviceList[0].id;
            this.servieName = this.serviceList[0].name;
            this.serviceAlias = this.serviceList[0].alias;
            this.quoteAlias = this.serviceList[0].quoteAlias;
            this.productExplain = this.tasks[0].productExplain || '请详细描述您的需求';
            this.qryItem(this.form.serviceId)
            this.qryLog()
        }
    });
    </script>

    <style>
    .el-tabs__header {
        margin: 0 !important;
    }
    </style>
    <% include footer.html %>