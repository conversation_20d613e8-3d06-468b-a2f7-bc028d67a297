// $(function() {
//     $('.navUl>li.navLi').hover(function() {
//         var $_child = $(this).children('ul');

//         if ($_child.length == 1) {
//             $_child.stop().fadeIn();
//         }
//     }, function() {
//         var $_child = $(this).children('ul');

//         if ($_child.length == 1) {
//             $_child.stop().fadeOut();
//         }
//     });

//     $('.navUl>li.navLi>ul>li:even').addClass('even');
// });
$(function () {
    // _userInfo();
    fixPlaceholder();
    $(".navSearchIcon").on("click",
        function () {
            var v = $("#keyWord").val().trim();
            v = encodeURIComponent(v);
            if (!v || v == "") {
                return
            };
            window.open("/search?q=" + v)
        });

    updateCartNumber("#minicart_ .up-cat-number");
    var $_drag = $(".drag");
    var dragH;
    for (var i = 0; i < $_drag.length; i++) {
        dragH = $_drag.eq(i).height();
        $(".navBor").eq(i).css("height", dragH)
    };
    var $_serverConLi = $(".show>.serverConLi");
    for (var i = 0; i < $_serverConLi.length; i++) {
        if (i % 2 == 1) {
            $_serverConLi.eq(i).addClass("serverRight0")
        }
    };
    var $_solutionConLi = $(".solutionConLi");
    for (var i = 0; i < $_solutionConLi.length; i++) {
        if (i % 2 == 1) {
            $_solutionConLi.eq(i).addClass("serverRight0")
        }
    };
    var $_recomentLi = $(".recomentLi");
    for (var i = 0; i < $_recomentLi.length; i++) {
        if (i == 2 || i == 5) {
            $_recomentLi.eq(i).addClass("serverRight0")
        }
    };
    if ($(".detailBox").length > 0) {
        boxFix();
        boxMove()
    };
    $(window).resize(function () {
        var banner = null;
        if ($(".detailBox").length > 0) {
            boxFix()
        }
    });
    banner(".bannerUl>li", "active", ".point>li", ".bannerLeft", ".bannerRight");

    function banner(a, b, c, d, e) {
        var width = $(a).width();
        index = 0;
        len = $(a).length - 1;

        function teb(index) {
            $(c).eq(index).addClass(b).siblings("").removeClass(b);
            $(".bannerUl").animate({
                marginLeft: -width * index
            })
        };
        $(c).click(function () {
            index = $(this).index();
            teb(index)
        });
        $(d).click(function () {
            index--;
            if (index < 0) {
                index = len
            };
            teb(index)
        });
        $(e).click(function () {
            index++;
            if (index > len) {
                index = 0
            };
            teb(index)
        });

        function timeRun() {
            time = setInterval(function () {
                    index++;
                    if (index > len) {
                        index = 0
                    };
                    teb(index)
                },
                5000)
        };
        timeRun()
    };
    var $_bor = $(".bor");
    $(".server .serverLi").click(function () {
        $(this).addClass("serverLiActive").siblings(".serverLi").removeClass("serverLiActive");
        var $_con = $(this).parents(".serverHead").siblings(".serverCon").children();
        var index = $(this).index();
        $_con.eq(index).addClass("show").siblings().removeClass("show");
        var $_serverConLi = $(".show>.serverConLi");
        for (var i = 0; i < $_serverConLi.length; i++) {
            if (i == 3 || i == 7) {
                $_serverConLi.eq(i).addClass("serverRight0")
            }
        };
        var w = $(this).outerWidth();
        var $obj = $(this).parent();
        var left = 0;
        for (var i = 0; i < index; i++) {
            left += $(">li", $obj).eq(i).outerWidth()
        };
        $_bor.animate({
            width: w,
            left: left
        })
    });
    var wloc = window.location.hash.replace("#/", "");
    $(".serverHead").each(function () {
        var hss = false;
        $("ul>li", this).each(function (i, item) {
            var hid = $(item).children("a").data("role");
            if (wloc == hid) {
                $(item).trigger("click");
                hss = true
            }
        });
        if (!hss) {
            $("ul>li:first", this).trigger("click")
        }
    });
    $(".pagesLi").click(function () {
        $(this).addClass("pagesLiActive").siblings(".pagesLi").removeClass("pagesLiActive")
    });
    var $_nav = $(".nav");
    var sc = $(document);
    $(window).scroll(function () {
        if (sc.scrollTop() >= 38) {
            $_nav.addClass("fix")
        } else {
            $_nav.removeClass("fix")
        }
    });
    var out;
    $(".navUl>li").hover(function () {
            clearTimeout(out);
            var $this = $(this),
                $c = $(this).children("ul");
            out = setTimeout(function () {
                    $c.stop().fadeIn(300)
                },
                100)
        },
        function () {
            clearTimeout(out);
            var $this = $(this),
                $c = $(this).children("ul");
            $c.stop().fadeOut(300)
        })
});

function boxFix() {
    var $_deban = $(".detailBox");
    if ($_deban.length == 0) {
        return
    };
    var offset = $_deban.offset();
    var $_ban = $(".bannerInfo");
    $_ban.css("position", "fixed");
    var sc = $(document);
    var left = offset.left + 825 + 59;
    var top = offset.top;
    $_ban.css("left", left).css("top", top);
    var fo = $("#helpInfo").offset().top,
        fh = $("#helpInfo").outerHeight(),
        bh = document.documentElement.clientHeight || document.body.clientHeight;
    var ph = $("body").height();
    var sh = $_ban.outerHeight();
    $(window).scroll(function () {
        if (sc.scrollTop() >= 110) {
            $_ban.addClass("ngsFix");
            if (sc.scrollTop() + 100 + sh >= fo - 100) {
                $_ban.css("top", bh - fh - $_ban.outerHeight() - 100)
            } else {
                $_ban.css("top", 100)
            }
        } else {
            $_ban.removeClass("ngsFix");
            $_ban.css("top", top - sc.scrollTop())
        }
    })
};

function boxMove() {
    var $_nav = $(".nav");
    var sc = $(document);
    $(window).scroll(function () {
        if (sc.scrollTop() >= 110) {
            $_nav.addClass("fix")
        } else {
            $_nav.removeClass("fix")
        }
    })
};

function _userInfo() {
    var uname = Cookies.get("UNAME");
    uname = uname ? uname.replace(/\+/g, "&nbsp;") : null;
    if (uname) {
        $("#headUl").html(
            '<li class="headLi">|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '<span onclick="phoneDialog()" style="cursor:pointer">400客服电话</span>' +
            '</li>' +
            '<li class="headLi">|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '<a rel="nofollow" href="javascript:;" onclick="addFavorite()">加入收藏</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</li>' +
            '<li class="headLi" style="margin-right:0">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '<a rel="nofollow" href="' + STORE_URL + '/myorder.html">我的订单</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '</li>' +
            '<li class="headLi">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '<a rel="nofollow" href="' + STORE_URL + '/member-center.html">个人中心</a>' +
            '</li>' +
            '<li class="headLi head_cart">|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '<a rel="nofollow" href="' + STORE_URL + '/cart.html" class="minicart shop_cart" id="minicart_">' +
            '<span class="minicart_num">' +
            '<img src="/static/images/top.cart.png">购物车（<b data-type="" class="up-cat-number">0</b>）' +
            '</span>' +
            '</a>' +
            '</li>' +
            '<li class="headLi">' +
            '您好，<span style="display:inline-block;min-width:3em;">' + uname + '</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '<a rel="nofollow" href="' + STORE_URL + '/passport-logout.html"">退出</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '</li>' +
            '<li class="headLi"></li>')
    } else {
        $("#headUl").html('<li class="headLi">|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '<span onclick="phoneDialog()" style="cursor:pointer">400客服电话</span>' +
            '</li>' +
            '<li class="headLi">|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '<a rel="nofollow" href="javascript:;" onclick="addFavorite()">加入收藏</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '</li>' +
            '<li class="headLi" style="margin-right:0">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '<a rel="nofollow" href="' + STORE_URL + '/myorder.html">我的订单</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '</li>' +
            '<li class="headLi">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '<a rel="nofollow" href="' + STORE_URL + '/member-center.html">个人中心</a>' +
            '</li>' +
            '<li class="headLi head_cart">|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '<a rel="nofollow" href="' + STORE_URL + '/cart.html"" class="minicart shop_cart" id="minicart_">' +
            '<span class="minicart_num">' +
            '<img src="/static/images/top.cart.png">购物车（<b data-type="" class="up-cat-number">0</b>）' +
            '</span>' +
            '</a>' +
            '</li>' +
            '<li class="headLi">' +
            '您好，请<a rel="nofollow" href="' + STORE_URL + '/passport-signin.html">登录</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '<a rel="nofollow" href="' + STORE_URL + '/passport-signup.html">注册</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
            '</li>' +
            '<li class="headLi"></li>')
    }
};

function _search(e) {
    e.preventDefault;
    var keycode = e.keyCode;
    if (keycode == 13) {
    }
};

function updateCartNumber(content) {
    content = $(content || ".up-cat-number");
    var type = $(content).attr("data-type");
    var number;
    type = (!type) ? 1 : type;
    if (type == 1) {
        number = Cookies.get("CARTNUMBER")
    } else {
        if (type == 2) {
            number = Cookies.get("CARTVARIETY")
        }
    };
    $(content).text(number || 0)
};

function goTop() {
    $("html,body").stop().animate({
        scrollTop: 0
    })
};
var url = document.location.href,
    title = "SGS Online";

function addFavorite() {
    if (window.sidebar && window.sidebar.addPanel) {
        window.sidebar.addPanel(title, url, "")
    } else {
        if (window.external && ("AddFavorite" in window.external)) {
            window.external.AddFavorite(url, title)
        } else {
            if (window.opera && window.print) {
                this.title = title;
                return true
            } else {
                alert("请使用 " + (navigator.userAgent.toLowerCase().indexOf("mac") != -1 ? "Command" : "CTRL") + " + D 加入收藏")
            }
        }
    }
};
var $pobj = null;

function PhoneShow(obj) {
    $pobj = $(obj);
    var $sobj = $pobj.children("span.sphone");
    if ($sobj.css("display") == "none") {
        $sobj.css("display", "block");
        $('.tipsss>ul').hide();
        record()
    } else {
        $sobj.css("display", "none")
    }
};

function composedPath(el) {
    var path = [];
    while (el) {
        path.push(el);
        if (el.tag_name === "HTML") {
            path.push(document);
            path.push(window);
            return path
        };
        el = el.parentElement
    }
};
document.addEventListener("click",
    function (event) {
        var event = event || window.event;
        var path = event.path || (event.composedPath && event.composedPath()) || composedPath(event.target);
        if ($pobj && path.indexOf($pobj[0]) < 0) {
            $pobj.children("span.sphone").css("display", "none")
        };
        if ($pobj && path.indexOf($pobj[0]) < 0) {
            $pobj.children("ul.zns").css("display", "none")
        }
    });

function phoneDialog() {
    var pw = $("html,body").height(),
        ph = $("html,body").height();
    $(".pshadeBox").height(ph).css("display", "block");
    $(".ptip_box").css("display", "block");
    record()
};

function complaintDialog() {
    var pw = $("html,body").height(),
        ph = $("html,body").height();
    $(".pshadeBox1").height(ph).css("display", "block");
    $(".ptip_box1").css("display", "block");
    record()
};

function closePD() {
    $(".pshadeBox, .ptip_box, .pshadeBox1, .ptip_box1").fadeOut()
};

function record() {
    var url = encodeURIComponent(window.location.href);
    $.get("/clickrecord/?url=" + url)
};

function ssShow(obj) {
    $pobj = $(obj);
    var $sobj = $pobj.children("ul");
    $sobj.css('top', -$('>li', $sobj).length * 33 / 2 + 28);
    if ($sobj.css("display") == "none") {
        $sobj.css("display", "block");
        $('.tips.phone>span.sphone').hide()
    } else {
        $sobj.css("display", "none")
    }
};

function fixPlaceholder() {
    $(".navSearchBox [placeholder]").focus(function () {
        var input = $(this);
        if (input.val() == input.attr("placeholder")) {
            input.val("");
            input.removeClass("placeholder")
        }
    }).blur(function () {
        var input = $(this);
        if (input.val() == "" || input.val() == input.attr("placeholder")) {
            input.addClass("placeholder");
            input.val(input.attr("placeholder"))
        }
    }).blur().parents("form").submit(function () {
        $(this).find("[placeholder]").each(function () {
            var input = $(this);
            if (input.val() == input.attr("placeholder")) {
                input.val("")
            }
        })
    })
}