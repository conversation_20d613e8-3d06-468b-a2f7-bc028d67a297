<% include header.html %>

    <div class="myLocation">
        <div class="myLocationBox">
            <div class="myLocation-word1"><a href="">首页</a></div>
            <div class="myLocation-icon1 icon"></div>
            <div class="myLocation-word2"><a href="/preview/news">新闻</a></div>
        </div>

    </div>
    <div class="newsBox">
        <div class="leftBox">
            <div class="news_title"><%- detail.title %></div>
            <div class="subhead">
                <div class="subhead1"><%- detail.gmt_publish_time %></div>
                <div class="subhead2"><%- detail.catalog_name %></div>
            </div>
            <div class="con1">
                <%- detail.content %>
            </div>

            <div class="chapters">
                <div class="lastchapter">上一篇：<% if(prevNews){ %><a href="detail-<%- prevNews.id %>.html"><%- prevNews.title %></a> <% }else{ %>无<% } %></div>
                <div class="nextchapter">下一篇：<% if(nextNews){ %><a href="detail-<%- nextNews.id %>.html"><%- nextNews.title %></a> <% }else{ %>无<% } %></div>
            </div>
        </div>

        <div class="rightBox">
            <div class="rightBox1">
                <div class="rela">相关内容：</div>

                <% for(var item of otherNews){ %>
                <div class="rightContitleBox">
                    <a class="rightContitle" href="detail-<%- item.id %>.html"><%- item.title %></a>
                    <div class="rightConSubtitle"><%- item.content %></div>
                </div>
                <% } %>
            </div>

        </div>
    </div>
</div>
<% include footer.html %>