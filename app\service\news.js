'use strict';

const Service = require('egg').Service;
const moment = require('moment');
const request = require('request');
const crypto = require('crypto');
const _siteInfo = require('../../config/info').siteInfo;
const solrUrl = _siteInfo.solrUrl,
  solrPstr = _siteInfo.solrPstr,
  solrPid = _siteInfo.solrPid;
const { env } = require('../../config/info').siteInfo;

class NewsService extends Service {
  async typeList() {
    const {
      app
    } = this;

    const list = await app.mysql.select('catalog', {
      columns: ['id', 'name', 'alias'],
      where: {
        is_delete: 0,
        parent_id: 3
      },
      orders: [
        ['id', 'desc']
      ]
    });

    for (let item of list) {
      item.num = await app.mysql.count('news', {
        catalog_id: item.id,
        is_delete: 0,
        is_publish: 1
      });
    }

    return {
      list
    };
  }

  async addType(params) {
    const {
      app
    } = this;

    const gmt_create = await moment(new Date()).format('YYYY-MM-DD HH:mm:ss');

    const logRow = {
      user_name: this.ctx.session.userInfo.name,
      log: '创建' + params.name,
      model: '新闻中心-新闻类别',
      name: params.name
    }

    const result = await app.mysql.beginTransactionScope(async conn => {
      await conn.insert('catalog', {
        name: params.name,
        alias: params.alias,
        parent_id: 3,
        gmt_create: gmt_create,
      });
      await conn.insert('logs', logRow);
      return {
        success: true
      };
    }, this.ctx);

    // const success = result.affectedRows === 1;
    return {
      success: result.success
    };
  }

  async typeGet(params) {
    const {
      app
    } = this;
    const info = await app.mysql.select('catalog', {
      columns: ['id', 'name', 'alias'],
      where: {
        id: params.id
      },
      limit: 1,
      offset: 0
    });

    return {
      info
    };
  }

  async typeEdit(params) {
    const {
      app
    } = this;
    // const result = await app.mysql.update('catalog', { id: Number(params.id), name: params.name, alias: params.alias });

    const logRow = {
      user_name: this.ctx.session.userInfo.name,
      log: '编辑' + params.name,
      model: '新闻中心-新闻类别',
      name: params.name
    }

    const result = await app.mysql.beginTransactionScope(async conn => {
      await conn.update('catalog', {
        id: Number(params.id),
        name: params.name,
        alias: params.alias
      });

      await conn.insert('logs', logRow);
      return {
        success: true
      };
    }, this.ctx);


    // const success = result.affectedRows === 1;
    return {
      success: result.success
    };
  }

  async typeDelete(params) {
    const {
      app
    } = this;
    // const result = await app.mysql.update('catalog', { id: params.id, is_delete: 1 });

    const result = await app.mysql.beginTransactionScope(async conn => {
      await conn.update('catalog', {
        id: params.id,
        is_delete: 1
      });

      const info = await app.mysql.get('catalog', {
        id: params.id
      });

      await conn.insert('logs', {
        user_name: this.ctx.session.userInfo.name,
        log: '删除' + info.name,
        model: '新闻中心-新闻类别',
        name: info.name
      });
      return {
        success: true
      };
    }, this.ctx);

    // const success = result.affectedRows === 1;
    return {
      success: result.success
    };
  }

  async getList(params) {
    if (!params) {
      params = {};
    }

    const {
      app
    } = this;
    let query = ['n.is_delete=0'];
    if (params && params.type && params.type != 0) {
      query.push('c.id=' + params.type);
    }

    if (params && params.title) {
      query.push('n.title LIKE "%' + params.title + '%"');
    }

    if (params && typeof params.is_publish == 'number') {
      query.push('n.is_publish=' + params.is_publish);
    }

    if (params && params.service) {
      query.push('cr.catalog_id=' + params.service);
    }

    if (params && params.trade) {
      query.push('cr.catalog_id=' + params.trade);
    }

    let baseCategoryIds_service = [];
    let baseCategoryIds_trade = []

    if (params && params.allowServiceList != null && params.allowServiceList.length > 0) {
      for (let idvalue of params.allowServiceList) {
        if (idvalue != null) {
          baseCategoryIds_service.push(idvalue);
        }
      }
    }

    if (params && params.allowTradeList != null && params.allowTradeList.length > 0) {
      for (let idvalue of params.allowTradeList) {
        if (idvalue != null) {
          baseCategoryIds_trade.push(idvalue);
        }
      }
    }

    if (baseCategoryIds_service.length == 0 && this.ctx.session.userInfo.type == 2) {
      query.push('1=2');
    }

    if (baseCategoryIds_trade.length == 0 && this.ctx.session.userInfo.type == 2) {
      query.push('1=2');
    }

    if (params.bu_id) {
      query.push('n.bu_id=' + params.bu_id);
    }

    let page = params.page || 1,
      limit = params.limit || 10;
    let start = (page - 1) * limit;


    var countsql = 'SELECT count(distinct(n.id)) as countval FROM news n LEFT JOIN catalog c ON n.catalog_id=c.id LEFT JOIN catalog_relation cr ON cr.news_id=n.id';
    var sql = 'SELECT n.id,n.alias,n.title,n.is_top,n.is_publish,n.last_modify,n.gmt_modify,n.gmt_publish_time AS time,c.name AS typeName, b.BU_NAME as bu_name FROM news n LEFT JOIN catalog c ON n.catalog_id=c.id LEFT JOIN catalog_relation cr ON cr.news_id=n.id  LEFT JOIN BU b ON n.bu_id=b.BU_ID';
    if (baseCategoryIds_trade.length > 0) {
      countsql += " left join (select news_id from catalog_relation where catalog_type=1 and news_id is not null and catalog_id not in (" + baseCategoryIds_trade.join(',') + ")) tmptrade on n.id=tmptrade.news_id";
      sql += " left join (select news_id from catalog_relation where catalog_type=1 and news_id is not null and catalog_id not in (" + baseCategoryIds_trade.join(',') + ")) tmptrade on n.id=tmptrade.news_id";
      query.push('tmptrade.news_id is null');
    }
    if (baseCategoryIds_service.length > 0) {
      countsql += " left join (select news_id from catalog_relation where catalog_type=2 and news_id is not null and catalog_id not in (" + baseCategoryIds_service.join(',') + ")) tmpservice on n.id=tmpservice.news_id";
      sql += " left join (select news_id from catalog_relation where catalog_type=2 and news_id is not null and catalog_id not in (" + baseCategoryIds_service.join(',') + ")) tmpservice on n.id=tmpservice.news_id";
      query.push('tmpservice.news_id is null');
    }
    countsql += ' WHERE ' + query.join(' AND ');
    const total = await app.mysql.query(countsql);
    sql += ' WHERE ' + query.join(' AND ') + ' GROUP BY n.id ORDER BY n.is_top DESC,n.gmt_modify DESC,n.gmt_publish_time DESC LIMIT ' + start + ',' + limit;


    const list = await app.mysql.query(sql);

    for (let item of list) {
      item.time = moment(item.time).format('YYYY-MM-DD HH:mm:ss');
      item.gmt_modify = moment(item.gmt_modify).format('YYYY-MM-DD HH:mm:ss');

      let tradeList = [],
        serviceList = [];
      let tradeCata = await this.app.mysql.select('catalog_relation', {
        where: {
          news_id: item.id,
          catalog_type: 1
        }
      });
      let serviceCate = await this.app.mysql.select('catalog_relation', {
        where: {
          news_id: item.id,
          catalog_type: 2
        }
      });
      let catalogList = await this.app.mysql.query(`select a.alias from catalog a,catalog_relation b where news_id=${item.id} and b.catalog_id=a.id and a.alias!='subclass'`)
      item.catalog = catalogList

      if (tradeCata && tradeCata.length > 0) {
        for (let ti of tradeCata) {
          let tradeInfo = await this.app.mysql.get('catalog', {
            id: ti.catalog_id
          });
          let tradeParantInfo = await this.app.mysql.get('catalog', {
            id: tradeInfo.parent_id
          });
          //tradeList.push(tradeParantInfo.name + '-' + tradeInfo.name);
          tradeList.push(tradeInfo.name);
        }
      }

      if (serviceCate && serviceCate.length > 0) {
        for (let si of serviceCate) {
          let serviceInfo = await this.app.mysql.get('catalog', {
            id: si.catalog_id
          });
          let serviceParantInfo = await this.app.mysql.get('catalog', {
            id: serviceInfo.parent_id
          });
          //serviceList.push(serviceParantInfo.name + '-' + serviceInfo.name);
          serviceList.push(serviceInfo.name);
        }
      }

      item.tradeList = tradeList;
      item.serviceList = serviceList;

      let checkflowObj = await this.ctx.service.checkflow.getDetail('news', item.id);

      item.tran_result = checkflowObj;
    }

    return {
      list: list,
      total: total[0]["countval"],
      page: page,
      limit: limit
    }
  }

  async getDetail(id) {
    const {
      app
    } = this;

    const detail = await app.mysql.get('news', {
      id: id
    });

    const tag = await app.mysql.select('tag_relation', {
      columns: ['id', 'sku_id', 'tag_name'],
      where: {
        'sku_id': detail.id
      },
    });

    const tradeCata = await app.mysql.select('catalog_relation', {
      where: {
        news_id: id,
        catalog_type: 1
      }
    });
    const serviceCata = await app.mysql.select('catalog_relation', {
      where: {
        news_id: id,
        catalog_type: 2
      }
    });
    const tagList = await app.mysql.select('tag_relation', {
      where: {
        news_id: id
      }
    });

    if (tradeCata && tradeCata.length > 0) {
      for (let ti of tradeCata) {
        let tradeInfo = await app.mysql.get('catalog', {
          id: ti.catalog_id
        });
        let tradeParantInfo = await app.mysql.get('catalog', {
          id: tradeInfo.parent_id
        });
        ti.parentName = tradeParantInfo.name;
        ti.name = tradeInfo.name;
        ti.id = tradeInfo.id;
      }
    }

    if (serviceCata && serviceCata.length > 0) {
      for (let si of serviceCata) {
        let serviceInfo = await app.mysql.get('catalog', {
          id: si.catalog_id
        });
        let serviceParantInfo = await app.mysql.get('catalog', {
          id: serviceInfo.parent_id
        });
        si.parentName = serviceParantInfo.name;
        si.name = serviceInfo.name;
        si.id = serviceInfo.id;
      }
    }

    if (detail.is_publish == 1) {
      detail.is_publish = true;
    } else {
      detail.is_publish = false;
    }

    if (detail.is_top == 1) {
      detail.is_top = true;
    } else {
      detail.is_top = false;
    }

    detail.tag = [];
    for (let item of tagList) {
      detail.tag.push(item.tag_name);
    }

    detail.tradeCata = tradeCata;
    detail.serviceCata = serviceCata;

    if (detail.content) {
      detail.content = detail.content.replace(/\n/g, '').replace(/\.\.\/\.\.\/static/g, '/static').replace(/\.\.\/static/g, '/static');
    }

    return {
      detail
    };
  }

  async newsAdd(params) {
    const {
      app
    } = this;

    const gmt_create = await moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
    const last_modify = this.ctx.session.userInfo.name;

    const row = {
      id: params.id,
      title: params.title,
      alias: params.alias,
      bu_id: params.bu_id,
      content: params.content,
      author: params.author,
      seo_text: params.seo_text,
      is_top: params.is_top ? 1 : 0,
      is_publish: params.is_publish ? 1 : 0,
      gmt_publish_time: params.gmt_publish_time ? moment(new Date(params.gmt_publish_time)).format('YYYY-MM-DD HH:mm:ss') : gmt_create,
      catalog_id: params.type,
      last_modify: last_modify,
      page_title: params.page_title,
      page_keywords: params.page_keywords,
      page_description: params.page_description,
      mpage_title: params.mpage_title,
      mpage_keywords: params.mpage_keywords,
      mpage_description: params.mpage_description
    }

    const tradeList = params.tradeList,
      serviceList = params.serviceList,
      tagList = params.tag;

    let result = '',
      sid = 0;

    let orgResource, orgCatat = [],
      orgCatat2 = [],
      orgCatas = [],
      orgCatas2 = [],
      tagCata = [],
      newCatat = [],
      newCatas = [],
      newTag = [];
    let changeValue = [];
    result = await app.mysql.beginTransactionScope(async conn => {
      if (params.id && params.id != 0) {
        sid = params.id;

        orgCatat = await conn.select('catalog_relation', {
          where: {
            news_id: sid,
            catalog_type: 1
          }
        });
        orgCatas = await conn.select('catalog_relation', {
          where: {
            news_id: sid,
            catalog_type: 2
          }
        });
        tagCata = await conn.select('tag_relation', {
          where: {
            news_id: sid
          }
        });
        for (let item of orgCatat) {
          orgCatat2.push(item.catalog_id);
        }

        for (let item of orgCatas) {
          orgCatas2.push(item.catalog_id);
        }

        for (let item of tradeList) {
          newCatat.push(item)
        }

        for (let item of serviceList) {
          newCatas.push(item)
        }

        if (newCatat.sort().toString() != orgCatat2.sort().toString()) {
          changeValue.push('修改基本属性-所属行业');
        }

        if (newCatas.sort().toString() != orgCatas2.sort().toString()) {
          changeValue.push('修改基本属性-所属服务');
        }

        let tagCata2 = [];
        for (let item of tagCata) {
          tagCata2.push(item.tag_name);
        }

        if (tagCata2.sort().toString() != tagList.sort().toString()) {
          changeValue.push('修改新闻内容-标签');
        }

        orgResource = await conn.get('news', {
          id: sid
        });

        if (params.title != orgResource.title) {
          changeValue.push('修改新闻内容-名称');
        }

        if (params.bu_id != orgResource.bu_id) {
          changeValue.push('修改基本属性-BU');
        }

        if (params.type != orgResource.catalog_id) {
          changeValue.push('修改基本属性-类型');
        }

        if (params.content != orgResource.content) {
          changeValue.push('修改新闻内容-内容');
        }

        if (params.author != orgResource.author) {
          changeValue.push('修改新闻内容-作者');
        }

        if (params.seo_text != orgResource.seo_text) {
          changeValue.push('修改新闻内容-关键字');
        }

        if (params.gmt_publish_time != orgResource.gmt_publish_time) {
          changeValue.push('修改新闻内容-发布时间');
        }

        if (params.is_publish != orgResource.is_publish) {
          changeValue.push('修改上下架');
        }

        if (params.is_top != orgResource.is_top) {
          changeValue.push('修改新闻内容-是否置顶');
        }

        if (params.page_title != orgResource.page_title) {
          changeValue.push('修改SEO设置-Title');
        }

        if (params.page_keywords != orgResource.page_keywords) {
          changeValue.push('修改SEO设置-Keywords');
        }

        if (params.page_description != orgResource.page_description) {
          changeValue.push('修改SEO设置-Description');
        }

        if (params.mpage_title != orgResource.mpage_title) {
          changeValue.push('修改移动端SEO设置-Title');
        }

        if (params.mpage_keywords != orgResource.mpage_keywords) {
          changeValue.push('修改移动端SEO设置-Keywords');
        }

        if (params.mpage_description != orgResource.mpage_description) {
          changeValue.push('修改移动端SEO设置-Description');
        }

        let checkflowResult = await this.ctx.service.checkflow.getDetail('news', params.id);
        row.gmt_modify = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        await conn.update('news', row);
        await conn.delete('catalog_relation', {
          news_id: sid
        });
        await conn.delete('tag_relation', {
          news_id: sid
        });

        if (row.is_publish == 1 && checkflowResult != null) //如果是发布在去修改状态
        {
          await conn.update('checkflow', {
            id: checkflowResult.id,
            tran_note: '',
            tran_status: 10,
            tran_admin_time: gmt_create,
            tran_admin_id: this.ctx.session.userInfo.id
          });
        } else if (checkflowResult != null) {
          await conn.update('checkflow', {
            id: checkflowResult.id,
            is_overdue: 1
          });
        }
        if (changeValue.length > 0) {
          await conn.insert('logs', {
            user_name: this.ctx.session.userInfo.name,
            news_id: sid,
            log: changeValue.join(','),
            model: '新闻中心',
            name: row.title
          });
        }
      } else {
        row.gmt_create = gmt_create;
        const insertResult = await conn.insert('news', row);
        sid = insertResult.insertId;

        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          news_id: sid,
          log: '创建' + row.title,
          model: '新闻中心',
          name: row.title
        });
      }

      let tradeNames = [],
        serviceNames = [];
      for (let item of tradeList) {
        await conn.insert('catalog_relation', {
          news_id: sid,
          catalog_id: item,
          catalog_type: 1,
          gmt_create: gmt_create
        });
        let cataInfo = await conn.get('catalog', {
          id: item
        });
        if (cataInfo) {
          tradeNames.push(cataInfo.name);
        }
      }

      for (let item of serviceList) {
        await conn.insert('catalog_relation', {
          news_id: sid,
          catalog_id: item,
          catalog_type: 2,
          gmt_create: gmt_create
        });
        let cataInfo = await conn.get('catalog', {
          id: item
        });
        if (cataInfo) {
          serviceNames.push(cataInfo.name);
        }
      }

      for (let item of tagList) {
        if (item) {
          await conn.insert('tag_relation', {
            tag_name: item,
            news_id: sid
          });
        }
      }

      const cReg = new RegExp(/(<p>)[^>]*>([\s\S]*?)(?=<\/p>)/gi);
      const newDetail = params.content.match(cReg);
      let outSolrCon = '';
      // for(let item of newDetail){
      //     item = item.replace(/<[^>]*>/g, "");
      //     if(item.trim() != '' && outSolrCon == ''){
      //         outSolrCon = item;
      //     }
      // }

      if (newDetail) {
        for (let item2 of newDetail) {
          item2 = item2.replace(/<[^>]*>/g, "");
          if (item2.trim() != '' && outSolrCon == '') {
            outSolrCon = item2;
          }
        }
      } else {
        outSolrCon = params.content.replace(/<[^>]*>/g, "");
      }

      // 获取html文本中的第一张图片
      let contentImg = ''
      params.content.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/, function (match, capture) {
        contentImg = capture
      });
      let cataInfo = await conn.get('catalog', {
        id: params.type
      });

      if (params.is_publish) {
        let options = {
          url: env[this.app.config.env].solrUrl + '/add',
          method: 'POST',
          headers: {
            pid: solrPid,
            timestamp: new Date().getTime(),
          },
          json: true,
          body: {
            id: 'N_' + sid,
            title: params.title,
            alias: row.alias,
            sub_title: '',
            content: params.content.replace(/<.*?>/g, "").replace(/\n/g, ''),
            // summary: outSolrCon,
            summary: '',
            type: '3',
            buy: 0,
            industry: tradeNames.join(','),
            service: serviceNames.join(','),
            imgUrl: contentImg,
            catalog: params.catalog,
            subType: cataInfo.name,
            createDate: moment(new Date()).format('YYYY/MM/DD')
          }
        }

        let parr = JSON.stringify(options.body);
        parr += solrPstr;
        parr = crypto.createHash('md5').update(parr).digest("hex");
        options.headers.sign = parr;

        request(options, function (error, response, body) {
          if (error || response.statusCode > 400) {
            return {
              success: false
            };
          }
        });
      } else {

        let options = {
          url: env[this.app.config.env].solrUrl + '/del',
          method: 'POST',
          headers: {
            pid: solrPid,
            timestamp: new Date().getTime(),
          },
          json: true,
          body: {
            id: 'N_' + sid,
          }
        }

        let parr = JSON.stringify(options.body);
        parr += solrPstr;
        parr = crypto.createHash('md5').update(parr).digest("hex");
        options.headers.sign = parr;

        request(options, function (error, response, body) {
          if (error || response.statusCode > 400) {
            return {
              success: false
            };
          }
        });
      }

      return {
        success: true
      };
    }, this.ctx);

    // if (result.success) {
    //   await this.ctx.service.baidu.Push(`/news/${params.alias}/detail-${sid}.html`);
    //   const cndParams = {
    //     fileUrls: [params.pageLink]
    //   }
    //   await this.ctx.service.external.CDNRefresh(cndParams);
    // }
    return {
      success: result.success,
      id: sid
    };
  }

  async newsDelete(id, pageLink) {
    const {
      app
    } = this;

    const result = await app.mysql.beginTransactionScope(async conn => {
      await conn.update('news', {
        id: id,
        is_delete: 1,
        gmt_modify :moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      });

      if (typeof id == 'object') {
        for (let item of id) {
          const info = await this.app.mysql.get('news', {
            id: item
          });
          await conn.insert('logs', {
            user_name: this.ctx.session.userInfo.name,
            news_id: item,
            log: '删除' + info.title,
            model: '新闻中心',
            name: info.title
          });
        }
      } else {
        const info = await this.app.mysql.get('news', {
          id: id
        });
        await conn.insert('logs', {
          user_name: this.ctx.session.userInfo.name,
          news_id: id,
          log: '删除' + info.title,
          model: '新闻中心',
          name: info.title
        });
      }

      return {
        success: true
      };
    }, this.ctx);

    let options = {
      url: env[this.app.config.env].solrUrl + '/del',
      method: 'POST',
      headers: {
        pid: solrPid,
        timestamp: new Date().getTime(),
      },
      json: true,
      body: {
        id: 'N_' + id,
      }
    }

    let parr = JSON.stringify(options.body);
    parr += solrPstr;
    parr = crypto.createHash('md5').update(parr).digest("hex");
    options.headers.sign = parr;

    request(options, function (error, response, body) {
      if (error || response.statusCode > 400) {
        return;
      }
    });

    // if (result.success) {
    //   // 保存成功推送CDN刷新
    //   const cndParams = {
    //     fileUrls: [pageLink]
    //   }
    //   await this.ctx.service.external.CDNRefresh(cndParams);
    // }

    // const success = result.affectedRows > 0;
    return {
      success: result.success
    };
  }

  async getNewsRelate(id) {
    const {
      app
    } = this;

    const thisInfo = await app.mysql.get('news', {
      id: id
    });

    const publishTime = moment(thisInfo.gmt_publish_time).format('YYYY-MM-DD HH:mm:ss');

    const prevItem = await app.mysql.query('SELECT id,title FROM news WHERE unix_timestamp(gmt_publish_time)<unix_timestamp("' + publishTime + '") AND is_publish=1 AND is_delete=0 ORDER BY gmt_publish_time DESC LIMIT 1');
    const nextItem = await app.mysql.query('SELECT id,title FROM news WHERE unix_timestamp(gmt_publish_time)>unix_timestamp("' + publishTime + '") AND is_publish=1 AND is_delete=0 ORDER BY gmt_publish_time DESC LIMIT 1');
    //const nextItem = await app.mysql.get('news',{gmt_publish_time: > publishTime});

    return {
      prevItem,
      nextItem
    }
  }

  async otherNews(id) {
    const {
      app
    } = this;
    const cata = await app.mysql.get('news', {
      id: id
    });
    const catalog_id = cata.catalog_id;
    const list = await app.mysql.query('SELECT id,title,content FROM news WHERE is_delete=0 AND is_publish=1 AND id!=' + id + ' AND catalog_id=' + catalog_id + ' ORDER BY is_top DESC,gmt_publish_time DESC LIMIT 0,10');

    return {
      list
    }
  }

  async updatePublish(params) {
    const {
      app
    } = this;

    if (!params.id) {
      return 'fail';
    }

    const row = {
      id: params.id,
      is_publish: params.is_publish
    }
    row.gmt_modify = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    const result = await app.mysql.update('news', row);

    const info = await app.mysql.get('news', {
      id: params.id
    });
    await this.ctx.service.logs.log({
      type: 'news',
      id: params.id,
      log: '修改上下架',
      model: '新闻中心',
      name: info.title
    });

    if (params.is_publish == 1) {
      const info = await app.mysql.get('news', {
        id: params.id
      });
      if (!info) {
        return;
      }

      let tradeNames = [],
        serviceNames = [];
      const tradeList = await app.mysql.select('catalog_relation', {
        news_id: params.id,
        catalog_type: 1
      });
      if (tradeList && tradeList.length > 0) {
        for (let item of tradeList) {
          let cataInfo = await app.mysql.get('catalog', {
            id: item.catalog_id
          });
          if (cataInfo) {
            tradeNames.push(cataInfo.name);
          }
        }
      }

      const serviceList = await app.mysql.select('catalog_relation', {
        news_id: params.id,
        catalog_type: 2
      });
      if (serviceList && serviceList.length > 0) {
        for (let item of serviceList) {
          let cataInfo = await app.mysql.get('catalog', {
            id: item.catalog_id
          });
          if (cataInfo) {
            serviceNames.push(cataInfo.name);
          }
        }
      }

      const cReg = new RegExp(/(<p>)[^>]*>([\s\S]*?)(?=<\/p>)/gi);
      const newDetail = info.content.match(cReg);
      let outSolrCon = '';
      if (newDetail) {
        for (let item2 of newDetail) {
          item2 = item2.replace(/<[^>]*>/g, "");
          if (item2.trim() != '' && outSolrCon == '') {
            outSolrCon = item2;
          }
        }
      } else {
        outSolrCon = info.content.replace(/<[^>]*>/g, "");
      }

      // 获取html文本中的第一张图片
      let contentImg = ''
      info.content.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/, function (match, capture) {
        contentImg = capture
      });
      let cataInfo = await app.mysql.get('catalog', {
        id: params.type
      });

      let options = {
        url: env[this.app.config.env].solrUrl + '/add',
        method: 'POST',
        headers: {
          pid: solrPid,
          timestamp: new Date().getTime(),
        },
        json: true,
        body: {
          id: 'N_' + info.id,
          title: info.title,
          alias: row.alias,
          sub_title: '',
          content: info.content.replace(/<.*?>/g, "").replace(/\n/g, ''),
          // summary: outSolrCon,
          summary: '',
          type: '3',
          buy: 0,
          industry: tradeNames.join(','),
          service: serviceNames.join(','),
          imgUrl: contentImg,
          catalog: tradeNames.concat(serviceNames).join(','),
          subType: cataInfo.name,
          createDate: moment(new Date()).format('YYYY/MM/DD')
        }
      }

      let parr = JSON.stringify(options.body);
      parr += solrPstr;
      parr = crypto.createHash('md5').update(parr).digest("hex");
      options.headers.sign = parr;

      request(options, function (error, response, body) {
        if (error || response.statusCode > 400) {
          return;
        }
      });
    } else {
      let options = {
        url: env[this.app.config.env].solrUrl + '/del',
        method: 'POST',
        headers: {
          pid: solrPid,
          timestamp: new Date().getTime(),
        },
        json: true,
        body: {
          id: 'N_' + params.id,
        }
      }

      let parr = JSON.stringify(options.body);
      parr += solrPstr;
      parr = crypto.createHash('md5').update(parr).digest("hex");
      options.headers.sign = parr;

      request(options, function (error, response, body) {
        if (error || response.statusCode > 400) {
          return;
        }
      });
    }

    const success = result.affectedRows === 1;
    return {
      success
    };
  }
}

module.exports = NewsService;
