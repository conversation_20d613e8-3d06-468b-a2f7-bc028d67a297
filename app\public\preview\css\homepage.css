html,body{
    margin: 0;padding: 0;
}
a{
    text-decoration: none;
}

*{
    font-family: Microsoft Yahei;
}
/*head css*/
.headBox{
    width: 100%;height: 38px;
    background: rgb(51,51,51);
}
.head{
    width: 1226px;height: 38px;background: rgb(51,51,51);
    margin: 0 auto;
}
.sgs{
    font-size: 12px;line-height: 36px;
    color: rgb(176,176,176);
    float: left;

}
.headUl{
    width: auto;height: 36px;
    float: right;
    margin: 0;padding: 0;
    /*margin-right: 87px;*/
}
.headLi{
    float: right;
    margin-right: 34px;
    line-height: 36px;
    color: rgb(176,176,176);
    font-size: 12px;
    list-style: none;
}
.headLi a{
    color: rgb(176,176,176);
}
.headLi.first{
    width: 118px;
    height: 100%;
    margin-right: 0;
    background: rgb(66,66,66);
}
.headLi-first-icon{
    width: 17px;height: 16px;
    margin-top: 10px;margin-left: 18px;
    display: inline-block;
    float: left;
    background: url(../images/shop.png) no-repeat 100% 100%;
    /*background-size: 100%;*/
}
.headLi-first-word{
    width: auto;height: 100%;line-height: 39px;
    margin-right: 14px;
    display: inline-block;
    float: right;
    font-size: 12px;
    color: rgb(176,176,176);
}
/*nav css*/
.navBox{
    width: 1226px;height: auto;
    margin: 0 auto;
    background: white;
}
.nav.fix{
    position: fixed;
    left: 0;
    top: 0;
    z-index: 2000;
    height: 100px;
    box-shadow: 0 0 10px rgba(0,0,0,.1);
    transition: .5s;
    /*overflow: hidden;*/
}

.nav{
    width: 100%;height: 0;margin: 0 auto;
    /*position: relative;*/
    background: rgb(255,255,255);
    /*overflow: hidden;*/
}
.navLi a{
    color: rgb(51,51,51);
}
.navLi a:hover{
    color: rgb(254,102,3);
    font-weight: bold;
}
.drag{
    width: 402px;
    height: auto;
    padding: 10px 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, .15);
    position: absolute;
    z-index: 9999;
    top: 97px;
    left: -152px;
    border-right: 1px solid rgba(0, 0, 0, .15);
    border-left: 1px solid rgba(0, 0, 0, .15);
    border-bottom: 1px solid rgba(0, 0, 0, .15);
    display: none;
    border-top: 3px solid #fe6602;
    background: white;
}
.navBor{
    position: absolute;width: 1px;background: #eeeeee;
    height: auto;top: 10px;left: 200px;
}
.dragAngel{
    width: 0;
    height: 0;
    position: absolute;
    overflow: hidden;
    font-size: 0;
    top: -13px;
    left: 190px;
    line-height: 0px;
    border-width: 0px 10px 10px;
    border-style: solid solid solid solid;
    border-left-color: transparent;
    border-right-color: transparent;
    border-top-color: transparent;
    border-bottom-color: rgb(254,102,3);

}
.dragLi{
    width: 200px;
    height: 35px;
    line-height: 35px;
    font-size: 14px;
    float: left;
    /*margin-left: 20px;*/
    text-align: center;
    list-style: none;

    /*margin-right: 300px;*/
}
.navImg{
    width: 108px;height: 53px;margin-top: 24px;
    /*background: #ccc;*/
    float: left;
    padding-bottom: 20px;
}
.navImg img{
    width: 100%;height: 100%;
}
.navUl{
    float: left;
    width: auto;height: 100%;
    /*margin-right: 700px;*/
    margin-left: 50px;
    margin-top: 0;margin-bottom: 0;
    padding: 0;
}
.navLi{
    width: 100px;height: 100px;line-height: 100px;
    font-size: 16px;color: rgb(51,51,51);
    float: left;
    position: relative;
    cursor: pointer;
    text-align: center;
    margin: 0 25px;
    list-style: none;

}

.navLi.s:hover{
    color: rgb(254,102,2);
    font-weight: bold;
}

.navLi.t:hover{
    color: rgb(254,102,3);
    font-weight: bold;
}
.navLi.t{

    margin-right: 0;
}
.drag .dragLi{
    color: rgb(51,51,51);
    font-weight: normal;
}
.navSearchBox{
    /*width: auto;*/
    height: 46px;
    width: 45%;
    float: right;
    margin-top: 27px;
}
.navSearchBox form{
    width: 100%;height: 100%;
}
.navSearch{
    width: 297px;height: 40px;
    border: none;outline: none;
    border: 1px solid rgb(254,102,2);
    float: right;
    padding: 2px 0 2px 10px;
    font-size: 16px;
    line-height: 40px;
}
.navSearchIcon{
    width: 56px;height: 46px;
    background: rgb(254,102,2);
    /*background: url(../images/1-3.jpg) no-repeat 50% 50%;*/
    /*background-size: contain;*/
    float: right;
    position: relative;
    cursor: pointer;
}
.navSearchIconIcon{
    width: 20px;height: 20px;
    background: url(../images/search.png) no-repeat 50% 50%;
    /*background-size: 100%;*/
    position: absolute;
    top: 0;left: 0;right: 0;bottom: 0;
    margin: auto;
}
/*banner css*/
.banner{
    width: 100%;height: 374px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    margin-bottom: 22px;
    /*background: url(../images/banner.jpg) no-repeat 100% 100%;*/

}
.bannerUl{
    width: 100%;height: 100%;
    position: relative;
}
.bannerLi1{
    background: url(../images/banner.jpg) no-repeat 50% 50%;
    background-size: cover;
	height: 100%;
}
.bannerLi2{
    background: url(../images/banner3.jpg) no-repeat 50% 50%;
    background-size: cover;
	height: 100%;
}
.point{
    width: 46px;
    height: 10px;
    left: 50%;
    margin-left: -23px;
    /*transform: translateX(50%);*/
    bottom: 28px;
    position: absolute;
    /*background: red;*/
}
.point li{
    position: relative;

    width: 10px;height: 10px;
    border-radius: 50%;
    background: white;
    float: left;
    margin-right: 13px;
    cursor: pointer;
    -ms-behavior: url(../PIE/ie-css3.htc);

}
.point li.active{
    background: rgb(254,102,2);
}
.bannerTitle{
    width: 100%;
    /*margin-top: 99px;*/
    text-align: center;
    position: absolute;
    top: 99px;
    font-size: 35px;
    color: rgb(255,254,254);
}
.bannerDetail{
    width: 100%;
    text-align: center;
    font-size: 16px;
    color: rgb(255,254,254);
    /*margin-top: 35px;*/
    position: absolute;
    top: 170px;
}
.bannerMore{
    width: 157px;height: 46px;
    position: absolute;
    bottom: 120px;
    left: 50%;
    bottom: 90px;
    margin-left: -90px;
    /*transform: translateX(-50%);*/
    line-height: 46px;
    text-align: center;
    color: rgb(255,255,255);

    background: rgb(0, 0, 0);    /*不支持rgba的浏览器*/
    background: rgba(0,0,0,.5);  /*支持rgba的浏览器*/
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#7f000000,endColorstr=#7f000000);

}
.bannerMore:hover .bannermoreBox{
    width: 157px;
    transition: .4s;
}
.bannermoreword{
    width: 157px;
    height: 100%;
    line-height: 46px;
    text-align: center;
    position: absolute;
    top: 0;left: 0;bottom: 0;right: 0;
    margin: auto;
    cursor: pointer;

}
.bannerLeft{
    width: 36px;height: 54px;
    top: 160px;;
    left: 80px;
    position: absolute;
    background: url(../images/leftarrow.png) no-repeat 100% 100%;

    display: none;
}
.bannerRight{
    width: 36px;height: 54px;
    top: 160px;
    right: 80px;
    position: absolute;
    background: url(../images/rightarrow.png) no-repeat 100% 100%;
    /*background-size: contain;*/
    display: none;
}
.banner:hover .bannerLeft{
    display: block;
}
.banner:hover .bannerRight{
    display: block;
}
.bannermoreword:hover .bannermoreBox{
    width: 180px;
    transition: .4s;
}
/*location css*/
.location{
    width: 1226px;
    height: 20px;
    margin: 0 auto;
}
.location ul{
    margin: 0;padding: 0;
}
.locationLi{
    float: left;
    margin-right: 20px;
    line-height: 17.78px;
    font-size: 16px;
    list-style: none;
}
.locationLi a{
    color: #000;
}
.locationLi.icon{
    width: 13px;height: 16px;
    background: url(../images/arrow.png) no-repeat;
}
.dragLi-icon{
    width: 13px;height: 16px;
    background: url(../images/arrow.png) no-repeat;
    float: left;
    margin-top: 12px;
    margin-right: 4px;
}
.dragLi-word{
    width: 100%;
    float: left;
    text-align: center;
}
/*.dragLi-word:nth-child(n){*/
    /*border-right: 2px solid #eeeeee;*/
    /*behavior: url(../PIE/PIE.htc);*/
/*}*/
.locationLi:nth-child(4){
    width: 10px;height: 15px;
    background: url(../images/arrow.png) no-repeat;
    /*background-size: contain;*/
}

/*introduce css*/
.introduce{
    width: 1226px;min-height: 400px;
    margin: 42px auto 30px;
}
.introduceTitle{
    font-size: 32px;
    color: rgb(51,51,51);
    margin-bottom: 35px;
}
.introduceLeft{
    width: 670px;
    /*float: left;*/
    display: inline-block;
}
.introduceLeft-word1{
    display: block;
    font-size: 16px;
    line-height: 1.69;
    color: rgb(102,102,102);
    margin-bottom: 20px;
}
.introduceLeft-word2{
    display: block;
    font-size: 16px;
    line-height: 1.69;
    color: rgb(102,102,102);
    margin-bottom: 33px;
}
.introduceLeft-question{
    width: 222px;height: 46px;
    text-align: center;line-height: 45px;
    color: rgb(254,102,3);
    cursor: pointer;
    border: 1px solid rgb(254,102,3);
}
.introduceRight{
    float: right;
    width: 457px;height: 277px;

    background: url(../images/9_03.png) no-repeat 100% 100%;
    background-size: 100%;
}
/*server css*/
.server{
    width: 100%;height: 680px;
    margin: 0px auto 0;
    background: rgb(250,250,250);
}
.serverBox{
    width: 1226px;margin: 0 auto;position: relative;
}
.serverHead{
    width: 100%;
    height: 74px;
    border-bottom: 3px solid rgb(242,242,242);
    position: relative;
    margin-bottom: 20px;
}
.serverHead ul{
    margin: 0;
    padding: 0;
}
.serverLi{
    width: 100px;
    height: 74px;
    padding: 0 10px;
    text-align: center;
    line-height: 80px;
    font-size: 16px;
    color: rgb(51,51,51);
    /*font-family: MicrosoftYaHei;*/
    float: left;
    cursor: pointer;
    margin-right: 18px;
    position: relative;
    list-style: none;
}

.serverLiActive{
    /*border-bottom: 3px solid rgb(254,102,3);*/
    /*font-family: MicrosoftYaHei;*/
    font-weight: bold;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    /*text-align: justify;*/
    color: rgb(254,102,3);
}
.bor{
    width: 120px;height: 3px;
    background: rgb(254,102,3);
    position: absolute;
    bottom: -3px;
    left: 0;
}
.serverIcon{
    font-size: 0;
    line-height: 0;
    border-width: 5px;
    border-color: rgb(254,102,3);
    border-right-width: 0;
    border-style: dashed;
    border-left-style: solid;
    border-top-color: transparent;
    border-bottom-color: transparent;
    position: absolute;
    right: 0;
    top: 36px;

}
.allServer{
    position: absolute;
    right: 12px;

    top: 32px;

    line-height: 16px;
    cursor: pointer;
}
.allServer a{
    color: rgb(254,102,3);
}
.allServer:hover{
    font-weight: 900;
}
.serverConLi:hover{
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
    -webkit-transform: translate3d(0, -2px, 0);
    transform: translate3d(0, -2px, 0);
    transition: .4s;
}
.serverConLi:hover .serverConLiWord1{
    color: rgb(254,102,3);
}
.serverConLi:hover .serverConLiWord2{
    color: #000000;
}
.serverCon{
    width: 100%;height: 519px;
}
.serverCon ul{
    width: 100%;height: 100%;
    position: absolute;
    display: none;
    margin: 0;padding: 0;
}
.serverCon ul.show{
    display: block;
}
.serverCon ul:first-child{
    width: 100%;height: auto;
    position: absolute;
    z-index: 10;
}
.serverConLi{
    width: 284px;height: 234px;margin-right: 30px;
    margin-bottom: 25px;
    float: left;
    position: relative;
    cursor: pointer;
    list-style: none;
    /*background: #cccccc;*/
}
.serverConLi:nth-child(4n){
    margin-right: 0;

}
.serverConLi img{
    width: 284px;height: 142px;
    display: block;
    /*background: #ccc;*/
}
.serverConLiWorBac{
    width: 100%;
    height: 92px;
    background: white;
    display: block;
    float: left;
}
.serverConLiWord1{
    width: 100%;
    height: auto;
    text-align: center;
    display: block;
    margin-top: 20px;
    font-size: 17px;
    line-height: 1.47;
    color: #000000;
    letter-spacing: normal;

}
.serverConLiWord2{
    width: 100%;
    height: auto;
    text-align: center;
    display: block;
    margin-top: 4px;
    font-size: 13px;
    color: #999999;
    letter-spacing: normal;
}
.pages{
    width: 100%;height: 18px;
    position: absolute;
    bottom: -40px;
}
.pages ul{
    width: 92px;height: 100%;
    position: absolute;
    left: 50%;transform: translateX(-50%);
    margin: 0;padding: 0;
}
.pagesLi{
    width: 23px;height: 18px;border-radius: 9px;
    text-align: center;
    float: left;
    /*background-color: #fe6603;*/
    margin-right: 7px;
    color: #cccccc;
    font-size: 12px;
    line-height: 18px;
    cursor: pointer;
    list-style: none;
}
.pagesLiActive{
    background-color: #fe6603;
    color: white;
}

/*solution css*/
.serverLi.solu{
    border-bottom: 3px solid rgb(254,102,3);
    /* font-family: MicrosoftYaHei; */
    font-weight: bold;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    /* text-align: justify; */
    color: #333333;
}
.serverLi.demo{
    border-bottom: 3px solid rgb(254,102,3);
    /* font-family: MicrosoftYaHei; */
    font-weight: bold;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    /* text-align: justify; */
    color: #333333;
}
.serverLi.reletive{
    border-bottom: 3px solid rgb(254,102,3);
    /* font-family: MicrosoftYaHei; */
    font-weight: bold;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    /* text-align: justify; */
    color: #333333;
}
.solutionConLi-word1:hover{
    color: rgb(254,102,3);
}
.cooperationDemo1 li:hover a{
    color: rgb(254,102,3);
}
.cooperationDemo2 li:hover a{
    color: rgb(254,102,3);
}
.resourceConLi-word:hover a{
    color: rgb(254,102,3);
}
.dragLi:hover{
    color: rgb(254,102,3);
}
.dragLi:hover .dragLi-icon{
    background: url(../images/arrow.png) no-repeat -15px 100%;
}
.bannermoreBox{
    width: 0;
    height: 100%;
    position: absolute;
    left: 0;top: 0;
    background: rgb(254,102,3);
    overflow: hidden;
}
.solutionCon{
    height: 442px;
}
.solutionCon ul{
    margin: 0;padding: 0;
}
.solutionConLi{
    width: 593px;
    height: 162px;
    float: left;
    position: relative;
    margin-bottom: 51px;
    margin-right: 38px;
    list-style: none;
}
/*.solutionConLi:nth-child(2n){*/
    /*margin-right: 0;*/
/*}*/
.solutionConLi img{
    width: 237px;height: 162px;display: block;
    background: #ccc;
    cursor: pointer;
}

.solutionConLi-word1{
    position: absolute;
    left: 265px;
    top: 18px;
    /*font-family: MicrosoftYaHei;*/
    font-size: 18px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.17;
    letter-spacing: normal;
    text-align: center;
    color: #333333;
    cursor: pointer;
}
.solutionConLi-word2{
    position: absolute;
    left: 265px;
    top: 76px;
    /*font-family: MicrosoftYaHei;*/
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.64;
    letter-spacing: normal;
    text-align: left;
    color: #999999;
    width: 334px;
}
.solutionConLi:hover{
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
    -webkit-transform: translate3d(0, -2px, 0);
    transform: translate3d(0, -2px, 0);
    transition: .4s;
}
.solution .serverBox .pages{
    bottom: 23px;
}
/*cooperation css*/
.cooperation{
    width: 100%;
    height: 338px;
    background: #f8f8f8;
    margin: 0 auto;
    position: relative;
}
.cooperationBox{
    width: 1226px;
    margin: 0 auto;
}
.cooperationDemo{
    margin-top: 50px;

}
.cooperationDemo li{
    list-style: initial;
    font-size: 16px;
    margin-bottom: 14px;

    cursor: pointer;
}
.cooperationDemo li a{
    color: #333333;
}
.cooperationDemo1{
    float: left;
    margin-left: 42px;
    padding: 0;
    margin-top: 0;
    margin-bottom: 0;
}
.cooperationDemo2{
    float: right;
    margin-right: 38px;
}
.cooperation .pages{
    bottom: 25px;
}
/*resource css*/
.resources{
    width: 1226px;height: 232px;margin: 0 auto;
}
.resourceBox{
    width: 1226px;height: 100%;
    margin: 0 auto;
}
.resourceBox .serverHead{
    margin-bottom: 38px;
}
.resourceCon{
    width: 1226px;height: 152px;
}
.resourceCon ul{
    padding: 0;margin: 0;
}
.resourceConLi{
    /*width: 190px;*/
    height: 20px;
    margin-right: 274px;
    margin-bottom: 26px;
    float: left;
    list-style: none;
}
.resourceConLi:first-child{
    margin-left: 14px;
}
.resourceConLi:nth-child(4){
    margin-left: 14px;
}
.resourceConLi:nth-child(3){
    margin-right: 0;
}
.resourceConLi:nth-child(3) img{

}
.resourceConLi img{
    display: inline-block;
    width: 18px;height: 20px;
    float: left;
    /*background: #ccc;*/
    margin-right: 12px;
    border: none;
    /*background: url(../images/liebiao.png) no-repeat 100% 100%;*/
    /*background-size: cover;*/
}
.resourceConLi-word{
    display: inline-block;
    float: right;
    /*font-family: MicrosoftYaHei;*/
    font-size: 16px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;

    cursor: pointer;
}
.resourceConLi-word a{
    color: #333333;
}
/*footer css*/
.footerBox{
    width: 100%;height: 459.5px;
    background: #111111;
}
.footer{
    width: 1400px;
    height: 459.5px;
    background-color: #111111;
    margin: 0 auto;
}
.footer-top{
    width: 1200px;height: 376.5px;
    margin: 0 auto;
    /*border-bottom: 1px solid #ffffff;*/
    position: relative;
}
.footer-top-title{
    position: absolute;
    top: 61px;
    left: -13.5px;
}
.footer-top-title ul{
    padding: 0;margin: 0;
}
.footer-top-title-li{
    /*font-family: FZLTCHK-GBK1-0;*/
    font-size: 17px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    color: #ffffff;
    float: left;
    margin-right: 116px;
    list-style: none;
}
.footer-top-title-li:nth-child(4){
    margin-right: 288.5px;
}
.help{
    position: absolute;
    top: 65px;left: 0;
}
.helpLi{
    margin-bottom: 30px;
    font-size: 16px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    cursor: pointer;
    color: #aaaaaa;
    list-style: none;
}
.helpLi a{
    color: #aaaaaa;
}
.ourServer{
    position: absolute;
    left: 183.5px;top: 65px;
}
.aboutUs{
    position: absolute;
    top: 65px;
    left: 383.5px;
}
.aboutUs .helpLi{
    /*font-family: FZLTHJW-GB1-0;*/
    font-size: 16px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    color: #aaaaaa;
}
.connect{
    position: absolute;
    left: 566.5px;top: 65px;
}
.footer-top-title-li:last-child{
    position: relative;
}
.weibo-img{
    width: 51px;height: 51px;
    border-radius: 50%;top: 44px;
    position: absolute;
    background: url(../images/weibo.png) 100% 100%;
    background-size: contain;
}
.weibo-word{
    width: 68px;
    position: absolute;
    top: 60px;
    left: 61px;
    /*font-family: FZLTHJW-GB1-0;*/
    font-size: 12px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    color: #999999;
}
.weixin-img{
    width: 51px;height: 51px;
    border-radius: 50%;top: 44px;
    position: absolute;
    left: 148.5px;
    background: url(../images/weixin.png) 100% 100%;
    background-size: contain;
}
.weixin-word{
    width: 68px;
    position: absolute;
    top: 60px;
    left: 208px;
    /*font-family: FZLTHJW-GB1-0;*/
    font-size: 12px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    color: #999999;
}
.footer-bottom{
    width: 1400px;
    height: 82.5px;
    text-align: center;
    line-height: 82.5px;
    /*font-family: DIN;*/
    font-size: 15px;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;

    color: #eaeaea;
}
.message{
    /*position: fixed;*/
    width: 40px;height: 40px;
    background-color: #fe6603;
    border-bottom: 1px solid rgba(0,0,0,.05);
    position: relative;
}
.messageIcon{
    width: 20px;height: 18px;
    background: url(../images/1-1.jpg);
    position: absolute;
    top: 0;left: 0;right: 0;bottom: 0;margin: auto;
}
.note{
    /*position: fixed;*/
    width: 30px;height: auto;
    text-align: center;
    padding: 10px 5px;
    background-color: #fe6603;
    color: white;
}
/*.noteIcon{*/
    /*width: 20px;height: 18px;*/
    /*!*background: url(../images/1-2.jpg);*!*/
    /*position: absolute;*/
    /*top: 0;left: 0;right: 0;bottom: 0;margin: auto;*/
/*}*/
.box{
    width: auto;
    height: auto;
    right: 0;top: 67%;
    position: fixed;
    z-index: 10;
    cursor: pointer;
}
.erweima{
    width: 296.5px;
    height: 185px;
    border-radius: 2px;
    background-color: #2f2f2f;
    position: absolute;
    top: 106.5px;
    left: 2.5px;
}
.weiboer{
    width: 114px;height: 114px;
    position: absolute;
    top: 22px;
    left: 22px;
    background: url(../images/weiboerweima.png) 100% 100%;
    background-size: 100%;
}
.weixiner{
    width: 114px;height: 114px;
    position: absolute;
    right: 22.5px;
    top: 22px;
    background: url(../images/weixinerweima.png) 100% 100%;
    background-size: 100%;
}
.weiboerword{
    width: 95px;
    height: 23.5px;
    font-family: FZLTHJW-GB1-0;
    font-size: 10px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.4;
    letter-spacing: 0.2px;
    text-align: center;
    color: #999999;
    position: absolute;
    right: 31.5px;
    bottom: 17.5px;
}
.weixinerword{
    width: 95px;
    height: 23.5px;
    font-family: FZLTHJW-GB1-0;
    font-size: 12px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.4;
    letter-spacing: 0.2px;
    text-align: center;
    color: #999999;
    position: absolute;
    left: 37.5px;
    bottom: 17.5px;
}
.weixinerword1{
    width: 122px;
    height: 23.5px;
    font-family: FZLTHJW-GB1-0;
    font-size: 12px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.4;
    letter-spacing: 0.2px;
    text-align: center;
    color: #999999;
    position: absolute;
    left: 23.5px;
    bottom: -1.5px;
}
.weiboerword1{
    width: 122px;
    height: 23.5px;
    font-family: FZLTHJW-GB1-0;
    font-size: 10px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.4;
    letter-spacing: 0.2px;
    text-align: center;
    color: #999999;
    position: absolute;
    right: 17.5px;
    bottom: -1.5px;
}
.line{
    width: 100%;height: 1px;
    background: #ffffff;opacity: 0.1;
    position: absolute;
    bottom: 0;
}
.serverRight0{
    margin-right: 0;
}
.footer-top-title-li-last{
    position: relative;
}
.footer-top-title-li-four{
    margin-right: 288.5px;
}
.introduceLeft-question{
    position: relative;
}
.introduceLeft-questionbox1{
    width: 0;height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}
.introduceLeft-question1{
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    left: 0;top: 0;

    /*overflow: hidden;*/
}
.introduceLeft-question:hover .introduceLeft-questionbox1{
    width: 223px;
    height: 100%;
    background: rgb(254,102,3);
    transition: .4s;
    display: block;
}
.introduceLeft-question:hover .introduceLeft-question1{
    color: white;
}
.baa{
    position: absolute;
    z-index: 999;
    width: 80%;
    height: 374px;
    top: 0;
    left: 10%;
}
.swiper-container {
    height: 374px;
}
.swiper-wrapper{
	width: 100%;
	height: 374px;
}

.swiper-button-next {
    width: 36px;
    height: 54px;
    background: url(../images/rightarrow.png) no-repeat 100% 100%;
}
.swiper-button-prev {
    width: 36px;
    height: 54px;
    background: url(../images/leftarrow.png) no-repeat 100% 100%;
}
.introduceLeft-word{
    min-height: 197px;
}

.rtfc a{
    color: #F60;
    text-decoration: none;
}