.nlBox{
    width: 100%;height: auto;margin-bottom: 42px;
}
.nlSearch{
    width: 100%;height: 62px;
    background: #f8f8f8;
    margin-bottom: 15px;
}
.location{
    margin-bottom: 20px;
}
.nlSearchBox{
    width: 1226px;height: 26px;margin: 0 auto;
    padding-top: 18px;
}
.tSearch{
    width: auto;height: 100%;
    display: inline-block;
    margin-right: 27px;
}
.dSearch{
    width: auto;height: 100%;
    display: inline-block;
    position: relative;
}
.nlSearchBox input{
    border: 1px solid rgb(254,102,3);
    outline: none;
    padding-left: 5px;
    height: 24px;
    line-height: 22px;
    padding-top: 0;
    padding-bottom: 0;
}
.nlSearchBox span{
    color: #999999;
    font-size: 14px;
}
.tSearch input{
    width: 142px;
}
.dSearch input{
    width: 255px;
    text-align: center;
    margin-right: 16px;
}
.calendar{
    width: 27px;height: 19px;
    position: absolute;
    background: url(../images/calendar.png) no-repeat 100% 100%;

    left: 80px;top: 6px;
    cursor: pointer;
}
.dSearchIb{
    width: 38px;height: 26px;background: url(../images/searchIcon.png);
    position: absolute;
    right: -45px;top: 0;
    background-size: contain;
    cursor: pointer;
}
.nlLists{
    width: 1226px;height: auto;
    margin: 0 auto;
}
.nlListsUl{
    width: 100%;height: auto;
    margin-bottom: 28px;
    margin-top: 0;
    padding: 0;
}
.nlListsLi{
    width: 100%;height: 45px;
    border-bottom: 1px solid #e5e5e5;
    list-style: none;
}
.nlListsI{
    width: 13px;height: 17px;display: inline-block;
    background: url(../images/textI.png);
    background-position: 0 50%;
}
.nlListsA{
    width: auto;height: 100%;line-height: 45px;
    color: #000000;
}
.nlListsA:hover{
    color: rgb(254,102,3);
    text-decoration: underline;
}
.nlListsT{
    width: auto;
    height: 100%;
    line-height: 45px;
    display: inline-block;
    float: right;
    color: #999999;
}
.serverpages{
    position: initial;
}
.bgBannerW1{
    margin-top: 90px;
}