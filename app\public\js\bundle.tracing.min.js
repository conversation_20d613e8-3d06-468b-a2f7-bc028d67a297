/*! @sentry/browser & @sentry/tracing 7.48.0 (253e16d) | https://github.com/getsentry/sentry-javascript */
var Sentry=function(t){class n{static __initStatic(){this.id="Replay"}__init(){this.name=n.id}constructor(t){n.prototype.__init.call(this),console.error("You are using new Replay() even though this bundle does not include replay.")}setupOnce(){}start(){}stop(){}flush(){}}n.__initStatic();const e=Object.prototype.toString;function i(t){switch(e.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return d(t,Error)}}function r(t,n){return e.call(t)===`[object ${n}]`}function s(t){return r(t,"ErrorEvent")}function o(t){return r(t,"DOMError")}function c(t){return r(t,"String")}function u(t){return null===t||"object"!=typeof t&&"function"!=typeof t}function a(t){return r(t,"Object")}function h(t){return"undefined"!=typeof Event&&d(t,Event)}function f(t){return Boolean(t&&t.then&&"function"==typeof t.then)}function l(t){return"number"==typeof t&&t!=t}function d(t,n){try{return t instanceof n}catch(t){return!1}}function p(t){return t&&t.Math==Math?t:void 0}const m="object"==typeof globalThis&&p(globalThis)||"object"==typeof window&&p(window)||"object"==typeof self&&p(self)||"object"==typeof global&&p(global)||function(){return this}()||{};function y(){return m}function v(t,n,e){const i=e||m,r=i.__SENTRY__=i.__SENTRY__||{};return r[t]||(r[t]=n())}const g=y();function _(t,n={}){try{let e=t;const i=5,r=[];let s=0,o=0;const c=" > ",u=c.length;let a;const h=Array.isArray(n)?n:n.keyAttrs,f=!Array.isArray(n)&&n.maxStringLength||80;for(;e&&s++<i&&(a=b(e,h),!("html"===a||s>1&&o+r.length*u+a.length>=f));)r.push(a),o+=a.length,e=e.parentNode;return r.reverse().join(c)}catch(t){return"<unknown>"}}function b(t,n){const e=t,i=[];let r,s,o,u,a;if(!e||!e.tagName)return"";i.push(e.tagName.toLowerCase());const h=n&&n.length?n.filter((t=>e.getAttribute(t))).map((t=>[t,e.getAttribute(t)])):null;if(h&&h.length)h.forEach((t=>{i.push(`[${t[0]}="${t[1]}"]`)}));else if(e.id&&i.push(`#${e.id}`),r=e.className,r&&c(r))for(s=r.split(/\s+/),a=0;a<s.length;a++)i.push(`.${s[a]}`);const f=["aria-label","type","name","title","alt"];for(a=0;a<f.length;a++)o=f[a],u=e.getAttribute(o),u&&i.push(`[${o}="${u}"]`);return i.join("")}class w extends Error{constructor(t,n="warn"){super(t),this.message=t,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=n}}const S=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function T(t,n=!1){const{host:e,path:i,pass:r,port:s,projectId:o,protocol:c,publicKey:u}=t;return`${c}://${u}${n&&r?`:${r}`:""}@${e}${s?`:${s}`:""}/${i?`${i}/`:i}${o}`}function E(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function $(t){return"string"==typeof t?function(t){const n=S.exec(t);if(!n)throw new w(`Invalid Sentry Dsn: ${t}`);const[e,i,r="",s,o="",c]=n.slice(1);let u="",a=c;const h=a.split("/");if(h.length>1&&(u=h.slice(0,-1).join("/"),a=h.pop()),a){const t=a.match(/^\d+/);t&&(a=t[0])}return E({host:s,pass:r,path:u,projectId:a,port:o,protocol:e,publicKey:i})}(t):E(t)}const k=["debug","info","warn","error","log","assert","trace"];function x(t,n=0){return"string"!=typeof t||0===n||t.length<=n?t:`${t.slice(0,n)}...`}function O(t,n){if(!Array.isArray(t))return"";const e=[];for(let n=0;n<t.length;n++){const i=t[n];try{e.push(String(i))}catch(t){e.push("[value cannot be serialized]")}}return e.join(n)}function j(t,n,e=!1){return!!c(t)&&(r(n,"RegExp")?n.test(t):!!c(n)&&(e?t===n:t.includes(n)))}function R(t,n=[],e=!1){return n.some((n=>j(t,n,e)))}function C(t,n,e){if(!(n in t))return;const i=t[n],r=e(i);if("function"==typeof r)try{D(r,i)}catch(t){}t[n]=r}function I(t,n,e){Object.defineProperty(t,n,{value:e,writable:!0,configurable:!0})}function D(t,n){const e=n.prototype||{};t.prototype=n.prototype=e,I(t,"__sentry_original__",n)}function L(t){return t.__sentry_original__}function M(t){if(i(t))return{message:t.message,name:t.name,stack:t.stack,...N(t)};if(h(t)){const n={type:t.type,target:A(t.target),currentTarget:A(t.currentTarget),...N(t)};return"undefined"!=typeof CustomEvent&&d(t,CustomEvent)&&(n.detail=t.detail),n}return t}function A(t){try{return n=t,"undefined"!=typeof Element&&d(n,Element)?_(t):Object.prototype.toString.call(t)}catch(t){return"<unknown>"}var n}function N(t){if("object"==typeof t&&null!==t){const n={};for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(n[e]=t[e]);return n}return{}}function F(t,n=40){const e=Object.keys(M(t));if(e.sort(),!e.length)return"[object has no keys]";if(e[0].length>=n)return x(e[0],n);for(let t=e.length;t>0;t--){const i=e.slice(0,t).join(", ");if(!(i.length>n))return t===e.length?i:x(i,n)}return""}function H(t){return U(t,new Map)}function U(t,n){if(a(t)){const e=n.get(t);if(void 0!==e)return e;const i={};n.set(t,i);for(const e of Object.keys(t))void 0!==t[e]&&(i[e]=U(t[e],n));return i}if(Array.isArray(t)){const e=n.get(t);if(void 0!==e)return e;const i=[];return n.set(t,i),t.forEach((t=>{i.push(U(t,n))})),i}return t}!function(){const t={enable:()=>{},disable:()=>{}};k.forEach((n=>{t[n]=()=>{}}))}();const P=/\(error: (.*)\)/;function q(...t){const n=t.sort(((t,n)=>t[0]-n[0])).map((t=>t[1]));return(t,e=0)=>{const i=[],r=t.split("\n");for(let t=e;t<r.length;t++){const e=r[t];if(e.length>1024)continue;const s=P.test(e)?e.replace(P,"$1"):e;if(!s.match(/\S*Error: /)){for(const t of n){const n=t(s);if(n){i.push(n);break}}if(i.length>=50)break}}return function(t){if(!t.length)return[];const n=t.slice(0,50),e=n[n.length-1].function;e&&/sentryWrapped/.test(e)&&n.pop();n.reverse();const i=n[n.length-1].function;i&&/captureMessage|captureException/.test(i)&&n.pop();return n.map((t=>({...t,filename:t.filename||n[n.length-1].filename,function:t.function||"?"})))}(i)}}const B="<anonymous>";function X(t){try{return t&&"function"==typeof t&&t.name||B}catch(t){return B}}const z=y();function W(){if(!("fetch"in z))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(t){return!1}}function G(t){return t&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}const J=y();const K=y(),Y={},V={};function Q(t){if(!V[t])switch(V[t]=!0,t){case"console":!function(){if(!("console"in K))return;k.forEach((function(t){t in K.console&&C(K.console,t,(function(n){return function(...e){tt("console",{args:e,level:t}),n&&n.apply(K.console,e)}}))}))}();break;case"dom":!function(){if(!("document"in K))return;const t=tt.bind(null,"dom"),n=ot(t,!0);K.document.addEventListener("click",n,!1),K.document.addEventListener("keypress",n,!1),["EventTarget","Node"].forEach((n=>{const e=K[n]&&K[n].prototype;e&&e.hasOwnProperty&&e.hasOwnProperty("addEventListener")&&(C(e,"addEventListener",(function(n){return function(e,i,r){if("click"===e||"keypress"==e)try{const i=this,s=i.__sentry_instrumentation_handlers__=i.__sentry_instrumentation_handlers__||{},o=s[e]=s[e]||{refCount:0};if(!o.handler){const i=ot(t);o.handler=i,n.call(this,e,i,r)}o.refCount++}catch(t){}return n.call(this,e,i,r)}})),C(e,"removeEventListener",(function(t){return function(n,e,i){if("click"===n||"keypress"==n)try{const e=this,r=e.__sentry_instrumentation_handlers__||{},s=r[n];s&&(s.refCount--,s.refCount<=0&&(t.call(this,n,s.handler,i),s.handler=void 0,delete r[n]),0===Object.keys(r).length&&delete e.__sentry_instrumentation_handlers__)}catch(t){}return t.call(this,n,e,i)}})))}))}();break;case"xhr":!function(){if(!("XMLHttpRequest"in K))return;const t=XMLHttpRequest.prototype;C(t,"open",(function(t){return function(...n){const e=n[1],i=this.__sentry_xhr_v2__={method:c(n[0])?n[0].toUpperCase():n[0],url:n[1],request_headers:{}};c(e)&&"POST"===i.method&&e.match(/sentry_key/)&&(this.__sentry_own_request__=!0);const r=()=>{const t=this.__sentry_xhr_v2__;if(t&&4===this.readyState){try{t.status_code=this.status}catch(t){}tt("xhr",{args:n,endTimestamp:Date.now(),startTimestamp:Date.now(),xhr:this})}};return"onreadystatechange"in this&&"function"==typeof this.onreadystatechange?C(this,"onreadystatechange",(function(t){return function(...n){return r(),t.apply(this,n)}})):this.addEventListener("readystatechange",r),C(this,"setRequestHeader",(function(t){return function(...n){const[e,i]=n,r=this.__sentry_xhr_v2__;return r&&(r.request_headers[e.toLowerCase()]=i),t.apply(this,n)}})),t.apply(this,n)}})),C(t,"send",(function(t){return function(...n){const e=this.__sentry_xhr_v2__;return e&&void 0!==n[0]&&(e.body=n[0]),tt("xhr",{args:n,startTimestamp:Date.now(),xhr:this}),t.apply(this,n)}}))}();break;case"fetch":!function(){if(!function(){if(!W())return!1;if(G(z.fetch))return!0;let t=!1;const n=z.document;if(n&&"function"==typeof n.createElement)try{const e=n.createElement("iframe");e.hidden=!0,n.head.appendChild(e),e.contentWindow&&e.contentWindow.fetch&&(t=G(e.contentWindow.fetch)),n.head.removeChild(e)}catch(t){}return t}())return;C(K,"fetch",(function(t){return function(...n){const{method:e,url:i}=function(t){if(0===t.length)return{method:"GET",url:""};if(2===t.length){const[n,e]=t;return{url:et(n),method:nt(e,"method")?String(e.method).toUpperCase():"GET"}}const n=t[0];return{url:et(n),method:nt(n,"method")?String(n.method).toUpperCase():"GET"}}(n),r={args:n,fetchData:{method:e,url:i},startTimestamp:Date.now()};return tt("fetch",{...r}),t.apply(K,n).then((t=>(tt("fetch",{...r,endTimestamp:Date.now(),response:t}),t)),(t=>{throw tt("fetch",{...r,endTimestamp:Date.now(),error:t}),t}))}}))}();break;case"history":!function(){if(!function(){const t=J.chrome,n=t&&t.app&&t.app.runtime,e="history"in J&&!!J.history.pushState&&!!J.history.replaceState;return!n&&e}())return;const t=K.onpopstate;function n(t){return function(...n){const e=n.length>2?n[2]:void 0;if(e){const t=it,n=String(e);it=n,tt("history",{from:t,to:n})}return t.apply(this,n)}}K.onpopstate=function(...n){const e=K.location.href,i=it;if(it=e,tt("history",{from:i,to:e}),t)try{return t.apply(this,n)}catch(t){}},C(K.history,"pushState",n),C(K.history,"replaceState",n)}();break;case"error":ct=K.onerror,K.onerror=function(t,n,e,i,r){return tt("error",{column:i,error:r,line:e,msg:t,url:n}),!(!ct||ct.__SENTRY_LOADER__)&&ct.apply(this,arguments)},K.onerror.__SENTRY_INSTRUMENTED__=!0;break;case"unhandledrejection":ut=K.onunhandledrejection,K.onunhandledrejection=function(t){return tt("unhandledrejection",t),!(ut&&!ut.__SENTRY_LOADER__)||ut.apply(this,arguments)},K.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0;break;default:return}}function Z(t,n){Y[t]=Y[t]||[],Y[t].push(n),Q(t)}function tt(t,n){if(t&&Y[t])for(const e of Y[t]||[])try{e(n)}catch(t){}}function nt(t,n){return!!t&&"object"==typeof t&&!!t[n]}function et(t){return"string"==typeof t?t:t?nt(t,"url")?t.url:t.toString?t.toString():"":""}let it;let rt,st;function ot(t,n=!1){return e=>{if(!e||st===e)return;if(function(t){if("keypress"!==t.type)return!1;try{const n=t.target;if(!n||!n.tagName)return!0;if("INPUT"===n.tagName||"TEXTAREA"===n.tagName||n.isContentEditable)return!1}catch(t){}return!0}(e))return;const i="keypress"===e.type?"input":e.type;(void 0===rt||function(t,n){if(!t)return!0;if(t.type!==n.type)return!0;try{if(t.target!==n.target)return!0}catch(t){}return!1}(st,e))&&(t({event:e,name:i,global:n}),st=e),clearTimeout(rt),rt=K.setTimeout((()=>{rt=void 0}),1e3)}}let ct=null;let ut=null;function at(){const t=m,n=t.crypto||t.msCrypto;if(n&&n.randomUUID)return n.randomUUID().replace(/-/g,"");const e=n&&n.getRandomValues?()=>n.getRandomValues(new Uint8Array(1))[0]:()=>16*Math.random();return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,(t=>(t^(15&e())>>t/4).toString(16)))}function ht(t){return t.exception&&t.exception.values?t.exception.values[0]:void 0}function ft(t){const{message:n,event_id:e}=t;if(n)return n;const i=ht(t);return i?i.type&&i.value?`${i.type}: ${i.value}`:i.type||i.value||e||"<unknown>":e||"<unknown>"}function lt(t,n,e){const i=t.exception=t.exception||{},r=i.values=i.values||[],s=r[0]=r[0]||{};s.value||(s.value=n||""),s.type||(s.type=e||"Error")}function dt(t,n){const e=ht(t);if(!e)return;const i=e.mechanism;if(e.mechanism={type:"generic",handled:!0,...i,...n},n&&"data"in n){const t={...i&&i.data,...n.data};e.mechanism.data=t}}function pt(t){if(t&&t.__sentry_captured__)return!0;try{I(t,"__sentry_captured__",!0)}catch(t){}return!1}function mt(t){return Array.isArray(t)?t:[t]}function yt(t,n=1/0,e=1/0){try{return gt("",t,n,e)}catch(t){return{ERROR:`**non-serializable** (${t})`}}}function vt(t,n=3,e=102400){const i=yt(t,n);return r=i,function(t){return~-encodeURI(t).split(/%..|./).length}(JSON.stringify(r))>e?vt(t,n-1,e):i;var r}function gt(t,n,e=1/0,i=1/0,r=function(){const t="function"==typeof WeakSet,n=t?new WeakSet:[];return[function(e){if(t)return!!n.has(e)||(n.add(e),!1);for(let t=0;t<n.length;t++)if(n[t]===e)return!0;return n.push(e),!1},function(e){if(t)n.delete(e);else for(let t=0;t<n.length;t++)if(n[t]===e){n.splice(t,1);break}}]}()){const[s,o]=r;if(null===n||["number","boolean","string"].includes(typeof n)&&!l(n))return n;const c=function(t,n){try{return"domain"===t&&n&&"object"==typeof n&&n.t?"[Domain]":"domainEmitter"===t?"[DomainEmitter]":"undefined"!=typeof global&&n===global?"[Global]":"undefined"!=typeof window&&n===window?"[Window]":"undefined"!=typeof document&&n===document?"[Document]":a(e=n)&&"nativeEvent"in e&&"preventDefault"in e&&"stopPropagation"in e?"[SyntheticEvent]":"number"==typeof n&&n!=n?"[NaN]":void 0===n?"[undefined]":"function"==typeof n?`[Function: ${X(n)}]`:"symbol"==typeof n?`[${String(n)}]`:"bigint"==typeof n?`[BigInt: ${String(n)}]`:`[object ${function(t){const n=Object.getPrototypeOf(t);return n?n.constructor.name:"null prototype"}(n)}]`}catch(t){return`**non-serializable** (${t})`}var e}(t,n);if(!c.startsWith("[object "))return c;if(n.__sentry_skip_normalization__)return n;let u=e;if("number"==typeof n.__sentry_override_normalization_depth__&&(u=n.__sentry_override_normalization_depth__),0===u)return c.replace("object ","");if(s(n))return"[Circular ~]";const h=n;if(h&&"function"==typeof h.toJSON)try{return gt("",h.toJSON(),u-1,i,r)}catch(t){}const f=Array.isArray(n)?[]:{};let d=0;const p=M(n);for(const t in p){if(!Object.prototype.hasOwnProperty.call(p,t))continue;if(d>=i){f[t]="[MaxProperties ~]";break}const n=p[t];f[t]=gt(t,n,u-1,i,r),d++}return o(n),f}var _t;function bt(t){return new St((n=>{n(t)}))}function wt(t){return new St(((n,e)=>{e(t)}))}!function(t){t[t.PENDING=0]="PENDING";t[t.RESOLVED=1]="RESOLVED";t[t.REJECTED=2]="REJECTED"}(_t||(_t={}));class St{__init(){this.i=_t.PENDING}__init2(){this.o=[]}constructor(t){St.prototype.__init.call(this),St.prototype.__init2.call(this),St.prototype.__init3.call(this),St.prototype.__init4.call(this),St.prototype.__init5.call(this),St.prototype.__init6.call(this);try{t(this.u,this.h)}catch(t){this.h(t)}}then(t,n){return new St(((e,i)=>{this.o.push([!1,n=>{if(t)try{e(t(n))}catch(t){i(t)}else e(n)},t=>{if(n)try{e(n(t))}catch(t){i(t)}else i(t)}]),this.l()}))}catch(t){return this.then((t=>t),t)}finally(t){return new St(((n,e)=>{let i,r;return this.then((n=>{r=!1,i=n,t&&t()}),(n=>{r=!0,i=n,t&&t()})).then((()=>{r?e(i):n(i)}))}))}__init3(){this.u=t=>{this.p(_t.RESOLVED,t)}}__init4(){this.h=t=>{this.p(_t.REJECTED,t)}}__init5(){this.p=(t,n)=>{this.i===_t.PENDING&&(f(n)?n.then(this.u,this.h):(this.i=t,this.m=n,this.l()))}}__init6(){this.l=()=>{if(this.i===_t.PENDING)return;const t=this.o.slice();this.o=[],t.forEach((t=>{t[0]||(this.i===_t.RESOLVED&&t[1](this.m),this.i===_t.REJECTED&&t[2](this.m),t[0]=!0)}))}}}function Tt(t){const n=[];function e(t){return n.splice(n.indexOf(t),1)[0]}return{$:n,add:function(i){if(!(void 0===t||n.length<t))return wt(new w("Not adding Promise because buffer limit was reached."));const r=i();return-1===n.indexOf(r)&&n.push(r),r.then((()=>e(r))).then(null,(()=>e(r).then(null,(()=>{})))),r},drain:function(t){return new St(((e,i)=>{let r=n.length;if(!r)return e(!0);const s=setTimeout((()=>{t&&t>0&&e(!1)}),t);n.forEach((t=>{bt(t).then((()=>{--r||(clearTimeout(s),e(!0))}),i)}))}))}}}function Et(t){if(!t)return{};const n=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!n)return{};const e=n[6]||"",i=n[8]||"";return{host:n[4],path:n[5],protocol:n[2],search:e,hash:i,relative:n[5]+e+i}}const $t=["fatal","error","warning","log","info","debug"];const kt=y(),xt={nowSeconds:()=>Date.now()/1e3};const Ot=function(){const{performance:t}=kt;if(!t||!t.now)return;return{now:()=>t.now(),timeOrigin:Date.now()-t.now()}}(),jt=void 0===Ot?xt:{nowSeconds:()=>(Ot.timeOrigin+Ot.now())/1e3},Rt=xt.nowSeconds.bind(xt),Ct=jt.nowSeconds.bind(jt),It=Ct,Dt=(()=>{const{performance:t}=kt;if(!t||!t.now)return;const n=36e5,e=t.now(),i=Date.now(),r=t.timeOrigin?Math.abs(t.timeOrigin+e-i):n,s=r<n,o=t.timing&&t.timing.navigationStart,c="number"==typeof o?Math.abs(o+e-i):n;return s||c<n?r<=c?t.timeOrigin:o:i})(),Lt=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function Mt(t,n=[]){return[t,n]}function At(t,n){const[e,i]=t;return[e,[...i,n]]}function Nt(t,n){const e=t[1];for(const t of e){if(n(t,t[0].type))return!0}return!1}function Ft(t,n){return(n||new TextEncoder).encode(t)}function Ht(t,n){const[e,i]=t;let r=JSON.stringify(e);function s(t){"string"==typeof r?r="string"==typeof t?r+t:[Ft(r,n),t]:r.push("string"==typeof t?Ft(t,n):t)}for(const t of i){const[n,e]=t;if(s(`\n${JSON.stringify(n)}\n`),"string"==typeof e||e instanceof Uint8Array)s(e);else{let t;try{t=JSON.stringify(e)}catch(n){t=JSON.stringify(yt(e))}s(t)}}return"string"==typeof r?r:function(t){const n=t.reduce(((t,n)=>t+n.length),0),e=new Uint8Array(n);let i=0;for(const n of t)e.set(n,i),i+=n.length;return e}(r)}function Ut(t,n){const e="string"==typeof t.data?Ft(t.data,n):t.data;return[H({type:"attachment",length:e.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType}),e]}const Pt={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor"};function qt(t){return Pt[t]}function Bt(t){if(!t||!t.sdk)return;const{name:n,version:e}=t.sdk;return{name:n,version:e}}function Xt(t,{statusCode:n,headers:e},i=Date.now()){const r={...t},s=e&&e["x-sentry-rate-limits"],o=e&&e["retry-after"];if(s)for(const t of s.trim().split(",")){const[n,e]=t.split(":",2),s=parseInt(n,10),o=1e3*(isNaN(s)?60:s);if(e)for(const t of e.split(";"))r[t]=i+o;else r.all=i+o}else o?r.all=i+function(t,n=Date.now()){const e=parseInt(`${t}`,10);if(!isNaN(e))return 1e3*e;const i=Date.parse(`${t}`);return isNaN(i)?6e4:i-n}(o,i):429===n&&(r.all=i+6e4);return r}const zt="baggage",Wt="sentry-",Gt=/^sentry-/;function Jt(t){return function(t){if(0===Object.keys(t).length)return;return Object.entries(t).reduce(((t,[n,e],i)=>{const r=`${encodeURIComponent(n)}=${encodeURIComponent(e)}`,s=0===i?r:`${t},${r}`;return s.length>8192?t:s}),"")}(Object.entries(t).reduce(((t,[n,e])=>(e&&(t[`sentry-${n}`]=e),t)),{}))}function Kt(t){return t.split(",").map((t=>t.split("=").map((t=>decodeURIComponent(t.trim()))))).reduce(((t,[n,e])=>(t[n]=e,t)),{})}const Yt="production";function Vt(t){const n=Ct(),e={sid:at(),init:!0,timestamp:n,started:n,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(t){return H({sid:`${t.sid}`,init:t.init,started:new Date(1e3*t.started).toISOString(),timestamp:new Date(1e3*t.timestamp).toISOString(),status:t.status,errors:t.errors,did:"number"==typeof t.did||"string"==typeof t.did?`${t.did}`:void 0,duration:t.duration,attrs:{release:t.release,environment:t.environment,ip_address:t.ipAddress,user_agent:t.userAgent}})}(e)};return t&&Qt(e,t),e}function Qt(t,n={}){if(n.user&&(!t.ipAddress&&n.user.ip_address&&(t.ipAddress=n.user.ip_address),t.did||n.did||(t.did=n.user.id||n.user.email||n.user.username)),t.timestamp=n.timestamp||Ct(),n.ignoreDuration&&(t.ignoreDuration=n.ignoreDuration),n.sid&&(t.sid=32===n.sid.length?n.sid:at()),void 0!==n.init&&(t.init=n.init),!t.did&&n.did&&(t.did=`${n.did}`),"number"==typeof n.started&&(t.started=n.started),t.ignoreDuration)t.duration=void 0;else if("number"==typeof n.duration)t.duration=n.duration;else{const n=t.timestamp-t.started;t.duration=n>=0?n:0}n.release&&(t.release=n.release),n.environment&&(t.environment=n.environment),!t.ipAddress&&n.ipAddress&&(t.ipAddress=n.ipAddress),!t.userAgent&&n.userAgent&&(t.userAgent=n.userAgent),"number"==typeof n.errors&&(t.errors=n.errors),n.status&&(t.status=n.status)}class Zt{constructor(){this.v=!1,this.g=[],this._=[],this.S=[],this.T=[],this.k={},this.O={},this.j={},this.R={},this.C={}}static clone(t){const n=new Zt;return t&&(n.S=[...t.S],n.O={...t.O},n.j={...t.j},n.R={...t.R},n.k=t.k,n.I=t.I,n.D=t.D,n.L=t.L,n.M=t.M,n.A=t.A,n._=[...t._],n.N=t.N,n.T=[...t.T],n.C={...t.C}),n}addScopeListener(t){this.g.push(t)}addEventProcessor(t){return this._.push(t),this}setUser(t){return this.k=t||{},this.L&&Qt(this.L,{user:t}),this.F(),this}getUser(){return this.k}getRequestSession(){return this.N}setRequestSession(t){return this.N=t,this}setTags(t){return this.O={...this.O,...t},this.F(),this}setTag(t,n){return this.O={...this.O,[t]:n},this.F(),this}setExtras(t){return this.j={...this.j,...t},this.F(),this}setExtra(t,n){return this.j={...this.j,[t]:n},this.F(),this}setFingerprint(t){return this.A=t,this.F(),this}setLevel(t){return this.I=t,this.F(),this}setTransactionName(t){return this.M=t,this.F(),this}setContext(t,n){return null===n?delete this.R[t]:this.R[t]=n,this.F(),this}setSpan(t){return this.D=t,this.F(),this}getSpan(){return this.D}getTransaction(){const t=this.getSpan();return t&&t.transaction}setSession(t){return t?this.L=t:delete this.L,this.F(),this}getSession(){return this.L}update(t){if(!t)return this;if("function"==typeof t){const n=t(this);return n instanceof Zt?n:this}return t instanceof Zt?(this.O={...this.O,...t.O},this.j={...this.j,...t.j},this.R={...this.R,...t.R},t.k&&Object.keys(t.k).length&&(this.k=t.k),t.I&&(this.I=t.I),t.A&&(this.A=t.A),t.N&&(this.N=t.N)):a(t)&&(t=t,this.O={...this.O,...t.tags},this.j={...this.j,...t.extra},this.R={...this.R,...t.contexts},t.user&&(this.k=t.user),t.level&&(this.I=t.level),t.fingerprint&&(this.A=t.fingerprint),t.requestSession&&(this.N=t.requestSession)),this}clear(){return this.S=[],this.O={},this.j={},this.k={},this.R={},this.I=void 0,this.M=void 0,this.A=void 0,this.N=void 0,this.D=void 0,this.L=void 0,this.F(),this.T=[],this}addBreadcrumb(t,n){const e="number"==typeof n?n:100;if(e<=0)return this;const i={timestamp:Rt(),...t};return this.S=[...this.S,i].slice(-e),this.F(),this}getLastBreadcrumb(){return this.S[this.S.length-1]}clearBreadcrumbs(){return this.S=[],this.F(),this}addAttachment(t){return this.T.push(t),this}getAttachments(){return this.T}clearAttachments(){return this.T=[],this}applyToEvent(t,n={}){if(this.j&&Object.keys(this.j).length&&(t.extra={...this.j,...t.extra}),this.O&&Object.keys(this.O).length&&(t.tags={...this.O,...t.tags}),this.k&&Object.keys(this.k).length&&(t.user={...this.k,...t.user}),this.R&&Object.keys(this.R).length&&(t.contexts={...this.R,...t.contexts}),this.I&&(t.level=this.I),this.M&&(t.transaction=this.M),this.D){t.contexts={trace:this.D.getTraceContext(),...t.contexts};const n=this.D.transaction;if(n){t.sdkProcessingMetadata={dynamicSamplingContext:n.getDynamicSamplingContext(),...t.sdkProcessingMetadata};const e=n.name;e&&(t.tags={transaction:e,...t.tags})}}return this.H(t),t.breadcrumbs=[...t.breadcrumbs||[],...this.S],t.breadcrumbs=t.breadcrumbs.length>0?t.breadcrumbs:void 0,t.sdkProcessingMetadata={...t.sdkProcessingMetadata,...this.C},this.U([...tn(),...this._],t,n)}setSDKProcessingMetadata(t){return this.C={...this.C,...t},this}U(t,n,e,i=0){return new St(((r,s)=>{const o=t[i];if(null===n||"function"!=typeof o)r(n);else{const c=o({...n},e);f(c)?c.then((n=>this.U(t,n,e,i+1).then(r))).then(null,s):this.U(t,c,e,i+1).then(r).then(null,s)}}))}F(){this.v||(this.v=!0,this.g.forEach((t=>{t(this)})),this.v=!1)}H(t){t.fingerprint=t.fingerprint?mt(t.fingerprint):[],this.A&&(t.fingerprint=t.fingerprint.concat(this.A)),t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint}}function tn(){return v("globalEventProcessors",(()=>[]))}function nn(t){tn().push(t)}const en=100;class rn{constructor(t,n=new Zt,e=4){this.P=e,this.q=[{scope:n}],t&&this.bindClient(t)}isOlderThan(t){return this.P<t}bindClient(t){this.getStackTop().client=t,t&&t.setupIntegrations&&t.setupIntegrations()}pushScope(){const t=Zt.clone(this.getScope());return this.getStack().push({client:this.getClient(),scope:t}),t}popScope(){return!(this.getStack().length<=1)&&!!this.getStack().pop()}withScope(t){const n=this.pushScope();try{t(n)}finally{this.popScope()}}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getStack(){return this.q}getStackTop(){return this.q[this.q.length-1]}captureException(t,n){const e=this.B=n&&n.event_id?n.event_id:at(),i=new Error("Sentry syntheticException");return this.X(((r,s)=>{r.captureException(t,{originalException:t,syntheticException:i,...n,event_id:e},s)})),e}captureMessage(t,n,e){const i=this.B=e&&e.event_id?e.event_id:at(),r=new Error(t);return this.X(((s,o)=>{s.captureMessage(t,n,{originalException:t,syntheticException:r,...e,event_id:i},o)})),i}captureEvent(t,n){const e=n&&n.event_id?n.event_id:at();return t.type||(this.B=e),this.X(((i,r)=>{i.captureEvent(t,{...n,event_id:e},r)})),e}lastEventId(){return this.B}addBreadcrumb(t,n){const{scope:e,client:i}=this.getStackTop();if(!i)return;const{beforeBreadcrumb:r=null,maxBreadcrumbs:s=en}=i.getOptions&&i.getOptions()||{};if(s<=0)return;const o={timestamp:Rt(),...t},c=r?function(t){if(!("console"in m))return t();const n=m.console,e={};k.forEach((t=>{const i=n[t]&&n[t].__sentry_original__;t in n&&i&&(e[t]=n[t],n[t]=i)}));try{return t()}finally{Object.keys(e).forEach((t=>{n[t]=e[t]}))}}((()=>r(o,n))):o;null!==c&&(i.emit&&i.emit("beforeAddBreadcrumb",c,n),e.addBreadcrumb(c,s))}setUser(t){this.getScope().setUser(t)}setTags(t){this.getScope().setTags(t)}setExtras(t){this.getScope().setExtras(t)}setTag(t,n){this.getScope().setTag(t,n)}setExtra(t,n){this.getScope().setExtra(t,n)}setContext(t,n){this.getScope().setContext(t,n)}configureScope(t){const{scope:n,client:e}=this.getStackTop();e&&t(n)}run(t){const n=on(this);try{t(this)}finally{on(n)}}getIntegration(t){const n=this.getClient();if(!n)return null;try{return n.getIntegration(t)}catch(t){return null}}startTransaction(t,n){return this.W("startTransaction",t,n)}traceHeaders(){return this.W("traceHeaders")}captureSession(t=!1){if(t)return this.endSession();this.G()}endSession(){const t=this.getStackTop().scope,n=t.getSession();n&&function(t,n){let e={};n?e={status:n}:"ok"===t.status&&(e={status:"exited"}),Qt(t,e)}(n),this.G(),t.setSession()}startSession(t){const{scope:n,client:e}=this.getStackTop(),{release:i,environment:r=Yt}=e&&e.getOptions()||{},{userAgent:s}=m.navigator||{},o=Vt({release:i,environment:r,user:n.getUser(),...s&&{userAgent:s},...t}),c=n.getSession&&n.getSession();return c&&"ok"===c.status&&Qt(c,{status:"exited"}),this.endSession(),n.setSession(o),o}shouldSendDefaultPii(){const t=this.getClient(),n=t&&t.getOptions();return Boolean(n&&n.sendDefaultPii)}G(){const{scope:t,client:n}=this.getStackTop(),e=t.getSession();e&&n&&n.captureSession&&n.captureSession(e)}X(t){const{scope:n,client:e}=this.getStackTop();e&&t(e,n)}W(t,...n){const e=sn().__SENTRY__;if(e&&e.extensions&&"function"==typeof e.extensions[t])return e.extensions[t].apply(this,n)}}function sn(){return m.__SENTRY__=m.__SENTRY__||{extensions:{},hub:void 0},m}function on(t){const n=sn(),e=un(n);return an(n,t),e}function cn(){const t=sn();if(t.__SENTRY__&&t.__SENTRY__.acs){const n=t.__SENTRY__.acs.getCurrentHub();if(n)return n}return function(t=sn()){n=t,n&&n.__SENTRY__&&n.__SENTRY__.hub&&!un(t).isOlderThan(4)||an(t,new rn);var n;return un(t)}(t)}function un(t){return v("hub",(()=>new rn),t)}function an(t,n){if(!t)return!1;return(t.__SENTRY__=t.__SENTRY__||{}).hub=n,!0}function hn(t){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;const n=cn().getClient(),e=t||n&&n.getOptions();return!!e&&(e.enableTracing||"tracesSampleRate"in e||"tracesSampler"in e)}function fn(t){return(t||cn()).getScope().getTransaction()}let ln=!1;function dn(){const t=fn();if(t){const n="internal_error";t.setStatus(n)}}dn.tag="sentry_tracingErrorCallback";class pn{__init(){this.spans=[]}constructor(t=1e3){pn.prototype.__init.call(this),this.J=t}add(t){this.spans.length>this.J?t.spanRecorder=void 0:this.spans.push(t)}}class mn{__init2(){this.traceId=at()}__init3(){this.spanId=at().substring(16)}__init4(){this.startTimestamp=It()}__init5(){this.tags={}}__init6(){this.data={}}__init7(){this.instrumenter="sentry"}constructor(t){if(mn.prototype.__init2.call(this),mn.prototype.__init3.call(this),mn.prototype.__init4.call(this),mn.prototype.__init5.call(this),mn.prototype.__init6.call(this),mn.prototype.__init7.call(this),!t)return this;t.traceId&&(this.traceId=t.traceId),t.spanId&&(this.spanId=t.spanId),t.parentSpanId&&(this.parentSpanId=t.parentSpanId),"sampled"in t&&(this.sampled=t.sampled),t.op&&(this.op=t.op),t.description&&(this.description=t.description),t.data&&(this.data=t.data),t.tags&&(this.tags=t.tags),t.status&&(this.status=t.status),t.startTimestamp&&(this.startTimestamp=t.startTimestamp),t.endTimestamp&&(this.endTimestamp=t.endTimestamp),t.instrumenter&&(this.instrumenter=t.instrumenter)}startChild(t){const n=new mn({...t,parentSpanId:this.spanId,sampled:this.sampled,traceId:this.traceId});return n.spanRecorder=this.spanRecorder,n.spanRecorder&&n.spanRecorder.add(n),n.transaction=this.transaction,n}setTag(t,n){return this.tags={...this.tags,[t]:n},this}setData(t,n){return this.data={...this.data,[t]:n},this}setStatus(t){return this.status=t,this}setHttpStatus(t){this.setTag("http.status_code",String(t));const n=function(t){if(t<400&&t>=100)return"ok";if(t>=400&&t<500)switch(t){case 401:return"unauthenticated";case 403:return"permission_denied";case 404:return"not_found";case 409:return"already_exists";case 413:return"failed_precondition";case 429:return"resource_exhausted";default:return"invalid_argument"}if(t>=500&&t<600)switch(t){case 501:return"unimplemented";case 503:return"unavailable";case 504:return"deadline_exceeded";default:return"internal_error"}return"unknown_error"}(t);return"unknown_error"!==n&&this.setStatus(n),this}isSuccess(){return"ok"===this.status}finish(t){this.endTimestamp="number"==typeof t?t:It()}toTraceparent(){let t="";return void 0!==this.sampled&&(t=this.sampled?"-1":"-0"),`${this.traceId}-${this.spanId}${t}`}toContext(){return H({data:this.data,description:this.description,endTimestamp:this.endTimestamp,op:this.op,parentSpanId:this.parentSpanId,sampled:this.sampled,spanId:this.spanId,startTimestamp:this.startTimestamp,status:this.status,tags:this.tags,traceId:this.traceId})}updateWithContext(t){return this.data=t.data||{},this.description=t.description,this.endTimestamp=t.endTimestamp,this.op=t.op,this.parentSpanId=t.parentSpanId,this.sampled=t.sampled,this.spanId=t.spanId||this.spanId,this.startTimestamp=t.startTimestamp||this.startTimestamp,this.status=t.status,this.tags=t.tags||{},this.traceId=t.traceId||this.traceId,this}getTraceContext(){return H({data:Object.keys(this.data).length>0?this.data:void 0,description:this.description,op:this.op,parent_span_id:this.parentSpanId,span_id:this.spanId,status:this.status,tags:Object.keys(this.tags).length>0?this.tags:void 0,trace_id:this.traceId})}toJSON(){return H({data:Object.keys(this.data).length>0?this.data:void 0,description:this.description,op:this.op,parent_span_id:this.parentSpanId,span_id:this.spanId,start_timestamp:this.startTimestamp,status:this.status,tags:Object.keys(this.tags).length>0?this.tags:void 0,timestamp:this.endTimestamp,trace_id:this.traceId})}}class yn extends mn{__init(){this.K={}}__init2(){this.R={}}__init3(){this.Y=void 0}constructor(t,n){super(t),yn.prototype.__init.call(this),yn.prototype.__init2.call(this),yn.prototype.__init3.call(this),this.V=n||cn(),this.Z=t.name||"",this.metadata={source:"custom",...t.metadata,spanMetadata:{}},this.tt=t.trimEnd,this.transaction=this;const e=this.metadata.dynamicSamplingContext;e&&(this.Y={...e})}get name(){return this.Z}set name(t){this.setName(t)}setName(t,n="custom"){this.Z=t,this.metadata.source=n}initSpanRecorder(t=1e3){this.spanRecorder||(this.spanRecorder=new pn(t)),this.spanRecorder.add(this)}setContext(t,n){null===n?delete this.R[t]:this.R[t]=n}setMeasurement(t,n,e=""){this.K[t]={value:n,unit:e}}setMetadata(t){this.metadata={...this.metadata,...t}}finish(t){if(void 0!==this.endTimestamp)return;this.name||(this.name="<unlabeled transaction>"),super.finish(t);const n=this.V.getClient();if(n&&n.emit&&n.emit("finishTransaction",this),!0!==this.sampled)return void(n&&n.recordDroppedEvent("sample_rate","transaction"));const e=this.spanRecorder?this.spanRecorder.spans.filter((t=>t!==this&&t.endTimestamp)):[];this.tt&&e.length>0&&(this.endTimestamp=e.reduce(((t,n)=>t.endTimestamp&&n.endTimestamp?t.endTimestamp>n.endTimestamp?t:n:t)).endTimestamp);const i=this.metadata,r={contexts:{...this.R,trace:this.getTraceContext()},spans:e,start_timestamp:this.startTimestamp,tags:this.tags,timestamp:this.endTimestamp,transaction:this.name,type:"transaction",sdkProcessingMetadata:{...i,dynamicSamplingContext:this.getDynamicSamplingContext()},...i.source&&{transaction_info:{source:i.source}}};return Object.keys(this.K).length>0&&(r.measurements=this.K),this.V.captureEvent(r)}toContext(){return H({...super.toContext(),name:this.name,trimEnd:this.tt})}updateWithContext(t){return super.updateWithContext(t),this.name=t.name||"",this.tt=t.trimEnd,this}getDynamicSamplingContext(){if(this.Y)return this.Y;const t=this.V||cn(),n=t&&t.getClient();if(!n)return{};const{environment:e,release:i}=n.getOptions()||{},{publicKey:r}=n.getDsn()||{},s=this.metadata.sampleRate,o=void 0!==s?s.toString():void 0,{segment:c}=t.getScope().getUser()||{},u=this.metadata.source,a=u&&"url"!==u?this.name:void 0,h=H({environment:e||Yt,release:i,transaction:a,user_segment:c,public_key:r,trace_id:this.traceId,sample_rate:o});return n.emit&&n.emit("createDsc",h),h}setHub(t){this.V=t}}const vn={idleTimeout:1e3,finalTimeout:3e4,heartbeatInterval:5e3},gn=["heartbeatFailed","idleTimeout","documentHidden","finalTimeout","externalFinish","cancelled"];class _n extends pn{constructor(t,n,e,i){super(i),this.nt=t,this.et=n,this.transactionSpanId=e}add(t){t.spanId!==this.transactionSpanId&&(t.finish=n=>{t.endTimestamp="number"==typeof n?n:It(),this.et(t.spanId)},void 0===t.endTimestamp&&this.nt(t.spanId)),super.add(t)}}class bn extends yn{__init(){this.activities={}}__init2(){this.it=0}__init3(){this.rt=!1}__init4(){this.st=!1}__init5(){this.ot=[]}__init6(){this.ct=gn[4]}constructor(t,n,e=vn.idleTimeout,i=vn.finalTimeout,r=vn.heartbeatInterval,s=!1){super(t,n),this.ut=n,this.ht=e,this.ft=i,this.lt=r,this.dt=s,bn.prototype.__init.call(this),bn.prototype.__init2.call(this),bn.prototype.__init3.call(this),bn.prototype.__init4.call(this),bn.prototype.__init5.call(this),bn.prototype.__init6.call(this),s&&n.configureScope((t=>t.setSpan(this))),this.yt(),setTimeout((()=>{this.rt||(this.setStatus("deadline_exceeded"),this.ct=gn[3],this.finish())}),this.ft)}finish(t=It()){if(this.rt=!0,this.activities={},"ui.action.click"===this.op&&this.setTag("finishReason",this.ct),this.spanRecorder){for(const n of this.ot)n(this,t);this.spanRecorder.spans=this.spanRecorder.spans.filter((n=>{if(n.spanId===this.spanId)return!0;n.endTimestamp||(n.endTimestamp=t,n.setStatus("cancelled"));return n.startTimestamp<t}))}if(this.dt){const t=this.ut.getScope();t.getTransaction()===this&&t.setSpan(void 0)}return super.finish(t)}registerBeforeFinishCallback(t){this.ot.push(t)}initSpanRecorder(t){if(!this.spanRecorder){const n=t=>{this.rt||this.nt(t)},e=t=>{this.rt||this.et(t)};this.spanRecorder=new _n(n,e,this.spanId,t),this.vt()}this.spanRecorder.add(this)}cancelIdleTimeout(t,{restartOnChildSpanChange:n}={restartOnChildSpanChange:!0}){this.st=!1===n,this.gt&&(clearTimeout(this.gt),this.gt=void 0,0===Object.keys(this.activities).length&&this.st&&(this.ct=gn[5],this.finish(t)))}setFinishReason(t){this.ct=t}yt(t){this.cancelIdleTimeout(),this.gt=setTimeout((()=>{this.rt||0!==Object.keys(this.activities).length||(this.ct=gn[1],this.finish(t))}),this.ht)}nt(t){this.cancelIdleTimeout(void 0,{restartOnChildSpanChange:!this.st}),this.activities[t]=!0}et(t){if(this.activities[t]&&delete this.activities[t],0===Object.keys(this.activities).length){const t=It();this.st?(this.ct=gn[5],this.finish(t)):this.yt(t+this.ht/1e3)}}_t(){if(this.rt)return;const t=Object.keys(this.activities).join("");t===this.bt?this.it++:this.it=1,this.bt=t,this.it>=3?(this.setStatus("deadline_exceeded"),this.ct=gn[0],this.finish()):this.vt()}vt(){setTimeout((()=>{this._t()}),this.lt)}}function wn(){const t=this.getScope().getSpan();return t?{"sentry-trace":t.toTraceparent()}:{}}function Sn(t,n,e){if(!hn(n))return t.sampled=!1,t;if(void 0!==t.sampled)return t.setMetadata({sampleRate:Number(t.sampled)}),t;let i;return"function"==typeof n.tracesSampler?(i=n.tracesSampler(e),t.setMetadata({sampleRate:Number(i)})):void 0!==e.parentSampled?i=e.parentSampled:void 0!==n.tracesSampleRate?(i=n.tracesSampleRate,t.setMetadata({sampleRate:Number(i)})):(i=1,t.setMetadata({sampleRate:i})),function(t){if(l(t)||"number"!=typeof t&&"boolean"!=typeof t)return!1;if(t<0||t>1)return!1;return!0}(i)&&i?(t.sampled=Math.random()<i,t.sampled,t):(t.sampled=!1,t)}function Tn(t,n){const e=this.getClient(),i=e&&e.getOptions()||{};(i.instrumenter||"sentry")!==(t.instrumenter||"sentry")&&(t.sampled=!1);let r=new yn(t,this);return r=Sn(r,i,{parentSampled:t.parentSampled,transactionContext:t,...n}),r.sampled&&r.initSpanRecorder(i._experiments&&i._experiments.maxSpans),e&&e.emit&&e.emit("startTransaction",r),r}function En(t,n,e,i,r,s,o){const c=t.getClient(),u=c&&c.getOptions()||{};let a=new bn(n,t,e,i,o,r);return a=Sn(a,u,{parentSampled:n.parentSampled,transactionContext:n,...s}),a.sampled&&a.initSpanRecorder(u._experiments&&u._experiments.maxSpans),c&&c.emit&&c.emit("startTransaction",a),a}function $n(){const t=sn();t.__SENTRY__&&(t.__SENTRY__.extensions=t.__SENTRY__.extensions||{},t.__SENTRY__.extensions.startTransaction||(t.__SENTRY__.extensions.startTransaction=Tn),t.__SENTRY__.extensions.traceHeaders||(t.__SENTRY__.extensions.traceHeaders=wn),ln||(ln=!0,Z("error",dn),Z("unhandledrejection",dn)))}function captureException(t,n){return cn().captureException(t,{captureContext:n})}function kn(t){cn().withScope(t)}function xn(t){const n=t.protocol?`${t.protocol}:`:"",e=t.port?`:${t.port}`:"";return`${n}//${t.host}${e}${t.path?`/${t.path}`:""}/api/`}function On(t,n){return e={sentry_key:t.publicKey,sentry_version:"7",...n&&{sentry_client:`${n.name}/${n.version}`}},Object.keys(e).map((t=>`${encodeURIComponent(t)}=${encodeURIComponent(e[t])}`)).join("&");var e}function jn(t,n={}){const e="string"==typeof n?n:n.tunnel,i="string"!=typeof n&&n.wt?n.wt.sdk:void 0;return e||`${function(t){return`${xn(t)}${t.projectId}/envelope/`}(t)}?${On(t,i)}`}function Rn(t,n,e,i){const r=Bt(e),s=t.type&&"replay_event"!==t.type?t.type:"event";!function(t,n){n&&(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||n.name,t.sdk.version=t.sdk.version||n.version,t.sdk.integrations=[...t.sdk.integrations||[],...n.integrations||[]],t.sdk.packages=[...t.sdk.packages||[],...n.packages||[]])}(t,e&&e.sdk);const o=function(t,n,e,i){const r=t.sdkProcessingMetadata&&t.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:t.event_id,sent_at:(new Date).toISOString(),...n&&{sdk:n},...!!e&&{dsn:T(i)},...r&&{trace:H({...r})}}}(t,r,i,n);delete t.sdkProcessingMetadata;return Mt(o,[[{type:s},t]])}const Cn=[];function In(t){const n=t.defaultIntegrations||[],e=t.integrations;let i;n.forEach((t=>{t.isDefaultInstance=!0})),i=Array.isArray(e)?[...n,...e]:"function"==typeof e?mt(e(n)):n;const r=function(t){const n={};return t.forEach((t=>{const{name:e}=t,i=n[e];i&&!i.isDefaultInstance&&t.isDefaultInstance||(n[e]=t)})),Object.keys(n).map((t=>n[t]))}(i),s=function(t,n){for(let e=0;e<t.length;e++)if(!0===n(t[e]))return e;return-1}(r,(t=>"Debug"===t.name));if(-1!==s){const[t]=r.splice(s,1);r.push(t)}return r}function Dn(t,n){n[t.name]=t,-1===Cn.indexOf(t.name)&&(t.setupOnce(nn,cn),Cn.push(t.name))}function Ln(t,n,e,i){const{normalizeDepth:r=3,normalizeMaxBreadth:s=1e3}=t,o={...n,event_id:n.event_id||e.event_id||at(),timestamp:n.timestamp||Rt()},c=e.integrations||t.integrations.map((t=>t.name));!function(t,n){const{environment:e,release:i,dist:r,maxValueLength:s=250}=n;"environment"in t||(t.environment="environment"in n?e:Yt);void 0===t.release&&void 0!==i&&(t.release=i);void 0===t.dist&&void 0!==r&&(t.dist=r);t.message&&(t.message=x(t.message,s));const o=t.exception&&t.exception.values&&t.exception.values[0];o&&o.value&&(o.value=x(o.value,s));const c=t.request;c&&c.url&&(c.url=x(c.url,s))}(o,t),function(t,n){n.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=[...t.sdk.integrations||[],...n])}(o,c),void 0===n.type&&function(t,n){const e=m.St;if(!e)return;let i;const r=Mn.get(n);r?i=r:(i=new Map,Mn.set(n,i));const s=Object.keys(e).reduce(((t,r)=>{let s;const o=i.get(r);o?s=o:(s=n(r),i.set(r,s));for(let n=s.length-1;n>=0;n--){const i=s[n];if(i.filename){t[i.filename]=e[r];break}}return t}),{}),o=new Set;try{t.exception.values.forEach((t=>{t.stacktrace.frames.forEach((t=>{t.filename&&o.add(t.filename)}))}))}catch(t){}t.debug_meta=t.debug_meta||{},t.debug_meta.images=t.debug_meta.images||[];const c=t.debug_meta.images;o.forEach((t=>{s[t]&&c.push({type:"sourcemap",code_file:t,debug_id:s[t]})}))}(o,t.stackParser);let u=i;e.captureContext&&(u=Zt.clone(u).update(e.captureContext));let a=bt(o);if(u){if(u.getAttachments){const t=[...e.attachments||[],...u.getAttachments()];t.length&&(e.attachments=t)}a=u.applyToEvent(o,e)}return a.then((t=>"number"==typeof r&&r>0?function(t,n,e){if(!t)return null;const i={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map((t=>({...t,...t.data&&{data:yt(t.data,n,e)}})))},...t.user&&{user:yt(t.user,n,e)},...t.contexts&&{contexts:yt(t.contexts,n,e)},...t.extra&&{extra:yt(t.extra,n,e)}};t.contexts&&t.contexts.trace&&i.contexts&&(i.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(i.contexts.trace.data=yt(t.contexts.trace.data,n,e)));t.spans&&(i.spans=t.spans.map((t=>(t.data&&(t.data=yt(t.data,n,e)),t))));return i}(t,r,s):t))}const Mn=new WeakMap;class An{__init(){this._integrations={}}__init2(){this.Tt=!1}__init3(){this.Et=0}__init4(){this.$t={}}__init5(){this.kt={}}constructor(t){if(An.prototype.__init.call(this),An.prototype.__init2.call(this),An.prototype.__init3.call(this),An.prototype.__init4.call(this),An.prototype.__init5.call(this),this.xt=t,t.dsn){this.Ot=$(t.dsn);const n=jn(this.Ot,t);this.jt=t.transport({recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:n})}}captureException(t,n,e){if(pt(t))return;let i=n&&n.event_id;return this.Rt(this.eventFromException(t,n).then((t=>this.Ct(t,n,e))).then((t=>{i=t}))),i}captureMessage(t,n,e,i){let r=e&&e.event_id;const s=u(t)?this.eventFromMessage(String(t),n,e):this.eventFromException(t,e);return this.Rt(s.then((t=>this.Ct(t,e,i))).then((t=>{r=t}))),r}captureEvent(t,n,e){if(n&&n.originalException&&pt(n.originalException))return;let i=n&&n.event_id;return this.Rt(this.Ct(t,n,e).then((t=>{i=t}))),i}captureSession(t){this.It()&&("string"!=typeof t.release||(this.sendSession(t),Qt(t,{init:!1})))}getDsn(){return this.Ot}getOptions(){return this.xt}getSdkMetadata(){return this.xt.wt}getTransport(){return this.jt}flush(t){const n=this.jt;return n?this.Dt(t).then((e=>n.flush(t).then((t=>e&&t)))):bt(!0)}close(t){return this.flush(t).then((t=>(this.getOptions().enabled=!1,t)))}setupIntegrations(){this.It()&&!this.Tt&&(this._integrations=function(t){const n={};return t.forEach((t=>{t&&Dn(t,n)})),n}(this.xt.integrations),this.Tt=!0)}getIntegrationById(t){return this._integrations[t]}getIntegration(t){try{return this._integrations[t.id]||null}catch(t){return null}}addIntegration(t){Dn(t,this._integrations)}sendEvent(t,n={}){if(this.Ot){let e=Rn(t,this.Ot,this.xt.wt,this.xt.tunnel);for(const t of n.attachments||[])e=At(e,Ut(t,this.xt.transportOptions&&this.xt.transportOptions.textEncoder));const i=this.Lt(e);i&&i.then((n=>this.emit("afterSendEvent",t,n)),null)}}sendSession(t){if(this.Ot){const n=function(t,n,e,i){const r=Bt(e);return Mt({sent_at:(new Date).toISOString(),...r&&{sdk:r},...!!i&&{dsn:T(n)}},["aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t]])}(t,this.Ot,this.xt.wt,this.xt.tunnel);this.Lt(n)}}recordDroppedEvent(t,n,e){if(this.xt.sendClientReports){const e=`${t}:${n}`;this.$t[e]=this.$t[e]+1||1}}on(t,n){this.kt[t]||(this.kt[t]=[]),this.kt[t].push(n)}emit(t,...n){this.kt[t]&&this.kt[t].forEach((t=>t(...n)))}Mt(t,n){let e=!1,i=!1;const r=n.exception&&n.exception.values;if(r){i=!0;for(const t of r){const n=t.mechanism;if(n&&!1===n.handled){e=!0;break}}}const s="ok"===t.status;(s&&0===t.errors||s&&e)&&(Qt(t,{...e&&{status:"crashed"},errors:t.errors||Number(i||e)}),this.captureSession(t))}Dt(t){return new St((n=>{let e=0;const i=setInterval((()=>{0==this.Et?(clearInterval(i),n(!0)):(e+=1,t&&e>=t&&(clearInterval(i),n(!1)))}),1)}))}It(){return!1!==this.getOptions().enabled&&void 0!==this.Ot}At(t,n,e){const i=this.getOptions(),r=Object.keys(this._integrations);return!n.integrations&&r.length>0&&(n.integrations=r),Ln(i,t,n,e)}Ct(t,n={},e){return this.Nt(t,n,e).then((t=>t.event_id),(t=>{}))}Nt(t,n,e){const i=this.getOptions(),{sampleRate:r}=i;if(!this.It())return wt(new w("SDK not enabled, will not capture event.","log"));const s=Fn(t),o=Nn(t),c=t.type||"error",u=`before send for type \`${c}\``;if(o&&"number"==typeof r&&Math.random()>r)return this.recordDroppedEvent("sample_rate","error",t),wt(new w(`Discarding event because it's not included in the random sample (sampling rate = ${r})`,"log"));const h="replay_event"===c?"replay":c;return this.At(t,n,e).then((e=>{if(null===e)throw this.recordDroppedEvent("event_processor",h,t),new w("An event processor returned `null`, will not send event.","log");if(n.data&&!0===n.data.__sentry__)return e;const r=function(t,n,e){const{beforeSend:i,beforeSendTransaction:r}=t;if(Nn(n)&&i)return i(n,e);if(Fn(n)&&r)return r(n,e);return n}(i,e,n);return function(t,n){const e=`${n} must return \`null\` or a valid event.`;if(f(t))return t.then((t=>{if(!a(t)&&null!==t)throw new w(e);return t}),(t=>{throw new w(`${n} rejected with ${t}`)}));if(!a(t)&&null!==t)throw new w(e);return t}(r,u)})).then((i=>{if(null===i)throw this.recordDroppedEvent("before_send",h,t),new w(`${u} returned \`null\`, will not send event.`,"log");const r=e&&e.getSession();!s&&r&&this.Mt(r,i);const o=i.transaction_info;if(s&&o&&i.transaction!==t.transaction){const t="custom";i.transaction_info={...o,source:t}}return this.sendEvent(i,n),i})).then(null,(t=>{if(t instanceof w)throw t;throw this.captureException(t,{data:{__sentry__:!0},originalException:t}),new w(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: ${t}`)}))}Rt(t){this.Et++,t.then((t=>(this.Et--,t)),(t=>(this.Et--,t)))}Lt(t){if(this.jt&&this.Ot)return this.emit("beforeEnvelope",t),this.jt.send(t).then(null,(t=>{}))}Ft(){const t=this.$t;return this.$t={},Object.keys(t).map((n=>{const[e,i]=n.split(":");return{reason:e,category:i,quantity:t[n]}}))}}function Nn(t){return void 0===t.type}function Fn(t){return"transaction"===t.type}function Hn(t,n,e=Tt(t.bufferSize||30)){let i={};function r(r){const s=[];if(Nt(r,((n,e)=>{const r=qt(e);if(function(t,n,e=Date.now()){return function(t,n){return t[n]||t.all||0}(t,n)>e}(i,r)){const i=Un(n,e);t.recordDroppedEvent("ratelimit_backoff",r,i)}else s.push(n)})),0===s.length)return bt();const o=Mt(r[0],s),c=n=>{Nt(o,((e,i)=>{const r=Un(e,i);t.recordDroppedEvent(n,qt(i),r)}))};return e.add((()=>n({body:Ht(o,t.textEncoder)}).then((t=>(i=Xt(i,t),t)),(t=>{throw c("network_error"),t})))).then((t=>t),(t=>{if(t instanceof w)return c("queue_overflow"),bt();throw t}))}return r.__sentry__baseTransport__=!0,{send:r,flush:t=>e.drain(t)}}function Un(t,n){if("event"===n||"transaction"===n)return Array.isArray(t)?t[1]:void 0}const Pn="7.48.0";let qn;class Bn{constructor(){Bn.prototype.__init.call(this)}static __initStatic(){this.id="FunctionToString"}__init(){this.name=Bn.id}setupOnce(){qn=Function.prototype.toString,Function.prototype.toString=function(...t){const n=L(this)||this;return qn.apply(n,t)}}}Bn.__initStatic();const Xn=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/];class zn{static __initStatic(){this.id="InboundFilters"}__init(){this.name=zn.id}constructor(t={}){this.xt=t,zn.prototype.__init.call(this)}setupOnce(t,n){const e=t=>{const e=n();if(e){const n=e.getIntegration(zn);if(n){const i=e.getClient(),r=i?i.getOptions():{},s=function(t={},n={}){return{allowUrls:[...t.allowUrls||[],...n.allowUrls||[]],denyUrls:[...t.denyUrls||[],...n.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...n.ignoreErrors||[],...Xn],ignoreTransactions:[...t.ignoreTransactions||[],...n.ignoreTransactions||[]],ignoreInternal:void 0===t.ignoreInternal||t.ignoreInternal}}(n.xt,r);return function(t,n){if(n.ignoreInternal&&function(t){try{return"SentryError"===t.exception.values[0].type}catch(t){}return!1}(t))return!0;if(function(t,n){if(t.type||!n||!n.length)return!1;return function(t){if(t.message)return[t.message];if(t.exception)try{const{type:n="",value:e=""}=t.exception.values&&t.exception.values[0]||{};return[`${e}`,`${n}: ${e}`]}catch(t){return[]}return[]}(t).some((t=>R(t,n)))}(t,n.ignoreErrors))return!0;if(function(t,n){if("transaction"!==t.type||!n||!n.length)return!1;const e=t.transaction;return!!e&&R(e,n)}(t,n.ignoreTransactions))return!0;if(function(t,n){if(!n||!n.length)return!1;const e=Wn(t);return!!e&&R(e,n)}(t,n.denyUrls))return!0;if(!function(t,n){if(!n||!n.length)return!0;const e=Wn(t);return!e||R(e,n)}(t,n.allowUrls))return!0;return!1}(t,s)?null:t}}return t};e.id=this.name,t(e)}}function Wn(t){try{let n;try{n=t.exception.values[0].stacktrace.frames}catch(t){}return n?function(t=[]){for(let n=t.length-1;n>=0;n--){const e=t[n];if(e&&"<anonymous>"!==e.filename&&"[native code]"!==e.filename)return e.filename||null}return null}(n):null}catch(t){return null}}zn.__initStatic();var Gn=Object.freeze({__proto__:null,FunctionToString:Bn,InboundFilters:zn});const Jn=m;const Kn=(t,n,e)=>{let i,r;return s=>{n.value>=0&&(s||e)&&(r=n.value-(i||0),(r||void 0===i)&&(i=n.value,n.delta=r,t(n)))}},Yn=()=>Jn.__WEB_VITALS_POLYFILL__?Jn.performance&&(performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]||(()=>{const t=Jn.performance.timing,n=Jn.performance.navigation.type,e={entryType:"navigation",startTime:0,type:2==n?"back_forward":1===n?"reload":"navigate"};for(const n in t)"navigationStart"!==n&&"toJSON"!==n&&(e[n]=Math.max(t[n]-t.navigationStart,0));return e})()):Jn.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0],Vn=()=>{const t=Yn();return t&&t.activationStart||0},Qn=(t,n)=>{const e=Yn();let i="navigate";return e&&(i=Jn.document.prerendering||Vn()>0?"prerender":e.type.replace(/_/g,"-")),{name:t,value:void 0===n?-1:n,rating:"good",delta:0,entries:[],id:`v3-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:i}},Zn=(t,n,e)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(t)){const i=new PerformanceObserver((t=>{n(t.getEntries())}));return i.observe(Object.assign({type:t,buffered:!0},e||{})),i}}catch(t){}},te=(t,n)=>{const e=i=>{"pagehide"!==i.type&&"hidden"!==Jn.document.visibilityState||(t(i),n&&(removeEventListener("visibilitychange",e,!0),removeEventListener("pagehide",e,!0)))};addEventListener("visibilitychange",e,!0),addEventListener("pagehide",e,!0)};let ne=-1;const ee=()=>(ne<0&&(ne="hidden"!==Jn.document.visibilityState||Jn.document.prerendering?1/0:0,te((({timeStamp:t})=>{ne=t}),!0)),{get firstHiddenTime(){return ne}}),ie={};function re(t){return"number"==typeof t&&isFinite(t)}function se(t,{startTimestamp:n,...e}){return n&&t.startTimestamp>n&&(t.startTimestamp=n),t.startChild({startTimestamp:n,...e})}function oe(t){return t/1e3}function ce(){return Jn&&Jn.addEventListener&&Jn.performance}let ue,ae,he=0,fe={};function le(){const t=ce();if(t&&Dt){t.mark&&Jn.performance.mark("sentry-tracing-init"),(t=>{const n=ee(),e=Qn("FID");let i;const r=t=>{t.startTime<n.firstHiddenTime&&(e.value=t.processingStart-t.startTime,e.entries.push(t),i(!0))},s=t=>{t.forEach(r)},o=Zn("first-input",s);i=Kn(t,e),o&&te((()=>{s(o.takeRecords()),o.disconnect()}),!0)})((t=>{const n=t.entries.pop();if(!n)return;const e=oe(Dt),i=oe(n.startTime);fe.fid={value:t.value,unit:"millisecond"},fe["mark.fid"]={value:e+i,unit:"second"}}));const n=(t=>{const n=Qn("CLS",0);let e,i=0,r=[];const s=t=>{t.forEach((t=>{if(!t.hadRecentInput){const s=r[0],o=r[r.length-1];i&&0!==r.length&&t.startTime-o.startTime<1e3&&t.startTime-s.startTime<5e3?(i+=t.value,r.push(t)):(i=t.value,r=[t]),i>n.value&&(n.value=i,n.entries=r,e&&e())}}))},o=Zn("layout-shift",s);if(o){e=Kn(t,n);const i=()=>{s(o.takeRecords()),e(!0)};return te(i),i}})((t=>{const n=t.entries.pop();n&&(fe.cls={value:t.value,unit:""},ae=n)})),e=(t=>{const n=ee(),e=Qn("LCP");let i;const r=t=>{const r=t[t.length-1];if(r){const t=Math.max(r.startTime-Vn(),0);t<n.firstHiddenTime&&(e.value=t,e.entries=[r],i())}},s=Zn("largest-contentful-paint",r);if(s){i=Kn(t,e);const n=()=>{ie[e.id]||(r(s.takeRecords()),s.disconnect(),ie[e.id]=!0,i(!0))};return["keydown","click"].forEach((t=>{addEventListener(t,n,{once:!0,capture:!0})})),te(n,!0),n}})((t=>{const n=t.entries.pop();n&&(fe.lcp={value:t.value,unit:"millisecond"},ue=n)}));return()=>{n&&n(),e&&e()}}return()=>{}}function de(t){const n=ce();if(!n||!Jn.performance.getEntries||!Dt)return;const e=oe(Dt),i=n.getEntries();let r,s;if(i.slice(he).forEach((n=>{const i=oe(n.startTime),o=oe(n.duration);if(!("navigation"===t.op&&e+i<t.startTimestamp))switch(n.entryType){case"navigation":!function(t,n,e){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach((i=>{pe(t,n,i,e)})),pe(t,n,"secureConnection",e,"TLS/SSL","connectEnd"),pe(t,n,"fetch",e,"cache","domainLookupStart"),pe(t,n,"domainLookup",e,"DNS"),function(t,n,e){se(t,{op:"browser",description:"request",startTimestamp:e+oe(n.requestStart),endTimestamp:e+oe(n.responseEnd)}),se(t,{op:"browser",description:"response",startTimestamp:e+oe(n.responseStart),endTimestamp:e+oe(n.responseEnd)})}(t,n,e)}(t,n,e),r=e+oe(n.responseStart),s=e+oe(n.requestStart);break;case"mark":case"paint":case"measure":{!function(t,n,e,i,r){const s=r+e,o=s+i;se(t,{description:n.name,endTimestamp:o,op:n.entryType,startTimestamp:s})}(t,n,i,o,e);const r=ee(),s=n.startTime<r.firstHiddenTime;"first-paint"===n.name&&s&&(fe.fp={value:n.startTime,unit:"millisecond"}),"first-contentful-paint"===n.name&&s&&(fe.fcp={value:n.startTime,unit:"millisecond"});break}case"resource":{const r=n.name.replace(Jn.location.origin,"");!function(t,n,e,i,r,s){if("xmlhttprequest"===n.initiatorType||"fetch"===n.initiatorType)return;const o={};"transferSize"in n&&(o["Transfer Size"]=n.transferSize);"encodedBodySize"in n&&(o["Encoded Body Size"]=n.encodedBodySize);"decodedBodySize"in n&&(o["Decoded Body Size"]=n.decodedBodySize);"renderBlockingStatus"in n&&(o["resource.render_blocking_status"]=n.renderBlockingStatus);const c=s+i;se(t,{description:e,endTimestamp:c+r,op:n.initiatorType?`resource.${n.initiatorType}`:"resource.other",startTimestamp:c,data:o})}(t,n,r,i,o,e);break}}})),he=Math.max(i.length-1,0),function(t){const n=Jn.navigator;if(!n)return;const e=n.connection;e&&(e.effectiveType&&t.setTag("effectiveConnectionType",e.effectiveType),e.type&&t.setTag("connectionType",e.type),re(e.rtt)&&(fe["connection.rtt"]={value:e.rtt,unit:"millisecond"}));re(n.deviceMemory)&&t.setTag("deviceMemory",`${n.deviceMemory} GB`);re(n.hardwareConcurrency)&&t.setTag("hardwareConcurrency",String(n.hardwareConcurrency))}(t),"pageload"===t.op){"number"==typeof r&&(fe.ttfb={value:1e3*(r-t.startTimestamp),unit:"millisecond"},"number"==typeof s&&s<=r&&(fe["ttfb.requestTime"]={value:1e3*(r-s),unit:"millisecond"})),["fcp","fp","lcp"].forEach((n=>{if(!fe[n]||e>=t.startTimestamp)return;const i=fe[n].value,r=e+oe(i),s=Math.abs(1e3*(r-t.startTimestamp));fe[n].value=s}));const n=fe["mark.fid"];n&&fe.fid&&(se(t,{description:"first input delay",endTimestamp:n.value+oe(fe.fid.value),op:"ui.action",startTimestamp:n.value}),delete fe["mark.fid"]),"fcp"in fe||delete fe.cls,Object.keys(fe).forEach((n=>{t.setMeasurement(n,fe[n].value,fe[n].unit)})),function(t){ue&&(ue.element&&t.setTag("lcp.element",_(ue.element)),ue.id&&t.setTag("lcp.id",ue.id),ue.url&&t.setTag("lcp.url",ue.url.trim().slice(0,200)),t.setTag("lcp.size",ue.size));ae&&ae.sources&&ae.sources.forEach(((n,e)=>t.setTag(`cls.source.${e+1}`,_(n.node))))}(t)}ue=void 0,ae=void 0,fe={}}function pe(t,n,e,i,r,s){const o=s?n[s]:n[`${e}End`],c=n[`${e}Start`];c&&o&&se(t,{op:"browser",description:r||e,startTimestamp:i+oe(c),endTimestamp:i+oe(o)})}const me=["localhost",/^\//],ye={traceFetch:!0,traceXHR:!0,tracingOrigins:me,tracePropagationTargets:me};function ve(t){const{traceFetch:n,traceXHR:e,tracePropagationTargets:i,tracingOrigins:r,shouldCreateSpanForRequest:s}={traceFetch:ye.traceFetch,traceXHR:ye.traceXHR,...t},o="function"==typeof s?s:t=>!0,c=t=>function(t,n){return R(t,n||me)}(t,i||r),u={};n&&Z("fetch",(t=>{!function(t,n,e,i){if(!hn()||!t.fetchData||!n(t.fetchData.url))return;if(t.endTimestamp){const n=t.fetchData.__span;if(!n)return;const e=i[n];return void(e&&(t.response?e.setHttpStatus(t.response.status):t.error&&e.setStatus("internal_error"),e.finish(),delete i[n]))}const r=cn().getScope(),s=r&&r.getSpan(),o=s&&s.transaction;if(s&&o){const n=s.startChild({data:{...t.fetchData,type:"fetch"},description:`${t.fetchData.method} ${t.fetchData.url}`,op:"http.client"});t.fetchData.__span=n.spanId,i[n.spanId]=n;const r=t.args[0];t.args[1]=t.args[1]||{};const c=t.args[1];e(t.fetchData.url)&&(c.headers=function(t,n,e,i){const r=Jt(n),s=e.toTraceparent(),o="undefined"!=typeof Request&&d(t,Request)?t.headers:i.headers;if(o){if("undefined"!=typeof Headers&&d(o,Headers)){const t=new Headers(o);return t.append("sentry-trace",s),r&&t.append(zt,r),t}if(Array.isArray(o)){const t=[...o,["sentry-trace",s]];return r&&t.push([zt,r]),t}{const t="baggage"in o?o.baggage:void 0,n=[];return Array.isArray(t)?n.push(...t):t&&n.push(t),r&&n.push(r),{...o,"sentry-trace":s,baggage:n.length>0?n.join(","):void 0}}}return{"sentry-trace":s,baggage:r}}(r,o.getDynamicSamplingContext(),n,c))}}(t,o,c,u)})),e&&Z("xhr",(t=>{!function(t,n,e,i){const r=t.xhr,s=r&&r.__sentry_xhr_v2__;if(!hn()||r&&r.__sentry_own_request__||!(r&&s&&n(s.url)))return;if(t.endTimestamp){const t=r.__sentry_xhr_span_id__;if(!t)return;const n=i[t];return void(n&&(n.setHttpStatus(s.status_code),n.finish(),delete i[t]))}const o=cn().getScope(),c=o&&o.getSpan(),u=c&&c.transaction;if(c&&u){const t=c.startChild({data:{...s.data,type:"xhr",method:s.method,url:s.url},description:`${s.method} ${s.url}`,op:"http.client"});if(r.__sentry_xhr_span_id__=t.spanId,i[r.__sentry_xhr_span_id__]=t,r.setRequestHeader&&e(s.url))try{r.setRequestHeader("sentry-trace",t.toTraceparent());const n=Jt(u.getDynamicSamplingContext());n&&r.setRequestHeader(zt,n)}catch(t){}}}(t,o,c,u)}))}const ge={...vn,markBackgroundTransactions:!0,routingInstrumentation:function(t,n=!0,e=!0){if(!Jn||!Jn.location)return;let i,r=Jn.location.href;n&&(i=t({name:Jn.location.pathname,startTimestamp:Dt,op:"pageload",metadata:{source:"url"}})),e&&Z("history",(({to:n,from:e})=>{void 0===e&&r&&-1!==r.indexOf(n)?r=void 0:e!==n&&(r=void 0,i&&i.finish(),i=t({name:Jn.location.pathname,op:"navigation",metadata:{source:"url"}}))}))},startTransactionOnLocationChange:!0,startTransactionOnPageLoad:!0,enableLongTask:!0,_experiments:{},...ye};class _e{__init(){this.name="BrowserTracing"}constructor(t){_e.prototype.__init.call(this),$n(),this.options={...ge,...t},void 0!==this.options._experiments.enableLongTask&&(this.options.enableLongTask=this.options._experiments.enableLongTask),t&&!t.tracePropagationTargets&&t.tracingOrigins&&(this.options.tracePropagationTargets=t.tracingOrigins),this.Ht=le(),this.options.enableLongTask&&Zn("longtask",(t=>{for(const n of t){const t=fn();if(!t)return;const e=oe(Dt+n.startTime),i=oe(n.duration);t.startChild({description:"Main UI thread blocked",op:"ui.long-task",startTimestamp:e,endTimestamp:e+i})}})),this.options._experiments.enableInteractions&&Zn("event",(t=>{for(const n of t){const t=fn();if(!t)return;if("click"===n.name){const e=oe(Dt+n.startTime),i=oe(n.duration);t.startChild({description:_(n.target),op:`ui.interaction.${n.name}`,startTimestamp:e,endTimestamp:e+i})}}}),{durationThreshold:0})}setupOnce(t,n){this.Ut=n;const{routingInstrumentation:e,startTransactionOnLocationChange:i,startTransactionOnPageLoad:r,markBackgroundTransactions:s,traceFetch:o,traceXHR:c,tracePropagationTargets:u,shouldCreateSpanForRequest:a,_experiments:h}=this.options;e((t=>{const e=this.Pt(t);return this.options._experiments.onStartRouteTransaction&&this.options._experiments.onStartRouteTransaction(e,t,n),e}),r,i),s&&Jn&&Jn.document&&Jn.document.addEventListener("visibilitychange",(()=>{const t=fn();if(Jn.document.hidden&&t){const n="cancelled";t.status||t.setStatus(n),t.setTag("visibilitychange","document.hidden"),t.finish()}})),h.enableInteractions&&this.qt(),ve({traceFetch:o,traceXHR:c,tracePropagationTargets:u,shouldCreateSpanForRequest:a})}Pt(t){if(!this.Ut)return;const{beforeNavigate:n,idleTimeout:e,finalTimeout:i,heartbeatInterval:r}=this.options,s="pageload"===t.op,o=s?be("sentry-trace"):null,u=s?be("baggage"):null,a=o?function(t){const n=t.match(Lt);if(!t||!n)return;let e;return"1"===n[3]?e=!0:"0"===n[3]&&(e=!1),{traceId:n[1],parentSampled:e,parentSpanId:n[2]}}(o):void 0,h=u?function(t){if(!c(t)&&!Array.isArray(t))return;let n={};if(Array.isArray(t))n=t.reduce(((t,n)=>({...t,...Kt(n)})),{});else{if(!t)return;n=Kt(t)}const e=Object.entries(n).reduce(((t,[n,e])=>(n.match(Gt)&&(t[n.slice(Wt.length)]=e),t)),{});return Object.keys(e).length>0?e:void 0}(u):void 0,f={...t,...a,metadata:{...t.metadata,dynamicSamplingContext:a&&!h?{}:h},trimEnd:!0},l="function"==typeof n?n(f):f,d=void 0===l?{...f,sampled:!1}:l;d.metadata=d.name!==f.name?{...d.metadata,source:"custom"}:d.metadata,this.Bt=d.name,this.Xt=d.metadata&&d.metadata.source;const p=this.Ut(),{location:m}=Jn,y=En(p,d,e,i,!0,{location:m},r);return y.registerBeforeFinishCallback((t=>{this.Ht(),de(t)})),y}qt(){let t;const n=()=>{const{idleTimeout:n,finalTimeout:e,heartbeatInterval:i}=this.options,r=fn();if(r&&r.op&&["navigation","pageload"].includes(r.op))return;if(t&&(t.setFinishReason("interactionInterrupted"),t.finish(),t=void 0),!this.Ut)return;if(!this.Bt)return;const s=this.Ut(),{location:o}=Jn,c={name:this.Bt,op:"ui.action.click",trimEnd:!0,metadata:{source:this.Xt||"url"}};t=En(s,c,n,e,!0,{location:o},i)};["click"].forEach((t=>{addEventListener(t,n,{once:!1,capture:!0})}))}}function be(t){const n=(e=`meta[name=${t}]`,g.document&&g.document.querySelector?g.document.querySelector(e):null);var e;return n?n.getAttribute("content"):null}function we(){$n()}const Se=m;let Te=0;function Ee(){return Te>0}function $e(){Te++,setTimeout((()=>{Te--}))}function ke(t,n={},e){if("function"!=typeof t)return t;try{const n=t.__sentry_wrapped__;if(n)return n;if(L(t))return t}catch(n){return t}const sentryWrapped=function(){const i=Array.prototype.slice.call(arguments);try{e&&"function"==typeof e&&e.apply(this,arguments);const r=i.map((t=>ke(t,n)));return t.apply(this,r)}catch(t){throw $e(),kn((e=>{e.addEventProcessor((t=>(n.mechanism&&(lt(t,void 0,void 0),dt(t,n.mechanism)),t.extra={...t.extra,arguments:i},t))),captureException(t)})),t}};try{for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(sentryWrapped[n]=t[n])}catch(t){}D(sentryWrapped,t),I(t,"__sentry_wrapped__",sentryWrapped);try{Object.getOwnPropertyDescriptor(sentryWrapped,"name").configurable&&Object.defineProperty(sentryWrapped,"name",{get:()=>t.name})}catch(t){}return sentryWrapped}function xe(t,n){const e=je(t,n),i={type:n&&n.name,value:Ce(n)};return e.length&&(i.stacktrace={frames:e}),void 0===i.type&&""===i.value&&(i.value="Unrecoverable error caught"),i}function Oe(t,n){return{exception:{values:[xe(t,n)]}}}function je(t,n){const e=n.stacktrace||n.stack||"",i=function(t){if(t){if("number"==typeof t.framesToPop)return t.framesToPop;if(Re.test(t.message))return 1}return 0}(n);try{return t(e,i)}catch(t){}return[]}const Re=/Minified React error #\d+;/i;function Ce(t){const n=t&&t.message;return n?n.error&&"string"==typeof n.error.message?n.error.message:n:"No error message"}function Ie(t,n,e,i){const r=Le(t,n,e&&e.syntheticException||void 0,i);return dt(r),r.level="error",e&&e.event_id&&(r.event_id=e.event_id),bt(r)}function De(t,n,e="info",i,r){const s=Me(t,n,i&&i.syntheticException||void 0,r);return s.level=e,i&&i.event_id&&(s.event_id=i.event_id),bt(s)}function Le(t,n,e,c,u){let f;if(s(n)&&n.error){return Oe(t,n.error)}if(o(n)||r(n,"DOMException")){const i=n;if("stack"in n)f=Oe(t,n);else{const n=i.name||(o(i)?"DOMError":"DOMException"),r=i.message?`${n}: ${i.message}`:n;f=Me(t,r,e,c),lt(f,r)}return"code"in i&&(f.tags={...f.tags,"DOMException.code":`${i.code}`}),f}if(i(n))return Oe(t,n);if(a(n)||h(n)){return f=function(t,n,e,i){const r=cn().getClient(),s=r&&r.getOptions().normalizeDepth,o={exception:{values:[{type:h(n)?n.constructor.name:i?"UnhandledRejection":"Error",value:`Non-Error ${i?"promise rejection":"exception"} captured with keys: ${F(n)}`}]},extra:{__serialized__:vt(n,s)}};if(e){const n=je(t,e);n.length&&(o.exception.values[0].stacktrace={frames:n})}return o}(t,n,e,u),dt(f,{synthetic:!0}),f}return f=Me(t,n,e,c),lt(f,`${n}`,void 0),dt(f,{synthetic:!0}),f}function Me(t,n,e,i){const r={message:n};if(i&&e){const i=je(t,e);i.length&&(r.exception={values:[{value:n,stacktrace:{frames:i}}]})}return r}const Ae=1024,Ne="Breadcrumbs";class Fe{static __initStatic(){this.id=Ne}__init(){this.name=Fe.id}constructor(t){Fe.prototype.__init.call(this),this.options={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t}}setupOnce(){this.options.console&&Z("console",He),this.options.dom&&Z("dom",function(t){function n(n){let e,i="object"==typeof t?t.serializeAttribute:void 0,r="object"==typeof t&&"number"==typeof t.maxStringLength?t.maxStringLength:void 0;r&&r>Ae&&(r=Ae),"string"==typeof i&&(i=[i]);try{const t=n.event;e=function(t){return t&&!!t.target}(t)?_(t.target,{keyAttrs:i,maxStringLength:r}):_(t,{keyAttrs:i,maxStringLength:r})}catch(t){e="<unknown>"}0!==e.length&&cn().addBreadcrumb({category:`ui.${n.name}`,message:e},{event:n.event,name:n.name,global:n.global})}return n}(this.options.dom)),this.options.xhr&&Z("xhr",Ue),this.options.fetch&&Z("fetch",Pe),this.options.history&&Z("history",qe)}addSentryBreadcrumb(t){this.options.sentry&&cn().addBreadcrumb({category:"sentry."+("transaction"===t.type?"transaction":"event"),event_id:t.event_id,level:t.level,message:ft(t)},{event:t})}}function He(t){for(let n=0;n<t.args.length;n++)if("ref=Ref<"===t.args[n]){t.args[n+1]="viewRef";break}const n={category:"console",data:{arguments:t.args,logger:"console"},level:(e=t.level,"warn"===e?"warning":$t.includes(e)?e:"log"),message:O(t.args," ")};var e;if("assert"===t.level){if(!1!==t.args[0])return;n.message=`Assertion failed: ${O(t.args.slice(1)," ")||"console.assert"}`,n.data.arguments=t.args.slice(1)}cn().addBreadcrumb(n,{input:t.args,level:t.level})}function Ue(t){const{startTimestamp:n,endTimestamp:e}=t,i=t.xhr.__sentry_xhr_v2__;if(!n||!e||!i)return;const{method:r,url:s,status_code:o,body:c}=i,u={method:r,url:s,status_code:o},a={xhr:t.xhr,input:c,startTimestamp:n,endTimestamp:e};cn().addBreadcrumb({category:"xhr",data:u,type:"http"},a)}function Pe(t){const{startTimestamp:n,endTimestamp:e}=t;if(e&&(!t.fetchData.url.match(/sentry_key/)||"POST"!==t.fetchData.method))if(t.error){const i=t.fetchData,r={data:t.error,input:t.args,startTimestamp:n,endTimestamp:e};cn().addBreadcrumb({category:"fetch",data:i,level:"error",type:"http"},r)}else{const i={...t.fetchData,status_code:t.response&&t.response.status},r={input:t.args,response:t.response,startTimestamp:n,endTimestamp:e};cn().addBreadcrumb({category:"fetch",data:i,type:"http"},r)}}function qe(t){let n=t.from,e=t.to;const i=Et(Se.location.href);let r=Et(n);const s=Et(e);r.path||(r=i),i.protocol===s.protocol&&i.host===s.host&&(e=s.relative),i.protocol===r.protocol&&i.host===r.host&&(n=r.relative),cn().addBreadcrumb({category:"navigation",data:{from:n,to:e}})}function Be(t,{metadata:n,tunnel:e,dsn:i}){const r={event_id:t.event_id,sent_at:(new Date).toISOString(),...n&&n.sdk&&{sdk:{name:n.sdk.name,version:n.sdk.version}},...!!e&&!!i&&{dsn:T(i)}},s=function(t){return[{type:"user_report"},t]}(t);return Mt(r,[s])}Fe.__initStatic();class Xe extends An{constructor(t){const n=Se.SENTRY_SDK_SOURCE||"npm";t.wt=t.wt||{},t.wt.sdk=t.wt.sdk||{name:"sentry.javascript.browser",packages:[{name:`${n}:@sentry/browser`,version:Pn}],version:Pn},super(t),t.sendClientReports&&Se.document&&Se.document.addEventListener("visibilitychange",(()=>{"hidden"===Se.document.visibilityState&&this.zt()}))}eventFromException(t,n){return Ie(this.xt.stackParser,t,n,this.xt.attachStacktrace)}eventFromMessage(t,n="info",e){return De(this.xt.stackParser,t,n,e,this.xt.attachStacktrace)}sendEvent(t,n){const e=this.getIntegrationById(Ne);e&&e.addSentryBreadcrumb&&e.addSentryBreadcrumb(t),super.sendEvent(t,n)}captureUserFeedback(t){if(!this.It())return;const n=Be(t,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.Lt(n)}At(t,n,e){return t.platform=t.platform||"javascript",super.At(t,n,e)}zt(){const t=this.Ft();if(0===t.length)return;if(!this.Ot)return;const n=(e=t,Mt((i=this.xt.tunnel&&T(this.Ot))?{dsn:i}:{},[[{type:"client_report"},{timestamp:r||Rt(),discarded_events:e}]]));var e,i,r;this.Lt(n)}}let ze;function We(t,n=function(){if(ze)return ze;if(G(Se.fetch))return ze=Se.fetch.bind(Se);const t=Se.document;let n=Se.fetch;if(t&&"function"==typeof t.createElement)try{const e=t.createElement("iframe");e.hidden=!0,t.head.appendChild(e);const i=e.contentWindow;i&&i.fetch&&(n=i.fetch),t.head.removeChild(e)}catch(t){}return ze=n.bind(Se)}()){let e=0,i=0;return Hn(t,(function(r){const s=r.body.length;e+=s,i++;const o={body:r.body,method:"POST",referrerPolicy:"origin",headers:t.headers,keepalive:e<=6e4&&i<15,...t.fetchOptions};try{return n(t.url,o).then((t=>(e-=s,i--,{statusCode:t.status,headers:{"x-sentry-rate-limits":t.headers.get("X-Sentry-Rate-Limits"),"retry-after":t.headers.get("Retry-After")}})))}catch(t){return ze=void 0,e-=s,i--,wt(t)}}))}function Ge(t){return Hn(t,(function(n){return new St(((e,i)=>{const r=new XMLHttpRequest;r.onerror=i,r.onreadystatechange=()=>{4===r.readyState&&e({statusCode:r.status,headers:{"x-sentry-rate-limits":r.getResponseHeader("X-Sentry-Rate-Limits"),"retry-after":r.getResponseHeader("Retry-After")}})},r.open("POST",t.url);for(const n in t.headers)Object.prototype.hasOwnProperty.call(t.headers,n)&&r.setRequestHeader(n,t.headers[n]);r.send(n.body)}))}))}const Je="?";function Ke(t,n,e,i){const r={filename:t,function:n,in_app:!0};return void 0!==e&&(r.lineno=e),void 0!==i&&(r.colno=i),r}const Ye=/^\s*at (?:(.*\).*?|.*?) ?\((?:address at )?)?(?:async )?((?:file|https?|blob|chrome-extension|address|native|eval|webpack|<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Ve=/\((\S*)(?::(\d+))(?::(\d+))\)/,Qe=[30,t=>{const n=Ye.exec(t);if(n){if(n[2]&&0===n[2].indexOf("eval")){const t=Ve.exec(n[2]);t&&(n[2]=t[1],n[3]=t[2],n[4]=t[3])}const[t,e]=hi(n[1]||Je,n[2]);return Ke(e,t,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],Ze=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:file|https?|blob|chrome|webpack|resource|moz-extension|safari-extension|safari-web-extension|capacitor)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,ti=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,ni=[50,t=>{const n=Ze.exec(t);if(n){if(n[3]&&n[3].indexOf(" > eval")>-1){const t=ti.exec(n[3]);t&&(n[1]=n[1]||"eval",n[3]=t[1],n[4]=t[2],n[5]="")}let t=n[3],e=n[1]||Je;return[e,t]=hi(e,t),Ke(t,e,n[4]?+n[4]:void 0,n[5]?+n[5]:void 0)}}],ei=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,ii=[40,t=>{const n=ei.exec(t);return n?Ke(n[2],n[1]||Je,+n[3],n[4]?+n[4]:void 0):void 0}],ri=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,si=[10,t=>{const n=ri.exec(t);return n?Ke(n[2],n[3]||Je,+n[1]):void 0}],oi=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\(.*\))? in (.*):\s*$/i,ci=[20,t=>{const n=oi.exec(t);return n?Ke(n[5],n[3]||n[4]||Je,+n[1],+n[2]):void 0}],ui=[Qe,ni,ii],ai=q(...ui),hi=(t,n)=>{const e=-1!==t.indexOf("safari-extension"),i=-1!==t.indexOf("safari-web-extension");return e||i?[-1!==t.indexOf("@")?t.split("@")[0]:Je,e?`safari-extension:${n}`:`safari-web-extension:${n}`]:[t,n]};class fi{static __initStatic(){this.id="GlobalHandlers"}__init(){this.name=fi.id}__init2(){this.Wt={onerror:li,onunhandledrejection:di}}constructor(t){fi.prototype.__init.call(this),fi.prototype.__init2.call(this),this.xt={onerror:!0,onunhandledrejection:!0,...t}}setupOnce(){Error.stackTraceLimit=50;const t=this.xt;for(const n in t){const e=this.Wt[n];e&&t[n]&&(e(),this.Wt[n]=void 0)}}}function li(){Z("error",(t=>{const[n,e,i]=yi();if(!n.getIntegration(fi))return;const{msg:r,url:o,line:u,column:a,error:h}=t;if(Ee()||h&&h.__sentry_own_request__)return;const f=void 0===h&&c(r)?function(t,n,e,i){const r=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;let o=s(t)?t.message:t,c="Error";const u=o.match(r);u&&(c=u[1],o=u[2]);return pi({exception:{values:[{type:c,value:o}]}},n,e,i)}(r,o,u,a):pi(Le(e,h||r,void 0,i,!1),o,u,a);f.level="error",mi(n,h,f,"onerror")}))}function di(){Z("unhandledrejection",(t=>{const[n,e,i]=yi();if(!n.getIntegration(fi))return;let r=t;try{"reason"in t?r=t.reason:"detail"in t&&"reason"in t.detail&&(r=t.detail.reason)}catch(t){}if(Ee()||r&&r.__sentry_own_request__)return!0;const s=u(r)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(r)}`}]}}:Le(e,r,void 0,i,!0);s.level="error",mi(n,r,s,"onunhandledrejection")}))}function pi(t,n,e,i){const r=t.exception=t.exception||{},s=r.values=r.values||[],o=s[0]=s[0]||{},u=o.stacktrace=o.stacktrace||{},a=u.frames=u.frames||[],h=isNaN(parseInt(i,10))?void 0:i,f=isNaN(parseInt(e,10))?void 0:e,l=c(n)&&n.length>0?n:function(){try{return g.document.location.href}catch(t){return""}}();return 0===a.length&&a.push({colno:h,filename:l,function:"?",in_app:!0,lineno:f}),t}function mi(t,n,e,i){dt(e,{handled:!1,type:i}),t.captureEvent(e,{originalException:n})}function yi(){const t=cn(),n=t.getClient(),e=n&&n.getOptions()||{stackParser:()=>[],attachStacktrace:!1};return[t,e.stackParser,e.attachStacktrace]}fi.__initStatic();const vi=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"];class gi{static __initStatic(){this.id="TryCatch"}__init(){this.name=gi.id}constructor(t){gi.prototype.__init.call(this),this.xt={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t}}setupOnce(){this.xt.setTimeout&&C(Se,"setTimeout",_i),this.xt.setInterval&&C(Se,"setInterval",_i),this.xt.requestAnimationFrame&&C(Se,"requestAnimationFrame",bi),this.xt.XMLHttpRequest&&"XMLHttpRequest"in Se&&C(XMLHttpRequest.prototype,"send",wi);const t=this.xt.eventTarget;if(t){(Array.isArray(t)?t:vi).forEach(Si)}}}function _i(t){return function(...n){const e=n[0];return n[0]=ke(e,{mechanism:{data:{function:X(t)},handled:!0,type:"instrument"}}),t.apply(this,n)}}function bi(t){return function(n){return t.apply(this,[ke(n,{mechanism:{data:{function:"requestAnimationFrame",handler:X(t)},handled:!0,type:"instrument"}})])}}function wi(t){return function(...n){const e=this;return["onload","onerror","onprogress","onreadystatechange"].forEach((t=>{t in e&&"function"==typeof e[t]&&C(e,t,(function(n){const e={mechanism:{data:{function:t,handler:X(n)},handled:!0,type:"instrument"}},i=L(n);return i&&(e.mechanism.data.handler=X(i)),ke(n,e)}))})),t.apply(this,n)}}function Si(t){const n=Se,e=n[t]&&n[t].prototype;e&&e.hasOwnProperty&&e.hasOwnProperty("addEventListener")&&(C(e,"addEventListener",(function(n){return function(e,i,r){try{"function"==typeof i.handleEvent&&(i.handleEvent=ke(i.handleEvent,{mechanism:{data:{function:"handleEvent",handler:X(i),target:t},handled:!0,type:"instrument"}}))}catch(t){}return n.apply(this,[e,ke(i,{mechanism:{data:{function:"addEventListener",handler:X(i),target:t},handled:!0,type:"instrument"}}),r])}})),C(e,"removeEventListener",(function(t){return function(n,e,i){const r=e;try{const e=r&&r.__sentry_wrapped__;e&&t.call(this,n,e,i)}catch(t){}return t.call(this,n,r,i)}})))}gi.__initStatic();class Ti{static __initStatic(){this.id="LinkedErrors"}__init(){this.name=Ti.id}constructor(t={}){Ti.prototype.__init.call(this),this.Gt=t.key||"cause",this.Jt=t.limit||5}setupOnce(){const t=cn().getClient();t&&nn(((n,e)=>{const i=cn().getIntegration(Ti);return i?function(t,n,e,i,r){if(!(i.exception&&i.exception.values&&r&&d(r.originalException,Error)))return i;const s=Ei(t,e,r.originalException,n);return i.exception.values=[...s,...i.exception.values],i}(t.getOptions().stackParser,i.Gt,i.Jt,n,e):n}))}}function Ei(t,n,e,i,r=[]){if(!d(e[i],Error)||r.length+1>=n)return r;const s=xe(t,e[i]);return Ei(t,n,e[i],i,[s,...r])}Ti.__initStatic();class $i{constructor(){$i.prototype.__init.call(this)}static __initStatic(){this.id="HttpContext"}__init(){this.name=$i.id}setupOnce(){nn((t=>{if(cn().getIntegration($i)){if(!Se.navigator&&!Se.location&&!Se.document)return t;const n=t.request&&t.request.url||Se.location&&Se.location.href,{referrer:e}=Se.document||{},{userAgent:i}=Se.navigator||{},r={...t.request&&t.request.headers,...e&&{Referer:e},...i&&{"User-Agent":i}},s={...t.request,...n&&{url:n},headers:r};return{...t,request:s}}return t}))}}$i.__initStatic();class ki{constructor(){ki.prototype.__init.call(this)}static __initStatic(){this.id="Dedupe"}__init(){this.name=ki.id}setupOnce(t,n){const e=t=>{if(t.type)return t;const e=n().getIntegration(ki);if(e){try{if(function(t,n){if(!n)return!1;if(function(t,n){const e=t.message,i=n.message;if(!e&&!i)return!1;if(e&&!i||!e&&i)return!1;if(e!==i)return!1;if(!Oi(t,n))return!1;if(!xi(t,n))return!1;return!0}(t,n))return!0;if(function(t,n){const e=ji(n),i=ji(t);if(!e||!i)return!1;if(e.type!==i.type||e.value!==i.value)return!1;if(!Oi(t,n))return!1;if(!xi(t,n))return!1;return!0}(t,n))return!0;return!1}(t,e.Kt))return null}catch(n){return e.Kt=t}return e.Kt=t}return t};e.id=this.name,t(e)}}function xi(t,n){let e=Ri(t),i=Ri(n);if(!e&&!i)return!0;if(e&&!i||!e&&i)return!1;if(e=e,i=i,i.length!==e.length)return!1;for(let t=0;t<i.length;t++){const n=i[t],r=e[t];if(n.filename!==r.filename||n.lineno!==r.lineno||n.colno!==r.colno||n.function!==r.function)return!1}return!0}function Oi(t,n){let e=t.fingerprint,i=n.fingerprint;if(!e&&!i)return!0;if(e&&!i||!e&&i)return!1;e=e,i=i;try{return!(e.join("")!==i.join(""))}catch(t){return!1}}function ji(t){return t.exception&&t.exception.values&&t.exception.values[0]}function Ri(t){const n=t.exception;if(n)try{return n.values[0].stacktrace.frames}catch(t){return}}ki.__initStatic();var Ci=Object.freeze({__proto__:null,GlobalHandlers:fi,TryCatch:gi,Breadcrumbs:Fe,LinkedErrors:Ti,HttpContext:$i,Dedupe:ki});const Ii=[new zn,new Bn,new gi,new Fe,new fi,new Ti,new ki,new $i];function Di(t){t.startSession({ignoreDuration:!0}),t.captureSession()}let Li={};Se.Sentry&&Se.Sentry.Integrations&&(Li=Se.Sentry.Integrations);const Mi={...Li,...Gn,...Ci};return Mi.Replay=n,Mi.BrowserTracing=_e,we(),t.Breadcrumbs=Fe,t.BrowserClient=Xe,t.BrowserTracing=_e,t.Dedupe=ki,t.FunctionToString=Bn,t.GlobalHandlers=fi,t.HttpContext=$i,t.Hub=rn,t.InboundFilters=zn,t.Integrations=Mi,t.LinkedErrors=Ti,t.Replay=n,t.SDK_VERSION=Pn,t.Scope=Zt,t.Span=mn,t.TryCatch=gi,t.WINDOW=Se,t.addBreadcrumb=function(t){cn().addBreadcrumb(t)},t.addExtensionMethods=we,t.addGlobalEventProcessor=nn,t.captureEvent=function(t,n){return cn().captureEvent(t,n)},t.captureException=captureException,t.captureMessage=function(t,n){const e="string"==typeof n?n:void 0,i="string"!=typeof n?{captureContext:n}:void 0;return cn().captureMessage(t,e,i)},t.captureUserFeedback=function(t){const n=cn().getClient();n&&n.captureUserFeedback(t)},t.chromeStackLineParser=Qe,t.close=function(t){const n=cn().getClient();return n?n.close(t):bt(!1)},t.configureScope=function(t){cn().configureScope(t)},t.createTransport=Hn,t.createUserFeedbackEnvelope=Be,t.defaultIntegrations=Ii,t.defaultStackLineParsers=ui,t.defaultStackParser=ai,t.eventFromException=Ie,t.eventFromMessage=De,t.flush=function(t){const n=cn().getClient();return n?n.flush(t):bt(!1)},t.forceLoad=function(){},t.geckoStackLineParser=ni,t.getCurrentHub=cn,t.getHubFromCarrier=un,t.init=function(t={}){void 0===t.defaultIntegrations&&(t.defaultIntegrations=Ii),void 0===t.release&&("string"==typeof __SENTRY_RELEASE__&&(t.release=__SENTRY_RELEASE__),Se.SENTRY_RELEASE&&Se.SENTRY_RELEASE.id&&(t.release=Se.SENTRY_RELEASE.id)),void 0===t.autoSessionTracking&&(t.autoSessionTracking=!0),void 0===t.sendClientReports&&(t.sendClientReports=!0);const n={...t,stackParser:(e=t.stackParser||ai,Array.isArray(e)?q(...e):e),integrations:In(t),transport:t.transport||(W()?We:Ge)};var e;!function(t,n){!0===n.debug&&console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.");const e=cn();e.getScope().update(n.initialScope);const i=new t(n);e.bindClient(i)}(Xe,n),t.autoSessionTracking&&function(){if(void 0===Se.document)return;const t=cn();if(!t.captureSession)return;Di(t),Z("history",(({from:t,to:n})=>{void 0!==t&&t!==n&&Di(cn())}))}()},t.lastEventId=function(){return cn().lastEventId()},t.makeFetchTransport=We,t.makeMain=on,t.makeXHRTransport=Ge,t.onLoad=function(t){t()},t.opera10StackLineParser=si,t.opera11StackLineParser=ci,t.setContext=function(t,n){cn().setContext(t,n)},t.setExtra=function(t,n){cn().setExtra(t,n)},t.setExtras=function(t){cn().setExtras(t)},t.setTag=function(t,n){cn().setTag(t,n)},t.setTags=function(t){cn().setTags(t)},t.setUser=function(t){cn().setUser(t)},t.showReportDialog=function(t={},n=cn()){if(!Se.document)return;const{client:e,scope:i}=n.getStackTop(),r=t.dsn||e&&e.getDsn();if(!r)return;i&&(t.user={...i.getUser(),...t.user}),t.eventId||(t.eventId=n.lastEventId());const s=Se.document.createElement("script");s.async=!0,s.src=function(t,n){const e=$(t),i=`${xn(e)}embed/error-page/`;let r=`dsn=${T(e)}`;for(const t in n)if("dsn"!==t)if("user"===t){const t=n.user;if(!t)continue;t.name&&(r+=`&name=${encodeURIComponent(t.name)}`),t.email&&(r+=`&email=${encodeURIComponent(t.email)}`)}else r+=`&${encodeURIComponent(t)}=${encodeURIComponent(n[t])}`;return`${i}?${r}`}(r,t),t.onLoad&&(s.onload=t.onLoad);const o=Se.document.head||Se.document.body;o&&o.appendChild(s)},t.startTransaction=function(t,n){return cn().startTransaction({...t},n)},t.winjsStackLineParser=ii,t.withScope=kn,t.wrap=function(t){return ke(t)()},t}({});
//# sourceMappingURL=bundle.tracing.min.js.map