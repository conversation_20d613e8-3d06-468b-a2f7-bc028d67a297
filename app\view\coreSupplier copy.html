<% include header.html %>
  <el-container id="Main">
    <% include eheader.html %>
      <el-container>
        <% include eside.html %>
          <el-main>
            <el-row>
              <el-col :span="24">
                <el-breadcrumb separator-class="el-icon-arrow-right">
                  <el-breadcrumb-item>
                    <a href='/'>首页</a>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    <a href='/other/list'>其他内容</a>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    编辑
                  </el-breadcrumb-item>
                </el-breadcrumb>
              </el-col>
            </el-row>
            <el-row>
              <h3 class="stit">基本属性</h3>
              <el-form :model="form" label-width="120px" ref="form">
                <el-form-item label="名称">
                  <el-input v-model:trim="page.title" disabled></el-input>
                </el-form-item>
                <el-form-item label="英文名称">
                  <el-input v-model:trim="page.alias" disabled></el-input>
                </el-form-item>
                <el-form-item label="预览地址">
                 <a href='<%- view_url %>/coreSupplier' target="_blank"> <%- view_url %>/coreSupplier</a>
                </el-form-item>
              </el-form>
              <hr>
              <h3 class="stit">内容</h3>
              <el-form :model="form" label-width="120px">
                <el-form-item label="banner图片">
                  <el-upload class="cover-uploader" :data='uploadData' name="file"
                    :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                    :show-file-list="false" :on-success="bannerSuccess" :before-upload="beforeAvatarUpload"
                    accept="image/jpeg,image/png,image/gif,image/pdf" style="width:810px;height:127px;">
                    <img v-if="form.banner_img" :src="form.banner_img" class="cover"
                      style="position: relative;z-index: 2;width:810px;height:127px;">
                    <i v-else class="el-icon-plus cover-uploader-icon"
                      style="width:810px;height:127px;line-height: 127px"></i>
                    <span
                      style="position: absolute;bottom:10px;display:block;width:100%;text-align:center;z-index:1">选择图片(JPG,PNG,GIF,
                      1920px * 300px)</span>
                  </el-upload>
                </el-form-item>
                <el-form-item label="富文本内容">
                  <textarea id='Content' v-model="form.content" cols="60" rows="4" class="el-input__inner content"
                    placeholder="富文本内容"></textarea>
                </el-form-item>
                <el-form-item label="供应商列表">
                  <el-button @click='handleAddtion' type='primary' size='small'
                    style="margin-bottom: 10px; float: right;">添加供应商</el-button>
                  <el-table :data="form.item_list" size='small' border>
                    <el-table-column prop="name" label="公司名称" width="250">
                    </el-table-column>
                    <el-table-column prop="link1" label="证书">
                      <template slot-scope="scope">
                        <a :href="scope.row.link1">{{ decodeURI(scope.row.link1.substr(scope.row.link1.lastIndexOf('/') + 1,
                          scope.row.link1.length)) }}</a>
                      </template>
                    </el-table-column>
                    <el-table-column prop="link2" label="报告">
                      <template slot-scope="scope">
                        <a :href="scope.row.link2">{{ decodeURI(scope.row.link2.substr(scope.row.link2.lastIndexOf('/') + 1,
                          scope.row.link2.length)) }}</a>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="240">
                      <template slot-scope="scope">
                        <el-button type='' size='small' icon='el-icon-top' @click='handleMove(scope.$index, "up")'
                          :disabled="!scope.$index"></el-button>
                        <el-button type='' size='small' icon='el-icon-bottom' @click='handleMove(scope.$index, "down")'
                          :disabled="scope.$index === form.item_list.length - 1"></el-button>
                        <el-button type='success' size='small' icon='el-icon-delete' @click='handleDel(scope.$index)'>
                        </el-button>
                        <el-button type='primary' size='small' icon='el-icon-edit'
                          @click='handleEdit(scope.row, scope.$index)'></el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-form>
              <hr>
              <h3 class="stit">SEO设置-PC端</h3>
              <el-row :gutter="20">
                <el-form label-width="120px">
                  <el-form-item label="Title">
                    <el-input v-model="form.page_title" auto-complete="off" maxlength="50"></el-input>
                  </el-form-item>
                  <el-form-item label="Keywords">
                    <el-input v-model="form.page_keywords" auto-complete="off" maxlength="50"></el-input>
                  </el-form-item>
                  <el-form-item label="Description">
                    <el-input v-model="form.page_description" auto-complete="off" maxlength="200"></el-input>
                  </el-form-item>
                </el-form>
              </el-row>
              <hr>
              <el-col :span="24" style="background: #39F;padding:12px 0;color:#FFF">
                <el-col :span="12" style="text-align:right;padding-right:12px;">
                  <el-button type="success" @click='onSubmit'> 保 存 </el-button>
                </el-col>
              </el-col>
            </el-row>
            <div class="logshow-wrapper" v-if="logs.list.length > 0">
              <el-row class="logshow" :class="{on: logshow}">
                <span class="icon"><i class="el-icon-arrow-up" @click="showLog"></i></span>
                <ul>
                  <li v-for="(item, index) of logs.list" :key='index'>{{item.time}}, {{item.user_name}}, {{item.log}}
                  </li>
                </ul>
                <el-pagination @current-change="logPageChange" :current-page.sync="logs.page" :page-size="logs.limit"
                  layout="total, prev, pager, next" :total="logs.total">
                </el-pagination>
              </el-row>
            </div>
            <el-dialog title="添加/编辑供应商" :visible.sync="dialog.visible" width="70%" :before-close="handleClose">
              <el-form :model="dialog" label-width="100px" :rules="rules" ref="form">
                <el-form-item label='公司名称:' prop='name'>
                  <el-input type="text" placeholder="请输入公司名称" v-model='dialog.name' maxlength='50' />
                </el-form-item>
                <el-form-item label='证书:' prop='link1'>
                  <el-upload class="cover-uploader" :data='uploadData' name="file"
                    :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                    :show-file-list="false" :on-success="link1Success" :before-upload="beforeAvatarUpload"
                    accept="image/jpeg,image/png,image/gif,application/pdf">
                    <span style="padding: 0 20px;">上传</span>
                  </el-upload>
                  <a :href="dialog.link1">{{ decodeURI(dialog.link1.substr(dialog.link1.lastIndexOf('/') + 1, dialog.link1.length))
                    }}</a>
                </el-form-item>
                <el-form-item label='报告:' prop='link2'>
                  <el-upload class="cover-uploader" :data='uploadData' name="file"
                    :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                    :show-file-list="false" :on-success="link2Success" :before-upload="beforeAvatarUpload"
                    accept="image/jpeg,image/png,image/gif,application/pdf">
                    <span style="padding: 0 20px;">上传</span>
                  </el-upload>
                  <a :href="dialog.link2">{{ decodeURI(dialog.link2.substr(dialog.link2.lastIndexOf('/') + 1, dialog.link2.length))
                    }}</a>
                </el-form-item>
              </el-form>
              <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" @click="handleSave">确 定</el-button>
              </span>
            </el-dialog>
          </el-main>
      </el-container>
  </el-container>

  <script src="/static/tinymce/tinymce.min.js"></script>
  <script>
    var ai = 0, bi = 0;
    var tinyConfig = {
      height: 600,
      language: 'zh_CN',
      menubar: false,
      plugins: 'advlist autolink link image lists charmap print preview code textcolor table paste media',
      toolbar: 'undo redo | formatselect fontsizeselect bold italic underline forecolor backcolor  | alignleft aligncenter alignright alignjustify superscript subscript | bullist numlist outdent indent | removeformat | image media link table | code preview',
      fontsize_formats: '12px 14px 16px 18px 20px 24px 36px',
      file_browser_callback_types: 'image',
      // images_upload_url: '<%- ticMallHost %>ticCenter/business/api.v0.platform/fileUpload/uploadOss',
      images_reuse_filename: true,
      // images_upload_base_path: '/',
      relative_urls: false,
      branding: false,
      width: '100%',
      images_upload_handler: function (blobInfo, success, failure) {
        let that = this
        let fd = new FormData()
        let file = blobInfo.blob();
        fd.append('file', file, file.name)
        fd.append('systemID', 5);
        let config = {
          headers: { 'Content-Type': 'multipart/form-data' }
        }
        axios.post('<%- ticMallHost %>ticCenter/business/api.v0.platform/fileUpload/uploadOss', fd, config)
          .then(function (result) {
            if (result.data.resultCode === '0') {
              success(result.data.data.fileName)
            } else {
              that.$message.success(result.data.res);
              failure()
            }
          });
      }
    };
    var main = new Vue({
      el: '#Main',
      data: {
        uploadData: { systemId: 5 },
        ticMallHost: '<%- ticMallHost %>',
        page: {
          title: '核心供应商',
          alias: 'coreSupplier',
        },
        form: {
          content: `<%- detail.content %>`,
          banner_img: '<%- detail.banner_img %>',
          item_list:  <%- JSON.stringify(detail.item_list) %>,
          page_title: '<%- detail.page_title %>',
          page_keywords: '<%- detail.page_keywords %>',
          page_description: '<%- detail.page_description %>',
        },
        loading: false,
        logs: <%- logs %>,
        logshow: false,
        dialog: {
          visible: false,
          index: -1,
          name: '',
          link1: '',
          link2: ''
        },
        rules: {
          name: [
            { required: true, message: '请输入名称' }
          ],
          link1: [
            { required: true, message: '请上传' }
          ],
          link2: [
            { required: true, message: '请上传' }
          ],
        },
      },
      methods: {
        bannerSuccess(res, file) {
          if (res.resultCode === '0') {
            this.form.banner_img = res.data.fileName;
          } else {
            this.$message.error(res.resultMsg);
          }
        },
        link1Success(res, file) {
          if (res.resultCode === '0') {
            this.dialog.link1 = res.data.fileName;
          } else {
            this.$message.error(res.resultMsg);
          }
        },
        link2Success(res, file) {
          if (res.resultCode === '0') {
            this.dialog.link2 = res.data.fileName;
          } else {
            this.$message.error(res.resultMsg);
          }
        },
        beforeAvatarUpload(file) {
          const isJPG = file.type === 'image/jpeg';
          const isPNG = file.type === 'image/png';
          const isGIF = file.type === 'image/gif';
          const isPDF = file.type === 'application/pdf';
          const isLt2M = file.size / 1024 / 1024 < 10;
          const fileNameLen = file.name.substr(0, file.name.indexOf('.')).length;
          if (fileNameLen > 75) {
            this.$message.error('文件名最多为75个字符!');
            return false;
          }

          if (!isJPG && !isPNG && !isGIF && !isPDF) {
            this.$message.error('图片格式错误!');
            return false;
          }
          if (!isLt2M) {
            this.$message.error('上传图片大小不能超过 10MB!');
            return false;
          }
          //return isJPG && isLt2M;
        },
        onSubmit() {
          var that = this;
          var loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(255, 255, 255, 0.7)'
          });
          var params = that.form;
          params.content = tinymce.get('Content').getContent();
          params._csrf = '<%- csrf %>';
          axios.post('/coreSupplier/save', params)
            .then(function (result) {
              loading.close();
              loading.close();
              if (result.data.success) {
                window.location.reload();
                that.$message({
                  message: '保存成功。',
                  type: 'success'
                });
              } else {
                that.$message.error(result.data.msg);
              }
            });
        },
        showLog() {
          if (this.logs.list.length > 1) {
            this.logshow = !this.logshow;
          }
        },
        logPageChange(page) {
          axios.post('/logslist', { page: page, model: '核心供应商', limit: 10 }).then(res => {
            let data = res.data;
            if (data.success) {
              this.logs = data.data;
            }
          })
        },
        handleAddtion() {
          this.dialog.visible = true
        },
        handleClose() {
          this.dialog.visible = false
          this.dialog.name = ''
          this.dialog.link1 = ''
          this.dialog.link2 = ''
          this.dialog.index = -1
          this.form.item_list = JSON.parse(JSON.stringify(this.form.item_list))
        },
        handleSave() {
          this.$refs['form'].validate((valid) => {
            if (valid) {
              let data = JSON.parse(JSON.stringify(this.dialog))
              if (data.index < 0) {
                delete data.visible
                this.form.item_list.push(data)
              } else {
                delete data.visible
                this.form.item_list[data.index] = data
              }
              this.handleClose()
            } else {
              return false;
            }
          });
        },
        handleMove(index, arrow) {
          if (arrow === 'up') {
            if (index === 0) {
              return;
            } else {
              this.form.item_list[index] = this.form.item_list.splice(index - 1, 1, this.form.item_list[index])[0];
            }
          } else {
            if (index === this.form.item_list.length - 1) {
              return;
            } else {
              this.form.item_list[index] = this.form.item_list.splice(index + 1, 1, this.form.item_list[index])[0];
            }
          }
        },
        handleDel(index) {
          this.$confirm('您确认删除？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.form.item_list.splice(index, 1)
          })
        },
        handleEdit(row, index) {
          this.dialog.name = row.name
          this.dialog.link1 = row.link1
          this.dialog.link2 = row.link2
          this.dialog.index = index
          this.dialog.visible = true
        }
      },
      mounted() {
        this.$nextTick(function () {
          tinyConfig.selector = '#Content';
          tinymce.init(tinyConfig);
          document.getElementById('preLoading').style.display = 'none';
        });
      }
    });
  </script>
  <% include footer.html %>