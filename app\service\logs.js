'use strict';

const Service = require('egg').Service;
const moment = require('moment');

class LogsService extends Service {
    async log(params) {
        const {
            app
        } = this;

        const row = {
            user_name: this.ctx.session.userInfo.name,
            log: params.log,
            model: params.model,
            name: params.name
        }

        if (params.type) {
            let type = params.type;
            row[type + '_id'] = params.id;
        }

        const result = await app.mysql.beginTransactionScope(async conn => {
            await conn.insert('logs', row);
            return {
                success: true
            };
        }, this.ctx);

        if (result.success) {
            return {
                msg: 'done'
            };
        }
    }

    async list(params) {
        const {
            app
        } = this;
        const list = await app.mysql.select('logs', {
            where: params,
            columns: ['id', 'user_name', 'log', 'create_time', 'model'],
            orders: [
                ['id', 'desc']
            ],
            offset: 0,
            limit: 10,
        });

        for (let item of list) {
            item.time = moment(item.create_time).format('YYYY-MM-DD HH:mm:ss');
        }
        return list;
    }

    async listAll(params) {
        const {
            app
        } = this;
        const page = params.page || 1,
            limit = params.limit || 30;

        let queryRow = [];
        if (params.model) {
            queryRow.push('model="' + params.model + '"');
        }

        if (params.name) {
            queryRow.push('name LIKE "%' + params.name + '%"');
        }

        if (params.user_name) {
            queryRow.push('user_name LIKE "%' + params.user_name + '%"');
        }

        if (params.date) {
            if (params.date[0]) {
                queryRow.push('create_time > "' + moment(params.date[0]).format('YYYY-MM-DD HH:mm:ss') + '"');
            }

            if (params.date[1]) {
                queryRow.push('create_time < "' + moment(params.date[1]).format('YYYY-MM-DD HH:mm:ss') + '"');
            }
        }

        if (params.type && params.id) {
            queryRow.push(params.type + '_id ="' + params.id + '"');
        }

        if (queryRow.length > 0) {
            queryRow = queryRow.join(' AND ');
            queryRow = ' WHERE ' + queryRow;
        }

        const count = await app.mysql.query('SELECT COUNT(*) AS len FROM logs' + queryRow);
        const list = await app.mysql.query('SELECT id,user_name,log,create_time,model,name FROM logs' + queryRow + ' ORDER BY id DESC LIMIT ' + (page - 1) * limit + ',' + limit);

        for (let item of list) {
            item.time = moment(item.create_time).format('YYYY-MM-DD HH:mm:ss');
        }
        return {
            list,
            total: count[0].len,
            page: page,
            limit: limit
        };
    }
}

module.exports = LogsService;