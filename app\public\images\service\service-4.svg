<?xml version="1.0" encoding="UTF-8"?>
<svg width="60px" height="60px" viewBox="0 0 60 60" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 52.2 (67145) - http://www.bohemiancoding.com/sketch -->
    <title>icon_qingguan</title>
    <desc>Created with Sketch.</desc>
    <g id="icon_qingguan" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle id="椭圆形-copy" stroke="#000000" stroke-width="4" fill-rule="nonzero" cx="30" cy="30" r="23"></circle>
        <circle id="椭圆形" stroke="#000000" stroke-width="4" fill-rule="nonzero" cx="30" cy="17" r="4"></circle>
        <path d="M30.5,48.4955538 L30.5,22" id="路径-20" stroke="#000000" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" fill-rule="nonzero"></path>
        <path d="M26,27.5 L35,27.5" id="路径-19" stroke="#000000" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" fill-rule="nonzero"></path>
        <path d="M18,31 C18,37.627417 23.372583,43 30,43 C36.627417,43 42,37.627417 42,31" id="路径" stroke="#FF7031" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" fill-rule="nonzero"></path>
        <polyline id="路径" stroke="#FF7031" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" fill-rule="nonzero" points="39 32 42 29 42 29 45 32"></polyline>
        <polyline id="路径-copy" stroke="#FF7031" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" fill-rule="nonzero" points="14 32 17 29 17 29 20 32"></polyline>
    </g>
</svg>