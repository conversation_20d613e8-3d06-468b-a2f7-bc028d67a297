<% include header.html %>
  <el-container id="Main">
    <% include eheader.html %>
      <el-container>
        <% include eside.html %>
          <el-main>
            <el-row>
              <el-col :span="24">
                <el-breadcrumb separator-class="el-icon-arrow-right">
                  <el-breadcrumb-item>
                    <a href='/'>首页</a>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    pc端首页
                  </el-breadcrumb-item>
                </el-breadcrumb>
              </el-col>
            </el-row>
            <el-row>
              <h2>
                首页地址：<a href="<%- view_url %>" target="_blank">
                  <%- view_url %>
                </a>
              </h2>
              <h3 class="stit">Banner设置</h3>
              <el-form label-width="120px" ref="form">
                <div v-for="(item,index) in banners" :key='index'>
                  <el-form-item label="banner图片">
                    <el-upload :data='uploadData' class="cover-uploader"
                      :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                      :show-file-list="false" :on-success="bannerSuccess" :before-upload="beforeAvatarUpload"
                      name="file" accept="image/jpeg,image/png,image/gif" style="width:735px;height:380px;">
                      <img v-if="item.img_path" :src="item.img_path" @click="_setIndex(index)" class="cover"
                        style="position: relative;z-index: 2;widows: 735px;height:380px;">
                      <i v-else class="el-icon-plus cover-uploader-icon" @click="_setIndex(index)"
                        style="width:735px;height:380px;line-height: 380px"></i>
                      <span
                        style="position: absolute;bottom:10px;display:block;width:100%;text-align:center;z-index:1">选择图片(JPG,PNG,GIF,
                        735px * 380px)</span>
                    </el-upload>
                  </el-form-item>

                  <el-form-item label="Banner标题">
                    <el-col :span="18">
                      <el-input v-model.trim="item.btn_text"></el-input>
                    </el-col>
                    <el-col :span="6" style="text-align:right">
                      <el-button type="primary" icon="el-icon-arrow-up" circle @click="_upBanner(index)"
                        :disabled="!index"></el-button>
                      <el-button type="primary" icon="el-icon-arrow-down" circle @click="_downBanner(index)"
                        :disabled="index === banners.length - 1"></el-button>
                      <el-button type="danger" icon="el-icon-delete" circle @click="_delBanner(index)">
                      </el-button>
                    </el-col>
                  </el-form-item>

                  <el-form-item label="Banner标题链接">
                    <el-input v-model.trim="item.btn_text_url"></el-input>
                  </el-form-item>

                  <el-form-item label="按钮链接">
                    <el-input v-model.trim="item.btn_url"></el-input>
                  </el-form-item>

                  <el-form-item label="按钮文字">
                    <el-input v-model.trim="item.btn_value"></el-input>
                  </el-form-item>

                  <el-form-item label="Banner文案">
                    <textarea v-model="item.btn_description" cols="60" rows="4" class="el-input__inner foolist foofaq"
                      style="height:100px" placeholder="Banner文案"></textarea>
                  </el-form-item>
                </div>
                <el-form-item label="">
                  <el-button type="success" @click='_addBanner'>新增Banner</el-button>
                </el-form-item>
              </el-form>
              <hr>
              <h3 class="stit">
                <p>在线下单</p>
              </h3>
              <el-row>
                <el-col :span="24">
                  <iframe :src="console_url + '/home/<USER>'" style="width: 100%; border: none; height: 800px;"></iframe>
                </el-col>
              </el-row>
              <hr>
              <h3 class="stit">
                <p>定制方案</p>
                <el-row>
                  <el-col :span="12"><el-input v-model="planTitle.title"  placeholder="请输入标题名称..."></el-input></el-col>
                  <el-col :span="12"><el-input v-model="planTitle.link"   placeholder="请输入标题链接..."></el-input></el-col>
                </el-row>
              </h3>
              <el-row>
                <el-col :span="24">
                  是否显示该楼层：
                  <el-switch v-model="plan.isShow" active-text="显示" inactive-text="不显示">
                  </el-switch>
                </el-col>
                <el-col :span="24">
                  楼层背景图：
                  <el-upload :data='uploadData'
                    :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                    :show-file-list="false" :on-success="handlePlanSuccess" :before-upload="beforeAvatarUpload"
                    name="file">
                    <img :src='plan.backgroundImg' v-if='plan.backgroundImg' style="width: 687px;" />
                    <el-button v-else size="small" type="primary">上传</el-button>
                  </el-upload>
                </el-col>
              </el-row>
              <hr>
              <h3 class="stit">楼层设置</h3>
              <div v-for="(item,i) in items" :key='i'>
                <el-row>
                  <el-col :span="24" style="text-align: right; padding-bottom: 10px;">
                    <el-button size="mini" type="primary" @click="_getFloor(i)">编辑楼层</el-button>
                    <el-popover placement="top" v-model="item.floatView">
                      <el-radio-group v-model="item.type">
                        <el-radio-button label="1">7</el-radio-button>
                        <el-radio-button label="2">3</el-radio-button>
                        <el-radio-button label="3">3</el-radio-button>
                        <el-radio-button label="4">新7</el-radio-button>
                      </el-radio-group>
                      <el-button slot="reference" size="mini" type="primary">布局
                      </el-button>
                    </el-popover>
                    <el-button type="primary" icon="el-icon-arrow-up" :disabled="!i" circle @click="_upLayout(i)">
                    </el-button>
                    <el-button type="primary" icon="el-icon-arrow-down" :disabled="i === items.length - 1" circle
                      @click="_downLayout(i)">
                    </el-button>
                    <el-button size="mini" type="danger" icon="el-icon-delete" @click="_delFloor(i)" circle>
                    </el-button>
                  </el-col>

                  <el-col :span="24">
                    <template v-if="item.type == 1">
                      <el-tabs type="border-card">
                        <el-tab-pane v-for="(node,n) in item.node" :label="node.title" :name="n" :key='n'>
                          <el-row>
                            <div v-for="(o, index) in node.skus" :key='index'>
                              <template v-if="index == 0">
                                <el-col :key="o"
                                  style="float:left;width:284px;height:498px;margin: 0 15px 0 0;text-align: center">
                                  <el-card :body-style="{ padding: '0px' }"
                                    style="width:284px;height:498px;position: relative;">
                                    <a v-if="!o.thumb_img" href="javacript:;" style="line-height:498px"
                                      @click="addSku(i,n,index)">添加</a>
                                    <template v-else>
                                      <a :href="o.url" target="_blank">
                                        <img :src="o.thumb_img" class="image" style="width:284px;height:498px;">
                                      </a>
                                      <el-button type="primary" icon="el-icon-edit" circle
                                        style="position:absolute;top:10px;right:10px" @click="_editSku(i,n,index)">
                                      </el-button>
                                    </template>
                                  </el-card>
                                </el-col>
                              </template>

                              <template v-else>
                                <el-col :key="o"
                                  style="float:left;width:284px;height:234px;margin: 0 15px 30px 0;overflow: visible;text-align:center;">
                                  <el-card :body-style="{ padding: '0px' }"
                                    style="width:284px;height:234px;position: relative;">
                                    <a v-if="!o.thumb_img" href="javacript:;" style="line-height:234px"
                                      @click="addSku(i,n,index)">添加</a>
                                    <template v-else>
                                      <a :href="o.url">
                                        <img :src="o.thumb_img" class="image" style="width:284px;height:142px;">
                                        <div style="padding: 14px;">
                                          <span>{{o.name}}</span>
                                          <div class="bottom clearfix">
                                            <span style="color:#999">{{ o.sub_title }}</span>
                                          </div>
                                        </div>
                                      </a>
                                      <el-button type="primary" icon="el-icon-edit" circle
                                        style="position:absolute;top:10px;right:10px" @click="_editSku(i,n,index)">
                                      </el-button>
                                    </template>

                                  </el-card>
                                </el-col>
                              </template>
                            </div>
                          </el-row>
                        </el-tab-pane>
                      </el-tabs>
                    </template>

                    <template v-else-if="item.type == 2">
                      <el-tabs type="border-card">
                        <el-tab-pane v-for="(node,n) in item.node" :label="node.title" :name="n" :key='n'>
                          <el-row>
                            <el-col v-for="(o, index) in node.skus" :key='index' :key="o"
                              style="float:left;width:390px;height:370px;margin: 0 10px 0 0;text-align: center">
                              <template v-if="index < 3">
                                <el-card :body-style="{ padding: '0px' }"
                                  style="width:390px;height:370px;position: relative;">
                                  <a v-if="!o.thumb_img" href="javacript:;" style="line-height:370px"
                                    @click="addSku(i,n,index)">添加</a>
                                  <template v-else>
                                    <a :href="o.url" target="_blank">
                                      <img :src="o.thumb_img" class="image" style="width:390px;height:192px;">
                                      <div style="padding: 14px;">
                                        <span>{{o.name}}</span>
                                        <div class="bottom clearfix">
                                          <span style="color:#999">{{ o.sub_title }}</span>
                                        </div>
                                      </div>
                                    </a>
                                    <el-button type="primary" icon="el-icon-edit" circle
                                      style="position:absolute;top:10px;right:10px" @click="_editSku(i,n,index)">
                                    </el-button>
                                  </template>
                                </el-card>
                              </template>
                            </el-col>
                          </el-row>
                        </el-tab-pane>
                      </el-tabs>
                    </template>

                    <template v-else-if="item.type == 3">
                      <el-tabs type="border-card">
                        <el-tab-pane v-for="(node,n) in item.node" :label="node.title" :name="n" :key='n'>
                          <el-row>
                            <el-col v-for="(o, index) in node.skus" :key='index' :key="o"
                              style="float:left;width:390px;height:370px;margin: 0 10px 0 0;text-align:center;color: #FFF">
                              <template v-if="index < 3">
                                <el-card :body-style="{ padding: '0px' }"
                                  style="width:390px;height:400px;position: relative;">
                                  <a v-if="!o.thumb_img" href="javacript:;" style="line-height:400px"
                                    @click="addSku(i,n,index)">添加</a>
                                  <template v-else>
                                    <img :src="o.thumb_img" class="image"
                                      style="width:390px;height:400px;position: absolute;top:0;left:0">
                                    <img src="/static/preview/images/opacity.png"
                                      style="width:390px;height:400px;position: absolute;top:0;left:0">
                                    <div style="padding: 35px 25px;position: relative;color:#FFF;text-align: left">
                                      <span style="font-size:23px;line-height: 2;">{{o.name}}</span>
                                      <div class="bottom clearfix">
                                        <p style="padding-bottom:40px">{{ o.sub_title }}</p>
                                        <a class="el-button el-button--warning" :href="o.url" target="_blank">了解更多</a>
                                      </div>
                                    </div>
                                    <el-button type="primary" icon="el-icon-edit" circle
                                      style="position:absolute;top:10px;right:10px" @click="_editSku(i,n,index)">
                                    </el-button>
                                  </template>
                                </el-card>
                              </template>
                            </el-col>
                          </el-row>
                        </el-tab-pane>
                      </el-tabs>
                    </template>

                    <template v-else-if="item.type == 4">
                      <el-row>
                        <el-col :span='2'>
                          <span style="line-height: 40px">楼层名称：</span>
                        </el-col>
                        <el-col :span='10'>
                          <el-input v-model='item.title' style="width: 400px;" placeholder="请输入楼层名称">
                          </el-input>
                        </el-col>
                        <el-col :span='10'>
                          <el-input v-model='item.link' style="width: 400px;" placeholder="请输入楼层链接">
                          </el-input>
                        </el-col>
                      </el-row>
                      <el-tabs type="border-card">
                        <el-tab-pane v-for="(node,n) in item.node" :label="node.title" :key='n'>
                          <el-row>
                            <div v-for="(o, index) in node.skus" :key='index'>
                              <template v-if="index == 0">
                                <el-col
                                  style="float:left;width:284px;height:498px;margin: 0 15px 0 0;text-align: center">
                                  <el-card :body-style="{ padding: '0px' }"
                                    style="width:284px;height:498px;position: relative;">
                                    <a v-if="!o.thumb_img" href="javacript:;" style="line-height:498px"
                                      @click="addSku(i,n,index)">添加</a>
                                    <template v-else>
                                      <a :href="o.url" target="_blank">
                                        <img :src="o.thumb_img" class="image" style="width:284px;height:498px;">
                                      </a>
                                      <el-button type="primary" icon="el-icon-edit" circle
                                        style="position:absolute;top:10px;right:10px" @click="_editSku(i,n,index)">
                                      </el-button>
                                    </template>
                                  </el-card>
                                </el-col>
                              </template>

                              <template v-else>
                                <el-col
                                  style="float:left;width:284px;height:234px;margin: 0 15px 30px 0;overflow: visible;text-align:center;">
                                  <el-card style="width:284px;height:234px;position: relative;">
                                    <a v-if="!o.thumb_img" href="javacript:;" style="line-height:234px"
                                      @click="addSku(i,n,index)">添加</a>
                                    <template v-else>
                                      <a :href="o.url">
                                        <div style="padding: 14px 0; text-align: left;">
                                          <span
                                            style="display: inline-block;height: 20px; max-width: 190px; overflow: hidden;">{{o.name}}</span>
                                          <i v-if='o.is_buy'
                                            style="display: inline-block; width: 47px; height: 19px; background: url('/static/images/is_buy.png');"></i>
                                          <div class="bottom clearfix">
                                            <span style="color:#999; display: block;height: 20px;overflow: hidden;">{{
                                              o.sub_title }}</span>
                                          </div>
                                        </div>
                                        <img :src="o.thumb_img" class="image" style="width:250px;height:90px;">
                                      </a>
                                      <el-button type="primary" icon="el-icon-edit" circle
                                        style="position:absolute;top:10px;right:10px" @click="_editSku(i,n,index)">
                                      </el-button>
                                    </template>
                                  </el-card>
                                </el-col>
                              </template>
                            </div>
                          </el-row>
                        </el-tab-pane>
                      </el-tabs>
                    </template>
                  </el-col>

                  <el-dialog title="楼层设置" :visible.sync="item.floorDialog">
                    <el-table :data="item.node">
                      <el-table-column property="title" label="名称" width="240">
                        <template slot-scope="scope">
                          <el-input v-model="scope.row.title"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column property="link" label="更多链接">
                        <template slot-scope="scope">
                          <el-input v-model="scope.row.link"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column property="title" label="排序" width="120">
                        <template slot-scope="scope">
                          <el-button type="" size="mini" icon="el-icon-upload2" style="padding:6px"
                            @click="_sortUp(i,scope.$index)"></el-button>
                          <el-button type="" size="mini" icon="el-icon-download" style="padding:6px"
                            @click="_sortDown(i,scope.$index)"></el-button>
                        </template>
                      </el-table-column>
                      <el-table-column property="title" label="操作" width="80">
                        <template slot-scope="scope">
                          <el-button size="mini" type="danger" @click="_delTab(i,scope.$index)">删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-button type="primary" size="small" @click="_addTab(i)" style="margin-top:10px">添加Tab
                    </el-button>
                    <div slot="footer" class="dialog-footer">
                      <el-button type="primary" @click="_closeFloorDialog(i)">确 定</el-button>
                    </div>
                  </el-dialog>

                  <el-dialog title="楼层设置" :visible.sync="shopFloorDialog">
                    <el-table :data="shops">
                      <el-table-column property="title" label="名称" width="240">
                        <template slot-scope="scope">
                          <el-input v-model="scope.row.title"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column property="more" label="更多链接">
                        <template slot-scope="scope">
                          <el-input v-model="scope.row.more"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column property="title" label="排序" width="120">
                        <template slot-scope="scope">
                          <el-button type="" size="mini" icon="el-icon-upload2" style="padding:6px"
                            @click="_sortUpShop(scope.$index)"></el-button>
                          <el-button type="" size="mini" icon="el-icon-download" style="padding:6px"
                            @click="_sortDownShop(scope.$index)"></el-button>
                        </template>
                      </el-table-column>
                      <el-table-column property="title" label="操作" width="80">
                        <template slot-scope="scope">
                          <el-button size="mini" type="danger" @click="_delTabShop(scope.$index)">删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-button type="primary" size="small" @click="_addTabShop(i)" style="margin-top:10px">添加Tab
                    </el-button>
                    <div slot="footer" class="dialog-footer">
                      <el-button type="primary" @click="shopFloorDialog = false">确 定</el-button>
                    </div>
                  </el-dialog>
                </el-row>
              </div>
              <el-button type="success" @click="addItem">添加楼层</el-button>
              <hr>
              <div v-for='(custom, index) in customItems' :key='index'>
                <el-row :gutter="20">
                  <el-col :span='12'>
                    <h3 class="stit">
                      自定义楼层{{index+1}}
                    </h3>
                  </el-col>
                  <el-col :span='12' style="text-align: right;">
                    <h2>
                      <el-button type="primary" :disabled="!index" icon="el-icon-arrow-up" circle
                        @click="_upCustomItem(index)">
                      </el-button>
                      <el-button type="primary" :disabled="index === customItems.length - 1" icon="el-icon-arrow-down"
                        circle @click="_downCustomItem(index)">
                      </el-button>
                    </h2>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span='24'>
                    楼层名称：<el-input v-model='custom.titleName' style='width: 80%;' placeholder="请输入楼层名称"
                      auto-complete="off">
                    </el-input>
                  </el-col>
                  <el-col :span='24' style="margin-top: 10px;">
                    更多链接：<el-input v-model='custom.titleUrl' style='width: 80%;' placeholder="请输入更多链接"
                      auto-complete="off"></el-input>
                  </el-col>
                </el-row>
                <template v-if='custom.type === 1 || custom.type === 3'>
                  <!-- 自定义1 -->
                  <el-row :gutter="20">
                    <el-col :span="8" class="item" v-for='(item, itemIndex) in custom.items' :key='itemIndex'>
                      <p>{{item.position}}</p>
                      <el-button type="primary" icon="el-icon-edit" circle
                        @click="_editCustomItem(index, itemIndex, item.position)" class="edit">
                      </el-button>
                      <div class="img" :style="{backgroundImage:'url(' + item.bg + ')'}">
                        <span>{{ item.title }}</span>
                      </div>
                      <div :class="{list:true, list3: custom.type === 3}">
                        <ul>
                          <li v-for="(list, index) in item.list" :key='index'>
                            <a :href='list.url'>{{list.name}}</a>
                          </li>
                          <li><a :href='item.more' class="more">更多></a></li>
                        </ul>
                      </div>
                    </el-col>
                  </el-row>
                </template>
                <template v-else>
                  <!-- 自定义2 -->
                  <el-row :gutter="20">
                    <el-col :span="8" v-for='(item, itemIndex) in custom.items' :key='itemIndex' class="item2">
                      <h3>{{item.position}}</h3>
                      <div :style="{ backgroundImage: 'url('+ item.bg +')' }">
                        <h2>
                          <a :href='item.more'>{{item.title}}</a>
                          <el-button type="primary" icon="el-icon-edit" circle
                            @click="_editCustomItem2(index, itemIndex, item.position)" class="edit">
                          </el-button>
                        </h2>
                        <p>
                          {{item.des}}
                        </p>
                        <ul>
                          <li v-for='(list, listIndex) in item.list' :key='listIndex'>
                            <a :href='list.url'>{{list.name}}</a>
                          </li>
                        </ul>
                      </div>
                    </el-col>
                  </el-row>
                </template>
              </div>
              <hr>
              <h3 class="stit">新闻/案例设置</h3>
              <el-row :gutter="20">
                <el-col :span="6" v-for="(item,i) in newsItems" :key='i' style="text-align:left;position: relative;">
                  <p style="font-size: 24px; line-height: 30px; height: 60px; overflow: hidden;" v-html='item.title'>
                  </p>
                  <p>{{item.time}}</p>
                  <p style="color: #999;font-size:14px;padding-bottom: 20px; line-height: 20px; height: 60px; overflow: hidden;"
                    v-html='item.content'></p>
                  <a class="el-button el-button--warning" :href="item.url" target="_blank">了解更多</a>
                  <el-button type="primary" icon="el-icon-edit" circle style="position:absolute;top:10px;right:40px"
                    @click="_editNews(i)"></el-button>
                </el-col>
              </el-row>
              <hr>
              <h3 class="stit">SEO设置</h3>
              <el-row :gutter="20">
                <el-form label-width="120px">
                  <el-form-item label="Title">
                    <el-input v-model="page_title" auto-complete="off" maxlength="50"></el-input>
                  </el-form-item>

                  <el-form-item label="Keywords">
                    <el-input v-model="page_keywords" auto-complete="off" maxlength="50"></el-input>
                  </el-form-item>

                  <el-form-item label="Description">
                    <el-input v-model="page_description" auto-complete="off" maxlength="200"></el-input>
                  </el-form-item>
                </el-form>
              </el-row>
            </el-row>
            <hr>
            <el-row>
              <el-col :span="24" style="background: #39F;padding:12px 0;color:#FFF">
                <el-col :span="12" style="padding-left:12px;">
                  <el-button v-if="hasEditPurview==1" @click="_cancel">放弃修改</el-button>
                </el-col>
                <el-col :span="12" style="text-align:right;padding-right:12px;">
                  <el-button v-if="hasEditPurview==1" type="" @click="preview"> 预 览 </el-button>
                  <el-button v-if="hasEditPurview==1" type="success" @click='onSubmit'>保存修改</el-button>
                  <el-button v-if="hasEditPurview==1" @click="_updateSku" type="warning">更新SKU信息</el-button>
                </el-col>
              </el-col>
            </el-row>

            <div class="logshow-wrapper" v-if="logs.list.length > 0">
              <el-row class="logshow" :class="{on: logshow}">
                <span class="icon"><i class="el-icon-arrow-up" @click="showLog"></i></span>
                <ul>
                  <li v-for="(item, index) of logs.list" :key='index'>{{item.time}}, {{item.user_name}}, {{item.log}}
                  </li>
                </ul>
                <el-pagination @current-change="logPageChange" :current-page.sync="logs.page" :page-size="logs.limit"
                  layout="total, prev, pager, next" :total="logs.total">
                </el-pagination>
              </el-row>
            </div>
          </el-main>
      </el-container>

      <el-dialog title="添加/编辑SKU" :visible.sync="skuDialog" width="640px">
        <el-form :model="skuEdit" label-width="90px">
          <el-form-item label="URL">
            <el-input v-model="skuEdit.skuUrl" auto-complete="off" style="width:380px"></el-input>
            <el-button @click.prevent="_getSku">查找</el-button>
          </el-form-item>

          <template v-if="skuEdit.sku_id">
            <el-form-item label="图片" prop="name">
              <img :src="skuEdit.thumb_img" style="width:284px">
              <el-upload :data='uploadData'
                :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                :show-file-list="false" :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload"
                name="file">
                <el-button size="small" type="primary">更换图片</el-button>
              </el-upload>
            </el-form-item>
            <el-form-item label="alt标签">
              <el-input v-model="skuEdit.alt" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item label="标题">
              <el-input v-model="skuEdit.title" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item label="副标题">
              <el-input v-model="skuEdit.sub_title" auto-complete="off"></el-input>
            </el-form-item>
          </template>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="_addSku()">确 定</el-button>
        </div>
      </el-dialog>

      <el-dialog title="编辑解决方案" :visible.sync="jfDialog" width="640px">
        <el-form :model="jfEdit" label-width="90px">
          <el-form-item label="URL">
            <el-input v-model="jfEdit.url" auto-complete="off" style="width:380px"></el-input>
            <el-button @click.prevent="_getJf">查找</el-button>
          </el-form-item>

          <template v-if="jfEdit.jfId">
            <el-form-item label="标题">
              <el-input v-model="jfEdit.title" auto-complete="off"></el-input>
            </el-form-item>

            <el-form-item label="副标题">
              <el-input v-model="jfEdit.sub_title" auto-complete="off"></el-input>
            </el-form-item>
          </template>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="_addJf()">确 定</el-button>
        </div>
      </el-dialog>

      <el-dialog title="编辑新闻/案例" :visible.sync="newsDialog" width="640px">
        <el-form :model="newsEdit" label-width="90px">
          <el-form-item label="URL">
            <el-input v-model="newsEdit.url" auto-complete="off" style="width:380px"></el-input>
            <el-button @click.prevent="_getNews">查找</el-button>
          </el-form-item>

          <template v-if="newsEdit.news_id">
            <el-form-item label="标题">
              <el-input v-model="newsEdit.title" auto-complete="off"></el-input>
            </el-form-item>

            <el-form-item label="内容">
              <el-input v-model="newsEdit.content" auto-complete="off" type="textarea"></el-input>
            </el-form-item>
          </template>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="_addNews">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 自定义楼层1编辑 -->
      <el-dialog :title="itemsDetailEdit.position" :visible.sync="customItemsDetailDialog" width="640px">
        <el-form :model="itemsDetailEdit" label-width="90px">
          <el-form-item label="服务图片">
            <img :src="itemsDetailEdit.bg" style="width:395px; height: 200px;">
            <div>(建议尺寸:宽度395px、高度200px)</div>
            <el-upload :data='uploadData'
              :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"' :show-file-list="false"
              :on-success="customItemSuccess" :before-upload="beforeAvatarUpload" name="file">
              <el-button size="small" type="primary">更换图片</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="alt标签">
            <el-input v-model="itemsDetailEdit.alt"></el-input>
          </el-form-item>
          <el-form-item label="服务名称">
            <el-input v-model="itemsDetailEdit.title"></el-input>
          </el-form-item>
          <el-form-item label="服务列表">
            <el-table :data="itemsDetailEdit.list" style="width: 100%">
              <el-table-column type="index" width="50" label='序号'>
              </el-table-column>
              <el-table-column prop="name" label="名称" width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.name"></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="url" label="链接">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.url"></el-input>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item label="更多链接">
            <el-input v-model="itemsDetailEdit.more"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="_saveCustomDetail">确 定</el-button>
        </div>
      </el-dialog>
      <!-- 自定义楼层2编辑 -->
      <el-dialog :title="itemsDetailEdit2.position" :visible.sync="customItems2DetailDialog" width="640px">
        <el-form :model="itemsDetailEdit2" label-width="90px">
          <el-form-item label="服务图片">
            <img :src="itemsDetailEdit2.bg" style="width:395px; height: 400px;">
            <div>(建议尺寸:宽度395px、高度400px)</div>
            <el-upload :data='uploadData'
              :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"' :show-file-list="false"
              :on-success="customItem2Success" :before-upload="beforeAvatarUpload" name="file">
              <el-button size="small" type="primary">更换图片</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="alt标签">
            <el-input v-model="itemsDetailEdit2.alt"></el-input>
          </el-form-item>
          <el-form-item label="服务名称">
            <el-input v-model="itemsDetailEdit2.title"></el-input>
          </el-form-item>
          <el-form-item label="服务链接">
            <el-input v-model="itemsDetailEdit2.more"></el-input>
          </el-form-item>
          <el-form-item label="服务描述">
            <el-input v-model="itemsDetailEdit2.des"></el-input>
          </el-form-item>
          <el-form-item label="服务列表">
            <el-table :data="itemsDetailEdit2.list" style="width: 100%">
              <el-table-column type="index" width="50" label='序号'>
              </el-table-column>
              <el-table-column prop="name" label="名称" width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.name"></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="url" label="链接">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.url"></el-input>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="_saveCustom2Detail">确 定</el-button>
        </div>
      </el-dialog>
      <!-- 添加商品弹窗 -->
      <el-dialog :title="shopEidt.position" :visible.sync="shopEidtDialog" width="640px">
        <el-form :model="shopEidt" label-width="90px">
          <el-form-item label="标题">
            <el-input v-model="shopEidt.title" placeholder="请输入标题..."></el-input>
          </el-form-item>
          <el-form-item label="描述">
            <el-input v-model="shopEidt.describe" placeholder="请输入描述..." type="textarea"></el-input>
          </el-form-item>
          <el-form-item label="现价">
            <el-input-number v-model="shopEidt.currPrice" :min="0" :max="99999999" label="请输入现价..."></el-input-number>
          </el-form-item>
          <el-form-item label="产品地址">
            <el-input v-model="shopEidt.productLink" placeholder="请输入产品地址..."></el-input>
          </el-form-item>
          <el-form-item label="产品主图">
            <el-upload :data='uploadData'
              :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"' :show-file-list="false"
              :on-success="handleShopSuccess" :before-upload="beforeAvatarUpload" name="file">
              <img :src='shopEidt.productImg' v-if='shopEidt.productImg' style="max-width: 500px;" />
              <el-button v-else size="small" type="primary">上传</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="alt标签">
            <el-input v-model="shopEidt.alt" placeholder="请输入alt标签..."></el-input>
          </el-form-item>
          <el-form-item label="是否热门">
            <el-switch v-model="shopEidt.isHot" active-text="是" inactive-text="否" active-value="1" inactive-value="0"></el-switch>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="saveShop">确 定</el-button>
        </div>
      </el-dialog>
  </el-container>
  <script>
    var main = new Vue({
      el: '#Main',
      data: {
        portal_url: '<%- portal_url %>',
        console_url: '<%- console_url %>',
        uploadData: { systemId: 5 },
        ticMallHost: '<%- ticMallHost %>',
        customItems: <%- JSON.stringify(detail.customItems) || '[]' %>,
        itemsDetailEdit: {},
        itemsDetailEdit2: {},
        customItemsDetailDialog: false,
        customItems2DetailDialog: false,
        page_title: '<%- detail.page_title %>',
        page_keywords: '<%- detail.page_keywords %>',
        page_description: '<%- detail.page_description %>',
        items: <%- JSON.stringify(detail.items) %>,
        skuEdit: {
          skuUrl: '',
          title: '',
          sub_title: '',
          thumb_img: '',
          alt: '',
          is_buy: '',
          sku_id: 0,
        },
        jfItems:<%- JSON.stringify(detail.jfItems) %>,
        jfEdit: {
          url: '',
          title: '',
          sub_title: '',
          case_id: 0,
        },
        newsItems:<%- JSON.stringify(detail.newsItems) %>,
        newsEdit: {
          url: '',
          content: '',
          sub_title: '',
          news_id: 0,
          category: 'news'
        },
        banners: <%- JSON.stringify(detail.banners) || '[]' %>,
        jfDialog: false,
        skuDialog: false,
        newsDialog: false,
        skuIndex: [],
        jfIndex: 0,
        newsIndex: 0,
        logs: <%- logs %>,
        logshow: false,
        hasEditPurview: 1,
        shops: <%- JSON.stringify(detail.shops) || '[]' %>,
        shopsTitle: <%- JSON.stringify(detail.shopsTitle) || '{}' %>,
        shopActiveName: 'shop0',
        shopEidt: {
          basicPrice: 0,
          currPrice: 0,
          label: true,
          tags: []
        },
        shopEidtDialog: false,
        inputVisible: false,
        shopTabIndex: 0, // tab索引
        shopItemIndex: 0, // item索引
        inputValue: '',
        shopFloorDialog: false,
        plan: <%- JSON.stringify(detail.plan) || '{}' %>,
        planTitle: <%- JSON.stringify(detail.planTitle) || '{}' %>,
      },
      methods: {
        addSku(i, n, index) {
          this.skuEdit.skuUrl = '';
          this.skuEdit.title = '';
          this.skuEdit.sub_title = '';
          this.skuEdit.thumb_img = '';
          this.skuEdit.alt = '';
          this.skuEdit.sku_id = 0;
          this.skuDialog = true;
          this.skuIndex = [i, n, index];
        },
        _getSku() {
          if (this.skuEdit.skuUrl == '') {
            return;
          }

          var that = this;
          var url = that.skuEdit.skuUrl;
          axios.post('/homepage/getSku', { skuUrl: url, _csrf: '<%- csrf %>' })
            .then(function (result) {
              if (result.data.success) {
                that.skuEdit.title = result.data.data.name;
                that.skuEdit.sub_title = result.data.data.sub_title;
                that.skuEdit.thumb_img = result.data.data.thumb_img;
                // 获取文件名
                const formartFileName = result.data.data.thumb_img.split('.');
                let fileName = '';
                // 带有域名的图片地址
                if (formartFileName.length > 2) {
                  fileName = formartFileName[3].split('/');
                } else {
                  fileName = formartFileName[0].split('/');
                }
                that.skuEdit.alt = fileName[fileName.length - 1];
                that.skuEdit.sku_id = result.data.data.id;
                that.skuEdit.is_buy = result.data.data.is_buy;
              } else {
                that.$message.error(result.data.msg);
              }
            }).catch(e => {
              return e;
            });
        },
        _getJf() {
          if (this.jfEdit.url == '') {
            return;
          }

          var that = this;
          var url = that.jfEdit.url;
          axios.post('/homepage/getJf', { url: url, _csrf: '<%- csrf %>' })
            .then(function (result) {
              if (result.data.success) {
                // window.location.reload();
                that.jfEdit.title = result.data.data.name;
                that.jfEdit.sub_title = result.data.data.sub_title;
                that.jfEdit.jfId = result.data.data.id;
              } else {
                that.$message.error(result.data.msg);
              }
            }).catch(e => {
              return e;
            });
        },
        _getNews() {
          if (this.newsEdit.url == '') {
            return;
          }

          var that = this;
          var url = that.newsEdit.url;
          if (url.includes('news')) {
            axios.post('/homepage/getNews', { url: url, _csrf: '<%- csrf %>' })
              .then(function (result) {
                if (result.data.success) {
                  that.newsEdit.title = result.data.data.title;
                  that.newsEdit.content = result.data.data.content;
                  that.newsEdit.news_id = result.data.data.id;
                  that.newsEdit.time = result.data.data.time;
                  that.newsEdit.category = 'news'
                } else {
                  that.$message.error(result.data.msg);
                }
              }).catch(e => {
                return e;
              });
          } else if (url.includes('case')) {
            const id = url.substring(url.indexOf('-') + 1, url.lastIndexOf('.'))
            axios.post('/case/dtl', { id })
              .then(function (result) {
                if (result.data.success) {
                  that.newsEdit.title = result.data.detail.title;
                  that.newsEdit.content = result.data.detail.content;
                  that.newsEdit.news_id = id;
                  that.newsEdit.time = result.data.detail.publishTime;
                  that.newsEdit.category = 'case'
                } else {
                  that.$message.error(result.data.msg);
                }
              });
          } else {
            that.$message.error('请输入正确的url');
          }
        },
        _editSku(i, n, index) {
          this.skuIndex = [i, n, index];
          var $_obj = this.items[i].node[n].skus[index];
          this.skuEdit.skuUrl = $_obj.url;
          this.skuEdit.title = $_obj.name;
          this.skuEdit.sub_title = $_obj.sub_title;
          this.skuEdit.thumb_img = $_obj.thumb_img;
          this.skuEdit.alt = $_obj.alt;
          this.skuEdit.sku_id = $_obj.id;
          this.skuEdit.is_buy = $_obj.is_buy ? true : false;
          this.skuDialog = true;
        },
        _addSku() {
          var arr = this.skuIndex;
          var $_obj = this.items[arr[0]].node[arr[1]].skus[arr[2]];
          $_obj.name = this.skuEdit.title;
          $_obj.url = this.skuEdit.skuUrl;
          $_obj.sub_title = this.skuEdit.sub_title;
          $_obj.thumb_img = this.skuEdit.thumb_img;
          $_obj.alt = this.skuEdit.alt;
          $_obj.id = this.skuEdit.sku_id;
          $_obj.is_buy ? this.skuEdit.is_buy = 1 : this.skuEdit.is_buy = 0;
          this.skuDialog = false;
        },
        _editJf(i) {
          this.jfIndex = i;

          var $_obj = this.jfItems[i];

          this.jfEdit.url = $_obj.url;
          this.jfEdit.title = $_obj.title;
          this.jfEdit.sub_title = $_obj.sub_title;
          this.jfEdit.jfId = $_obj.id;
          this.jfDialog = true;

        },
        _addJf() {
          var $_obj = this.jfItems[this.jfIndex];

          $_obj.title = this.jfEdit.title;
          $_obj.url = this.jfEdit.url;
          $_obj.sub_title = this.jfEdit.sub_title;
          $_obj.id = this.jfEdit.jfId;

          this.jfDialog = false;
        },
        _editNews(i) {
          this.newsIndex = i;
          var $_obj = this.newsItems[i];
          this.newsEdit.url = $_obj.url;
          this.newsEdit.title = $_obj.title;
          this.newsEdit.content = $_obj.content;
          this.newsEdit.news_id = $_obj.id;
          this.newsEdit.time = $_obj.time;
          this.newsEdit.category = $_obj.category;
          this.newsDialog = true;
        },
        _addNews() {
          var $_obj = this.newsItems[this.newsIndex];
          $_obj.title = this.newsEdit.title;
          $_obj.url = this.newsEdit.url;
          $_obj.content = this.newsEdit.content;
          $_obj.id = this.newsEdit.news_id;
          $_obj.time = this.newsEdit.time;
          $_obj.category = this.newsEdit.category;
          this.newsDialog = false;
        },
        _getFloor(i) {
          this.items[i].floorDialog = true;
        },
        _delFloor(i) {
          this.$confirm('是否删除此楼层?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.items.splice(i, 1);
          }).catch(e => {
            return e;
          });
        },
        _delTab(i, n) {
          this.$confirm('是否删除此Tab?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.items[i].node.splice(n, 1);
          }).catch(e => {
            return e;
          });
        },
        _delTabShop(i) {
          this.$confirm('是否删除此Tab?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.shops.splice(i, 1);
          }).catch(e => {
            return e;
          });
        },
        _addTab(i) {
          var type = this.items[i].type;
          var skus = [];
          // if(type == 1){
          //     for(var z=0;z<7;z++){
          //         skus.push({});
          //     }
          // }else{
          //     for(var z=0;z<3;z++){
          //         skus.push({});
          //     }
          // }
          for (var z = 0; z < 7; z++) {
            skus.push({});
          }
          var newTab = {
            title: '新增',
            link: '',
            skus: skus,
          }

          this.items[i].node.push(newTab);
        },
        _addTabShop(i) {
          this.shops.push({
            title: 'Tab',
            more: '',
            list: [
              {}, {}, {}, {}, {}, {}, {}
            ]
          });
        },
        _closeFloorDialog(i) {
          this.items[i].floorDialog = false;
        },
        addItem() {
          var newFloor = {
            type: 4,
            node: [
              {
                title: '新增',
                link: '',
                skus: [
                  {},
                  {},
                  {},
                  {},
                  {},
                  {},
                  {},
                ],
              },
            ],
            floorDialog: false,
          }

          this.items.push(newFloor);
        },
        beforeAvatarUpload(file) {
          // 上传文件赋值图片明到alt属性
          this.itemsDetailEdit2.alt = this.itemsDetailEdit.alt = this.skuEdit.alt = this.shopEidt.alt = file.name.split('.')[0];
          const isJPG = file.type === 'image/jpeg';
          const isPNG = file.type === 'image/png';
          const isGIF = file.type === 'image/gif';
          const isLt2M = file.size / 1024 / 1024 < 10;

          if (!isJPG && !isPNG && !isGIF) {
            this.$message.error('图片格式错误!');
            return false;
          }
          if (!isLt2M) {
            this.$message.error('上传图片大小不能超过 10MB!');
            return false;
          }
        },
        handleAvatarSuccess(res, file) {
          var img = res.cover;
          var arr = this.skuIndex;
          var $_obj = this.items[arr[0]].node[arr[1]].skus[arr[2]];
          if (res.resultCode === '0') {
            this.skuEdit.thumb_img = res.data.fileName;
            $_obj.thumb_img = res.data.fileName;
          } else {
            this.$message.error(res.resultMsg);
          }
        },
        _sortDown(i, n) {
          var node = this.items[i].node;
          if (n == node.length - 1) {
            return;
          }

          var tempOption = node[n + 1];
          Vue.set(node, n + 1, node[n]);
          Vue.set(node, n, tempOption);
        },
        _sortDownShop(index) {
          if (index !== this.shops.length - 1) {
            this.shops[index] = this.shops.splice(index + 1, 1, this.shops[index])[0];
          }
        },
        _sortUp(i, n) {
          var node = this.items[i].node
          if (n == 0) {
            return;
          }
          var tempOption = node[n - 1];
          Vue.set(node, n - 1, node[n]);
          Vue.set(node, n, tempOption);
        },
        _sortUpShop(index) {
          if (index) {
            this.shops[index] = this.shops.splice(index - 1, 1, this.shops[index])[0];
          }
        },
        _cancel() {
          window.location.reload();
        },
        preview() {
          var that = this;
          var params = {
            items: that.items,
            customItems: that.customItems,
            jfItems: that.jfItems,
            newsItems: that.newsItems,
            _csrf: '<%- csrf %>',
          }

          axios.post('/preview/prehome', params).
            then(function (result) {
              if (result.data.success) {
                setTimeout(function () {
                  window.open('/preview/prehome/' + result.data.data);
                }, 500);
              }
            }).catch(e => {
              return e;
            });
        },
        onSubmit() {
          var that = this;
          var params = {
            page_title: that.page_title,
            page_keywords: that.page_keywords,
            page_description: that.page_description,
            items: that.items,
            customItems: that.customItems,
            jfItems: that.jfItems,
            newsItems: that.newsItems,
            banners: that.banners,
            shops: that.shops,
            plan: that.plan,
            _csrf: '<%- csrf %>',
            shopsTitle: that.shopsTitle,
            planTitle: that.planTitle
          }

          axios.post('/homepage/save', params).
            then(function (result) {
              if (result.data.success) {
                window.location.reload();
                that.$message({
                  message: '保存成功',
                  type: 'success'
                });
              }
            }).catch(e => {
              alert(0)
            });
        },
        _updateSku() {
          var that = this;
          axios.post('/homepage/save', { type: 'updatesku', _csrf: '<%- csrf %>', }).
            then(function (result) {
              if (result.data.success) {
                that.$message({
                  message: '更新成功',
                  type: 'success'
                });
              }
            });
        },
        showLog() {
          if (this.logs.list.length > 1) {
            this.logshow = !this.logshow;
          }
        },
        logPageChange(page) {
          axios.post('/logslist', { page: page, model: 'pc端首页配置', limit: 10 }).then(res => {
            let data = res.data;
            if (data.success) {
              this.logs = data.data;
            }

          })
        },
        _addBanner() {
          this.banners.push({ img_path: '', btn_text: '', btn_url: 'https://', btn_description: '' });
        },
        bannerSuccess(res) {
          if (res.resultCode === '0') {
            this.banners[bIndex].img_path = res.data.fileName;
          } else {
            this.$message.error(res.resultMsg);
          }
        },
        customItemSuccess(res) {
          if (res.resultCode === '0') {
            this.itemsDetailEdit.bg = res.data.fileName;
            this.customItems[this.itemsDetailEdit.index].items[this.itemsDetailEdit.itemIndex].bg = res.data.fileName;
          } else {
            this.$message.error(res.resultMsg);
          }
        },
        customItem2Success(res) {
          if (res.resultCode === '0') {
            this.itemsDetailEdit2.bg = res.data.fileName;
            this.customItems[this.itemsDetailEdit2.index].items[this.itemsDetailEdit2.itemIndex].bg = res.data.fileName;
          } else {
            this.$message.error(res.resultMsg);
          }
        },
        _setIndex(index) {
          bIndex = index;
        },
        _delBanner(index) {
          this.$confirm('是否删除此Banner?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.banners.splice(index, 1);
          }).catch(e => {
            return e;
          });
        },
        _upBanner(index) {
          if (index === 0) {
            return;
          } else {
            this.banners[index] = this.banners.splice(index - 1, 1, this.banners[index])[0];
          }
        },
        _downBanner(index) {
          if (index === this.banners.length - 1) {
            return;
          } else {
            this.banners[index] = this.banners.splice(index + 1, 1, this.banners[index])[0];
          }
        },
        _upLayout(index) {
          if (index === 0) {
            return;
          } else {
            this.items[index] = this.items.splice(index - 1, 1, this.items[index])[0];
          }
        },
        _downLayout(index) {
          if (index === this.items.length - 1) {
            return;
          } else {
            this.items[index] = this.items.splice(index + 1, 1, this.items[index])[0];
          }
        },
        _upCustomItem(index) {
          if (index) {
            this.customItems[index] = this.customItems.splice(index - 1, 1, this.customItems[index])[0];
          }
        },
        _downCustomItem(index) {
          if (index !== this.customItems.length - 1) {
            this.customItems[index] = this.customItems.splice(index + 1, 1, this.customItems[index])[0];
          }
        },
        _editCustomItem(index, itemIndex, title) {
          
          this.customItemsDetailDialog = true
          this.itemsDetailEdit = Object.assign({ index, itemIndex }, this.customItems[index].items[itemIndex])
        },
        _editCustomItem2(index, itemIndex, title) {
          
          this.customItems2DetailDialog = true
          this.itemsDetailEdit2 = Object.assign({ index, itemIndex }, this.customItems[index].items[itemIndex])
        },
        _saveCustomDetail() {
          
          this.customItemsDetailDialog = false
          this.customItems[this.itemsDetailEdit.index].items[this.itemsDetailEdit.itemIndex].title = this.itemsDetailEdit.title
          this.customItems[this.itemsDetailEdit.index].items[this.itemsDetailEdit.itemIndex].more = this.itemsDetailEdit.more
          this.customItems[this.itemsDetailEdit.index].items[this.itemsDetailEdit.itemIndex].alt = this.itemsDetailEdit.alt
        },
        _saveCustom2Detail() {
          
          this.customItems2DetailDialog = false
          this.customItems[this.itemsDetailEdit2.index].items[this.itemsDetailEdit2.itemIndex].title = this.itemsDetailEdit2.title
          this.customItems[this.itemsDetailEdit2.index].items[this.itemsDetailEdit2.itemIndex].more = this.itemsDetailEdit2.more
          this.customItems[this.itemsDetailEdit2.index].items[this.itemsDetailEdit2.itemIndex].des = this.itemsDetailEdit2.des
          this.customItems[this.itemsDetailEdit2.index].items[this.itemsDetailEdit2.itemIndex].alt = this.itemsDetailEdit2.alt
        },
        // 添加商品
        addShop(tab, item) {
          this.shopEidtDialog = true;
          this.shopTabIndex = tab;
          this.shopItemIndex = item;
          this.shopEidt = {
            tags: []
          }
        },
        editShop(tab, item) {
          this.shopEidtDialog = true;
          this.shopTabIndex = tab;
          this.shopItemIndex = item;
          this.shopEidt = this.shops[this.shopTabIndex].list[this.shopItemIndex];
          this.shopEidt.tags = !this.shopEidt.tags ? [] : this.shopEidt.tags
        },
        handleClose(tag) {
          this.shopEidt.tags.splice(this.shopEidt.tags.indexOf(tag), 1);
        },
        showInput() {
          this.inputVisible = true;
          this.$nextTick(_ => {
            this.$refs.saveTagInput.$refs.input.focus();
          });
        },
        handleInputConfirm() {
          let inputValue = this.inputValue;
          if (inputValue) this.shopEidt.tags.push(inputValue);
          this.inputVisible = false;
          this.inputValue = '';
        },
        handleShopSuccess(res, file) {
          if (res.resultCode === '0') {
            this.shopEidt.productImg = res.data.fileName;
          } else {
            this.$message.error(res.resultMsg);
          }
          this.shopEidt = JSON.parse(JSON.stringify(this.shopEidt))
          console.log(this.shopEidt)
        },
        // 保存商品信息
        saveShop() {
          console.log(this.shopEidt)
          this.shops[this.shopTabIndex].list[this.shopItemIndex] = this.shopEidt;
          this.shopEidtDialog = false;
        },
        handlePlanSuccess(res, file) {
          if (res.resultCode === '0') {
            this.plan.backgroundImg = res.data.fileName;
          } else {
            this.$message.error(res.resultMsg);
          }
          this.plan = JSON.parse(JSON.stringify(this.plan))
        },
      },
      mounted() {
        this.$nextTick(() => {
          if (!this.customItems.length) {
            this.customItems = [{
              "type": 1,
              "titleName": "",
              "titleUrl": "",
              "items": [{
                "position": "自定义服务左",
                "title": "",
                "bg": "",
                "more": "",
                "list": [{}, {}, {}, {}, {}, {}, {}, {}]
              }, {
                "position": "自定义服务中",
                "title": "",
                "bg": "",
                "more": "",
                "list": [{}, {}, {}, {}, {}, {}, {}, {}]
              }, {
                "position": "自定义服务右",
                "title": "",
                "bg": "",
                "more": "",
                "list": [{}, {}, {}, {}, {}, {}, {}, {}]
              }]
            }, {
              "type": 2,
              "titleName": "培训服务",
              "titleUrl": "",
              "items": [{
                "position": "自定义服务左",
                "title": "",
                "des": "",
                "bg": "",
                "more": "",
                "list": [{}, {}, {}, {},]
              }, {
                "position": "自定义服务中",
                "title": "",
                "bg": "",
                "more": "",
                "list": [{}, {}, {}, {},],
                "des": ""
              }, {
                "position": "自定义服务右",
                "title": "",
                "bg": "",
                "more": "",
                "list": [{}, {}, {}, {},],
                "des": ""
              }]
            }, {
              "type": 3,
              "titleName": "",
              "titleUrl": "",
              "items": [{
                "position": "自定义服务左",
                "title": "",
                "bg": "",
                "more": "",
                "list": [{}, {}, {}, {}, {}, {}, {}, {}]
              }, {
                "position": "自定义服务中",
                "title": "",
                "bg": "",
                "more": "",
                "list": [{}, {}, {}, {}, {}, {}, {}, {}]
              }, {
                "position": "自定义服务右",
                "title": "",
                "bg": "",
                "more": "",
                "list": [{}, {}, {}, {}, {}, {}, {}, {}]
              }]
            }]
          }
          if (!this.shops.length) {
            this.shops = [
              {
                title: 'Tab',
                more: '',
                list: [
                  {}, {}, {}, {}, {}, {}, {}
                ]
              }
            ]
          }
          document.getElementById('preLoading').style.display = 'none';
        });
      },
    });
  </script>
  <style scoped>
    li,
    ul {
      list-style: none;
    }

    .item {
      position: relative;
    }

    .item p {
      text-align: center;
    }

    .item .edit {
      position: absolute;
      top: 70px;
      right: 30px;
      z-index: 2;
    }

    .item .img {
      width: 390px;
      height: 200px;
      line-height: 200px;
      text-align: center;
      border: 1px solid #ddd;
      background-size: cover;
    }

    .item .list ul {
      margin: 0;
      padding: 10px 21px;
      width: 350px;
    }

    .item .list li {
      border-bottom: 1px dashed #ddd;
      float: left;
      width: 33.33%;
      height: 30px;
      line-height: 30px;
      overflow: hidden;
    }

    .item .list li:last-child {
      text-align: right;
    }

    .item .list li .more {
      background: #fe6602;
      color: #fff;
      font-size: 12px;
      padding: 3px 10px;
      border-radius: 3px;
    }

    .item .list3 {
      position: relative;
    }

    .item .list3 ul {
      height: 93px;
    }

    .item .list3 li {
      width: auto;
      padding-right: 20px;
    }

    .item .list3 li .more {
      position: absolute;
      right: 27px;
      top: 78px;
      height: 20px;
      line-height: 20px;
      padding: 0 10px;
    }

    .item2 h3 {
      text-align: center;
      font-weight: normal;
    }

    .item2>div {
      width: 390px;
      height: 400px;
      text-align: center;
      border: 1px solid #ddd;
      background-size: cover;
      padding: 20px;
      box-sizing: border-box;
    }

    .item2 h2 {
      height: 40px;
    }

    .item2 h2 a {
      float: left;
      display: block;
      height: 40px;
      line-height: 40px;
      overflow: hidden;
      width: 300px;
      text-overflow: ellipsis;
      white-space: normal;
    }

    .item2 h2 button {
      float: right;
    }

    .item2 p {
      height: 40px;
      line-height: 20px;
      text-align: left;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      width: 100%;
    }

    .item2 ul,
    .item2 li {
      padding: 0;
      margin: 0;
    }

    .item2 ul {
      padding-top: 100px;
    }

    .item2 li {
      height: 25px;
      line-height: 25px;
      text-align: left;
    }

    .item2 li a {
      display: block;
      height: 25px;
      line-height: 25px;
      text-align: left;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      width: 348px;
    }

    .el-table td,
    .el-table th {
      padding: 3px 0;
    }

    .shopItemSide {
      background-size: cover;
      width: 286px;
      height: 500px;
      position: relative;
    }

    .shopItemSide a {
      width: 112px;
      height: 38px;
      line-height: 38px;
      background: #FE6602;
      color: #fff;
      font-size: 14px;
      text-align: center;
      position: absolute;
      z-index: 2;
      bottom: 50px;
      left: 90px;
      ;
    }

    .shopItemSide img {
      position: absolute;
      z-index: 1;
      width: 286px;
      height: 500px;
      top: 0;
      left: 0;
    }

    .shopItem {
      text-align: left;
      padding: 50px 15px 0 15px;
    }

    .tag_lab {
      display: flex;
      justify-content: space-between;
    }

    .tag_lab .tag span {
      color: #FE6602;
      padding-right: 10px;
    }

    /* .tag_lab .label1 {
        background: url('./../public/images/home/<USER>');
    }
    .tag_lab .label2 {
        background: url('./../public/images/home/<USER>');
    } */
    .shopItem h2 {
      height: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #333333;
    }

    .shopItem p {
      font-size: 12px;
      color: #878787;
      line-height: 18px;
    }

    .shopItem .bottom {
      display: flex;
      justify-content: space-between;
    }

    .shopItem .bottom .currPrice {
      color: #FE6602;
      font-size: 14px;
    }

    .shopItem .bottom .basicPrice {
      color: #969696;
      font-size: 12px;
      text-decoration: line-through;
    }

    .shopItem .bottom a {
      color: #fff;
      width: 77px;
      text-align: center;
      height: 25px;
      line-height: 25px;
      background: #FE6602;
      font-size: 12px;
    }
  </style>
  <style>
    .el-tag+.el-tag {
      margin-left: 10px;
    }

    .button-new-tag {
      margin-left: 10px;
      height: 32px;
      line-height: 30px;
      padding-top: 0;
      padding-bottom: 0;
    }

    .input-new-tag {
      width: 90px;
      margin-left: 10px;
      vertical-align: bottom;
    }
  </style>
  <% include footer.html %>