<% include header.html %>
<el-container id="Main">
    <% include eheader.html %>
    <el-container>
        <% include eside.html %>
        <el-main>
            <el-row>
                <el-col :span="24">
                    <el-breadcrumb separator-class="el-icon-arrow-right">
                        <el-breadcrumb-item>
                            <a href='/'>首页</a>
                        </el-breadcrumb-item>
                        <el-breadcrumb-item>
                            移动端首页
                        </el-breadcrumb-item>
                    </el-breadcrumb>
                </el-col>
            </el-row>
            <el-row style="display:none">
                <h3 class="stit">Banner设置</h3>
            </el-row>
            首页地址：<a href="<%- mview_url %>" target="_blank"><%- mview_url %></a>
            <el-row style="width:1236px;">
                <h3 class="stit">Banner设置</h3>
                <el-form label-width="120px" ref="form">
                    <template v-for="(item, index) in banners" :key='index'>
                        <el-col :span="20">
                            <el-form-item label="banner图片">
                                <el-upload class="cover-uploader" 
                                    :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"'
                                    :data='uploadData'
                                    :show-file-list="false" 
                                    :on-success="bannerSuccess"
                                    :before-upload="beforeAvatarUpload" 
                                    name="file"
                                    accept="image/jpeg,image/png,image/gif" style="width:750px;height:360px;">
                                    <img v-if="item.img_path" :src="item.img_path" @click="_setIndex(index)" class="cover"
                                        style="position: relative;z-index: 2;width:750px;height:360px;">
                                    <i v-else class="el-icon-plus cover-uploader-icon" @click="_setIndex(index)"
                                        style="width:750px;height:360px;line-height: 360px"></i>
                                    <span
                                        style="position: absolute;bottom:10px;display:block;width:100%;text-align:center;z-index:1">
                                        选择图片(JPG,PNG,GIF,750px * 360px)
                                    </span>
                                </el-upload>
                            </el-form-item>
                            <el-form-item label="链接">
                                <el-input v-model.trim="item.btn_url"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-button type="primary" icon="el-icon-arrow-up" circle @click="_upBanner(index)"
                                style='display: block; margin-bottom: 5px;' :disabled="!index"></el-button>
                            <br />
                            <el-button type="primary" icon="el-icon-arrow-down" circle @click="_downBanner(index)"
                                style='display: block; margin-bottom: 5px;' :disabled="index === banners.length - 1"></el-button>
                            <br />
                            <el-button type="danger" icon="el-icon-delete" circle @click="_delBanner(index)"
                                style='display: block;'></el-button>
                        </el-col>
                    </template>
                    <hr />
                    <el-form-item label="">
                        <el-button type="success"  icon="el-icon-plus" @click='_addBanner'>新增Banner</el-button>
                    </el-form-item>
                </el-form>
                <hr>
                <h3 class="stit">服务分类楼层预览</h3>
                <el-row class="serviceList">
                    <el-col :span="4" v-for='(item, index) in serviceList' :key='index'>
                        <template v-if='index < 9'>
                            <template v-if='item.icon_url'>
                                <i :style="{ backgroundImage:'url(/static/images/service/' + item.icon_url + '.svg)' }"></i>
                            </template>
                            <template v-else>
                                <i :style="{ backgroundImage:'url(/static/images/service/more.svg)' }"></i>
                            </template>
                            <p>{{item.short_name && item.short_name.substr(0, 6) || item.name && item.name.substr(0, 6)}}</p>
                        </template>
                        <template v-if='index == 9'>
                            <i :style="{ backgroundImage:'url(/static/images/service/more.svg)' }"></i>
                            <p>更多</p>
                        </template>
                    </el-col>
                </el-row>
                <hr>
                <h3 class="stit">楼层设置</h3>
                <el-row v-for='(item, index) in items1' :key='index'>
                    <el-col :span="7">
                        <el-form label-width="80px">
                            <el-form-item label="楼层名称">
                                <el-input placeholder="请输入楼层名称" v-model='item.tit'></el-input>
                            </el-form-item>
                        </el-form>
                    </el-col>
                    <el-col :span="11">
                        <el-form label-width="80px">
                            <el-form-item label="更多链接">
                                <!-- <el-input placeholder="请输入楼层名称" v-model='item.more_link'></el-input> -->
                                <el-input placeholder="请输入楼层名称" v-model="item.more_link">
                                    <template slot="prepend"><%- mview_url %>/</template>
                                </el-input>
                            </el-form-item>
                        </el-form>
                    </el-col>
                    <el-col :span="6" style="text-align: right;">
                        <el-button type="primary" icon="el-icon-arrow-up" :disabled="!index" circle @click="_upItem(index)"></el-button>
                        <el-button type="primary" icon="el-icon-arrow-down" :disabled="index === items1.length - 1" circle @click="_downItem(index)"></el-button>
                        <el-button type="danger" icon="el-icon-delete" circle @click="_delItem(index)"></el-button>
                    </el-col>
                    <el-col :span="24">
                        <el-row :gutter="20" class="itemList">
                            <el-col :span="6" v-for='(list, listIndex) in item.list' :key='listIndex'>
                                <el-card :body-style="{ padding: '0px' }">
                                    <img :src="list.thumb_img" />
                                    <p>{{ list.title }}</p>
                                    <el-button type="primary" icon="el-icon-edit" @click="_editSku(list, index, listIndex)" circle style="top: 10px;"></el-button>
                                    <el-button type="danger" icon="el-icon-delete" @click='_delSku(index, listIndex)' circle style="top: 60px;"></el-button>
                                </el-card>
                            </el-col>
                            <el-col :span="6">
                                <el-card :body-style="{ padding: '0px' }">
                                    <el-button type="text" class="add" @click="_addSkuModal(index)"  icon="el-icon-plus"></el-button>
                                </el-card>
                            </el-col>
                        </el-row>
                    </el-col>
                </el-row>
                
                <el-button type="success"  icon="el-icon-plus" @click="addItem">添加楼层</el-button>
                <hr>
                <h3 class="stit">解决方案楼层设置</h3>
                <el-row :gutter="100" class="planList">
                    <el-col :span="12" v-for="(item, i) in jfItems" :key='i' style="text-align:center;position: relative;">
                        <img :src='"/static/images/plan/"+ (i+1) +".svg"' style="display:block;margin: 20px auto">
                        <p style="font-size: 17px">{{item.title}}</p>
                        <p style="color: #999;font-size:14px;width:232px;margin:10px auto">{{item.sub_title}}</p>
                        <el-button type="primary" icon="el-icon-edit" circle
                            style="position:absolute;top:10px;right:40px" @click="_editJf(i)"></el-button>
                    </el-col>
                </el-row>
                <hr>
                <h3 class="stit">新闻/案例设置</h3>
                <el-row :gutter="20" class="newsList">
                    <el-col :span="24" v-for="(item,i) in newsItems" :key='i' style="text-align:left;position: relative;">
                        <p class="tit">{{item.title}}</p>
                        <p>
                            <time>{{item.time}}</time>
                            <span v-if='item.type'>{{item.type}}</span>
                        </p>
                        <img :src='item.thumb_img' />
                        <el-button type="primary" icon="el-icon-edit" circle style="position:absolute;top:10px;right:0px" @click="_editNews(i)"></el-button>
                        <el-button type="danger" icon="el-icon-delete" circle style="position:absolute;top:60px;right:0px" @click="_delNews(i)"></el-button>
                    </el-col>
                    <el-col>
                </el-row :span="24">
                    <el-button type="success" icon="el-icon-plus" @click="_addNewsModal()">添加新闻</el-button>
                </el-col>
                <hr>
                <h3 class="stit">SEO设置</h3>
                <el-row :gutter="20">
                    <el-form label-width="120px">
                        <el-form-item label="Title">
                            <el-input v-model="page_title" auto-complete="off" maxlength="50"></el-input>
                        </el-form-item>

                        <el-form-item label="Keywords">
                            <el-input v-model="page_keywords" auto-complete="off" maxlength="50"></el-input>
                        </el-form-item>

                        <el-form-item label="Description">
                            <el-input v-model="page_description" auto-complete="off" maxlength="200"></el-input>
                        </el-form-item>
                    </el-form>
                </el-row>
            </el-row>
            <hr>
            <el-row>
                <el-col :span="24" style="background: #39F;padding:12px 0;color:#FFF">
                    <el-col :span="12" style="padding-left:12px;">
                        <el-button v-if="hasEditPurview==1" @click="_cancel">放弃修改</el-button>
                    </el-col>
                    <el-col :span="12" style="text-align:right;padding-right:12px;">
                        <el-button v-if="hasEditPurview==1" type="success" @click='onSubmit'>保存修改</el-button>
                    </el-col>
                </el-col>
            </el-row>
            <div class="logshow-wrapper" v-if="logs.list.length > 0">
                <el-row class="logshow" :class="{on: logshow}">
                    <span class="icon"><i class="el-icon-arrow-up" @click="showLog"></i></span>
                    <ul>
                        <li v-for="(item, index) of logs.list" :key='index'>{{item.time}}, {{item.user_name}}, {{item.log}}</li>
                    </ul>
                    <el-pagination @current-change="logPageChange" :current-page.sync="logs.page"
                        :page-size="logs.limit" layout="total, prev, pager, next" :total="logs.total">
                    </el-pagination>
                </el-row>
            </div>
        </el-main>
    </el-container>

    <el-dialog title="添加/编辑SKU" :visible.sync="skuDialog" width="640px">
        <el-form :model="skuEdit" label-width="90px">
            <el-form-item label="URL">
                <el-input v-model="skuEdit.sku_url" auto-complete="off" style="width:380px"></el-input>
                <el-button @click.prevent="_getSku">查找</el-button>
            </el-form-item>

            <template v-if="skuEdit.sku_id">
                <el-form-item label="图片" prop="name">
                    <img :src="skuEdit.thumb_img" style="width:385px; height: 200px;">
                    <el-upload :data='uploadData' :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"' :show-file-list="false"
                        :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload" name="file">
                        <el-button size="small" type="primary">更换图片</el-button>
                    </el-upload>
                </el-form-item>

                <el-form-item label="标题">
                    <el-input v-model="skuEdit.title" auto-complete="off"></el-input>
                </el-form-item>
            </template>

        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="_addSku()">确 定</el-button>
        </div>
    </el-dialog>

    <el-dialog title="编辑解决方案" :visible.sync="jfDialog" width="640px">
        <el-form :model="jfEdit" label-width="90px">
            <el-form-item label="URL">
                <el-input v-model="jfEdit.url" auto-complete="off" style="width:380px"></el-input>
                <el-button @click.prevent="_getJf">查找</el-button>
            </el-form-item>

            <template v-if="jfEdit.jfId">
                <el-form-item label="标题">
                    <el-input v-model="jfEdit.title" auto-complete="off"></el-input>
                </el-form-item>

                <el-form-item label="副标题">
                    <el-input v-model="jfEdit.sub_title" auto-complete="off"></el-input>
                </el-form-item>
            </template>

        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="_addJf()">确 定</el-button>
        </div>
    </el-dialog>

    <el-dialog title="编辑新闻/案例" :visible.sync="newsDialog" width="640px">
        <el-form :model="newsEdit" label-width="90px">
            <el-form-item label="URL">
                <el-input v-model="newsEdit.url" auto-complete="off" style="width:380px"></el-input>
                <el-button @click.prevent="_getNews">查找</el-button>
            </el-form-item>

            <template v-if="newsEdit.news_id">
                <el-form-item label="标题">
                    <el-input v-model="newsEdit.title" auto-complete="off"></el-input>
                </el-form-item>

                <el-form-item label="图片">
                    <img :src="newsEdit.thumb_img" style="width:284px">
                    <el-upload :data='uploadData' :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"' :show-file-list="false" :on-success="handleAvatarSuccessNews"
                        :before-upload="beforeAvatarUpload" name="file">
                        <el-button size="small" type="primary">更换图片</el-button>
                    </el-upload>
                </el-form-item>
            </template>

        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="_addNews">确 定</el-button>
        </div>
    </el-dialog>
</el-container>
<script>
    var main = new Vue({
        el: '#Main',
        data: {
            uploadData: {systemId: 5},
            ticMallHost: '<%- ticMallHost %>',
            page_title: '<%- detail.page_title %>',
            page_keywords: '<%- detail.page_keywords %>',
            page_description: '<%- detail.page_description %>',
            items1: <%- JSON.stringify(detail.items1) || '[]' %>,
            skuEdit: {
                sku_url: '',
                title: '',
                thumb_img: '',
                is_buy: '',
                sku_id: 0,
            },
            jfItems:<%- JSON.stringify(detail.jfItems) || '[]' %>,
            jfEdit: {
                url: '',
                title: '',
                sub_title: '',
                case_id: 0,
            },
            newsItems:<%- JSON.stringify(detail.newsItems) || '[]' %>,
            newsEdit: {
                url: '',
                content: '',
                sub_title: '',
                news_id: 0,
                type: '',
                thumb_img: ''
            },
            banners: <%- JSON.stringify(detail.banners) || '[]' %>,
            jfDialog: false,
            skuDialog: false,
            newsDialog: false,
            skuIndex: [],
            jfIndex: 0,
            newsIndex: 0,
            logs: <%- logs %>,
            logshow: false,
            hasEditPurview: 1,
            serviceList: <%- JSON.stringify(serviceList) || '[]' %>,
            itemIndex: 0,
            listIndex: 0,
            edit: false,
            newsEditFlag: false
        },
        methods: {
            addSku(i, n, index) {
                this.skuEdit.sku_url = '';
                this.skuEdit.title = '';
                this.skuEdit.sub_title = '';
                this.skuEdit.thumb_img = '';
                this.skuEdit.sku_id = 0;
                this.skuDialog = true;

                this.skuIndex = [i, n, index];
            },
            _getSku() {
                if (this.skuEdit.sku_url == '') {
                    return;
                }


            //     skuEdit: {
            //         sku_url: '',
            //             title: '',
            //                 thumb_img: '',
            //                     is_buy: '',
            //                         sku_id: 0,
            // },
                var that = this;
                var url = that.skuEdit.sku_url;
                axios.post('/homepage/getSku', { skuUrl: url, _csrf: '<%- csrf %>' })
                    .then(function (result) {
                        if (result.data.success) {
                            // window.location.reload();
                            that.skuEdit.title = result.data.data.name;
                            // that.skuEdit.sub_title = result.data.data.sub_title;
                            that.skuEdit.thumb_img = result.data.data.m_thumb_img || result.data.data.thumb_img;
                            that.skuEdit.sku_id = result.data.data.id;
                            // that.skuEdit.is_buy = result.data.data.is_buy;
                        } else {
                            that.$message.error(result.data.msg);
                        }
                    }).catch(e => {
                        return e;
                    });
            },
            _getJf() {
                if (this.jfEdit.url == '') {
                    return;
                }

                var that = this;
                var url = that.jfEdit.url;
                axios.post('/homepage/getJf', { url: url, _csrf: '<%- csrf %>' })
                    .then(function (result) {
                        if (result.data.success) {
                            // window.location.reload();
                            that.jfEdit.title = result.data.data.name;
                            that.jfEdit.sub_title = result.data.data.sub_title;
                            that.jfEdit.jfId = result.data.data.id;
                        } else {
                            that.$message.error(result.data.msg);
                        }
                    }).catch(e => {
                        return e;
                    });
            },
            _getNews() {
                if (this.newsEdit.url == '') {
                    return;
                }

                var that = this;
                var url = that.newsEdit.url;
                if (url.includes('news')) {
                    axios.post('/homepage/getNews', { url: url, _csrf: '<%- csrf %>' })
                        .then(function (result) {
                            if (result.data.success) {
                                that.newsEdit.title = result.data.data.title;
                                that.newsEdit.content = result.data.data.content;
                                that.newsEdit.news_id = result.data.data.id;
                                that.newsEdit.time = result.data.data.time;
                                that.newsEdit.type = result.data.data.type;
                                that.newsEdit.thumb_img = result.data.data.thumb_url || result.data.data.contentImg; //先取缩略图再取文本内的图片
                                that.newsEdit.category = 'news'
                            } else {
                                that.$message.error(result.data.msg);
                            }
                        }).catch(e => {
                            return e;
                        });
                 } else if (url.includes('case')) {
                    const id = url.substring(url.indexOf('-') + 1, url.lastIndexOf('.'))
                    axios.post('/case/dtl', { id })
                        .then(function (result) {
                            if (result.data.success) {
                                
                                that.newsEdit.title = result.data.detail.title;
                                that.newsEdit.content = result.data.detail.content;
                                that.newsEdit.news_id = id;
                                that.newsEdit.time = result.data.detail.publishTime;
                                that.newsEdit.type = result.data.detail.name;
                                that.newsEdit.thumb_img = result.data.detail.contentImg; //先取缩略图再取文本内的图片
                                that.newsEdit.category = 'case';
                            } else {
                                that.$message.error(result.data.msg);
                            }
                        });
                } else {
                    that.$message.error('请输入正确的url');
                }
            },
            _editSku(list, index, listIndex) {
                // this.skuIndex = [i, n, index];
                // var $_obj = this.items[i].node[n].skus[index];
                this.skuEdit.sku_url = list.sku_url;
                this.skuEdit.title = list.title;
                this.skuEdit.sub_title = list.sub_title;
                this.skuEdit.thumb_img = list.thumb_img;
                this.skuEdit.sku_id = list.sku_id;
                this.skuEdit.is_buy = list.is_buy;
                this.itemIndex = index;
                this.listIndex = listIndex
                this.edit = true
                this.skuDialog = true;
            },
            _delSku(index, listIndex) {
                this.$confirm('是否删除此数据?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.items1[index].list.splice(listIndex, 1);
                }).catch(e => {
                    return e;
                });
            },
            _delItem(index) {
                this.$confirm('是否删除此楼层?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.items1.splice(index, 1);
                }).catch(e => {
                    return e;
                });
            },
            _addSku() {
                if (this.edit) {
                    const item = this.items1[this.itemIndex].list[this.listIndex]
                    const tempObj = {...this.skuEdit}
                    item.title = tempObj.title;
                    item.sku_url = tempObj.sku_url;
                    item.thumb_img = tempObj.thumb_img;
                    item.sku_id = tempObj.sku_id;
                    item.is_buy = tempObj.is_buy;
                } else {
                    const tempObj = { ...this.skuEdit }
                    this.items1[this.itemIndex].list.push(tempObj)
                }
                this.skuDialog = false;
            },
            _addSkuModal(index) {
                this.skuEdit.sku_url = ''
                this.skuEdit.title = ''
                this.skuEdit.thumb_img = ''
                this.skuEdit.is_buy = ''
                this.skuEdit.sku_id = 0
                this.edit = false
                this.itemIndex = index
                this.skuDialog = true;
            },
            _editJf(i) {
                this.jfIndex = i;
                var $_obj = this.jfItems[i];
                this.jfEdit.url = $_obj.url;
                this.jfEdit.title = $_obj.title;
                this.jfEdit.sub_title = $_obj.sub_title;
                this.jfEdit.jfId = $_obj.id;
                this.jfDialog = true;
            },
            _addJf() {
                var $_obj = this.jfItems[this.jfIndex];
                $_obj.title = this.jfEdit.title;
                $_obj.url = this.jfEdit.url;
                $_obj.sub_title = this.jfEdit.sub_title;
                $_obj.id = this.jfEdit.jfId;
                this.jfDialog = false;
            },
            _editNews(i) {
                this.newsEditFlag = true
                this.newsIndex = i;
                var $_obj = this.newsItems[i];
                this.newsEdit.url = $_obj.url;
                this.newsEdit.title = $_obj.title;
                this.newsEdit.content = $_obj.content;
                this.newsEdit.news_id = $_obj.id;
                this.newsEdit.time = $_obj.time;
                this.newsEdit.thumb_img = $_obj.thumb_img;
                this.newsEdit.type = $_obj.type;
                this.newsEdit.category = $_obj.category;
                this.newsDialog = true;
            },
            _addNewsModal() {
                this.newsEdit.url = ''
                this.newsEdit.title = ''
                this.newsEdit.content = ''
                this.newsEdit.sub_title = ''
                this.newsEdit.news_id = ''
                this.newsEdit.type = ''
                this.newsEdit.category = ''
                this.newsEdit.thumb_img = ''
                this.newsEditFlag = false
                this.newsDialog = true;
            },
            _delNews(index) {
                this.$confirm('是否删除此新闻?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.newsItems.splice(index, 1);
                }).catch(e => {
                    return e;
                });
            },
            _addNews() {
                if(this.newsEditFlag) {
                    var $_obj = this.newsItems[this.newsIndex];
                    $_obj.title = this.newsEdit.title;
                    $_obj.url = this.newsEdit.url;
                    $_obj.content = this.newsEdit.content;
                    $_obj.id = this.newsEdit.news_id;
                    $_obj.time = this.newsEdit.time;
                    $_obj.type = this.newsEdit.type;
                    $_obj.category = this.newsEdit.category;
                    $_obj.thumb_img = this.newsEdit.thumb_img;
                } else {
                    const tempObj = { ...this.newsEdit }
                    // this.items1[this.itemIndex].list.push(tempObj)
                    this.newsItems.push(tempObj)
                }
                this.newsDialog = false;
            },
            addItem() {
                var newFloor = {
                    tit: '',
                    list: [],
                    floorDialog: false,
                }
                this.items1.push(newFloor);
            },
            beforeAvatarUpload(file) {
                const isJPG = file.type === 'image/jpeg';
                const isPNG = file.type === 'image/png';
                const isGIF = file.type === 'image/gif';
                const isLt2M = file.size / 1024 / 1024 < 10;

                if (!isJPG && !isPNG && !isGIF) {
                    this.$message.error('图片格式错误!');
                    return false;
                }
                if (!isLt2M) {
                    this.$message.error('上传图片大小不能超过 10MB!');
                    return false;
                }
            },
            handleAvatarSuccess(res, file) {
                if (res.resultCode === '0') {
                    this.skuEdit.thumb_img = res.data.fileName;
                    this.items1[this.itemIndex].list[this.listIndex].thumb_img = res.data.fileName;
                } else {
                    this.$message.error(res.resultMsg);
                }
            },
            handleAvatarSuccessNews(res, file) {
                if (res.resultCode === '0') {
                    this.newsEdit.thumb_img = res.data.fileName;
                    this.newsItems[this.newsIndex].thumb_img = res.data.fileName;
                } else {
                    this.$message.error(res.resultMsg);
                }
            },
            _cancel() {
                window.location.reload();
            },
            onSubmit() {
                var that = this;
                var params = {
                    page_title: that.page_title,
                    page_keywords: that.page_keywords,
                    page_description: that.page_description,
                    items1: that.items1,
                    jfItems: that.jfItems,
                    newsItems: that.newsItems,
                    banners: that.banners,
                    _csrf: '<%- csrf %>',
                }

                let flag = true
                that.items1.forEach(item => {
                    if(item.more_link && item.more_link.substr(0, 1) === '/') {
                        flag = false
                    }
                })
                if (flag) {
                    axios.post('/homepage/saveMobile', params).
                        then(function (result) {
                            if (result.data.success) {
                                window.location.reload();
                                that.$message({
                                    message: '保存成功',
                                    type: 'success'
                                });
                            }
                        }).catch(e => {
                            return e;
                        });
                } else {
                    that.$message({
                        message: '楼层设置更多链接错误，请修改后再提交',
                        type: 'error'
                    });
                }
            },
            showLog() {
                if (this.logs.list.length > 1) {
                    this.logshow = !this.logshow;
                }
            },
            logPageChange(page) {
                axios.post('/logslist', { page: page, model: '移动端首页配置', limit: 10 }).then(res => {
                    let data = res.data;
                    if (data.success) {
                        this.logs = data.data;
                    }

                })
            },
            beforeAvatarUpload(file) {
                const isJPG = file.type === 'image/jpeg';
                const isPNG = file.type === 'image/png';
                const isGIF = file.type === 'image/gif';
                const isLt2M = file.size / 1024 / 1024 < 10;

                if (!isJPG && !isPNG && !isGIF) {
                    this.$message.error('图片格式错误!');
                    return false;
                }
                if (!isLt2M) {
                    this.$message.error('上传图片大小不能超过 10MB!');
                    return false;
                }
                //return isJPG && isLt2M;
            },
            _addBanner() {
                this.banners.push({ img_path: '', btn_text: '', btn_url: 'https://', btn_description: '' });
            },
            bannerSuccess(res, file) {
                if (res.resultCode === '0') {
                    this.banners[bIndex].img_path = res.data.fileName;
                } else {
                    this.$message.error(res.resultMsg);
                }
            },
            _setIndex(index) {
                bIndex = index;
            },
            _delBanner(index) {
                this.$confirm('是否删除此Banner?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.banners.splice(index, 1);
                }).catch(e => {
                    return e;
                });
            },
            _upBanner(index) {
                if (index === 0) {
                    return;
                } else {
                    this.banners[index] = this.banners.splice(index - 1, 1, this.banners[index])[0];
                }
            },
            _downBanner(index) {
                if (index === this.banners.length - 1) {
                    return;
                } else {
                    this.banners[index] = this.banners.splice(index + 1, 1, this.banners[index])[0];
                }
            },
            _upItem(index) {
                if (index === 0) {
                    return;
                } else {
                    this.items1[index] = this.items1.splice(index - 1, 1, this.items1[index])[0];
                }
            },
            _downItem(index) {
                if (index === this.items1.length - 1) {
                    return;
                } else {
                    this.items1[index] = this.items1.splice(index + 1, 1, this.items1[index])[0];
                }
            }
        },
        mounted() {
            this.$nextTick(() => {
                document.getElementById('preLoading').style.display = 'none';
            });
        }
    });
</script>

<style>
    .serviceList .el-col-4 {
        text-align: center;
    }
    .serviceList i {
        display: inline-block;
        width: 40px;
        height: 40px;
        background-size: 100%;
    }
   
    .itemList .el-col-6 {
        padding-bottom: 20px;
    }
    .itemList .el-card__body {
        position: relative;
        height: 200px;
    }
    .itemList .el-card__body img {
        height: 150px;
        width: 100%;
    }
    .itemList .el-card__body p {
        height: 40px;
        line-height: 40px;
        padding-left: 5px;
        margin: 0;
    }
    .itemList .el-card__body button {
        position: absolute;
        right: 10px;
    }
    .itemList .el-card__body button.add {
        top: 85px;
        left: 135px;
    } 
    .planList .el-col-12 {
        text-align: center;
    }
    .planList img {
        display: inline-block;
    }
    .newsList .el-col-24 {
        border-bottom: 1px dashed #ddd;
    }
    .newsList p {
    }
    .newsList .tit {
        font-size: 24px;
    }
    .newsList time {
        color: #999;
    }
    .newsList span {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 5px 10px;
    }
    .newsList img {
        height: 100px;
        float: right;
        position: absolute;
        top: 10px;
        right: 0;
        margin-right: 50px;
    }
</style>
<% include footer.html %>