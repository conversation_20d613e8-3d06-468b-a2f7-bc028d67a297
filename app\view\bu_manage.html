<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        设置
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        BU配置
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row v-loading="loading">
                            <el-col :span="12">
                                <el-table :data="tableList.list" stripe border style="width: 100%">
                                    <el-table-column prop="id" label="" width="60">
                                        <template slot-scope="scope">
                                            {{scope.$index+1}}
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="buName" label="BU名称">
                                    </el-table-column>
                                    <el-table-column fixed="right" label="操作" width="160">
                                        <template slot-scope="scope">
                                            <el-button-group>
                                                <el-button  v-if="hasEditPurview==1" type="primary" size="mini" @click='_edit(scope.row.buId, scope.row.buName)'>编辑</el-button>
                                                <el-button  v-if="hasEditPurview==1" type="danger" size="mini" @click='_del(scope.row.buId, scope.row.buName)'>删除</el-button>
                                            </el-button-group>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <div class="logshow-wrapper" v-if="logs.list.length > 0">
                                    <el-row class="logshow" :class="{on: logshow}">
                                        <span class="icon"><i class="el-icon-arrow-up" @click="showLog"></i></span>
                                        <ul>
                                            <li v-for="(item, index) of logs.list" :key='index'>{{item.createTime}}, {{item.userName}}, {{item.log}}
                                            </li>
                                        </ul>
                                        <el-pagination @current-change="qryLog" :current-page.sync="logParam.pageNum" :page-size="logParam.pageRow"
                                            layout="total, prev, pager, next" :total="logs.total">
                                        </el-pagination>
                                    </el-row>
                                </div>
                            </el-col>
                            <el-col :span="12"  v-if="hasEditPurview==1" >
                                <el-form ref="form" :model="form" label-width="90px">
                                    <el-form-item label="BU名称">
                                        <el-input v-model="form.buName" maxlength="20"></el-input>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="onSubmit" v-if="form.buId == 0">新建</el-button>
                                        <el-button type="primary" @click="onModify" v-else>编辑</el-button>
                                        <el-button @click="_default">取消</el-button>
                                    </el-form-item>
                                </el-form>
                            </el-col>
                        </el-row>
                    </el-main>
            </el-container>
    </el-container>
    <script>
    var main = new Vue({
        el: '#Main',
        data: {
            tableList: {
                list: <%- list %>,
                total: 0,
                curr: 0,
                limit: 10
            },
            form: {
                buId: 0,
                buName: '',
            },
            pageData: {
                page: 1
            },
            loading: false,
            logs: <%- logs %>,
            logshow: false,
			hasCheckedPurview: <%- hasCheckedPurview %>,
			hasPubviewPurview: <%- hasPubviewPurview %>,
            hasEditPurview: <%- hasEditPurview %>,
            logParam: {
                operatorType: 8,
                operatorId: '',
                pageRow: 10,
                pageNum: 1
            }
        },
        methods: {
            handleCurrentChange(r) {
                this.form.page = r;
            },
            onSubmit() {
                var that = this;
                that.loading = true;
                var params = this.form;
                if(params.buName.trim()) {
                    axios.post('/setting/buManage/add', params)
                        .then(function(result) {
                            that.loading = false;
                            if (result.data.resultCode === '0') {
                                window.location.reload();
                            } else{
                                that.$message.error(result.data.resultMsg);
                            }
                        });
                } else {
                    that.loading = false;
                    that.$message.error('BU名称不能为空');
                }
            },
            _edit(buId, buName) {
                var that = this;
                this.form.buId = buId;
                this.form.buName = buName;
            },
            _del(buId,buName) {
                var that = this;
                this.$confirm('是否删除'+buName+'？', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                    that.loading = true;
                    axios.post('/setting/buManage/del', { buId: buId, buName: buName })
                    .then(function(result) {
                        if (result.data.resultCode === '0') {
                            window.location.reload();
                        } else {
                            that.loading = false;
                            that.$message.error(result.data.resultMsg);
                        }
                    });
                }).catch(e =>{
                    return e;
                });
            },
            onModify() {
                var that = this;
                that.loading = true;
                var params = this.form;
                if (params.buName.trim()) {
                axios.post('/setting/buManage/mod', params)
                    .then(function(result) {
                        if (result.data.resultCode === '0') {
                            window.location.reload();
                        } else {
                            that.$message.error(result.data.resultMsg);
                        }
                    });
                } else {
                    that.loading = false;
                    that.$message.error('BU名称不能为空');
                }
            },
            _default() {
                this.form.buId = 0;
                this.form.buName = '';
            },
            showLog(){
                if(this.logs.list.length > 1){
                    this.logshow = !this.logshow;
                }
            },
            qryLog(page) {
                this.logParam.pageNum = page
                axios.post('/log/qry', this.logParam)
                    .then(res => {
                        if (res.data.resultCode === '0') {
                            this.logs = {
                                list: res.data.data.items,
                                total: res.data.data.totalNum
                            };
                            console.log(this.logs)
                        }
                    }).catch(e => {
                        this.$message.error(e);
                    });
            }
        },
        mounted(){
            this.$nextTick(() => {
                document.getElementById('preLoading').style.display = 'none';
                this.qryLog()
            })
        }
    });
    </script>
    <% include footer.html %>