"use strict";

const Service = require("egg").Service;
const moment = require("moment");
const request = require("request");
const crypto = require("crypto");
const _siteInfo = require("../../config/info").siteInfo;
const solrUrl = _siteInfo.solrUrl,
  solrPstr = _siteInfo.solrPstr,
  solrPid = _siteInfo.solrPid;
const { env } = require("../../config/info").siteInfo;
class CaseService extends Service {
  async typeList() {
    const { app } = this;

    const list = await app.mysql.select("catalog", {
      columns: ["id", "name", "alias", "is_show", "sort_num", "is_activity"],
      where: {
        is_delete: 0,
        parent_id: 5,
      },
      orders: [["sort_num", "desc"]],
    });

    for (let item of list) {
      item.num = await app.mysql.count("cases", {
        catalog_id: item.id,
        is_delete: 0,
        is_publish: 1,
      });
      item.is_show = item.is_show ? true : false;
      item.is_activity = item.is_activity ? true : false;
    }

    return {
      list,
    };
  }

  async addType(params) {
    const { app } = this;

    const gmt_create = await moment().format("YYYY-MM-DD HH:mm:ss");
    let is_show = params.is_show ? 1 : 0;
    let is_activity = params.is_activity ? 1 : 0;
    const cataRow = {
      name: params.name,
      alias: params.alias,
      parent_id: 5,
      sort_num: params.sort_num,
      is_show,
      is_activity,
      gmt_create: gmt_create,
    };

    const logRow = {
      user_name: this.ctx.session.userInfo.name,
      log: "创建" + params.name,
      model: "资讯中心-案例类别",
      name: params.name,
    };

    const result = await app.mysql.beginTransactionScope(async (conn) => {
      await conn.insert("catalog", cataRow);
      await conn.insert("logs", logRow);
      return {
        success: true,
      };
    }, this.ctx);

    // const success = result.affectedRows === 1;
    return {
      success: result.success,
    };
  }

  async typeGet(params) {
    const { app } = this;
    const info = await app.mysql.select("catalog", {
      columns: ["id", "name", "alias", "is_show", "sort_num", "is_activity"],
      where: {
        id: params.id,
      },
      limit: 1,
      offset: 0,
    });
    info[0].is_show = info[0].is_show ? true : false;
    info[0].is_activity = info[0].is_activity ? true : false;

    return {
      info,
    };
  }

  async typeEdit(params) {
    const { app } = this;

    const logRow = {
      user_name: this.ctx.session.userInfo.name,
      log: "编辑" + params.name,
      model: "资讯中心-案例类别",
      name: params.name,
    };

    const result = await app.mysql.beginTransactionScope(async (conn) => {
      await conn.update("catalog", {
        id: Number(params.id),
        name: params.name,
        alias: params.alias,
        is_show: params.is_show,
        is_activity: params.is_activity,
        sort_num: params.sort_num,
      });
      await conn.insert("logs", logRow);
      return {
        success: true,
      };
    }, this.ctx);

    // const success = result.affectedRows === 1;
    return {
      success: result.success,
    };
  }

  async typeDelete(params) {
    const { app } = this;
    // const result = await app.mysql.update('catalog', { id: params.id, is_delete: 1 });

    const result = await app.mysql.beginTransactionScope(async (conn) => {
      await conn.update("catalog", {
        id: params.id,
        is_delete: 1,
      });
      const info = await conn.get("catalog", {
        id: params.id,
      });
      const logRow = {
        user_name: this.ctx.session.userInfo.name,
        log: "删除" + info.name,
        model: "资讯中心-案例类别",
        name: info.name,
      };
      await conn.insert("logs", logRow);
      return {
        success: true,
      };
    }, this.ctx);

    // const success = result.affectedRows === 1;
    return {
      success: result.success,
    };
  }

  async getList(params) {
    const { app } = this;
    let query = ["n.is_delete=0"];
    if (params.type && params.type != 0) {
      query.push("c.id=" + params.type);
    }

    if (params.title) {
      query.push('n.title LIKE "%' + params.title + '%"');
    }

    if (params && typeof params.is_publish == "number") {
      query.push("n.is_publish=" + params.is_publish);
    }

    if (params && params.service) {
      query.push("cr.catalog_id=" + params.service);
    }

    if (params && params.trade) {
      query.push("cr.catalog_id=" + params.trade);
    }

    let baseCategoryIds_service = [];
    let baseCategoryIds_trade = [];

    if (
      params &&
      params.allowServiceList != null &&
      params.allowServiceList.length > 0
    ) {
      for (let idvalue of params.allowServiceList) {
        if (idvalue != null) {
          baseCategoryIds_service.push(idvalue);
        }
      }
    }

    if (
      params &&
      params.allowTradeList != null &&
      params.allowTradeList.length > 0
    ) {
      for (let idvalue of params.allowTradeList) {
        if (idvalue != null) {
          baseCategoryIds_trade.push(idvalue);
        }
      }
    }

    if (
      baseCategoryIds_service.length == 0 &&
      this.ctx.session.userInfo.type == 2
    ) {
      query.push("1=2");
    }

    if (
      baseCategoryIds_trade.length == 0 &&
      this.ctx.session.userInfo.type == 2
    ) {
      query.push("1=2");
    }

    if (params.create_time) {
      let time = params.create_time;
      query.push(`n.gmt_create>= '${time[0]} 00:00:00'`);
      query.push(`n.gmt_create<= '${time[1]} 23:59:59'`);
    }

    if (params.bu_id) {
      query.push("n.bu_id=" + params.bu_id);
    }

    let page = params.page || 1,
      limit = params.limit || 10;
    let start = (page - 1) * limit;

    var countsql =
      "SELECT count(distinct(n.id)) as countval FROM cases n LEFT JOIN catalog c ON n.catalog_id=c.id LEFT JOIN catalog_relation cr ON cr.case_id=n.id";

    var sql =
      "SELECT n.id,n.title,n.alias,n.is_top,n.is_hot,n.is_publish, n.is_public, n.is_copy,n.gmt_publish_time AS publish_time,n.gmt_create AS create_time,c.name AS typeName, b.BU_NAME as bu_name FROM cases n LEFT JOIN catalog c ON n.catalog_id=c.id LEFT JOIN catalog_relation cr ON cr.case_id=n.id LEFT JOIN BU b ON n.bu_id=b.BU_ID";

    if (baseCategoryIds_trade.length > 0) {
      countsql +=
        " left join (select case_id from catalog_relation where catalog_type=1 and case_id is not null and catalog_id not in (" +
        baseCategoryIds_trade.join(",") +
        ")) tmptrade on n.id=tmptrade.case_id";

      sql +=
        " left join (select case_id from catalog_relation where catalog_type=1 and case_id is not null and catalog_id not in (" +
        baseCategoryIds_trade.join(",") +
        ")) tmptrade on n.id=tmptrade.case_id";

      query.push("tmptrade.case_id is null");
    }

    if (baseCategoryIds_service.length > 0) {
      countsql +=
        " left join (select case_id from catalog_relation where catalog_type=2 and case_id is not null and catalog_id not in (" +
        baseCategoryIds_service.join(",") +
        ")) tmpservice on n.id=tmpservice.case_id";

      sql +=
        " left join (select case_id from catalog_relation where catalog_type=2 and case_id is not null and catalog_id not in (" +
        baseCategoryIds_service.join(",") +
        ")) tmpservice on n.id=tmpservice.case_id";

      query.push("tmpservice.case_id is null");
    }

    countsql += " WHERE " + query.join(" AND ");
    const total = await app.mysql.query(countsql);

    sql +=
      " WHERE " +
      query.join(" AND ") +
      " GROUP BY n.id ORDER BY n.is_top DESC,n.gmt_modify DESC,n.gmt_publish_time DESC LIMIT " +
      start +
      "," +
      limit;

    const list = await app.mysql.query(sql);

    for (let item of list) {
      item.create_time = moment(item.create_time).format("YYYY-MM-DD HH:mm:ss");
      item.publish_time = moment(item.publish_time).format(
        "YYYY-MM-DD HH:mm:ss"
      );

      let tradeList = [],
        serviceList = [];
      let tradeCata = await this.app.mysql.select("catalog_relation", {
        where: {
          case_id: item.id,
          catalog_type: 1,
        },
      });
      let serviceCate = await this.app.mysql.select("catalog_relation", {
        where: {
          case_id: item.id,
          catalog_type: 2,
        },
      });
      let catalogList = await this.app.mysql.query(
        `select a.alias from catalog a,catalog_relation b where case_id=${item.id} and b.catalog_id=a.id and a.alias!='subclass'`
      );
      item.catalog = catalogList;

      if (tradeCata && tradeCata.length > 0) {
        for (let ti of tradeCata) {
          let tradeInfo = await this.app.mysql.get("catalog", {
            id: ti.catalog_id,
          });
          //let tradeParantInfo = await this.app.mysql.get('catalog', { id: tradeInfo.parent_id });
          //tradeList.push(tradeParantInfo.name + '-' + tradeInfo.name);
          if (tradeInfo) {
            tradeList.push(tradeInfo.name);
          }
        }
      }

      if (serviceCate && serviceCate.length > 0) {
        for (let si of serviceCate) {
          let serviceInfo = await this.app.mysql.get("catalog", {
            id: si.catalog_id,
          });
          //let serviceParantInfo = await this.app.mysql.get('catalog', { id: serviceInfo.parent_id });
          //serviceList.push(serviceParantInfo.name + '-' + serviceInfo.name);
          if (serviceInfo) {
            serviceList.push(serviceInfo.name);
          }
        }
      }

      item.tradeList = tradeList;
      item.serviceList = serviceList;

      // todo ticView需要改数据，ticCms不需要
      let checkflowObj = await this.ctx.service.checkflow.getDetail(
        "case",
        item.id
      );

      item.tran_result = checkflowObj;
    }

    console.log(list);

    return {
      list: list,
      total: total[0]["countval"],
      page: page,
      limit: limit,
    };
  }

  async getDetail(id) {
    const { app } = this;

    const detail = await app.mysql.get("cases", {
      id: id,
    });

    const tag = await app.mysql.select("tag_relation", {
      columns: ["id", "sku_id", "tag_name"],
      where: {
        sku_id: detail.id,
      },
    });

    const tradeCata = await app.mysql.select("catalog_relation", {
      where: {
        case_id: id,
        catalog_type: 1,
      },
    });
    const serviceCata = await app.mysql.select("catalog_relation", {
      where: {
        case_id: id,
        catalog_type: 2,
      },
    });
    const tagList = await app.mysql.select("tag_relation", {
      where: {
        case_id: id,
      },
    });

    if (tradeCata && tradeCata.length > 0) {
      for (let ti of tradeCata) {
        let tradeInfo = await app.mysql.get("catalog", {
          id: ti.catalog_id,
        });
        let tradeParantInfo = await app.mysql.get("catalog", {
          id: tradeInfo.parent_id,
        });
        ti.parentName = tradeParantInfo.name;
        ti.name = tradeInfo.name;
        ti.id = tradeInfo.id;
      }
    }

    if (serviceCata && serviceCata.length > 0) {
      for (let si of serviceCata) {
        let serviceInfo = await app.mysql.get("catalog", {
          id: si.catalog_id,
        });
        let serviceParantInfo = await app.mysql.get("catalog", {
          id: serviceInfo.parent_id,
        });
        si.parentName = serviceParantInfo.name;
        si.name = serviceInfo.name;
        si.id = serviceInfo.id;
      }
    }

    if (detail.is_publish == 1) {
      detail.is_publish = true;
    } else {
      detail.is_publish = false;
    }

    if (detail.is_top == 1) {
      detail.is_top = true;
    } else {
      detail.is_top = false;
    }

    detail.tag = [];
    for (let item of tagList) {
      detail.tag.push(item.tag_name);
    }

    detail.tradeCata = tradeCata;
    detail.serviceCata = serviceCata;

    if (detail.content) {
      detail.content = detail.content
        .replace(/\n/g, "")
        .replace(/\.\.\/\.\.\/static/g, "/static")
        .replace(/\.\.\/static/g, "/static");
    }

    return {
      detail,
    };
  }

  async caseAdd(params) {
    const { app } = this;

    const gmt_create = await moment().format("YYYY-MM-DD HH:mm:ss");
    const row = {
      id: params.id,
      title: params.title,
      alias: params.alias,
      bu_id: params.bu_id,
      content: params.content,
      author: params.author,
      original: params.original,
      seo_text: params.seo_text,
      is_top: params.is_top ? 1 : 0,
      is_hot: params.is_hot,
      is_public: params.is_public,
      is_publish: params.is_publish ? 1 : 0,
      gmt_publish_time: params.gmt_publish_time
        ? moment(params.gmt_publish_time).format("YYYY-MM-DD HH:mm:ss")
        : gmt_create,
      catalog_id: params.type,
      page_title: params.page_title,
      page_keywords: params.page_keywords,
      page_description: params.page_description,
      mpage_title: params.mpage_title,
      mpage_keywords: params.mpage_keywords,
      mpage_description: params.mpage_description,
    };

    const tradeList = params.tradeList,
      serviceList = params.serviceList,
      tagList = params.tag;

    let result = "",
      sid = 0;

    let orgResource,
      orgCatat = [],
      orgCatat2 = [],
      orgCatas = [],
      orgCatas2 = [],
      tagCata = [],
      newCatat = [],
      newCatas = [],
      newTag = [];
    let changeValue = [];
    if (params.id && params.id != 0) {
      sid = params.id;

      orgCatat = await this.app.mysql.select("catalog_relation", {
        where: {
          case_id: sid,
          catalog_type: 1,
        },
      });
      orgCatas = await this.app.mysql.select("catalog_relation", {
        where: {
          case_id: sid,
          catalog_type: 2,
        },
      });
      tagCata = await this.app.mysql.select("tag_relation", {
        where: {
          case_id: sid,
        },
      });
      for (let item of orgCatat) {
        orgCatat2.push(item.catalog_id);
      }

      for (let item of orgCatas) {
        orgCatas2.push(item.catalog_id);
      }

      for (let item of tradeList) {
        newCatat.push(item);
      }

      for (let item of serviceList) {
        newCatas.push(item);
      }

      if (newCatat.sort().toString() != orgCatat2.sort().toString()) {
        changeValue.push("修改基本属性-所属行业");
      }

      if (newCatas.sort().toString() != orgCatas2.sort().toString()) {
        changeValue.push("修改基本属性-所属服务");
      }

      let tagCata2 = [];
      for (let item of tagCata) {
        tagCata2.push(item.tag_name);
      }

      if (tagCata2.sort().toString() != tagList.sort().toString()) {
        changeValue.push("修改案例内容-标签");
      }

      orgResource = await this.app.mysql.get("cases", {
        id: sid,
      });

      if (params.title != orgResource.title) {
        changeValue.push("修改案例内容-名称");
      }

      if (params.bu_id != orgResource.bu_id) {
        changeValue.push("修改案例基本属性-BU");
      }

      if (params.type != orgResource.catalog_id) {
        changeValue.push("修改基本属性-类型");
      }

      if (params.content != orgResource.content) {
        changeValue.push("修改案例内容-内容");
      }

      if (params.author != orgResource.author) {
        changeValue.push("修改案例内容-作者");
      }

      if (params.original != orgResource.original) {
        changeValue.push("修改案例内容-原创");
      }

      if (params.seo_text != orgResource.seo_text) {
        changeValue.push("修改案例内容-关键字");
      }

      if (params.gmt_publish_time != orgResource.gmt_publish_time) {
        changeValue.push("修改案例内容-发布时间");
      }

      if (params.is_publish != orgResource.is_publish) {
        changeValue.push("修改上下架");
      }

      if (params.is_top != orgResource.is_top) {
        changeValue.push("修改案例内容-是否置顶");
      }

      if (params.is_hot != orgResource.is_hot) {
        changeValue.push("修改案例内容-是否首页展示");
      }

      if (params.is_public != orgResource.is_public) {
        changeValue.push("修改案例内容-是否会员权限");
      }

      if (params.page_title != orgResource.page_title) {
        changeValue.push("修改SEO设置-Title");
      }

      if (params.page_keywords != orgResource.page_keywords) {
        changeValue.push("修改SEO设置-Keywords");
      }

      if (params.page_description != orgResource.page_description) {
        changeValue.push("修改SEO设置-Description");
      }

      if (params.mpage_title != orgResource.mpage_title) {
        changeValue.push("修改移动端SEO设置-Title");
      }

      if (params.mpage_keywords != orgResource.mpage_keywords) {
        changeValue.push("修改移动端SEO设置-Keywords");
      }

      if (params.mpage_description != orgResource.mpage_description) {
        changeValue.push("修改移动端SEO设置-Description");
      }

      // if(changeValue.length > 0){
      //     await this.ctx.service.logs.log({
      //         type: 'case',
      //         id: sid,
      //         log: changeValue.join(','),
      //         model: '资讯中心',
      //         name: row.title
      //     });
      // }

      // result = await this.app.mysql.update('cases', row);

      // const dropCatalog = await this.app.mysql.delete('catalog_relation', { case_id: sid });
      // const dropTag = await this.app.mysql.delete('tag_relation', { case_id: sid });

      let checkflowResult = await this.ctx.service.checkflow.getDetail(
        "case",
        params.id
      );
      result = await app.mysql.beginTransactionScope(async (conn) => {
        row.gmt_modify = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
        await conn.update("cases", row);
        await conn.delete("catalog_relation", {
          case_id: sid,
        });
        await conn.delete("tag_relation", {
          case_id: sid,
        });

        if (row.is_publish == 1 && checkflowResult != null) {
          await conn.update("checkflow", {
            id: checkflowResult.id,
            tran_note: "",
            tran_status: 10,
            tran_admin_time: gmt_create,
            tran_admin_id: this.ctx.session.userInfo.id,
          });
        } else if (checkflowResult != null) {
          await conn.update("checkflow", {
            id: checkflowResult.id,
            is_overdue: 1,
          });
        }
        if (changeValue.length > 0) {
          const logRow = {
            user_name: this.ctx.session.userInfo.name,
            case_id: sid,
            log: changeValue.join(","),
            model: "资讯中心",
            name: row.title,
          };
          await conn.insert("logs", logRow);
        }
        return {
          success: true,
        };
      }, this.ctx);
    } else {
      row.gmt_create = gmt_create;
      result = await app.mysql.beginTransactionScope(async (conn) => {
        const insertResult = await conn.insert("cases", row);
        sid = insertResult.insertId;

        const logRow = {
          user_name: this.ctx.session.userInfo.name,
          case_id: sid,
          log: "创建" + row.title,
          model: "资讯中心",
          name: row.title,
        };
        await conn.insert("logs", logRow);
        return {
          success: true,
        };
      }, this.ctx);
    }

    let tradeNames = [],
      serviceNames = [];
    for (let item of tradeList) {
      await this.app.mysql.insert("catalog_relation", {
        case_id: sid,
        catalog_id: item,
        catalog_type: 1,
        gmt_create: gmt_create,
      });
      let cataInfo = await app.mysql.get("catalog", {
        id: item,
      });
      if (cataInfo) {
        tradeNames.push(cataInfo.name);
      }
    }

    for (let item of serviceList) {
      await this.app.mysql.insert("catalog_relation", {
        case_id: sid,
        catalog_id: item,
        catalog_type: 2,
        gmt_create: gmt_create,
      });
      let cataInfo = await app.mysql.get("catalog", {
        id: item,
      });
      if (cataInfo) {
        serviceNames.push(cataInfo.name);
      }
    }

    for (let item of tagList) {
      if (item) {
        await this.app.mysql.insert("tag_relation", {
          tag_name: item,
          case_id: sid,
        });
      }
    }

    const cReg = new RegExp(/(<p>)[^>]*>([\s\S]*?)(?=<\/p>)/gi);
    const newDetail = params.content.match(cReg);
    let outSolrCon = "";
    if (newDetail) {
      for (let item2 of newDetail) {
        item2 = item2.replace(/<[^>]*>/g, "");
        if (item2.trim() != "" && outSolrCon == "") {
          outSolrCon = item2;
        }
      }
    } else {
      outSolrCon = params.content.replace(/<[^>]*>/g, "");
    }

    // 获取html文本中的第一张图片
    let contentImg = "";
    params.content.replace(
      /<img [^>]*src=['"]([^'"]+)[^>]*>/,
      function (match, capture) {
        contentImg = capture;
      }
    );
    let cataInfo = await app.mysql.get("catalog", {
      id: params.type,
    });

    if (params.is_publish) {
      let options = {
        url: env[this.app.config.env].solrUrl + "/add",
        method: "POST",
        headers: {
          pid: solrPid,
          timestamp: new Date().getTime(),
        },
        json: true,
        body: {
          id: "C_" + sid,
          title: params.title,
          alias: row.alias,
          sub_title: "",
          content: params.content.replace(/<.*?>/g, "").replace(/\n/g, ""),
          // summary: outSolrCon,
          // summary: '',
          type: "2",
          buy: 0,
          industry: tradeNames.join(","),
          service: serviceNames.join(","),
          imgUrl: contentImg,
          catalog: params.catalog,
          subType: cataInfo.name,
          createDate: moment(new Date()).format("YYYY/MM/DD"),
          catalogType: params.catalogType,
        },
      };

      let parr = JSON.stringify(options.body);
      parr += solrPstr;
      parr = crypto.createHash("md5").update(parr).digest("hex");
      options.headers.sign = parr;

      request(options, function (error, response, body) {
        if (error || response.statusCode > 400) {
          return;
        }
      });
    } else {
      let options = {
        url: env[this.app.config.env].solrUrl + "/del",
        method: "POST",
        headers: {
          pid: solrPid,
          timestamp: new Date().getTime(),
        },
        json: true,
        body: {
          id: "C_" + sid,
        },
      };

      let parr = JSON.stringify(options.body);
      parr += solrPstr;
      parr = crypto.createHash("md5").update(parr).digest("hex");
      options.headers.sign = parr;

      request(options, function (error, response, body) {
        if (error || response.statusCode > 400) {
          return;
        }
      });
    }

    // if (result.success) {
    //   await this.ctx.service.baidu.Push(`/case/${params.alias}/detail-${sid}.html`);
    //   const cndParams = {
    //     fileUrls: [params.pageLink]
    //   }
    //   await this.ctx.service.external.CDNRefresh(cndParams);
    // }
    return {
      success: result.success,
      id: sid,
    };
  }

  async caseDelete(id, pageLink) {
    const { app } = this;

    const result = await app.mysql.beginTransactionScope(async (conn) => {
      await conn.update("cases", {
        id: id,
        is_delete: 1,
        gmt_modify: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      });

      if (typeof id == "object") {
        for (let item of id) {
          const info = await this.app.mysql.get("cases", {
            id: item,
          });
          const logRow = {
            user_name: this.ctx.session.userInfo.name,
            case_id: item,
            log: "删除" + info.title,
            model: "资讯中心",
            name: info.title,
          };
          await conn.insert("logs", logRow);
        }
      } else {
        const info = await this.app.mysql.get("cases", {
          id: id,
        });
        const logRow = {
          user_name: this.ctx.session.userInfo.name,
          case_id: id,
          log: "删除" + info.title,
          model: "资讯中心",
          name: info.title,
        };
        await conn.insert("logs", logRow);
      }

      return {
        success: true,
      };
    }, this.ctx);

    let options = {
      url: env[this.app.config.env].solrUrl + "/del",
      method: "POST",
      headers: {
        pid: solrPid,
        timestamp: new Date().getTime(),
      },
      json: true,
      body: {
        id: "C_" + id,
      },
    };

    let parr = JSON.stringify(options.body);
    parr += solrPstr;
    parr = crypto.createHash("md5").update(parr).digest("hex");
    options.headers.sign = parr;

    request(options, function (error, response, body) {
      if (error || response.statusCode > 400) {
        return;
      }
    });

    // if(result.success) {
    //   // 保存成功推送CDN刷新
    //   const cndParams = {
    //     fileUrls: [pageLink]
    //   }
    //   await this.ctx.service.external.CDNRefresh(cndParams);
    // }

    return {
      success: result.success,
    };
  }

  async getCaseRelate(id) {
    const { app } = this;

    const thisInfo = await app.mysql.get("cases", {
      id: id,
    });

    const publishTime = moment(thisInfo.gmt_publish_time).format(
      "YYYY-MM-DD HH:mm:ss"
    );

    const prevItem = await app.mysql.query(
      'SELECT id,title FROM cases WHERE unix_timestamp(gmt_publish_time)<unix_timestamp("' +
        publishTime +
        '") AND is_publish=1 AND is_delete=0 ORDER BY gmt_publish_time DESC LIMIT 1'
    );
    const nextItem = await app.mysql.query(
      'SELECT id,title FROM cases WHERE unix_timestamp(gmt_publish_time)>unix_timestamp("' +
        publishTime +
        '") AND is_publish=1 AND is_delete=0 ORDER BY gmt_publish_time DESC LIMIT 1'
    );
    //const nextItem = await app.mysql.get('news',{gmt_publish_time: > publishTime});

    return {
      prevItem,
      nextItem,
    };
  }

  async otherCase(id, cid) {
    const { app } = this;
    const cata = await app.mysql.get("catalog_relation", {
      case_id: id,
    });
    if (cata) {
      const catalog_id = cata.catalog_id;
      const list = await app.mysql.query(
        "SELECT n.id,n.title,n.content FROM cases n LEFT JOIN catalog_relation cr ON n.id=cr.case_id WHERE n.is_delete=0 AND n.is_publish=1 AND n.id!=" +
          id +
          " AND cr.catalog_id=" +
          catalog_id +
          " ORDER BY n.is_top DESC,n.gmt_publish_time DESC LIMIT 0,10"
      );

      return {
        list,
      };
    } else {
      return {
        list: [],
      };
    }
  }

  async updatePublish(params) {
    const { app } = this;

    if (!params.id) {
      return "fail";
    }

    const row = {
      id: params.id,
      is_publish: params.is_publish,
    };
    row.gmt_modify = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
    const result = await app.mysql.update("cases", row);

    const info = await app.mysql.get("cases", {
      id: params.id,
    });
    await this.ctx.service.logs.log({
      type: "case",
      id: params.id,
      log: "修改上下架",
      model: "资讯中心",
      name: info.title,
    });

    // 获取html文本中的第一张图片
    let contentImg = "";
    params.content.replace(
      /<img [^>]*src=['"]([^'"]+)[^>]*>/,
      function (match, capture) {
        contentImg = capture;
      }
    );
    let cataInfo = await app.mysql.get("catalog", {
      id: params.type,
    });

    if (params.is_publish == 1) {
      const info = await app.mysql.get("cases", {
        id: params.id,
      });
      if (!info) {
        return;
      }

      let tradeNames = [],
        serviceNames = [];
      const tradeList = await app.mysql.select("catalog_relation", {
        case_id: params.id,
        catalog_type: 1,
      });
      if (tradeList && tradeList.length > 0) {
        for (let item of tradeList) {
          let cataInfo = await app.mysql.get("catalog", {
            id: item.catalog_id,
          });
          if (cataInfo) {
            tradeNames.push(cataInfo.name);
          }
        }
      }

      const serviceList = await app.mysql.select("catalog_relation", {
        case_id: params.id,
        catalog_type: 2,
      });
      if (serviceList && serviceList.length > 0) {
        for (let item of serviceList) {
          let cataInfo = await app.mysql.get("catalog", {
            id: item.catalog_id,
          });
          if (cataInfo) {
            serviceNames.push(cataInfo.name);
          }
        }
      }
      const cReg = new RegExp(/(<p>)[^>]*>([\s\S]*?)(?=<\/p>)/gi);
      const newDetail = info.content.match(cReg);
      let outSolrCon = "";
      if (newDetail) {
        for (let item2 of newDetail) {
          item2 = item2.replace(/<[^>]*>/g, "");
          if (item2.trim() != "" && outSolrCon == "") {
            outSolrCon = item2;
          }
        }
      } else {
        outSolrCon = info.content.replace(/<[^>]*>/g, "");
      }
      let options = {
        url: env[this.app.config.env].solrUrl + "/add",
        method: "POST",
        headers: {
          pid: solrPid,
          timestamp: new Date().getTime(),
        },
        json: true,
        body: {
          id: "C_" + info.id,
          title: info.title,
          alias: row.alias,
          sub_title: "",
          content: info.content.replace(/<.*?>/g, "").replace(/\n/g, ""),
          // summary: outSolrCon,
          summary: "",
          type: "2",
          buy: 0,
          industry: tradeNames.join(","),
          service: serviceNames.join(","),
          imgUrl: contentImg,
          catalog: params.catalog,
          subType: cataInfo.name,
          createDate: moment(new Date()).format("YYYY/MM/DD"),
        },
      };

      let parr = JSON.stringify(options.body);
      parr += solrPstr;
      parr = crypto.createHash("md5").update(parr).digest("hex");
      options.headers.sign = parr;

      request(options, function (error, response, body) {
        if (error || response.statusCode > 400) {
          return;
        }
      });
    } else {
      let options = {
        url: env[this.app.config.env].solrUrl + "/del",
        method: "POST",
        headers: {
          pid: solrPid,
          timestamp: new Date().getTime(),
        },
        json: true,
        body: {
          id: "C_" + params.id,
        },
      };

      let parr = JSON.stringify(options.body);
      parr += solrPstr;
      parr = crypto.createHash("md5").update(parr).digest("hex");
      options.headers.sign = parr;

      request(options, function (error, response, body) {
        if (error || response.statusCode > 400) {
          return;
        }
      });
    }

    const success = result.affectedRows === 1;
    return {
      success,
    };
  }

  async caseModify(params) {
    const { app } = this;

    const row = {
      id: params.id,
      is_top: params.is_top ? 1 : 0,
      is_hot: params.is_hot,
      is_public: params.is_public,
      title: params.title,
      gmt_modify: await moment().format("YYYY-MM-DD HH:mm:ss"),
    };
    row.gmt_modify = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
    const result = await app.mysql.update("cases", row);

    return {
      success: result.affectedRows == 1 ? true : false,
    };
  }

  async caseModiyMultiple(params) {
    const { app } = this;

    const result = await app.mysql.query(
      `UPDATE cases SET ${params.column}=1 WHERE id in (${params.id.join(",")})`
    );

    return {
      success: result.protocol41,
    };
  }

  // 获取case详情
  async caseDtl(params) {
    const { app } = this;

    const { id } = params;
    // const detail = await app.mysql.select('cases', {
    //     columns: ['id', 'title', 'is_delete', 'is_publish', 'content', 'gmt_publish_time'],
    //     where: {
    //         id
    //     },
    // });
    const detail = await app.mysql.query(
      `SELECT a.id, a.title, a.hits, a.is_delete, a.is_publish, a.content, a.gmt_publish_time, b.name FROM cases a LEFT JOIN catalog b ON a.catalog_id = b.id WHERE a.id = ${id}`
    );
    // 获取html文本中的第一张图片
    detail[0].content.replace(
      /<img [^>]*src=['"]([^'"]+)[^>]*>/,
      function (match, capture) {
        detail[0].contentImg = capture;
      }
    );
    detail[0].content = detail[0].content.replace(/&lt;/g, "<");
    detail[0].content = detail[0].content.replace(/&gt;/g, ">");
    detail[0].content = detail[0].content.replace(/&nbsp;/g, " ");
    detail[0].content = detail[0].content.replace(/&mdash;/g, "—");
    detail[0].content = detail[0].content.replace(/<[^>]*>/g, "");
    detail[0].publishTime = moment(detail[0].gmt_publish_time).format(
      "YYYY / MM / DD"
    );
    return {
      success: true,
      detail: detail[0],
    };
  }

  /* 保存推荐的sku */
  async saveSku(params) {
    const { app } = this;
    /* 先将数据重置为失效 */
    const setFail = await app.mysql.query(
      `UPDATE content_recommend SET STATE = 0  WHERE CONTENT_ID = ${params.id}`
    );
    /* 在进行输入插入 */
    let now = new Date();
    now = moment().format("YYYY-MM-DD HH:mm:ss");
    /* 为空不做处理 */
    if (params.recommends && params.recommends.length) {
      const values = params.recommends
        .map((v) => {
          return `('cases', ${params.id}, 'SKU', '${v.sku_id || ""}', '${ v.sku_image || ""  }', '${v.sku_url || ""}', '${v.sku_name || ""}', '${  v.sub_title || "" }', 1, '${now}', '${now}')`;
        }).join(",");
      // console.log(`INSERT INTO content_recommend (CONTENT_TYPE, CONTENT_ID, RELATE_CONTENT_TYPE, RELATE_CONTENT_ID, RELATE_CONTENT_IMAGE, RELATE_CONTENT_URL,RELATE_CONTENT_NAME, RELATE_CONTENT_SUB_TITLE, STATE, CREATE_DATE, STATE_DATE) values ${values}`)
      const detail = await app.mysql.query(
        `INSERT INTO content_recommend (CONTENT_TYPE, CONTENT_ID, RELATE_CONTENT_TYPE, RELATE_CONTENT_ID, RELATE_CONTENT_IMAGE, RELATE_CONTENT_URL,RELATE_CONTENT_NAME, RELATE_CONTENT_SUB_TITLE, STATE, CREATE_DATE, STATE_DATE) values ${values}`
      );
      return {
        success: true,
        detail: detail[0],
      };
    } else {
      return {
        success: true,
        detail: {},
      };
    }
  }

  /* 获取推荐的sku */
  async getSku(id) {
    const { app } = this;
    const detail = await app.mysql.query(
      `select * from content_recommend cr where  CONTENT_TYPE = 'cases' and  RELATE_CONTENT_TYPE = 'SKU' and STATE = 1 and CONTENT_ID = ${id}`
    );
    return {
      success: true,
      detail: detail,
    };
  }
}

module.exports = CaseService;
