'use strict';

const Controller = require('egg').Controller;
const _info = require('../../config/info').siteInfo;
const { env } = require('../../config/info').siteInfo;

class CatalogController extends Controller {
  async serviceCata() {
    const {
      ctx
    } = this;

    const params = [];

    let onlyEditPurview = 0;

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '服务分类权限');

      if (dataids == null) {
        dataids = {
          edit: 0
        };
        dataids.ids = [0];
      }

      params.ids = dataids.ids;
    }

    const serviceList = await ctx.service.catalog.getSercataList(params, '1,2');

    if (this.ctx.session.userInfo.type == 1) {
      onlyEditPurview = 0;
    } else {
      const purviewList = await ctx.service.admin.getMenuPurview2(this.ctx.session.userInfo.name, '编辑+归类');

      for (var menuId of purviewList) {
        if (menuId == 3) //3是菜单表里面的服务分类
        {
          onlyEditPurview = 1;
          break;
        }
      }
    }

    const logs = await ctx.service.logs.listAll({
      model: '服务分类',
      page: 1,
      limit: 10
    });


    await ctx.render('catalog_service', {
      ticMallHost: env[this.app.config.env].ticMallHost,
      site_title: _info.site_title,
      page_title: '服务分类配置',
      active: '2-1',
      csrf: ctx.csrf,
      list: JSON.stringify(serviceList.list),
      length: serviceList.list.length - 1,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
      onlyEditPurview: onlyEditPurview
    });
  }

  async trade() {
    const {
      ctx
    } = this;

    const params = [];

    let onlyEditPurview = 0;

    if (this.ctx.session.userInfo.type != 1) {
      let dataids = await ctx.service.admin.getDataPurview(this.ctx.session.userInfo.name, '行业分类权限');
      if (dataids == null) {
        dataids = {
          edit: 0
        };
        dataids.ids = [0];
      }
      params.ids = dataids.ids;
    }
    const tradeList = await ctx.service.catalog.getTradeList(params);
    const logs = await ctx.service.logs.listAll({
      model: '行业分类',
      page: 1,
      limit: 10
    });

    if (this.ctx.session.userInfo.type == 1) {
      onlyEditPurview = 0;
    } else {
      const purviewList = await ctx.service.admin.getMenuPurview2(this.ctx.session.userInfo.name, '编辑+归类');

      for (var menuId of purviewList) {
        if (menuId == 4) //4是菜单表里面的行业分类
        {
          onlyEditPurview = 1;
          break;
        }
      }
    }

    await ctx.render('catalog_trade', {
      site_title: _info.site_title,
      page_title: '行业分类配置',
      active: '2-2',
      csrf: ctx.csrf,
      list: JSON.stringify(tradeList.list),
      length: tradeList.list.length - 1,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs),
      onlyEditPurview: onlyEditPurview
    });
  }

  async tradeAdd() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const name = params.name,
      en_name = params.en_name,
      alias = params.alias,
      short = params.short,
      pid = params.pid;
    const result = await ctx.service.catalog.tradeAdd(name, en_name, alias, short, pid);
    await ctx.service.external.navigationUpdate(name, en_name, alias, short, pid);
    await ctx.service.external.navigationRefresh();

    if (result.info) {
      ctx.body = {
        success: true,
        data: result.info
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async tradeUpdateSingle() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const id = Number(params.id),
      type = params.type,
      value = params.value;
    const result = await ctx.service.catalog.tradeUpdateSingle(id, value, type);
    // await ctx.service.external.navigationUpdate(id, value, type);
    // await ctx.service.external.navigationRefresh();
    const isNavi = value ? 1 : 0
    await ctx.service.external.navigationSetting({ id, isNavi }, {});

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async tradeSort() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const id = params.id,
      sort = params.sort,
      type = params.type,
      pid = params.pid;
    const result = await ctx.service.catalog.tradeSort(id, sort, type, pid);
    await ctx.service.external.navigationUpdate(id, sort, type, pid);
    await ctx.service.external.navigationRefresh();

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async tradeDelete() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const id = params.id;

    const result = await ctx.service.catalog.tradeDelete(id, params);
    await ctx.service.external.navigationUpdate(id);
    await ctx.service.external.navigationRefresh();
    let success = result.success;

    if (success) {
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      ctx.body = {
        fail: true,
        data: null
      };
    }
  }

  async tradeEdit() {
    const {
      ctx
    } = this;
    const params = ctx.params;
    const type = params.type;
    const id = Number(params.id) || '';

    const detail = await ctx.service.catalog.getCatalogInfo(id);
    await ctx.service.external.navigationUpdate(id);
    await ctx.service.external.navigationRefresh();
    const logs = await ctx.service.logs.listAll({
      type: 'catalog',
      id: id,
      page: 1,
      limit: 10
    });

    let banner = detail.banner;
    if (!banner || banner.length == 0) {
      banner = [{
        img_path: '',
        btn_text: '',
        btn_url: 'https://',
        btn_description: ''
      }];
    }
    let page_title = '编辑服务';
    let active = '2-1';
    if (type == 'trade') {
      page_title = '编辑行业';
      active = '2-2';
    }
    let onlyEditPurview = 0;
    if (this.ctx.session.userInfo.type != 1) {
      const purviewList = await ctx.service.admin.getMenuPurview2(this.ctx.session.userInfo.name, '编辑+归类');
      var menuId_value = 3; //3是菜单表里面的服务分类
      if (type == 'trade') {
        menuId_value = 4; //行业菜单
      }
      for (var menuId of purviewList) {
        if (menuId == menuId_value) {
          onlyEditPurview = 1;
          break;
        }
      }
    }

    if (detail.info.description) {
      detail.info.description = detail.info.description.replace(/\n/g, '\\n');
    }

    await ctx.render('trade_edit', {
      site_title: _info.site_title,
      ticMallHost: env[this.app.config.env].ticMallHost,
      page_title: page_title,
      active: active,
      csrf: ctx.csrf,
      info: detail.info,
      banner: banner,
      children: detail.children,
      solution: detail.solution || [],
      bigType: type,
      cases: detail.cases,
      resource: detail.resource,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      onlyEditPurview: onlyEditPurview,
      logs: JSON.stringify(logs)
    });

  }

  async tradeSave() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;

    const result = await ctx.service.catalog.editCatalogInfo(params);

    if (result.success) {
      // 保存成功推送CDN刷新
      // if (result.success) {
      //   const cndParams = {
      //     fileUrls: [params.pageLink]
      //   }
      //   await ctx.service.external.CDNRefresh(cndParams);
      // }
      ctx.body = {
        success: true,
        data: null
      };
    } else {
      let msg = '保存失败';
      if (result == 1001) {
        msg = '已存在相同名称的分类，请修改分类名称。';
      }
      ctx.body = {
        fail: true,
        msg: msg
      };
    }
  }

  async catalogSkusList() {
    const {
      ctx
    } = this;
    let params = ctx.params;

    if (!params.page) {
      params.page = 1;
    }

    if (!params.limit) {
      params.limit = 30;
    }
    const result = await ctx.service.sku.getCatalogSkus(params, '1,2');
    const logs = await ctx.service.logs.listAll({
      type: 'catalog',
      id: params.id,
      page: 1,
      limit: 10
    });
    //await ctx.service.sku.updateSkuCata();

    let active = '2-1';
    if (params.bigType == 'trade') {
      active = '2-2';
    }
    await ctx.render('catalog_sku_list', {
      type: 'skus',
      site_title: _info.site_title,
      page_title: '归类SKU',
      active: active,
      csrf: ctx.csrf,
      bigType: params.bigType,
      list: JSON.stringify(result.list),
      total: result.total,
      id: params.id,
      cataList: result.cataList,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs)
    });
  }

  /* 归类解决方案 */
  async catalogSolutionList() {
    const {
      ctx
    } = this;
    let params = ctx.params;

    if (!params.page) {
      params.page = 1;
    }

    if (!params.limit) {
      params.limit = 30;
    }
    const result = await ctx.service.sku.getCatalogSkus(params, '3,4');
    const logs = await ctx.service.logs.listAll({
      type: 'catalog',
      id: params.id,
      page: 1,
      limit: 10
    });
    //await ctx.service.sku.updateSkuCata();

    let active = '2-1';
    if (params.bigType == 'trade') {
      active = '2-2';
    }
    await ctx.render('catalog_sku_list', {
      type: 'solution',
      site_title: _info.site_title,
      page_title: '归类解决方案',
      active: active,
      csrf: ctx.csrf,
      bigType: params.bigType,
      list: JSON.stringify(result.list),
      total: result.total,
      id: params.id,
      cataList: result.cataList,
      view_url: env[this.app.config.env].view_url,
      mview_url: env[this.app.config.env].mview_url,
      userInfo: ctx.session.userInfo,
      logs: JSON.stringify(logs)
    });
  }

  async getCatalogSkus2() {
    const {
      ctx
    } = this;
    let params = ctx.request.body;
    let type = ''
    if (params.type === 'skus') {
      type = '1,2';
    }
    if (params.type === 'solution') {
      type = '3,4';
    }
    const result = await ctx.service.sku.getCatalogSkus(params, type);

    ctx.body = result;
  }

  async saveRow() {
    const {
      ctx
    } = this;
    let params = ctx.request.body;

    const result = await ctx.service.catalog.saveRow(params);

    ctx.body = result;
  }

  async defaultSort() {
    const {
      ctx
    } = this;
    let id = ctx.request.body.id;

    const result = await ctx.service.catalog.defaultSort(id);

    ctx.body = result;
  }
};

module.exports = CatalogController;