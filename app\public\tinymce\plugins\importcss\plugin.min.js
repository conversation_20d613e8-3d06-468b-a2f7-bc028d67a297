!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),d=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),i=tinymce.util.Tools.resolve("tinymce.EditorManager"),n=tinymce.util.Tools.resolve("tinymce.Env"),y=tinymce.util.Tools.resolve("tinymce.util.Tools"),s=function(e){return e.getParam("importcss_merge_classes")},r=function(e){return e.getParam("importcss_exclusive")},_=function(e){return e.getParam("importcss_selector_converter")},u=function(e){return e.getParam("importcss_selector_filter")},l=function(e){return e.getParam("importcss_groups")},a=function(e){return e.getParam("importcss_append")},f=function(e){return e.getParam("importcss_file_filter")},m=function(e){var t=n.cacheSuffix;return"string"==typeof e&&(e=e.replace("?"+t,"").replace("&"+t,"")),e},g=function(e,t){var n=e.settings,r=!1!==n.skin&&(n.skin||"lightgray");return!!r&&t===(n.skin_url?e.documentBaseURI.toAbsolute(n.skin_url):i.baseURL+"/skins/"+r)+"/content"+(e.inline?".inline":"")+".min.css"},x=function(t){return"string"==typeof t?function(e){return-1!==e.indexOf(t)}:t instanceof RegExp?function(e){return t.test(e)}:t},T=function(o,e,s){var u=[],n={};y.each(o.contentCSS,function(e){n[e]=!0}),s||(s=function(e,t){return t||n[e]});try{y.each(e.styleSheets,function(e){!function t(e,n){var r,i=e.href;if((i=m(i))&&s(i,n)&&!g(o,i)){y.each(e.imports,function(e){t(e,!0)});try{r=e.cssRules||e.rules}catch(c){}y.each(r,function(e){e.styleSheet?t(e.styleSheet,!0):e.selectorText&&y.each(e.selectorText.split(","),function(e){u.push(y.trim(e))})})}}(e)})}catch(t){}return u},k=function(e,t){var n,r=/^(?:([a-z0-9\-_]+))?(\.[a-z0-9_\-\.]+)$/i.exec(t);if(r){var i=r[1],c=r[2].substr(1).split(".").join(" "),o=y.makeMap("a,img");return r[1]?(n={title:t},e.schema.getTextBlockElements()[i]?n.block=i:e.schema.getBlockElements()[i]||o[i.toLowerCase()]?n.selector=i:n.inline=i):r[2]&&(n={inline:"span",title:t.substr(1),classes:c}),!1!==s(e)?n.classes=c:n.attributes={"class":c},n}},P=function(e,t){return null===t||!1!==r(e)},c=k,t=function(h){h.on("renderFormatsMenu",function(e){var t,p={},c=x(u(h)),v=e.control,o=(t=l(h),y.map(t,function(e){return y.extend({},e,{original:e,selectors:{},filter:x(e.filter),item:{text:e.title,menu:[]}})})),s=function(e,t){if(f=e,g=p,!(P(h,m=t)?f in g:f in m.selectors)){u=e,a=p,P(h,l=t)?a[u]=!0:l.selectors[u]=!0;var n=(c=(i=h).plugins.importcss,o=e,((s=t)&&s.selector_converter?s.selector_converter:_(i)?_(i):function(){return k(i,o)}).call(c,o,s));if(n){var r=n.name||d.DOM.uniqueId();return h.formatter.register(r,n),y.extend({},v.settings.itemDefaults,{text:n.title,format:r})}}var i,c,o,s,u,l,a,f,m,g;return null};a(h)||v.items().remove(),y.each(T(h,e.doc||h.getDoc(),x(f(h))),function(n){if(-1===n.indexOf(".mce-")&&(!c||c(n))){var e=(r=o,i=n,y.grep(r,function(e){return!e.filter||e.filter(i)}));if(0<e.length)y.each(e,function(e){var t=s(n,e);t&&e.item.menu.push(t)});else{var t=s(n,null);t&&v.add(t)}}var r,i}),y.each(o,function(e){0<e.item.menu.length&&v.add(e.item)}),e.control.renderNew()})},o=function(t){return{convertSelectorToFormat:function(e){return c(t,e)}}};e.add("importcss",function(e){return t(e),o(e)})}();