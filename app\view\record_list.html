<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        反馈中心
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="24">
                                <el-form :model="form" :inline="true">
                                    <el-form-item label="时间">
                                        <el-date-picker v-model="form.date" type="datetimerange" align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable>
                                        </el-date-picker>
                                    </el-form-item>

                                    <el-form-item>
                                        <el-button type="primary" @click="getList">查询</el-button>
                                        <el-button @click=_default>重置</el-button>
                                        <el-button type="success" @click="_export">导出</el-button>
                                    </el-form-item>
                                </el-form>
                            </el-col>
                        </el-row>

                        <el-row v-loading="loading">

                            <el-table :data="tableList.list" stripe style="width: 100%">
                                <el-table-column prop="id" label="ID" width="40">
                                </el-table-column>
                                <el-table-column prop="url" label="URL">
                                </el-table-column>
                                <el-table-column prop="ip" label="IP">
                                </el-table-column>
                                <el-table-column prop="create_time" label="时间">
                                </el-table-column>
                            </el-table>
                            <el-pagination @current-change="handleCurrentChange" background :current-page.sync="form.page" :page-size="form.limit"
                                layout="total, prev, pager, next" :total="tableList.total" style="padding:10px 0;text-align: right;">
                            </el-pagination>
                        </el-row>
                    </el-main>
            </el-container>
    </el-container>
    <script>
    var main = new Vue({
        el: '#Main',
        data: {
            tableList: {},
            form: {
                url:'',
                ip: '',
                date: '',
                page:1,
                limit: 10,
            },
            loading: false,
        },
        methods: {
            handleCurrentChange(r) {
                this.getList();
            },
            getList(){
                var that = this;
                
                that.loading = true;
                var params = this.form;
                params._csrf = '<%- csrf %>';
                axios.post('/analysis/record', params)
                    .then(function(result) {
                        that.loading = false;
                        if (result.data.success) {
                            for(let item of result.data.data.list){
                                if(item.create_time){
                                    item.create_time = moment(item.create_time).format('YYYY-MM-DD HH:mm:ss');
                                }
                            }
                            that.tableList = result.data.data;
                        }else{
                            that.$message.error('获取列表出错');
                        }
                    });
            },
            _default(){
                this.form.url = '';
                this.form.ip = '';
                this.form.date = '';
            },
            _export(){

                var params = this.form;
                var that = this;
                that.loading = true;
                params._csrf = '<%- csrf %>';
                axios.post('/analysis/record_export', params)
                    .then(function(result) {
                        that.loading = false;
                        if(result.data.success){
                            window.location = result.data.data;
                        }
                    });
            }
        },
        mounted(){
            this.$nextTick(function(){
                this.getList();
                document.getElementById('preLoading').style.display = 'none';
            });
        }
    });
    </script>
    <% include footer.html %>