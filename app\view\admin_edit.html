<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        <a href='/admin/list'>用户配置</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        修改
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row>
                            <h3 class="stit">基本资料</h3>
                            <el-form :model="form" label-width="120px"  ref="form">
                                <el-form-item label="用户名称">
                                    <el-input maxlength="50"   v-model:trim="form.name"></el-input>
                                </el-form-item>

                                <el-form-item label="所属分组">
                                    <el-select multiple v-model="form.role_id" placeholder="请选择" style="width:100%">
                                        <el-option v-for="item in roleList"  :value="item.id" :key="item.id" :label="item.name"></el-option>
                                    </el-select>
                                </el-form-item>
                               
                            </el-form>
                           
                            <hr>
                            <el-col :span="24" style="background: #39F;padding:12px 0;color:#FFF">
                                <el-col :span="12" style="padding-left:12px;">
                                    <el-button @click="_cancel">返回列表</el-button>
                                </el-col>
                                <el-col :span="12" style="text-align:right;padding-right:12px;">
                                    <el-button type="success" @click='onSubmit'> 保 存 </el-button>
                                </el-col>
                            </el-col>
                        </el-row>
                    </el-main>
            </el-container>
    </el-container>
    <script>
    var ai = 0, bi = 0;
    var main = new Vue({
        el: '#Main',
        data: {
			roleList: <%- roleList %>,
            form: {
                id: <%- id %>,
                name: '<%- form.name %>',
				role_id:<%- form.roleid_json %>
            },
            loading: false,
        },
		created: function () {
			
			 var that = this;
			 //去除冻结分组
			  var newRoleIds=[];
			  ;
			 for(var j=0;j<that.form.role_id.length;j++)
			 {
				 var roleid=that.form.role_id[j];
				 
				for(var i=0;i<that.roleList.length;i++)
				 {
					 var item=that.roleList[i];
					 ;
					 if(item.id==roleid)
					 {
						 newRoleIds.push(roleid);
						 break;
					 }
				 }
			 }
			
		  that.form.role_id=newRoleIds;
			
		},
        methods: {
            onSubmit(){
                var that = this;
				
                 if(!this.form.name){
                    this.$message.error('请输入用户名称');
                    return;
                }
				
				this.$confirm('是否确认修改数据？', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(()=>{
					this.$refs['form'].validate((valid) => {
						if (valid) {
							var loading = this.$loading({
								lock: true,
								text: 'Loading',
								spinner: 'el-icon-loading',
								background: 'rgba(255, 255, 255, 0.7)'
							});

							var params = that.form;
							params._csrf = '<%- csrf %>';
							axios.post('/admin/save', params)
								.then(function(result) {
									loading.close();
									loading.close();
									if (result.data.success) {
										that.$message({
											message: '保存成功。',
											type: 'success'
										});

										window.location = '/admin/list';
									}else{
										that.$message.error(result.data.msg);
									}
								});
						} else {
							return false;
						}
					});
				});
            },
            
            _cancel(){
                window.location = '/admin/list/';
            }
        },
        mounted(){
            this.$nextTick(function(){
              
                document.getElementById('preLoading').style.display = 'none';
            });
        }
    });
    </script>
    <% include footer.html %>