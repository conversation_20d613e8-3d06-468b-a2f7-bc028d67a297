<% include header.html %>
    <el-container id="Main">
        <% include eheader.html %>
            <el-container>
                <% include eside.html %>
                    <el-main>
                        <el-row>
                            <el-col :span="24">
                                <el-breadcrumb separator-class="el-icon-arrow-right">
                                    <el-breadcrumb-item>
                                        <a href='/'>首页</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        <a href='/other/list'>其他内容</a>
                                    </el-breadcrumb-item>
                                    <el-breadcrumb-item>
                                        添加
                                    </el-breadcrumb-item>
                                </el-breadcrumb>
                            </el-col>
                        </el-row>
                        <el-row>
                            <h3 class="stit">基本属性</h3>
                            <el-form :model="form" label-width="120px" :rules="rules" ref="form">
                                <el-form-item label="名称">
                                    <el-input v-model:trim="form.title"></el-input>
                                </el-form-item>

                                <el-form-item label="英文名称">
                                    <el-input v-model:trim="form.alias"></el-input>
                                </el-form-item>
                                <!--
                                <el-form-item label="页面链接" v-if="form.id != 0">
                                    <el-input readonly :value="'http://aonxin.com:7001/preview/news/detail-' + form.id + '.html'"></el-input>
                                </el-form-item>
                                -->
                            </el-form>
                            <hr>
                            <h3 class="stit">内容</h3>
                            <el-form :model="form" label-width="120px">
                                <el-form-item label="banner图片">
                                    <el-upload class="cover-uploader" 
                                    :data='uploadData' 
                                    name="file" 
                                    :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"' 
                                    :show-file-list="false" :on-success="bannerSuccess" 
                                    :before-upload="beforeAvatarUpload" 
                                    accept="image/jpeg,image/png,image/gif" 
                                    style="width:810px;height:127px;">
                                        <img v-if="form.banner_img" :src="form.banner_img" class="cover" style="position: relative;z-index: 2;width:810px;height:127px;">
                                        <i v-else class="el-icon-plus cover-uploader-icon" style="width:810px;height:127px;line-height: 127px"></i>
                                        <span style="position: absolute;bottom:10px;display:block;width:100%;text-align:center;z-index:1">选择图片(JPG,PNG,GIF, 1920px * 300px)</span>
                                    </el-upload>
                                </el-form-item>
                                <el-form-item label="移动端Banner图片">
                                    <el-upload class="cover-uploader" :data='uploadData' name="file" 
                                    :action='ticMallHost + "ticCenter/business/api.v0.platform/fileUpload/uploadOss"' 
                                    :show-file-list="false" :on-success="mbannerSuccess" 
                                    :before-upload="beforeAvatarUpload" 
                                    accept="image/jpeg,image/png,image/gif" 
                                    style="width:750px;height:360px;">
                                        <img v-if="form.m_banner_img" :src="form.m_banner_img" class="cover" style="position: relative;z-index: 2;width:750px;height:360px;">
                                        <i v-else class="el-icon-plus cover-uploader-icon" style="width:750px;height:360px;line-height: 360px"></i>
                                        <span style="position: absolute;bottom:10px;display:block;width:100%;text-align:center;z-index:1">选择图片(JPG,PNG,GIF, 750px * 360px)</span>
                                    </el-upload>
                                </el-form-item>

                                <el-form-item label="Banner标题">
                                    <el-input v-model.trim="form.banner_text"></el-input>
                                </el-form-item>

                                <el-form-item label="banner文案">
                                    <el-input v-model.trim="form.banner_description"></el-input>
                                </el-form-item>

                                <el-form-item label="内容">
                                    <textarea v-model="form.content" cols="60" rows="5" class="el-input__inner" style="height:600px" id="Content"></textarea>
                                </el-form-item>

                                <el-form-item label="关键字">
                                        <el-input v-model:trim="form.seo_text"></el-input>
                                    </el-form-item>
                            </el-form>

                            <hr>
                            <h3 class="stit">SEO设置-PC端</h3>
                            <el-row :gutter="20">
                                <el-form label-width="120px">
                                    <el-form-item label="Title">
                                        <el-input v-model="form.page_title" auto-complete="off" maxlength="50"></el-input>
                                    </el-form-item>
                            
                                    <el-form-item label="Keywords">
                                        <el-input v-model="form.page_keywords" auto-complete="off" maxlength="50"></el-input>
                                    </el-form-item>
                            
                                    <el-form-item label="Description">
                                        <el-input v-model="form.page_description" auto-complete="off" maxlength="200"></el-input>
                                    </el-form-item>
                                </el-form>
                            </el-row>
                            <hr>
                            <h3 class="stit">SEO设置-移动端<el-button size="small" style="margin-left: 20px;" @click="handleCopyTDK" type="primary">同PC设置</el-button></h3>
                            <el-row :gutter="20">
                                <el-form label-width="120px">
                                    <el-form-item label="mTitle">
                                        <el-input v-model="form.mpage_title" auto-complete="off" maxlength="50"></el-input>
                                    </el-form-item>
                            
                                    <el-form-item label="mKeywords">
                                        <el-input v-model="form.mpage_keywords" auto-complete="off" maxlength="50"></el-input>
                                    </el-form-item>
                            
                                    <el-form-item label="mDescription">
                                        <el-input v-model="form.mpage_description" auto-complete="off" maxlength="200"></el-input>
                                    </el-form-item>
                                </el-form>
                            </el-row>
                            <hr>
                            <el-col :span="24" style="background: #39F;padding:12px 0;color:#FFF">
                                <el-col :span="12" style="padding-left:12px;">
                                    <el-button @click="_cancel">返回列表</el-button>
                                </el-col>
                                <el-col :span="12" style="text-align:right;padding-right:12px;">
                                    <el-button type="" @click="preview"> 预 览 </el-button>
                                    <el-button type="success" @click='onSubmit'> 保 存 </el-button>
                                </el-col>
                            </el-col>
                        </el-row>
                    </el-main>
            </el-container>
    </el-container>
    <script src="/static/tinymce/tinymce.min.js"></script>
    <script>
    var ai = 0, bi = 0;
    var tinyConfig = {
        height: 600,
        language: 'zh_CN',
        menubar: false,
        plugins: 'advlist autolink link image lists charmap print preview code textcolor table paste media',
        toolbar: 'undo redo | formatselect fontsizeselect bold italic underline forecolor backcolor  | alignleft aligncenter alignright alignjustify superscript subscript | bullist numlist outdent indent | removeformat | image media link table | code preview',
        fontsize_formats: '12px 14px 16px 18px 20px 24px 36px',
        file_browser_callback_types: 'image',
        // images_upload_url: '<%- ticMallHost %>ticCenter/business/api.v0.platform/fileUpload/uploadOss',
        images_reuse_filename: true,
        // images_upload_base_path: '/',
        relative_urls: false,
        branding: false,
        width: '100%',
        images_upload_handler: function (blobInfo, success, failure) {
            let that = this
            let fd = new FormData()
            let file = blobInfo.blob();
            fd.append('file', file, file.name)
            fd.append('systemID', 5);
            let config = {
                headers: { 'Content-Type': 'multipart/form-data' }
            }
            axios.post('<%- ticMallHost %>ticCenter/business/api.v0.platform/fileUpload/uploadOss', fd, config)
                .then(function (result) {
                    if (result.data.resultCode === '0') {
                        success(result.data.data.fileName)
                    } else {
                        that.$message.success(result.data.res);
                        failure()
                    }
                });
        }
    };
    var main = new Vue({
        el: '#Main',
        data: {
            uploadData: { systemId: 5 },
            ticMallHost: '<%- ticMallHost %>',
            form: {
                id: 0,
                title: '',
                content: '',
                seo_text: '',
                banner_img: '',
                banner_text: '',
                banner_description: '',
                page_title:'',
                page_keywords:'',
                page_description:'',
                m_banner_img: '',
                mpage_title: '',
                mpage_keywords: '',
                mpage_description: '',
            },
            loading: false,
            rules: {
                name: [
                    { required: true, message: '请输入名称', trigger: 'blur' },
                    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
                    { validator: nameCheck, trigger: 'blur' },
                ],
            },
        },
        methods: {
            handleCopyTDK(){
                this.form.mpage_title = this.form.page_title;
                this.form.mpage_keywords = this.form.page_keywords;
                this.form.mpage_description = this.form.page_description;
            },
            bannerSuccess(res, file) {
                if (res.resultCode === '0') {
                    this.form.banner_img = res.data.fileName;
                } else {
                    this.$message.error(res.resultMsg);
                }
            },
            mbannerSuccess(res, file) {
                if (res.resultCode === '0') {
                    this.form.m_banner_img = res.data.fileName;
                } else {
                    this.$message.error(res.resultMsg);
                }
            },
            beforeAvatarUpload(file) {
                const isJPG = file.type === 'image/jpeg';
                const isPNG = file.type === 'image/png';
                const isGIF = file.type === 'image/gif';
                const isLt2M = file.size / 1024 / 1024 < 10;

                if (!isJPG && !isPNG && !isGIF) {
                    this.$message.error('图片格式错误!');
                    return false;
                }
                if (!isLt2M) {
                    this.$message.error('上传图片大小不能超过 10MB!');
                    return false;
                }
                //return isJPG && isLt2M;
            },
            onSubmit(){
                var that = this;
                
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        var loading = this.$loading({
                            lock: true,
                            text: 'Loading',
                            spinner: 'el-icon-loading',
                            background: 'rgba(255, 255, 255, 0.7)'
                        });

                        var params = that.form;
                        params.content = tinymce.get('Content').getContent();
                        params._csrf = '<%- csrf %>';
                        axios.post('/other/save', params)
                            .then(function(result) {
                                loading.close();
                                loading.close();
                                if (result.data.success) {
                                    //window.location.href = '/sku/service'
                                    that.$message({
                                        message: '保存成功。',
                                        type: 'success'
                                    });

                                    window.location = '/other/edit/'+result.data.data;
                                }else{
                                    that.$message.error(result.data.msg);
                                }
                            });
                    } else {
                        return false;
                    }
                });
            },
            _default() {
               
            },
            _cancel(){
                window.location = '/other/list/';
            },
            preview(){
                var that = this;
                
                that.form.content = tinymce.get('Content').getContent();
                
                var params = that.form;
                params._csrf = '<%- csrf %>';
                axios.post('/preview/preother',params).
                    then(function(result){
                        if(result.data.success){
                            window.open('/preview/preother/'+result.data.data);
                        }
                    });
            }
        },
        mounted(){
            this.$nextTick(function(){
                tinyConfig.selector = '#Content';
                tinymce.init(tinyConfig);

                document.getElementById('preLoading').style.display = 'none';
            });
        }
    });
    </script>
    <% include footer.html %>